FROM repo-dev.htsc/public-cncp-image-base-local/node:20 AS builder
# FROM node:20-alpine AS builder

WORKDIR /src

ENV PRISMA_ENGINES_MIRROR http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/prisma

COPY package.json yarn.lock .npmrc ./
COPY .husky .husky

RUN yarn --ignore-engines

COPY . .
RUN yarn build

FROM repo-dev.htsc/public-cncp-image-base-local/node:20 AS runner
# FROM node:20-alpine AS runner

WORKDIR /app

COPY --from=builder /src/dist .
COPY --from=builder /src/node_modules/ ./node_modules/

RUN dnf install -y rsync git cronie diffutils patch && dnf clean all

RUN git config --global user.email "<EMAIL>"
RUN git config --global user.name "user"

# Corepack
# COPY docker/corepack.tgz /corepack.tgz
# RUN corepack enable pnpm
# RUN corepack install -g /corepack.tgz

# Setup workspace
# COPY scripts /app/scripts
# RUN node /app/scripts/setup-workspace.mjs docker
# RUN cd /workspace && yarn

# 安装xdg-utils
RUN yum install -y xdg-utils

# 设置NPM的默认registry
RUN npm config set registry http://registry.npm.htsc/

# 安装自定义项目所需的最小全局依赖
RUN npm install -g husky

# 先设置 Yarn 的默认 registry
RUN yarn config set registry http://registry.npm.htsc/

# 用 Yarn 全局安装 PNPM
RUN yarn global add pnpm@9.15.0

# 配置 pnpm 减少文件监听
RUN pnpm config set auto-install-peers false
RUN pnpm config set dedupe-peer-dependents false
RUN pnpm config set prefer-symlinked-executables false

COPY docker/entrypoint.sh entrypoint.sh
COPY scripts scripts

# Create crontab entry to run archive.mjs at 2:00 AM daily
RUN echo "0 2 * * * node /app/scripts/archive.mjs >> /app/archive-log.txt 2>&1" > /etc/cron.d/archive-cron
RUN chmod 0644 /etc/cron.d/archive-cron

ENV NODE_ENV production
ENV PORT 8080

# 设置环境变量优化 Node.js 和 pnpm 行为
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PNPM_HOME="/app/.pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# FIXME 应用需要自己绑定到所有网络接口，否则无法访问
# host: '0.0.0.0'
EXPOSE 8000
EXPOSE 8001
EXPOSE 8002
EXPOSE 8003
EXPOSE 8004

EXPOSE 8080

ENTRYPOINT ["/app/entrypoint.sh"]