#!/usr/bin/env node

import fs from 'fs/promises';
import { createWriteStream } from 'fs';
import stream from 'stream';
import { pipeline } from 'stream/promises';
import path from 'path';
import yazl from 'yazl';
import moment from 'moment';

// 获取HOME_DIR环境变量
const homeDir = process.env.HOME_DIR;

if (!homeDir) {
  console.error('错误: 未设置HOME_DIR环境变量');
  process.exit(1);
}

// 计算一个月前的时间戳
const oneMonthAgo = moment().subtract(1, 'm').toDate();

async function main() {
  try {
    console.log(`扫描目录: ${homeDir}`);
    
    // 读取目录中的所有条目
    const entries = await fs.readdir(homeDir, { withFileTypes: true });
    
    // 过滤出所有文件夹
    const folders = entries.filter(entry => entry.isDirectory());
    
    console.log(`找到 ${folders.length} 个文件夹`);
    
    let processedCount = 0;
    let archivedCount = 0;
    
    // 处理每个文件夹
    for (const folder of folders) {
      const folderPath = path.join(homeDir, folder.name);
      
      try {
        // 获取文件夹的状态信息
        const stats = await fs.stat(folderPath);
        const modifiedTime = stats.mtime;
        
        // 检查文件夹是否超过一个月未修改
        if (modifiedTime < oneMonthAgo) {
          await archiveDirectory({
            home: homeDir,
            name: folder.name
          });
          archivedCount++;
        } else {
          console.log(`跳过: ${folderPath} (最近一个月内有修改)`);
        }
        
        processedCount++;
      } catch (err) {
        console.error(`处理 ${folderPath} 时出错:`, err);
      }
    }
    
    console.log('\n总结:');
    console.log(`处理的文件夹: ${processedCount}`);
    console.log(`压缩的文件夹: ${archivedCount}`);
    
  } catch (err) {
    console.error('执行过程中出错:', err);
    process.exit(1);
  }
}

main();

async function archiveDirectory({ home, name }) {
  const folderPath = path.join(home, name);
  console.log(`压缩: ${folderPath}`);
  const nodeModulesPath = path.join(folderPath, 'node_modules');
  try {    
    // 删除node_modules文件夹
    await fs.rm(nodeModulesPath, { recursive: true, force: true });
    // 压缩folderPath为zip
    await pipeline(
      await zipDirectory(folderPath),
      createWriteStream(path.join(home, `${name}.zip`)),
    );
    await fs.rm(folderPath, { recursive: true, force: true });
  } catch (err) {
    // node_modules文件夹不存在，跳过
    console.error(`压缩 ${folderPath} 时出错:`, err);
  }
}

async function zipDirectory(dir) {
  const zip = new yazl.ZipFile();
  const files = await readDirectory(dir, { recursive: true });
  for (const [name, type] of files) {
    if (type === 'File') {
      zip.addFile(path.join(dir, name), name);
    }
  }
  zip.end();
  return new stream.Readable().wrap(zip.outputStream);
}

async function readDirectory(dir, options) {
  const files = await fs.readdir(dir, {
    withFileTypes: true,
    recursive: options?.recursive ?? false,
  });
  return files.map((file) => [
    path.join(path.relative(dir, file.parentPath ?? file.path), file.name),
    file.isDirectory() ? 'Directory' : file.isSymbolicLink() ? 'SymbolicLink' : 'File',
  ]);
}
