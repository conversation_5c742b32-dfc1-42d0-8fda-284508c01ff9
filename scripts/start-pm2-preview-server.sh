#!/bin/bash

# 启动 PM2 Preview Server 脚本

echo "🚀 启动 PM2 Preview Server..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查 yarn 是否安装
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn 未安装，请先安装 Yarn"
    exit 1
fi

# 进入 pm2-preview-server 目录
cd pm2-preview-server

# 检查是否存在 node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    yarn install
fi

# 检查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "📝 创建 .env 文件..."
    cp .env.example .env
fi

# 启动服务
echo "🎯 启动 PM2 Preview Server 服务..."
yarn start:dev

echo "✅ PM2 Preview Server 已启动！"
echo "🌐 服务地址: http://localhost:3001"
echo "📚 API 文档: http://localhost:3001/api" 