#!/usr/bin/env node

import { createHash } from "node:crypto";
import { unlink, mkdir, rm, copyFile, readFile } from "node:fs/promises";
import { relative, join } from "node:path";
import chokidar from "chokidar";
import { config } from "dotenv";

config();

console.log(
  "Starting watcher..., HOME_DIR:",
  process.env.HOME_DIR,
  "WEBIDE_DIR:",
  process.env.WEBIDE_DIR,
);

// 智能配置文件监听器
const usePolling = process.env.CHOKIDAR_USEPOLLING === 'true';
const pollingInterval = parseInt(process.env.CHOKIDAR_INTERVAL || '3000', 10);

console.log(`[sync-webide] File watcher mode: ${usePolling ? 'polling' : 'native'}`);
if (usePolling) {
  console.log(`[sync-webide] Polling interval: ${pollingInterval}ms`);
}

const watcher = chokidar.watch(process.env.WEBIDE_DIR, {
  ignored: (path, _stats) => {
    // 优化：添加更多忽略规则，减少文件监听器数量
    return path.includes("node_modules") ||
           path.includes("/.git/") ||
           path.includes("\\.git\\") ||
           path.includes("/.next/") ||
           path.includes("/dist/") ||
           path.includes("/build/") ||
           path.includes("/.cache/") ||
           path.includes("/coverage/") ||
           path.includes("/tmp/") ||
           path.includes("/temp/") ||
           path.includes(".log") ||
           path.includes(".tmp") ||
           path.includes("/.pnpm/") ||
           path.includes("/.npm/") ||
           path.includes("/yarn-error.log") ||
           path.includes("/npm-debug.log");
  },
  ignoreInitial: true,
  persistent: true,
  usePolling: usePolling, // 根据环境变量决定
  interval: usePolling ? pollingInterval : undefined,
  binaryInterval: usePolling ? pollingInterval + 1000 : undefined,
  awaitWriteFinish: {
    stabilityThreshold: usePolling ? 500 : 200,
    pollInterval: usePolling ? 200 : 100
  },
  // 限制监听深度，避免监听过深的目录结构
  depth: 10,
  // 错误处理优化
  followSymlinks: false,
  ignorePermissionErrors: true
});

watcher
  .on("ready", () => {
    console.log("[sync-webide] Initial scan complete. Ready for changes");
  })
  .on("addDir", (path) => {
    try {
      console.log("[sync-webide] addDir", path, relpath(path));
      mkdir(relpath(path), { recursive: true });
    } catch (error) {
      console.error(`[sync-webide] Error in addDir: ${error.message}`);
    }
  })
  .on("unlinkDir", (path) => {
    try {
      console.log("[sync-webide] unlinkDir", path);
      rm(relpath(path), { recursive: true });
    } catch (error) {
      console.error(`[sync-webide] Error in unlinkDir: ${error.message}`);
    }
  })
  .on("add", (path) => {
    try {
      console.log("[sync-webide] add", path);
      copyFile(path, relpath(path));
    } catch (error) {
      console.error(`[sync-webide] Error in add: ${error.message}`);
    }
  })
  .on("change", async (path) => {
    try {
      console.log("[sync-webide] change", path);
      const dest = relpath(path);
      let same = false;
      try {
        same = await compareFiles(path, dest);
      } catch (e) {
        console.error(`[sync-webide] Error comparing files: ${e.message}`);
      }
      if (!same) {
        await copyFile(path, dest);
      }
    } catch (error) {
      console.error(`[sync-webide] Error in change: ${error.message}`);
    }
  })
  .on("unlink", (path) => {
    try {
      console.log("[sync-webide] unlink", path);
      unlink(relpath(path));
    } catch (error) {
      console.error(`[sync-webide] Error in unlink: ${error.message}`);
    }
  })
  .on("error", (error) => {
    console.error(`[sync-webide] Watcher error: ${error.message}`);
    // 如果是ENOSPC错误，提供建议
    if (error.code === 'ENOSPC') {
      console.error('[sync-webide] 💡 File watcher limit reached. Consider:');
      console.error('[sync-webide]    - Running with --privileged flag');
      console.error('[sync-webide]    - Using --sysctl fs.inotify.max_user_watches=524288');
      console.error('[sync-webide]    - Container will automatically fallback to polling mode');
    }
  });

async function digest(path) {
  const hash = createHash("sha256");
  hash.update(await readFile(path));
  return hash.digest("hex");
}

// Compare files by digest
async function compareFiles(src, dest) {
  const [srcDigest, destDigest] = await Promise.all([
    digest(src),
    digest(dest),
  ]);
  return srcDigest === destDigest;
}

function relpath(path) {
  return join(process.env.HOME_DIR, relative(process.env.WEBIDE_DIR, path));
}
