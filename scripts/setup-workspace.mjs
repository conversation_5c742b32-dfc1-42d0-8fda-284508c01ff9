#!/usr/bin/env node

import { mkdir, readFile, writeFile, copyFile } from 'node:fs/promises';
import { homedir } from 'node:os';
import { join } from 'node:path';

const isDocker = process.argv[2] === 'docker';
let outDir, srcDir, workspaces;
if (isDocker) {
  srcDir = './assets/templates/workspace-template';
  outDir = '/workspace';
  workspaces = [
    'data/*',
  ];
} else {
  srcDir = './src/assets/templates/workspace-template';
  outDir = join(homedir(), '.ai-coding/playground');
  workspaces = ['*'];
}
const json = await readFile(join(srcDir, 'package.json'), 'utf-8');
const pkg = JSON.parse(json);
delete pkg.scripts;

pkg.name = 'workspace-root';
pkg.workspaces = workspaces;

await mkdir(outDir, { recursive: true });
await copyFile(join(srcDir, '.yarnrc'), join(outDir, '.yarnrc'));
await copyFile(join(srcDir, 'yarn.lock'), join(outDir, 'yarn.lock'));
await writeFile(join(outDir, 'package.json'), JSON.stringify(pkg, null, 2));
