{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:lib": "vite build -c low-code.config.ts", "lint": "eslint -c .eslintrc.json ./src/**/**/*.{ts,tsx} --fix", "preview": "vite preview"}, "dependencies": {"@ai-sdk/react": "^1.1.18", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language-data": "^6.5.1", "@codemirror/theme-one-dark": "^6.1.2", "@heroui/react": "^2.7.0", "@react-aria/visually-hidden": "3.8.19", "@react-types/shared": "3.27.0", "ai": "^4.1.46", "ansi-to-react": "^6.1.6", "antd": "^5.24.3", "axios": "^1.8.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "copy-to-clipboard": "^3.3.3", "dedent": "^1.5.3", "delay": "^6.0.0", "fabric": "^6.7.0", "framer-motion": "^12.5.0", "immer": "^10.1.1", "lodash": "^4.17.21", "marked": "^15.0.7", "mitt": "^3.0.1", "moment": "^2.30.1", "pathe": "^2.0.3", "react": "18.3.1", "react-dom": "18.3.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "6.23.0", "reactflow": "^11.11.4", "shiki": "^3.0.0", "store": "^2.0.12", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "xmldom": "^0.6.0", "xpath": "^0.0.34", "yet-another-react-lightbox": "^3.11.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@types/lodash": "^4.17.15", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/xmldom": "^0.1.34", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.38", "prettier": "3.3.3", "typescript": "5.6.3", "unplugin-icons": "^22.1.0", "vite": "^5.2.0", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.3.2"}}