import react from '@vitejs/plugin-react'
import Icons from 'unplugin-icons/vite'
import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'
import tsconfigPaths from 'vite-tsconfig-paths'

// 登录接口路径
const LOG_IN_URL = 'http://168.63.85.222/web-gw/';

// 登录接口路径
const WEB_AUTH_URL = 'http://168.63.85.222/web/auth/ai-coding/';

// analyzer接口路径
const ANALYZER_URL = 'http://168.63.85.222/analyzer/';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    tsconfigPaths(),
    react(),
    Icons({
      compiler: 'jsx',
      jsx: 'react',
    }),
    svgr(),
  ],
  server: {
    proxy: {
      // 登录接口转发
      '/api/user/': {
        target: LOG_IN_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/\/api\/user/g, ''),
      },
      // 登录接口转发
      '/api/web-auth/': {
        target: WEB_AUTH_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/\/api\/user/g, ''),
      },
      // web bundle analyzer接口转发
      '/api/analyzer/': {
        target: ANALYZER_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/\/api\/analyzer/g, ''),
      },
      '/api/': {
        target: 'http://localhost:8080',
        // target: 'http://*************:8080',
        changeOrigin: true,
      }
    }
  },
})
