import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import Icons from 'unplugin-icons/vite'
import svgr from 'vite-plugin-svgr'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    tsconfigPaths(),
    react(),
    Icons({
      compiler: 'jsx',
      jsx: 'react',
    }),
    svgr(),
  ],
  build: {
    minify: false,
    lib: {
      entry: './src/LowCodeLib.tsx',
      name: 'AiCoding',
      fileName: 'AiCoding',
    },
    rollupOptions: {
      external: ['react/jsx-runtime', 'react', 'react-dom', 'react-router-dom'],
      output: {
        exports: 'named',
      },
    },
  },
})
