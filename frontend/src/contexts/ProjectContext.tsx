import { createContext, useContext, ReactNode } from 'react';
import { DesignProject } from '@/apis';

interface ProjectContextType {
  project?: DesignProject;
  onProjectUpdate?: () => void;
  setProject?: (project: DesignProject) => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

interface ProjectProviderProps {
  children: ReactNode;
  project?: DesignProject;
  onProjectUpdate?: () => void;
  setProject?: (project: DesignProject) => void;
}

export function ProjectProvider({ children, project, onProjectUpdate, setProject }: ProjectProviderProps) {
  return (
    <ProjectContext.Provider value={{ project, onProjectUpdate, setProject }}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);

  return context; // 返回undefined如果没有在Provider中使用
}