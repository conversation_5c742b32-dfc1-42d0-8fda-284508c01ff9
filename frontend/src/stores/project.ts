import { create } from 'zustand';
import { immer } from "zustand/middleware/immer";
import { createProject, listProjects, Project, updateProject } from '@/apis';

interface ProjectState {
  projects: Project[];
  current: string | null;
  addProject: (project: Omit<Project, 'id' | 'created' | 'user'>) => Promise<Project>;
  updateProject: (id: string, project: Partial<Project>) => Promise<Project>;
  removeProject: (project: Project) => void;
  setCurrent: (id?: string) => void;
  clearCurrent: () => void;
  currentProject: () => Project | null;
  // 新增：初始化方法
  initializeFromStorage: () => void;
}

export const useProjectStore = create<ProjectState>()(
  immer((set, get) => {
    // 异步加载项目列表
    (async () => {
      try {
        const projects = await listProjects();

        set({ projects: projects as Project[] });
      } catch (error) {
        console.error('Failed to load projects:', error);
        set({ projects: [] });
      }
    })()

    return {
      projects: [],
      current: null,
      async addProject(project: Omit<Project, 'id' | 'created' | 'user'>) {
        const ret = await createProject(project);
        const newProject = { ...project, ...ret } as Project;
        
        set({ projects: [...get().projects, newProject] });
        
        // 自动设置为当前项目并同步到localStorage
        const newProjectId = (ret as Project).id;

        set({ current: newProjectId });
        localStorage.setItem('ai-coding-project', newProjectId);
        
        // 触发事件通知其他组件
        window.dispatchEvent(new CustomEvent('chatProjectChanged', { detail: { projectId: newProjectId } }));

        return ret as Project;
      },
      async updateProject(id: string, project: Partial<Project>) {
        const ret = await updateProject({ ...project, id });

        set({ projects: get().projects.map(p => p.id === id ? { ...p, ...(ret as Project) } : p) });

        return ret as Project;
      },
      removeProject: (project: Project) => {
        set({ projects: get().projects.filter(p => p.id !== project.id) });
        if (get().current === project.id) {
          // 清除当前项目并同步到localStorage
          set({ current: null });
          localStorage.removeItem('ai-coding-project');
          
          // 触发事件通知其他组件
          window.dispatchEvent(new CustomEvent('chatProjectChanged', { detail: { projectId: null } }));
        }
      },
      setCurrent: (id?: string) => {
        const projectId = id ?? null;

        if (get().current === projectId) {
          return;
        }

        set({ current: projectId });
        
        // 同步到localStorage
        if (projectId) {
          localStorage.setItem('ai-coding-project', projectId);
        } else {
          localStorage.removeItem('ai-coding-project');
        }
        
        // 触发事件通知其他组件
        window.dispatchEvent(new CustomEvent('chatProjectChanged', { detail: { projectId } }));
      },
      clearCurrent: () => {
        set({ current: null });
        localStorage.removeItem('ai-coding-project');
        
        // 触发事件通知其他组件
        window.dispatchEvent(new CustomEvent('chatProjectChanged', { detail: { projectId: null } }));
      },
      currentProject: () => {
        return get().projects.find(p => p.id === get().current) ?? null;
      },
      initializeFromStorage: () => {
        // 从localStorage初始化current项目
        const storedProject = localStorage.getItem('ai-coding-project');

        if (storedProject) {
          set({ current: storedProject });
        }
      },
    };
  })
);
