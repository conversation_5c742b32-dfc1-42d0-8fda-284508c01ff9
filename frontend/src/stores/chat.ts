import { CreateMessage, Message } from "@ai-sdk/react";
import { createContext } from "react";

export const ChatContext = createContext<{
  reload(): void;
  stop(): void;
  append(message: Message | CreateMessage): void;
  streamFile(path: string, content: string): void;
  deleteMessage(messageId: string): void;
  editMessage(messageId: string, content: string): void;
  openFile(version: number, path: string): Promise<void>;
  changeVersion(version: number): void;
} | undefined>(undefined);
