import { create } from 'zustand';
import { immer } from "zustand/middleware/immer";

type ThemeType = 'light' | 'dark';

interface ThemeState {
  theme: ThemeType;
  isDark: boolean;
  setTheme: (theme: ThemeType) => void;
}

export const useThemeStore = create<ThemeState>()(
  immer((set, get) => {
    const setThemeToDOM = (theme: string) => {
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(theme);
    };
    const themeValue = localStorage.getItem('theme');
    const theme = themeValue === 'light' || !themeValue ? 'light' : 'dark';

    setThemeToDOM(theme);

    return {
      theme,
      isDark: theme === 'dark',
      setTheme: (theme: ThemeType) => {
        localStorage.setItem('theme', theme);
        setThemeToDOM(theme);
        set({ theme, isDark: theme === 'dark' });
      },
    };
  })
);
