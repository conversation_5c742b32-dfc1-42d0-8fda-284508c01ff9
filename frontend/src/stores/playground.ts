import { createContext } from 'react';
import { create } from 'zustand';
import { immer } from "zustand/middleware/immer";
import { changeVersion, createDirectory, deleteFile, getFile, getPlayground, listFiles, moveFile, restartPlayground, saveFile, startPlayground, stopPlayground, versionStatus, updatePlaygroundCustomPreviewUrl } from '@/apis';
import { dirname } from '@/utils/path';

// From vscode file api
export enum FileType {
  Unknown = 0,
  File = 1,
  Directory = 2,
  SymbolicLink = 64
}

export type Playground = {
  id: string;
  name: string;
  desc: string;
  type: string;
  created: string;
  model: string;
  user: string;
  archived?: boolean;
  isPublic: boolean;
  tags?: string[] | null;
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
  enableCustomPreview?: boolean; // 是否支持用户自定义预览地址
  customPreviewUrl?: string; // 用户自定义的预览地址（持久化保存）
  enablePreview?: boolean;
  stepByStepData?: any;
  projectId?: string;
}

export type LogLine = {
  type: 'out' | 'error';
  text: string;
  time: string;
}

// 防抖工具函数
function debounce<T extends (...args: any[]) => Promise<any>>(
  func: T,
  wait: number
): T {
  let timeout: number | null = null;
  let resolveQueue: Array<{ resolve: Function; reject: Function }> = [];
  let isExecuting = false;
  let cachedResult: any = null;
  let cacheTimestamp = 0;
  const CACHE_DURATION = 5000; // 5秒缓存

  return ((...args: any[]) => {
    return new Promise((resolve, reject) => {
      // 检查缓存
      const now = Date.now();

      if (cachedResult && (now - cacheTimestamp < CACHE_DURATION)) {
        console.log('🎯 [Debounce] 使用缓存结果');
        resolve(cachedResult);

        return;
      }

      // 如果正在执行，加入队列
      if (isExecuting) {
        console.log('⏳ [Debounce] 函数执行中，加入等待队列');
        resolveQueue.push({ resolve, reject });

        return;
      }

      // 清除之前的超时
      if (timeout) {
        clearTimeout(timeout);
      }

      // 设置新的超时
      timeout = setTimeout(async () => {
        isExecuting = true;
        try {
          console.log('🚀 [Debounce] 执行防抖函数');
          const result = await func(...args);
          
          // 缓存结果
          cachedResult = result;
          cacheTimestamp = Date.now();
          
          // 返回结果给当前调用
          resolve(result);
          
          // 返回结果给队列中的所有调用
          resolveQueue.forEach(({ resolve: queueResolve }) => {
            queueResolve(result);
          });
          resolveQueue = [];
        } catch (error) {
          console.error('❌ [Debounce] 防抖函数执行失败:', error);
          reject(error);
          
          // 传播错误给队列中的所有调用
          resolveQueue.forEach(({ reject: queueReject }) => {
            queueReject(error);
          });
          resolveQueue = [];
        } finally {
          isExecuting = false;
          timeout = null;
        }
      }, wait);
    });
  }) as T;
}

// 创建防抖版本的 listFiles
const debouncedListFilesMap = new Map<string, ReturnType<typeof debounce>>();

function getDebouncedListFiles(id: string) {
  if (!debouncedListFilesMap.has(id)) {
    const debouncedFunc = debounce(async () => {
      console.log(`📂 [Debounced] 执行 listFiles (id: ${id})`);

      return await listFiles({ id });
    }, 300); // 300ms 防抖延迟
    
    debouncedListFilesMap.set(id, debouncedFunc);
  }

  return debouncedListFilesMap.get(id)!;
}

interface PlaygroundState {
  id: string;
  playground?: Playground;
  url?: string;
  files:  Record<string, {
    fileType: FileType;
    content?: string;
  }>;
  openFiles: string[];
  activeFile?: string;
  logs: LogLine[];
  currentVersion: string;
  versionList: string[];
  startDebounceTimer?: ReturnType<typeof setTimeout> | null; // 🔧 修复：使用setTimeout返回类型
  isFirstStart?: boolean; // 🔧 新增：标记是否首次启动
  start: () => Promise<void>;
  startInternal: () => Promise<void>; // 🔧 新增：内部启动方法
  stop: () => Promise<void>;
  restart: () => Promise<void>;
  updateUrl: (newUrl: string) => void; // 新增：更新预览地址
  openFile: (...pathes: string[]) => Promise<void>;
  readFiles: () => Promise<void>;
  getFile: (path: string) => string | undefined;
  readFile: (path: string) => Promise<string>;
  closeFile: (path: string) => void;
  addFile: (path: string) => Promise<void>;
  createDirectory: (path: string, server?: boolean) => Promise<void>;
  saveFile: (path: string, content: string, server?: boolean) => Promise<void>;
  moveFile: (oldPath: string, newPath: string) => Promise<void>;
  deleteFile: (path: string) => Promise<void>;
  activateFile: (path: string) => void;
  closeAllFiles: () => void;
  loadVersions: () => Promise<void>;
  changeVersion: (version: string) => Promise<void>;
}

export const PlaygroundContext = createContext<StoreType | undefined>(undefined);
export type StoreType = ReturnType<typeof createPlaygroundStore>;

/** Create a new playground store
 * @param id The playground id
 * @param source The initial state of the store. This allows new store to be created from an existing one.
 */
export const createPlaygroundStore = (id: string, source?: PlaygroundState) => create<PlaygroundState>()(
  immer(
    (set, get) => ({
      id,
      files: source?.files ?? {} as Record<string, {
        fileType: FileType;
        content?: string;
      }>,
      openFiles: source?.openFiles ?? [] as string[],
      activeFile: source?.activeFile,
      logs: source?.logs ?? [] as LogLine[],
      currentVersion: source?.currentVersion ?? '',
      versionList: [...(source?.versionList ?? [])],
      
      // 🔧 新增：防抖定时器
      startDebounceTimer: null as ReturnType<typeof setTimeout> | null,
      isFirstStart: true, // 🔧 新增：标记是否首次启动
      
      async start() {
        // 🔧 首次启动立即执行，不防抖
        if (get().isFirstStart) {
          get().isFirstStart = false;
          console.log('🚀 [Store.start] 首次启动，立即执行');

          return get().startInternal();
        }
        
        // 🔧 防抖机制：清除之前的定时器
        if (get().startDebounceTimer) {
          clearTimeout(get().startDebounceTimer!);
          console.log('🔄 [Store.start] 检测到重复调用，清除之前的定时器');
        }
        
        // 🔧 设置新的防抖定时器
        const debouncePromise = new Promise<void>((resolve, reject) => {
          get().startDebounceTimer = setTimeout(async () => {
            try {
              console.log('🚀 [Store.start] 执行防抖后的启动逻辑');
              await get().startInternal();
              resolve();
            } catch (error) {
              reject(error);
            }
          }, 100); // 减少到100ms防抖
        });
        
        return debouncePromise;
      },
      
      // 🔧 新增：内部启动方法，实际的启动逻辑
      async startInternal() {
        const pg = await getPlayground({ id });

        set({ playground: pg });

        if (pg.archived) {
          return;
        }

        get().loadVersions();

        // 🔧 检查是否应该启动预览服务（未显式关闭则默认开启）
        if (!get().url && pg.enablePreview !== false) {
          // 检查是否有基础代码文件
          // const fileList = await listFiles({ id });
          // const hasBasicFiles = fileList.some((file: any) => 
          //   file.path.includes('index.') || 
          //   file.path.includes('App.') || 
          //   file.path.includes('main.')
          // );

          // if (!hasBasicFiles) {
          //   console.log('⏳ [Store.start] 等待基础代码生成完成后再启动预览服务');

          //   return;
          // }

          try {
            const ret = await startPlayground({ id });

            // 优先使用数据库中保存的自定义预览URL
            const finalUrl = pg.enableCustomPreview && pg.customPreviewUrl 
              ? pg.customPreviewUrl 
              : (ret as any).url;

            set({ url: finalUrl });
            
            console.log('🔗 [Store.start] 预览地址设置:', {
              enableCustomPreview: pg.enableCustomPreview,
              customPreviewUrl: pg.customPreviewUrl,
              startPlaygroundUrl: (ret as any).url,
              finalUrl
            });
          } catch (e) {
            set((state) => {
              state.playground!.archived = true;
            });

            return;
          }
        }
        
        // 使用防抖版本的 listFiles
        console.log('🔄 [Store.start] 开始获取文件列表');
        const debouncedListFiles = getDebouncedListFiles(id);
        const fileList = await debouncedListFiles();
        const files = Object.fromEntries(fileList
          .map((e: any) => [e.path, {
            fileType: e.fileType,
          }]),
        );

        set((state) => {
          state.files = files;
        });
        console.log('✅ [Store.start] 文件列表更新完成，文件数量:', Object.keys(files).length);
      },
      async stop() {
        stopPlayground({ id });
      },
      async restart() {
        try {
          const ret = await restartPlayground({ id });
          
          // 获取playground信息以确定是否使用自定义预览
          const pg = get().playground;
          
          // 🔧 修复：使用类型断言处理API返回类型
          const restartResult = ret as { url: string };
          
          if (restartResult?.url) {
            // 优先使用数据库中保存的自定义预览URL
            const finalUrl = pg?.enableCustomPreview && pg?.customPreviewUrl 
              ? pg.customPreviewUrl 
              : restartResult.url;
              
            set({ url: finalUrl });
            
            console.log('🔄 [Store.restart] 重启完成，更新预览地址:', {
              enableCustomPreview: pg?.enableCustomPreview,
              customPreviewUrl: pg?.customPreviewUrl,
              restartUrl: restartResult.url,
              finalUrl
            });
          }
        } catch (error) {
          console.error('❌ [Store.restart] 重启失败:', error);
        }
      },
      updateUrl(newUrl: string) {
        set({ url: newUrl });
        console.log('✅ [Store.updateUrl] 预览地址已更新:', newUrl);
        
        // 如果playground支持自定义预览，同时保存到数据库
        const playground = get().playground;

        if (playground?.enableCustomPreview) {
          updatePlaygroundCustomPreviewUrl({ id: playground.id, customPreviewUrl: newUrl })
            .then(() => {
              console.log('💾 [Store.updateUrl] 自定义预览地址已保存到数据库:', newUrl);
              
              // 更新本地playground状态
              set((state) => {
                if (state.playground) {
                  state.playground.customPreviewUrl = newUrl;
                }
              });
            })
            .catch((error) => {
              console.error('❌ [Store.updateUrl] 保存自定义预览地址失败:', error);
            });
        }
      },
      async openFile(...pathes: string[]) {
        const { files } = get();
        
        const inFiles = pathes.filter(e => {
          // 标准化路径格式进行比较
          const normalizedPath = e.replace(/\\/g, '/');
          
          // 首先尝试直接匹配
          let exists = files?.[e]?.fileType === FileType.File;
          
          // 如果直接匹配失败，尝试标准化路径匹配
          if (!exists) {
            exists = files?.[normalizedPath]?.fileType === FileType.File;
          }
          
          // 如果还是失败，尝试在所有文件中查找标准化后匹配的路径
          if (!exists) {
            const fileKeys = Object.keys(files || {});
            const matchingKey = fileKeys.find(key => 
              key.replace(/\\/g, '/') === normalizedPath && files[key]?.fileType === FileType.File
            );

            if (matchingKey) {
              exists = true;
              // 使用找到的实际key替换原始路径
              const index = pathes.indexOf(e);

              if (index !== -1) {
                pathes[index] = matchingKey;
              }
            }
          }
          
          return exists;
        });
        
        
        if (inFiles.length === 0) {
          console.warn('没有找到有效的文件，无法打开');

          return;
        }
        
        set((state) => {
          for (const path of inFiles) {
            if (!state.openFiles.includes(path)) {
              state.openFiles.push(path);
            }
          }
          // Activate last file
          const lastFile = inFiles.slice(inFiles.length - 1)[0];

          state.activeFile = lastFile;
        });
      },
      activateFile(path: string) {
        set((state) => {
          state.activeFile = path;
        });
      },
      async readFiles() {
        // 使用防抖版本的 listFiles
        console.log('🔄 [Store.readFiles] 开始获取文件列表');
        const debouncedListFiles = getDebouncedListFiles(id);
        const fileList = await debouncedListFiles();
        const files = Object.fromEntries(fileList
          .map((e: any) => [e.path, {
            fileType: e.fileType,
          }]),
        );

        set((state) => {
          state.files = files;
        });
        console.log('✅ [Store.readFiles] 文件列表更新完成，文件数量:', Object.keys(files).length);
      },
      getFile(path: string) {
        const { files } = get();

        return files?.[path]?.content;
      },
      async readFile(path: string) {
        const { files } = get();
        
        // 如果文件内容已存在，直接返回，避免重复请求
        if (files?.[path]?.content !== undefined) {
          console.log(`[Store] 文件内容已存在，直接返回: ${path}`);

          return files[path].content;
        }
        
        console.log(`[Store] 开始读取文件: ${path}`);
        const ret = await getFile({ id, path });
        
        // 检查返回的数据是否符合预期
        if (typeof ret !== 'string') {
          
          // 尝试智能转换
          let stringContent: string;

          if (ret === null || ret === undefined) {
            stringContent = '';
          } else if (Array.isArray(ret)) {
            try {
              stringContent = JSON.stringify(ret, null, 2);
            } catch (error) {
              stringContent = `[无法序列化的数组，长度: ${ret.length}]`;
            }
          } else if (typeof ret === 'object') {
            try {
              stringContent = JSON.stringify(ret, null, 2);
            } catch (error) {
              console.error(`❌ [playground] 对象序列化失败:`, error);
              stringContent = `[无法序列化的对象]`;
            }
          } else {
            stringContent = String(ret);
          }
          
          set((state) => {
            state.files[path] = {
              ...state.files[path],
              content: stringContent,
            };
          });

          return stringContent;
        }
        
        set((state) => {
          state.files[path] = {
            ...state.files[path],
            content: ret,
          };
        });

        return ret;
      },
      closeFile(path: string) {
        set((state) => {
          const idx = state.openFiles.indexOf(path);

          if (idx !== -1) {
            state.openFiles.splice(idx, 1);
          }
        });
      },
      async createDirectory(path: string, server=true) {
        if (!path) {
          return;
        }
        set((state) => {
          path.split('/').reduce((prev, curr) => {
            const next = prev ? `${prev}/${curr}` : curr;

            if (!state.files[next]) {
              state.files[next] = {
                fileType: FileType.Directory,
              };
            }

            return next;
          }, '');
        });
        if (server) {
          await createDirectory({ id, path });
        }
      },
      async addFile(path: string) {
        await get().createDirectory(dirname(path));
        set((state) => {
          state.files[path] = {
            fileType: FileType.File,
            content: '',
          };
        });
        await saveFile({ id, path, content: '' });
      },
      /** Move a file, support the following scenarios
       * - Move a file into a directory
       * - Move a directory into another directory
       * @param oldPath the old path, could be a file or a directory
       * @param newPath the new path, could be a file or a directory
       */
      async moveFile(oldPath: string, newPath: string) {
        await moveFile({ id, oldPath, newPath });
        set((state) => {
          for (const [path, value] of Object.entries(state.files)) {
            const updatedPath = shouldUpdatePath(oldPath, newPath, path);

            if (updatedPath) {
              delete state.files[path];
              state.files[updatedPath] = value;
            }
          }
          const openFiles = [];

          for (const path of state.openFiles) {
            openFiles.push(shouldUpdatePath(oldPath, newPath, path) || path);
          }
          state.openFiles = openFiles;

          if (state.activeFile) {
            state.activeFile = shouldUpdatePath(oldPath, newPath, state.activeFile) || state.activeFile;
          }
        });
      },
      async deleteFile(path: string) {
        await deleteFile({ id, path });
        set((state) => {
          delete state.files[path];
        });
      },
      async saveFile(path: string, content: string, server = true) {
        await get().createDirectory(dirname(path), server);
        set((state) => {
          state.files[path] = {
            fileType: FileType.File,
            content,
          };
        });
        if (server) {
          await saveFile({ id, path, content });
        }
      },
      // 关闭编辑器中所有已打开文件
      closeAllFiles() {
        set((state) => {
          state.openFiles = [];
        });
      },
      async loadVersions() {
        get().closeAllFiles();
        const data = await versionStatus({ id });

        set({
          versionList: (data as { list: string[]; current: string; }).list,
          currentVersion: (data as { list: string[]; current: string; }).current,
        });
        await get().readFiles();
      },
      async changeVersion(version: string) {
        if (get().currentVersion === version) {
          return;
        }

        await changeVersion({ id, version });
        set((state) => {
          state.currentVersion = version;
        });
        get().closeAllFiles();
        await get().readFiles();
      },
    })
  ),
);

/**
 * Updates path if it is the oldPath or starts with oldPath and is a subdirectory.
 * @param oldPath the old path
 * @param newPath the new path
 * @param path the path to update
 * @returns the updated path, or undefined if the path should not be updated
 */
function shouldUpdatePath(oldPath: string, newPath: string, path: string) {
  if (oldPath === path) {
    return newPath;
  }
  // path is subdirectory or file of the directory oldPath
  if (path.startsWith(oldPath) && path[oldPath.length] === '/') {
    return path.replace(new RegExp(`^${oldPath}`), newPath);
  }
}
