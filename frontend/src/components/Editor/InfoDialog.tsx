import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>dalBody, <PERSON>dalContent, ModalFooter } from '@heroui/modal';
import { Button } from '@heroui/button';
import { Textarea } from '@heroui/input';
import { addToast } from "@heroui/toast";
import { List, Descriptions } from 'antd';
import { installDeps, playgroundInfo, uninstallDeps } from '@/apis';
import IconDelete from '~icons/mdi/delete';

export const InfoDialog: React.FC<{
  open: boolean;
  onClose: () => void;
  id: string;
}> = ({
  id,
  open,
  onClose,
}) => {
  const [info, setInfo] = useState<Record<string, any>>();

  async function load() {
    setDeps('');
    const info = await playgroundInfo({ id, type: 'package.json' });
    setInfo(info);
  }

  useEffect(() => {
    if (open) {
      load();
    }
  }, [id, open]);
  const [deps, setDeps] = useState<string>('');
  const [removed, setRemoved] = useState<Set<string>>(() => new Set());

  const [modifying, setModifying] = useState(false);
  async function commit() {
    const depsTrimmed = deps.trim();
    if (!depsTrimmed && !removed.size) {
      addToast({
        title: '没有依赖更改',
        color: 'warning',
      });
      return;
    }
    setModifying(true);
    let ret;
    
    const removedDeps = Array.from(removed).join('\n');
    if (removedDeps) {
      ret = await uninstallDeps({ id, deps: removedDeps });
      if (ret !== 0) {
        addToast({
          title: '依赖删除失败',
          color: 'danger',
        });
        load();
        setModifying(false);
        return;
      }
    }

    if (depsTrimmed) {
      ret = await installDeps({ id, deps: depsTrimmed });
      if (ret !== 0) {
        addToast({
          title: '依赖安装失败',
          color: 'danger',
        });
        load();
        setModifying(false);
        return;
      }
    }

    setModifying(false);

    onClose();
    addToast({
      title: '修改已保存',
      color: 'success',
    });
  }

  return (
    <Modal isOpen={open} onClose={onClose}>
      <ModalContent>
        {(onClose) => (
          <>
            <ModalBody>
              <main className="flex flex-col mt-4">
                <section className="my-2">
                  <h1 className="mb-1">npm包信息</h1>
                  <Descriptions size="small" column={1} items={[
                    {
                      key: 'name',
                      label: '名称',
                      children: info?.name,
                    },
                    {
                      key: 'description',
                      label: '描述',
                      children: info?.description,
                    },
                  ]} />
                </section>
                {
                  info?.dependencies && Object.keys(info.dependencies).length ? (
                    <section className="my-2">
                      <h1 className="mb-1">已安装依赖</h1>
                      <DepsList deps={info?.dependencies} onRemove={(name) => {
                        setRemoved(new Set([...removed, name]));
                        const dependencies = { ... info.dependencies };
                        delete dependencies[name];
                        setInfo({ ...info, dependencies });
                      }} />
                    </section>
                  ) : null
                }
                <section className="my-2">
                  <h1 className="mb-1">安装依赖</h1>
                  <Textarea
                    className='block'
                    placeholder='例如： moment@^1.2.0'
                    rows={4}
                    value={deps}
                    onChange={(e) => setDeps(e.target.value)}
                  />
                </section>
              </main>
            </ModalBody>
            <ModalFooter>
              <Button variant='light' onPress={onClose}>取消</Button>
              <Button color="primary" onPress={commit} isLoading={modifying}>保存</Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

function DepsList({ deps, onRemove }: { deps: Record<string, any>; onRemove: (name: string) => void }) {
  return (
    <List bordered size='small'>
      {
        Object.entries(deps).map((e, i) => (
          <List.Item key={i} actions={[
            <Button
              className="align-middle -mr-[var(--ant-padding-xs)]"
              title="删除"
              isIconOnly
              variant="light"
              onPress={() => onRemove(e[0])}
            >
              <IconDelete />
            </Button>
          ]}><span className='flex-1'>{e[0]}@{e[1]}</span></List.Item>
        ))
      }
    </List>
  );
}
