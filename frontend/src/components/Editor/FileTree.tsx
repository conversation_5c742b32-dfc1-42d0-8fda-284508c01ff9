import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Tooltip } from "@heroui/react";
import { Tree, TreeDataNode, TreeProps, Dropdown, MenuProps } from 'antd';
import { ReactNode, useContext, useEffect, useMemo, useRef, useState, useCallback } from "react";
import { FileType, PlaygroundContext } from "@/stores/playground";
import { confirm } from "@/utils/modal";
import { splitPath } from "@/utils/path";
import IconFile from '~icons/mdi/file-outline';
import IconFolder from '~icons/mdi/folder-outline';

type FileTreeDataNode = TreeDataNode & {
  name?: string;
  path?: string;
  children?: FileTreeDataNode[];
};

export const FileTree = () => {
  const useStore = useContext(PlaygroundContext)!;
  const store = useStore();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const contextRef = useRef<TreeDataNode>();
  const [editKey, setEditKey] = useState<string>();
  const [addNode, setAddNode] = useState<[FileType, string]>();
  
  const treeData = useMemo(() => {
    const { moveFile } = useStore.getState();
    const data = files2TreeData({ moveFile }, store.files);

    if (addNode) {
      const [type, path] = addNode;
      let targetNodes: FileTreeDataNode[] | undefined = data;
      
      // 如果有路径，需要深入到对应的目录
      if (path) {
        const pathSegments = path.split('/').filter(seg => seg.length > 0);
        
        // 递归查找目标目录
        const findTargetNodes = (nodes: FileTreeDataNode[], segments: string[]): FileTreeDataNode[] | undefined => {
          if (segments.length === 0) {
            return nodes;
          }
          
          const [currentSegment, ...remainingSegments] = segments;
          const targetNode = nodes.find(node => node.name === currentSegment && !node.isLeaf);
          
          if (targetNode && targetNode.children) {
            return findTargetNodes(targetNode.children, remainingSegments);
          }
          
          return undefined;
        };
        
        targetNodes = findTargetNodes(data, pathSegments);
      }
      
      // 如果找到了目标目录，添加新节点
      if (targetNodes) {
        const isFile = type === FileType.File;

        targetNodes.unshift({
          title: '',
          key: 0, // key: 0 is a special key for adding new node
          switcherIcon: isFile ? <i className="codicon codicon-file align-middle" /> : null,
          isLeaf: isFile,
        });
      }
    }

    return data;
  }, [store.files, addNode]);

  useEffect(() => {
    const [_type, path] = addNode || [];

    if (!path) return;
    
    // 确保添加节点时，其父目录都处于展开状态
    const expandParentDirectories = (targetPath: string) => {
      const segments = targetPath.split('/').filter(seg => seg.length > 0);
      const pathsToExpand: string[] = [];
      
      // 构建所有父目录的路径
      for (let i = 1; i <= segments.length; i++) {
        const parentPath = segments.slice(0, i).join('/');

        if (store.files[parentPath]?.fileType === FileType.Directory) {
          pathsToExpand.push(parentPath);
        }
      }
      
      // 确保所有父目录都展开
      setExpandedKeys(prev => {
        const newExpanded = [...prev];

        pathsToExpand.forEach(path => {
          if (!newExpanded.includes(path)) {
            newExpanded.push(path);
          }
        });

        return newExpanded;
      });
    };
    
    expandParentDirectories(path);
  }, [addNode, store.files]);

  const onExpand: TreeProps['onExpand'] = useCallback((expandedKeysValue: React.Key[]) => {
    // console.log('文件树展开状态变化:', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
  }, []);

  const addMode = useCallback((type: FileType) => {
    const selected = selectedKeys[0] as string;
    const entry = store.files[selected];
    let path = '';

    if (entry) {
      if (entry.fileType === FileType.File) {
        path = splitPath(selected).parent;
      } else {
        path = selected;
      }
    }
    setAddNode([type, path]);
  }, [selectedKeys, store.files]);

  const items: MenuProps['items'] = [
    {
      label: '删除',
      key: 'delete',
    },
    {
      label: '重命名',
      key: 'rename',
    },
  ];

  const onMenuClick = useCallback(({ key }: { key: string }) => {
    if (key === 'delete') {
      (async () => {
        const isFile = Boolean(contextRef.current?.isLeaf);
        const type = isFile ? '文件' : '文件夹';
        const nodeKey = contextRef.current?.key;
        const ret = await confirm({
          title: `删除${type}`,
          message: `确定删除${type} ${nodeKey}？`,
        });

        if (ret) {
          if (nodeKey) {
            store.deleteFile(String(nodeKey));
          }
        }
      })();
    } else if (key === 'rename') {
      const nodeKey = contextRef.current?.key;

      if (nodeKey) {
        setEditKey(String(nodeKey));
      }
    }
  }, [store]);

  const onContextMenu = useCallback(({ node }: { event: React.MouseEvent; node: TreeDataNode }) => {
    contextRef.current = node;
  }, []);

  const titleRender = useCallback((node: TreeDataNode) => {
    const nodeKeyStr = String(node.key);

    if (nodeKeyStr === editKey) {
      // Rename node
      return (
        <RenameInput
          initialValue={nodeKeyStr}
          onCancel={() => setEditKey(undefined)}
          onCommit={(value) => {
            const parent = splitPath(nodeKeyStr).parent;
            const newPath = parent ? `${parent}/${value}` : value;

            store.moveFile(nodeKeyStr, newPath);
            setEditKey(undefined);
          }}
        />
      )
    } else if (node.key === 0) {
      // Add node
      return (
        <RenameInput
          onCancel={() => setAddNode(undefined)}
          onCommit={(value) => {
            const [type, path] = addNode!;

            if (type === FileType.File) {
              store.addFile(path ? `${path}/${value}` : value);
            } else if (type === FileType.Directory) {
              store.createDirectory(path ? `${path}/${value}` : value);
            } else {
              throw new Error('Invalid file type');
            }
            setAddNode(undefined);
          }}
        />
      );
    }

    return node.title as ReactNode;
  }, [editKey, addNode, store]);

  return (
    <>
      <div className="flex-1 flex flex-col">
        <header className="flex gap-1">
          <Tooltip content="新建文件" placement="bottom">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={() => addMode(FileType.File)}
            >
              <IconFile height={16} width={16} />
            </Button>
          </Tooltip>
          <Tooltip content="新建文件夹" placement="bottom">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={() => addMode(FileType.Directory)}
            >
              <IconFolder height={16} width={16} />
            </Button>
          </Tooltip>
        </header>
        <div className="py-2 whitespace-nowrap overflow-auto">
          <Dropdown
            menu={{ items, onClick: onMenuClick }}
            trigger={['contextMenu']}
          >
            <Tree
              blockNode
              selectable
              showIcon
              autoExpandParent={false}
              checkable={false}
              expandedKeys={expandedKeys}
              selectedKeys={selectedKeys}
              titleRender={titleRender}
              treeData={treeData}
              onExpand={onExpand}
              onRightClick={onContextMenu}
              onSelect={(selectedKeysValue, info) => {
                console.log('Tree onSelect 触发:', selectedKeysValue, info);
                const { node } = info;
                const nodeKey = node.key;
                
                if (nodeKey === 0) {
                  return;
                }
                
                const nodeKeyStr = String(nodeKey);
                const file = store.files?.[nodeKeyStr];

                console.log('选择文件节点:', nodeKeyStr, '文件信息:', file);
                
                if (file) {
                  setSelectedKeys(selectedKeysValue);
                  if (file.fileType === FileType.File) {
                    console.log('准备打开文件:', nodeKeyStr);
                    store.openFile(nodeKeyStr);
                  }
                } else {
                  console.warn(`文件节点 ${nodeKeyStr} 在 store.files 中未找到`);
                  console.log('当前所有文件:', Object.keys(store.files || {}));
                }
              }}
            />
          </Dropdown>
        </div>
      </div>
    </>
  );
};

function RenameInput({ initialValue, onCommit, onCancel }: {
  initialValue?: string;
  onCommit: (value: string) => void;
  onCancel: () => void;
}) {
  const [value, setValue] = useState(initialValue ?? '');
  const [inputInvalid, setInputInvalid] = useState(false);

  function commit() {
    if (inputInvalid) {
      onCancel();

      return;
    }
    if (value != initialValue && value) {
      onCommit(value);
    } else {
      onCancel();
    }
  }

  return (
    <Input
      autoFocus
      isInvalid={inputInvalid}
      size="sm"
      value={value}
      onBlur={commit}
      onChange={(e) => {
        const value = e.target.value;

        setValue(value);
        setInputInvalid(/[<>:"'\/\\|?*]/.test(value));
      }}
      onKeyUp={(evt) => {
        if (evt.key === 'Enter') {
          commit();
        } else if (evt.key === 'Escape') {
          onCancel();
        }
      }}
    />
  );
}

function files2TreeData(
  fns: {
    moveFile: (source: string, target: string) => Promise<void>;
  },
  files?: Record<string, {
    fileType: FileType;
  }>,
): FileTreeDataNode[] {
  if (!files) return [];

  const treeData: TreeDataNode[] = [];
  const folderMap: Record<string, TreeDataNode['children']> = {};
  
  // 首先规范化所有文件路径，确保使用统一的路径分隔符
  const normalizedFiles: Record<string, { fileType: FileType; originalPath: string }> = {};

  for (const [originalPath, fileInfo] of Object.entries(files)) {
    // 使用pathe的normalize来统一路径格式
    const normalizedPath = originalPath.replace(/\\/g, '/');

    normalizedFiles[normalizedPath] = {
      ...fileInfo,
      originalPath,
    };
  }
  
  const fileNames = Object.keys(normalizedFiles);
  
  // 按路径深度排序，确保父目录先创建
  const directories = fileNames.filter(fileName => normalizedFiles[fileName].fileType === FileType.Directory);

  directories.sort((a, b) => {
    const depthA = a.split('/').length;
    const depthB = b.split('/').length;

    if (depthA !== depthB) return depthA - depthB;

    return a.localeCompare(b);
  });
  
  // 创建目录节点
  for (const dirName of directories) {
    const { parent, name } = splitPath(dirName);
    const originalPath = normalizedFiles[dirName].originalPath;
    
    const node: FileTreeDataNode = {
      key: originalPath, // 使用原始路径作为key，确保与store中的路径一致
      name: name,
      path: originalPath,
      children: [],
      isLeaf: false,
      title: (
        <span draggable className="block"
          onDragOver={(evt) => {
            const isValidSource = [...evt.dataTransfer.items].some(e => e.type == 'source');

            if (isValidSource) {
              evt.preventDefault();
              evt.dataTransfer.dropEffect = 'move';
            }
          }}
          onDragStart={(evt) => {
            evt.dataTransfer?.setData('source', originalPath);
          }}
          onDrop={(evt) => {
            const source = evt.dataTransfer.getData('source');

            // prevent move to self and move to child
            if (originalPath === source || originalPath.startsWith(source) && originalPath[source.length] === '/' || !source) {
              return;
            }
            const { name: sourceName } = splitPath(source);
            const target = `${originalPath}/${sourceName}`;

            if (source !== target) {
              fns.moveFile(source, target);
            }
          }}
        >{name}</span>
      )
    };
    
    // 将目录节点添加到正确的位置
    if (parent && folderMap[parent]) {
      folderMap[parent].push(node);
    } else if (!parent) {
      treeData.push(node);
    }
    
    // 将目录添加到folderMap中，以便后续子项可以找到它
    folderMap[dirName] = node.children;
  }
  
  // 然后处理所有文件
  const regularFiles = fileNames.filter(fileName => normalizedFiles[fileName].fileType === FileType.File);
  
  for (const fileName of regularFiles) {
    const { parent, name } = splitPath(fileName);
    const originalPath = normalizedFiles[fileName].originalPath;
    
    const node: FileTreeDataNode = {
      key: originalPath, // 使用原始路径作为key
      name: name,
      path: originalPath,
      isLeaf: true,
      switcherIcon: <i className="codicon codicon-file align-middle" />,
      title: (
        <span draggable className="block"
          onDragOver={(evt) => {
            const isValidSource = [...evt.dataTransfer.items].some(e => e.type == 'source');

            if (isValidSource) {
              evt.preventDefault();
              evt.dataTransfer.dropEffect = 'move';
            }
          }}
          onDragStart={(evt) => {
            evt.dataTransfer?.setData('source', originalPath);
          }}
          onDrop={(evt) => {
            const source = evt.dataTransfer.getData('source');

            if (originalPath === source || !source) {
              return;
            }
            // Drop on file, move to parent directory
            const dir = splitPath(originalPath).parent;
            const { name: sourceName } = splitPath(source);
            const target = dir ? `${dir}/${sourceName}` : sourceName;

            if (source !== target) {
              fns.moveFile(source, target);
            }
          }}
        >{name}</span>
      )
    };
    
    // 将文件节点添加到正确的位置
    if (parent && folderMap[parent]) {
      folderMap[parent].push(node);
    } else if (!parent) {
      treeData.push(node);
    }
  }
  
  // 对每个目录的子项进行排序：目录在前，文件在后，然后按名称排序
  const sortChildren = (children: TreeDataNode[]) => {
    children.sort((a, b) => {
      const aIsDir = !a.isLeaf;
      const bIsDir = !b.isLeaf;
      
      if (aIsDir && !bIsDir) return -1;
      if (!aIsDir && bIsDir) return 1;
      
      const aName = (a as FileTreeDataNode).name || '';
      const bName = (b as FileTreeDataNode).name || '';

      return aName.localeCompare(bName);
    });
    
    // 递归排序子目录
    children.forEach(child => {
      if (child.children && child.children.length > 0) {
        sortChildren(child.children);
      }
    });
  };
  
  // 排序根级项目
  sortChildren(treeData);
  
  return treeData;
}
