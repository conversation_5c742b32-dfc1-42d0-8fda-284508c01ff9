import { But<PERSON> } from "@heroui/button";
import { Tooltip } from "@heroui/react";
import clsx from "clsx";
import { useState } from "react";
import { InfoDialog } from './InfoDialog';
import { InstallDialog } from './InstallDialog';
import { baseUrl, getPlayground } from "@/apis";
import IconDownload from "~icons/mdi/download";
import IconPackageVariant from "~icons/mdi/package-variant";
import IconPlusBoxOutline from "~icons/mdi/plus-box-outline";

export function ExtraTools({ id, className }: {
  id: string;
  className?: string;
}) {
  const [showInfo, setShowInfo] = useState(false);
  const [deploying, setDeploying] = useState(false);
  const [installOpen, setInstallOpen] = useState<{
    id: string;
    name: string;
  }>();
  const isInVSCode = window.name === "vscodeWebview";

  return (
    <div className={clsx("flex items-center px-2 py-1", className)}>
      <Tooltip content="npm包" placement="bottom">
        <Button
          isIconOnly
          className="ml-1"
          size="sm"
          variant="light"
          onPress={() => setShowInfo(true)}
        >
          <IconPackageVariant height={16} width={16} />
        </Button>
      </Tooltip>
      {/* {!isInVSCode && <Tooltip content="WebIDE打开">
        <Button
          disabled
          isIconOnly
          className="ml-1"
          size="sm"
          variant="light"
          onPress={() => {
            window.open(`${baseUrl}${id}/@webide`, '_blank');
          }}
        >
          <IconOpenInApp height={16} width={16} />
        </Button>
      </Tooltip>} */}
      {!isInVSCode && <Tooltip content="下载" placement="bottom">
        <Button
          isIconOnly
          className="ml-1"
          size="sm"
          variant="light"
          onPress={() => window.open(`${baseUrl}${id}/@download`, '_blank')}
        >
          <IconDownload height={16} width={16} />
        </Button>
      </Tooltip>}
      {isInVSCode && (
        <Tooltip content="添加到工程">
          <Button
            isIconOnly
            className="ml-1"
            size="sm"
            variant="light"
            onPress={async () => {
              const playground = await getPlayground({ id });

              setInstallOpen({
                id,
                name: playground.name,
              });
            }}
          >
            <IconPlusBoxOutline height={16} width={16} />
          </Button>
        </Tooltip>
      )}
      {/* <Tooltip content={deploying ? '部署中...' : '部署'}>
        <Button
          isIconOnly
          className="ml-1"
          disabled={deploying}
          isLoading={deploying}
          size="sm"
          variant="light"
          onPress={async () => {
            const yes = await confirm({
              title: '部署',
              message: '确定要部署吗？',
            });

            if (yes) {
              setDeploying(true);
              try {
                await deploy({ id, mode: 'preview' });
              } catch(e) {
                addToast({
                  title: '部署',
                  description: '部署失败',
                  color: 'danger',
                });

                return;
              } finally {
                setDeploying(false);
              }
              addToast({
                title: '部署成功',
                description: '点击打开预览',
                color: 'success',
                endContent: (
                  <Button size="sm" variant="flat" onPress={() => window.open(`http://ai-coding.uat.saas.htsc/artifact/${id}/dist-preview/index.html`, '_blank')}>
                    打开
                  </Button>
                ),
              });
            }
          }}
        >
          <IconRocketLaunchOutline height={16} width={16} />
        </Button>
      </Tooltip> */}
      <InfoDialog id={id} open={showInfo} onClose={() => setShowInfo(false)} />
      <InstallDialog
        open={!!installOpen}
        playground={installOpen}
        onClose={() => {
          setInstallOpen(undefined);
        }}
      />
    </div>
  );
}
