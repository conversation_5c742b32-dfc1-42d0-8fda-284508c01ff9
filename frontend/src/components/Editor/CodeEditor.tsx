import { indentWithTab } from "@codemirror/commands";
import { javascript } from "@codemirror/lang-javascript"
import { Transaction } from "@codemirror/state";
import { oneDark } from '@codemirror/theme-one-dark';
import { keymap } from "@codemirror/view";
import clsx from "clsx";
import { EditorView, basicSetup } from "codemirror";
import { debounce } from "lodash";
import { useCallback, useContext, useEffect, useRef } from "react";
import { useStore } from "zustand";
import styles from './Editor.module.css';
import { PlaygroundContext } from "@/stores/playground";
import { useThemeStore } from "@/stores/theme";

// 安全地将内容转换为字符串
function safeStringify(content: any): string {

  if (content === null || content === undefined) {
    return '';
  }
  if (typeof content === 'string') {
    return content;
  }
  if (typeof content === 'object') {
    try {
      const result = JSON.stringify(content, null, 2);

      return result;
    } catch (error) {
      
      // 更智能的fallback处理
      if (Array.isArray(content)) {
        // 如果是数组，尝试处理每个元素
        try {
          const stringifiedArray = content.map((item, index) => {
            if (typeof item === 'object' && item !== null) {
              try {
                return JSON.stringify(item, null, 2);
              } catch {
                console.warn(`⚠️ [CodeEditor] 数组元素 ${index} 序列化失败，使用toString`);

                return String(item);
              }
            }

            return String(item);
          });
          const result = `[\n${stringifiedArray.map(item => `  ${item}`).join(',\n')}\n]`;

          return result;
        } catch (arrayError) {
          console.error(`❌ [CodeEditor] 数组处理失败:`, arrayError);
          const fallbackResult = `数组内容处理失败: ${content.length} 个元素`;

          return fallbackResult;
        }
      } else {
        // 对于其他对象类型
        try {
          const keys = Object.keys(content);
          const result = `对象包含 ${keys.length} 个属性: ${keys.join(', ')}`;

          return result;
        } catch (objError) {
          console.error(`❌ [CodeEditor] 对象处理失败:`, objError);
          const fallbackResult = '对象内容无法处理';

          return fallbackResult;
        }
      }
    }
  }
  
  // 对于其他类型（number, boolean等）
  const result = String(content);

  return result;
}

export function CodeEditor({ path, className }: {
  path: string;
  className?: string;
}) {
  
  const editorRef = useRef<HTMLDivElement>(null);
  const { theme } = useThemeStore();
  const pgStore = useContext(PlaygroundContext)!;
  const store = useStore(pgStore);
  const editorViewRef = useRef<EditorView>();
  const debouncedSave = useCallback(debounce(store.saveFile, 500, { maxWait: 1000 }), []);
  const fileData = useStore(pgStore, () => store.files?.[path]);
  
  // 使用 ref 来追踪文件读取状态，避免重复读取
  const fileReadStatusRef = useRef<Record<string, boolean>>({});
  
  useEffect(() => {
    const editorEl = editorRef.current!;
    const extensions = [
      basicSetup,
      keymap.of([indentWithTab]),
      javascript({
        jsx: true,
        typescript: true,
      }),
      EditorView.updateListener.of(update => {
        const isRemoteUpdate = update.transactions.some(a => a.annotation(Transaction.remote));

        if (!update.docChanged || isRemoteUpdate) {
          return;
        }
        if (update.docChanged && update.startState.doc.length > 0) {
          debouncedSave(path, update.state.doc.toString());
        }
      }),
    ];

    if (theme == 'dark') {
      extensions.push(oneDark);
    }
  
    const view = new EditorView({
      extensions,
      parent: editorEl,
      root: document,
    });

        editorViewRef.current = view;

    // 只有在文件内容不存在且未曾读取过时，才从远程读取
    if (fileData?.content === undefined && !fileReadStatusRef.current[path]) {
      console.log(`[CodeEditor] 文件内容不存在，从远程读取: ${path}`);
      fileReadStatusRef.current[path] = true; // 标记为已读取
      store.readFile(path);
    } else if (fileData?.content !== undefined) {
      console.log(`[CodeEditor] 文件内容已存在，跳过远程读取: ${path}`);
    } else {
      console.log(`[CodeEditor] 文件已在读取中，跳过重复读取: ${path}`);
    }
    
    return () => {
      view.destroy();
    };
  }, [path]); // 添加 path 依赖，确保路径变化时重新初始化

  useEffect(() => {
    if (store.activeFile === path) {
      const view = editorViewRef.current;

      if (view) {
        view.focus();
      }
    }
  }, [path, store.activeFile, fileData]);

  // Updata content when file content changed
  useEffect(() => {
    
    const view = editorViewRef.current;
    const oldText = view?.state.doc.toString();
    
    // 安全地处理文件内容
    const rawContent = fileData?.content;

    const newText = safeStringify(rawContent);
    
    if (view && newText !== oldText) {
      try {
        // 保存当前滚动位置
        const scrollTop = view.scrollDOM.scrollTop;
        const scrollLeft = view.scrollDOM.scrollLeft;
        
        // Apppend mode, move selction to last line
        if (oldText && newText.startsWith(oldText)) {
          const lastLine = newText.lastIndexOf('\n') + 1;

          view.dispatch(
            {
              annotations: [Transaction.remote.of(true), Transaction.addToHistory.of(false)],
              changes: { from: oldText.length, insert: newText.slice(oldText.length) },
              selection: { anchor: lastLine, head: lastLine }, // Move selection to last line
              // 移除 scrollIntoView: true，避免自动滚动
            },
          );
          
          // 在追加模式下，只有当用户滚动到文档底部附近时才自动滚动到新内容
          const { scrollHeight, clientHeight } = view.scrollDOM;
          const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100; // 100px 容差
          
          if (isNearBottom) {
            // 用户在底部附近，平滑滚动到新内容
            requestAnimationFrame(() => {
              view.scrollDOM.scrollTo({
                top: view.scrollDOM.scrollHeight,
                left: scrollLeft,
                behavior: 'smooth'
              });
            });
          } else {
            // 用户不在底部，保持当前滚动位置
            requestAnimationFrame(() => {
              view.scrollDOM.scrollTo({
                top: scrollTop,
                left: scrollLeft
              });
            });
          }
        } else {
          // 完全替换模式，保持滚动位置
          view.dispatch({
            annotations: [Transaction.remote.of(true), Transaction.addToHistory.of(false)],
            changes: { from: 0, to: view.state.doc.length, insert: newText },
          });
          
          // 恢复滚动位置
          requestAnimationFrame(() => {
            view.scrollDOM.scrollTo({
              top: Math.min(scrollTop, view.scrollDOM.scrollHeight - view.scrollDOM.clientHeight),
              left: scrollLeft
            });
          });
        }
      } catch (error) {
        console.error('Failed to update CodeMirror content:', error);
        console.error('Content type:', typeof rawContent);
        console.error('Content value:', rawContent);
        
        // 降级处理：尝试强制创建一个新的EditorView
        try {
          view.dispatch({
            annotations: [Transaction.remote.of(true), Transaction.addToHistory.of(false)],
            changes: { from: 0, to: view.state.doc.length, insert: '' },
          });
        } catch (fallbackError) {
          console.error('Fallback also failed:', fallbackError);
        }
      }
    }
  }, [fileData]);

  return (
    <div
      ref={editorRef}
      className={clsx('flex-1 max-w-full', className, styles.codeEditor)}
    />
  );
}
