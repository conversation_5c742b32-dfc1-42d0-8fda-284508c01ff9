import type { TabsProps } from 'antd';
import { Tabs } from "antd";
import clsx from "clsx";
import { useContext, useMemo } from "react";
import { useStore } from "zustand/react";
import { AssetViewer } from './AssetViewer';
import { CodeEditor } from "./CodeEditor";
import { ExtraTools } from "./ExtraTools";
import panelStyles from './Panels.module.css';
import { PlaygroundContext } from "@/stores/playground";
import { isBinaryFile } from '@/utils/file';
import { splitPath } from "@/utils/path";

export function EditorGroup() {
  const pgStore = useContext(PlaygroundContext)!;
  const store = useStore(pgStore);
  const activeFile = useStore(pgStore, (state) => state.activeFile);

  // 使用 useMemo 避免重复创建 items，只有当 openFiles 变化时才重新创建
  const items: TabsProps['items'] = useMemo(() => {
    console.log(`[EditorGroup] 重新创建 items，打开文件数量: ${store.openFiles.length}`);

    return store.openFiles.map((path) => {
      const { name } = splitPath(path);

      return {
        key: path,
        label: name,
        children: isBinaryFile(path) ? <AssetViewer path={path} /> : <CodeEditor path={path} />
      };
    });
  }, [store.openFiles]);

  return (
    <>
      {
        items && items.length > 0 ? (
          <Tabs
            activeKey={ activeFile }
            className={clsx('max-w-full max-h-full min-h-0', panelStyles.panels)}
            hideAdd={ true }
            items={ items }
            size="small"
            tabBarExtraContent={<ExtraTools id={store.id} />}
            type="editable-card"
            onChange={(key: string) => {
              store.activateFile(key);
            }}
            onEdit={(key, action) => {
              if (action === 'remove') {
                store.closeFile(key as string);
              }
            }}
          />
        ) : (
          <div className="flex flex-col flex-1">
            <Tabs
              className={clsx('max-w-full max-h-full min-h-0', panelStyles.panels)}
              hideAdd={ true }
              items={ [] }
              size="small"
              tabBarExtraContent={<ExtraTools id={store.id} />}
              type="editable-card"
             />
          </div>
        )
      }
    </>
  );
}
