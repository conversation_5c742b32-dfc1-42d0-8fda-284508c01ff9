import { useContext, useEffect, useState } from "react";
import { useStore } from "zustand";
import { PlaygroundContext } from "@/stores/playground";
import { isImage } from "@/utils/file";

export function AssetViewer({ path }: { path: string } ) {
  const pgStore = useContext(PlaygroundContext)!;
  const store = useStore(pgStore);
  const [data, setData] = useState<string>();
  const supported = isImage(path);
  
  useEffect(() => {
    const loadData = async () => {
      const fileData = await store.readFile(path);
      const idx = path.lastIndexOf('.');
      const ext = path.slice(idx + 1);
      setData(`data:image/${ext};base64,` + fileData);
    };

    if (supported) {
      loadData();
    }
  }, [supported]);

  return (
    <div className="flex-1 flex flex-col justify-center items-center">
      { !supported ? <h1>不支持此文件展示</h1> : (
        data && <img src={data} />
      )}
    </div>
  );
}
