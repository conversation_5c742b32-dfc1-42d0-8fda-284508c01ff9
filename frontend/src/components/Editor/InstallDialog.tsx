import { useEffect, useState } from 'react';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalContent, <PERSON><PERSON><PERSON>ooter, ModalHeader } from '@heroui/modal';
import { addToast } from "@heroui/toast";
import { baseUrl } from "@/apis";

// 生成一个唯一的消息ID
function generateMessageId() {
  return Math.random().toString(36).substring(2, 15);
}

// 创建一个Promise映射表，用于存储等待响应的Promise
const pendingRequests = new Map();

// 监听从父窗口(VSCode webview)发来的消息
window.addEventListener("message", (event) => {
  const message = event.data;
  // 如果是响应消息，找到对应的Promise并解决它
  if (message.command === "htsc-suite-response" && message.id) {
    const { resolve, reject } = pendingRequests.get(message.id) || {};
    if (resolve && reject) {
      if (message.success) {
        resolve(message.data);
      } else {
        reject(new Error(message.error || "未知错误"));
      }
      pendingRequests.delete(message.id);
    }
  }
});

// 调用VSCode扩展的downloadAndSaveResource方法
async function downloadAndSaveResource(playgroundId: string, targetFolder: string) {
  const id = generateMessageId();
  
  // 创建一个Promise，稍后在收到响应时解决
  const promise = new Promise<any>((resolve, reject) => {
    pendingRequests.set(id, { resolve, reject });
    
    // 向父窗口(VSCode webview)发送消息
    window.parent.postMessage({
      command: "downloadAndSaveResource",
      id: id,
      url:`${location.origin}${baseUrl}${playgroundId}/@download`,
      folder: targetFolder
    }, "*");
    
    // 设置超时处理
    setTimeout(() => {
      if (pendingRequests.has(id)) {
        pendingRequests.delete(id);
        reject(new Error("请求超时"));
      }
    }, 300000); // 300秒超时
  });

  return promise;
}

export const InstallDialog: React.FC<{
  open: boolean;
  onClose: () => void, playground?: {
    id: string;
    name: string;
  };
}> = ({
  open,
  onClose,
  playground,
}) => {
  const [installFolder, setInstallFolder] = useState<string>('src/playground-component');
  const [installStatus, setInstallStatus] = useState<string>('');

  async function install() {
    try {
      if (!playground) {
        throw new Error("未选择组件");
      }
      setInstallStatus('开始安装');

      await downloadAndSaveResource(playground.id, installFolder);
      setInstallStatus('安装成功');
    } catch (err) {
      addToast({
        title: err as string,
      });
    }
  }
  useEffect(() => {
    if (open) {
      setInstallStatus('');
      if (playground?.name) {
        setInstallFolder('src/' + playground.name);
      }
    }
  }, [open]);

  let content: React.ReactNode,
    footer: React.ReactNode;
  if (installStatus) {
    content = (
      <p className='my-6'>{installStatus}</p>
    )
  } else {
    content = (
      <>
        <p>将在项目中安装该组件，是否继续？</p>
        <label className="flex items-center gap-1">
          <span className='text-sm'>安装目录：</span>
          <Input
            className="w-60"
            value={installFolder}
            onChange={(e) => setInstallFolder((e.target as HTMLInputElement).value)}
          />
        </label>
      </>
    );
    footer = (
      <>
        <Button variant="light" size="sm" onPress={onClose}>取消</Button>
        <Button color="primary" size="sm" onPress={install}>安装</Button>
      </>
    );
  }

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
    >
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="flex flex-col gap-1">组件安装</ModalHeader>
            <ModalBody>
              { content }
            </ModalBody>
            <ModalFooter>
              { footer }
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
