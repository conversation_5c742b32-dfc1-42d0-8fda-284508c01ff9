import { addToast } from "@heroui/react";
import clsx from "clsx";
import { useState, useEffect } from "react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import Logo from "@/assets/images/logo.svg?react";
import { menuItems } from "@/config/menu";
import { useProjectStore } from "@/stores/project";
import { useSidebarStore } from "@/stores/sidebar";
import IconChat from '~icons/mdi/chat-outline';
import IconChevronDown from '~icons/mdi/chevron-down';
import IconChevronRight from '~icons/mdi/chevron-right';

export function Sidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { current } = useProjectStore();
  const { isCollapsed } = useSidebarStore();
  const [expandedMenus, setExpandedMenus] = useState<string[]>(['project']);
  
  const isProjectRelated = location.pathname.startsWith('/project/') || 
                          location.pathname.startsWith('/workflow') ||
                          location.pathname.startsWith('/multi-workflow');

  const handleProjectItemClick = (e: React.MouseEvent, menuId: string) => {
    if (!current) {
      addToast({
        title: '请先选择一个项目',
        color: 'warning'
      });
      navigate('/project-list');
    } else {
      navigate(`/${menuId}/${current}`);
    } 
  };

  useEffect(() => {
    const handleProjectChange = (event: CustomEvent) => {
      // 项目变更处理逻辑可以在这里添加
    };

    window.addEventListener('chatProjectChanged', handleProjectChange as EventListener);
    
    return () => {
      window.removeEventListener('chatProjectChanged', handleProjectChange as EventListener);
    };
  }, []);

  useEffect(() => {
    if (isCollapsed) {
      setExpandedMenus([]);
    }
  }, [isCollapsed]);

  const toggleSubmenu = (menuId: string) => {
    if (isCollapsed) return;
    
    setExpandedMenus(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  const sidebarWidth = isCollapsed ? 'w-[54px]' : 'w-[246px]';

  return (
    <div className={clsx(
      "flex flex-col h-full transition-all duration-300 dark:bg-zinc-900/50 backdrop-blur-sm",
      sidebarWidth
    )}>
      <div className={clsx(
        "h-8 flex items-center transition-all duration-300",
        isCollapsed ? "justify-center px-3" : "justify-start px-4"
      )}>
        <NavLink
          className="flex items-center gap-2"
          to="/"
        >
          <div className="w-6 h-6 flex-shrink-0 flex items-center justify-center">
            <Logo
              className={clsx(
                "transition-transform duration-300",
                isCollapsed && "scale-[0.833]"
              )}
              fontSize={24}
            />
          </div>
          <div
            className={clsx(
              "overflow-hidden transition-all duration-300",
              isCollapsed ? "max-w-0 opacity-0" : "max-w-full opacity-100"
            )}
          >
            <span className="font-bold text-lg whitespace-nowrap">魔方智能生成</span>
          </div>
        </NavLink>
      </div>

      <nav className="flex-1 p-2 overflow-y-auto">
        <div className="space-y-1">
          <NavLink
            className={({ isActive }) => clsx(
              "flex items-center justify-center w-full my-1 py-2.5 text-sm font-medium rounded-lg transition-colors",
              isCollapsed
                ? (isActive
                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-800")
                : "border bg-white text-gray-800 hover:bg-gray-100 dark:bg-white dark:text-gray-800 dark:hover:bg-gray-200 border-gray-200 dark:border-gray-200 px-3",
            )}
            title={isCollapsed ? "新对话" : undefined}
            to="/"
          >
            {isCollapsed ? <IconChat className="w-5 h-5 flex-shrink-0" /> : <span>新对话</span>}
          </NavLink>

          {menuItems.map((item) => (
            <div key={item.id}>
              {item.hasSubmenu ? (
                <button
                  className={clsx(
                    "w-full flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-colors",
                    "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-800"
                  )}
                  onClick={() => toggleSubmenu(item.id)}
                >
                  <div className="flex items-center gap-3">
                    <item.icon className="w-5 h-5 flex-shrink-0" />
                    {!isCollapsed && <span>{item.label}</span>}
                  </div>
                  {!isCollapsed && (
                    expandedMenus.includes(item.id) ? 
                      <IconChevronDown className="w-4 h-4" /> : 
                      <IconChevronRight className="w-4 h-4" />
                  )}
                </button>
              ) : (
                <NavLink
                  className={({ isActive }) => clsx(
                    "flex items-center gap-3 px-3 py-2.5 text-sm font-medium rounded-lg transition-colors",
                    isActive
                      ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-800"
                  )}
                  title={isCollapsed ? item.label : undefined}
                  to={item.path!}
                >
                  <item.icon className="w-5 h-5 flex-shrink-0" />
                  {!isCollapsed && <span>{item.label}</span>}
                </NavLink>
              )}

              {item.hasSubmenu && expandedMenus.includes(item.id) && !isCollapsed && (
                <div className="ml-6 mt-1 space-y-1">
                  {item.submenu?.map((subItem) => (
                    <NavLink
                      key={subItem.id}
                      className={({ isActive }) => {
                        const isApplicationCodeActive = subItem.id === 'project' && isProjectRelated;
                        const isPrototypeCodeActive = subItem.id === 'prototype' && location.pathname.startsWith('/prototype');
                        const shouldHighlight = isPrototypeCodeActive || isApplicationCodeActive;
                        
                        return clsx(
                          "flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors",
                          shouldHighlight
                            ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-zinc-800/50"
                        );
                      }}
                      to={subItem.id === 'prototype' || subItem.id === 'project' ? '#' : subItem.path!}
                      onClick={(e) => {
                        if (subItem.id === 'prototype' || subItem.id === 'project') {
                          e.preventDefault();
                          handleProjectItemClick(e, subItem.id);
                        } 
                      }}
                    >
                      <subItem.icon className="w-4 h-4 flex-shrink-0" />
                      <span>{subItem.label}</span>
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </nav>

      <div className="border-t border-gray-200 dark:border-zinc-800 p-4 flex items-center justify-center">
        <div
          className={clsx(
            "text-xs text-gray-500 dark:text-gray-400 text-center overflow-hidden transition-all duration-300 ease-in-out",
            isCollapsed ? "max-w-0 opacity-0" : "max-w-xs opacity-100"
          )}
        >
          <span className="whitespace-nowrap">@Copyright 2025 Huatai Securities</span>
        </div>
      </div>
    </div>
  )
}