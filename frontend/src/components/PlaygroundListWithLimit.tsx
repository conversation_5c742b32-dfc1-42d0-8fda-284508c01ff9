import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@heroui/react";
import moment from 'moment';
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { tv } from "tailwind-variants";
import { getChat, listPlaygrounds } from "@/apis";
import { useUserInfoStore } from '@/hooks/login';
import { type Playground } from "@/stores/playground";
import { useProjectStore } from '@/stores/project';
import { splitModel } from "@/utils/model";
import IconArrowRight from '~icons/mdi/arrow-right';

const cardStyle = tv({
  base: 'p-1 text-zinc-100 dark:text-zinc-200',
  variants: {
    type: {
      react: 'bg-blue-500 dark:bg-blue-600',
      vue: 'bg-green-500 dark:bg-green-600',
      lit: 'bg-amber-500 dark:bg-amber-600',
    }
  },
})

function useFirstMessage(id: string) {
  const [firstMessage, setFirstMessage] = useState('');

  useEffect(() => {
    const load = async () => {
      if (!id) {
        setFirstMessage('');
      } else {
        const ret = await getChat({ id, take: 1, sort: 'asc' });

        setFirstMessage(ret[0].content);
      }
    };

    load();
  }, [id]);

  return { firstMessage };
}

export function PlaygroundListWithLimit({ projectId }: { projectId?: string | null }) {
  const { userInfo } = useUserInfoStore();
  const navigate = useNavigate();
  const { projects } = useProjectStore();
  const [playgrounds, setPlaygrounds] = useState<Playground[]>([]);
  const [infoOpen, setInfoOpen] = useState('');

  useEffect(() => {
    const load = async () => {
      const ret = await listPlaygrounds({ 
        user: userInfo?.username,
        take: 30,
        sort: 'desc'
      });

      // Filter by project on client side if in project context
      if (projectId) {
        const filteredPlaygrounds = ret.filter((p: Playground) => p.projectId === projectId);

        setPlaygrounds(filteredPlaygrounds);
      } else {
        // Show only playgrounds that do not have a projectId
        const unassignedPlaygrounds = ret.filter((p: Playground) => !p.projectId);
        
        setPlaygrounds(unassignedPlaygrounds);
      }
    };

    load();

    // Listen for project changes to refresh the list
    const handleProjectChange = () => {
      load();
    };

    window.addEventListener('chatProjectChanged', handleProjectChange);
    
    return () => {
      window.removeEventListener('chatProjectChanged', handleProjectChange);
    };
  }, [userInfo, projectId]);

  // Get project name by ID
  const getProjectName = (id: string | undefined) => {
    if (!id) return null;
    const project = projects.find(p => p.id === id);

    return project?.name || '未知项目';
  };

  const onShowInfo = (id: string) => {
    setInfoOpen(id);
  };
  const onCloseInfo = () => {
    setInfoOpen('');
  };

  const { firstMessage } = useFirstMessage(infoOpen);

  const selectedProjectName = getProjectName(projectId || '');

  return (
    <div>
      <div className="max-w-7xl mx-auto px-6 space-y-8">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-bold text-default-800 dark:text-default-200">
              {projectId ? `${selectedProjectName} 的对话` : '最近的对话'}
            </h2>
            <p className="text-sm text-default-500 mt-1">
              {projectId 
                ? `查看你在 ${selectedProjectName} 项目中的 AI 编程对话`
                : '查看你最近创建的 AI对话，继续你的开发之旅'
              }
            </p>
          </div>
          <Button 
            className="text-sm text-default-500 hover:!bg-transparent hover:text-default-800"
            endContent={<IconArrowRight className="w-4 h-4" />}
            variant="light"
            onPress={() => navigate(projectId ? `/square?projectId=${projectId}` : '/square')}
          >
            查看所有
          </Button>
        </div>
      
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
          {playgrounds.map(({ id, name, desc, created, type, model, projectId, user }) => (
            <div
              key={id}
              className="cursor-pointer group"
              onClick={() => {
                window.open(`/chat/${id}`, '_blank');
              }}
            >
              {/* Preview Area */}
              <div className="aspect-[1.618] bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center relative overflow-hidden">
                <div className="absolute top-2 right-2 flex gap-2 z-10">
                  <Chip size="sm" variant="flat">
                    <div className="flex items-center gap-1.5">
                      <div className={`w-2 h-2 rounded-full ${
                        type === 'react' ? 'bg-blue-500' : 
                        type === 'vue' ? 'bg-green-500' : 
                        'bg-amber-500'
                      }`} />
                      <span className="text-xs">{type || '通用'}</span>
                    </div>
                  </Chip>
                  {projectId && (
                    <Chip color="secondary" size="sm" variant="flat">
                      {getProjectName(projectId)}
                    </Chip>
                  )}
                </div>
                <span className="text-sm text-neutral-500 dark:text-neutral-400">No screenshot available</span>
                <div className="absolute bottom-2 left-3 z-10 flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-300">
                  <span className="font-medium truncate">{user}</span>
                  {!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::') && (
                    <span className="font-medium truncate">· {model ? splitModel(model).modelName : 'Unknown Model'}</span>
                  )}
                  <span className="text-nowrap">· {moment(created).fromNow()}</span>
                </div>
              </div>

              {/* Content Area */}
              <div className="mt-4 flex items-start justify-between">
                <div className="min-w-0">
                  <h3 className="font-semibold text-default-900 truncate" title={name}>{name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate" title={desc || ''}>
                    {desc || ''}
                  </p>
                </div>
                <div className="flex items-center pl-2">
                  <IconArrowRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 group-hover:translate-x-1 transition-all duration-200" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {infoOpen && (
        <Modal isOpen={!!infoOpen} size="lg" onClose={onCloseInfo}>
          <ModalContent>
            {(onClose) => {
              const playground = playgrounds.find(e => e.id === infoOpen);

              if (!playground) return null;
              
              const { name, desc, created, type, model } = playground;
              const fields: Record<string, string> = {
                '名称': name,
                '描述': desc,
                '创建时间': moment(created).format('YYYY-MM-DD HH:mm:ss'),
                '类型': type,
              };
              
              // 只有当模型不以openrouter::或siliconflow::开头时才显示模型字段
              if (!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::')) {
                fields['模型'] = model;
              }

              return (
                <>
                  <ModalHeader className="flex flex-col gap-1">信息</ModalHeader>
                  <ModalBody>
                    <h1>npm包信息</h1>
                    <dl className="grid gap-4 grid-cols-[60px_1fr] border rounded-lg p-4">
                      {Object.entries(fields).map(([key, value]) => (
                        <div key={key} className="contents">
                          <dt className="text-sm font-medium text-muted-foreground">{key}</dt>
                          <dd className="text-base font-semibold">{value}</dd>
                        </div>
                      ))}
                    </dl>
                    <h1 className="mt-2">开发需求</h1>
                    <div className="border rounded-lg p-4">
                      <p>{firstMessage}</p>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <Button color="danger" variant="light" onPress={onClose}>
                      关闭
                    </Button>
                  </ModalFooter>
                </>
              );
            }}
          </ModalContent>
        </Modal>
      )}
    </div>
  );
} 