/* 样式通用设置 */
.markdown-body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: #24292f;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  /* 标题样式 */
  .markdown-body h1,
  .markdown-body h2,
  .markdown-body h3,
  .markdown-body h4,
  .markdown-body h5,
  .markdown-body h6 {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    color: #24292f;
  }
  
  .markdown-body h1 {
    font-size: 2em;
  }
  
  .markdown-body h2 {
    font-size: 1.75em;
  }
  
  .markdown-body h3 {
    font-size: 1.5em;
  }
  
  .markdown-body h4 {
    font-size: 1.25em;
  }
  
  .markdown-body h5 {
    font-size: 1.125em;
  }
  
  .markdown-body h6 {
    font-size: 1em;
    color: #6a737d;
  }
  
  /* 段落 */
  .markdown-body p {
    margin-bottom: 1em;
    font-size: 1rem;
  }
  
  /* 链接样式 */
  .markdown-body a {
    color: #0366d6;
    text-decoration: none;
  }
  
  .markdown-body a:hover {
    text-decoration: underline;
  }
  
  /* 无序列表 */
  .markdown-body ul {
    list-style-type: disc;
    padding-left: 20px;
  }
  
  .markdown-body li {
    margin-bottom: 0.5em;
  }
  
  /* 有序列表 */
  .markdown-body ol {
    list-style-type: decimal;
    padding-left: 20px;
  }
  
  .markdown-body li {
    margin-bottom: 0.5em;
  }
  
  /* 代码块 */
  .markdown-body pre {
    background-color: #f6f8fa;
    padding: 20px;
    border-radius: 6px;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-bottom: 1em;
  }
  
  .markdown-body code {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f6f8fa;
    border-radius: 4px;
    padding: 0.2rem 0.4rem;
    font-size: 0.875rem;
    color: #e83e8c;
  }
  
  /* Blockquote */
  .markdown-body blockquote {
    border-left: 4px solid #dfe2e5;
    padding-left: 16px;
    font-style: italic;
    color: #6a737d;
    margin: 1em 0;
  }
  
  /* 图片样式 */
  .markdown-body img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 20px 0;
  }
  
  /* 表格样式 */
  .markdown-body table {
    width: 100%;
    margin-bottom: 1em;
    border-collapse: collapse;
    border-spacing: 0;
  }
  
  .markdown-body th,
  .markdown-body td {
    padding: 8px 16px;
    border: 1px solid #dfe2e5;
    text-align: left;
  }
  
  .markdown-body th {
    background-color: #f6f8fa;
    font-weight: 600;
  }
  
  .markdown-body td {
    background-color: #fff;
  }
  
  /* 代码高亮（需要结合其他库如 Prism.js 或 Highlight.js 使用） */
  .markdown-body code.language-javascript,
  .markdown-body code.language-css,
  .markdown-body code.language-html {
    background-color: #f6f8fa;
    padding: 0.2rem 0.4rem;
    font-size: 0.875rem;
    border-radius: 4px;
    color: #e83e8c;
  }