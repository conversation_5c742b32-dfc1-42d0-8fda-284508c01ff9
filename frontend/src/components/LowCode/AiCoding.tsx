import { useEffect, useMemo, useState } from "react";
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  useDisclosure,
} from "@heroui/modal";
import { Button } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { createPlaygroundStore, PlaygroundContext } from "@/stores/playground";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import img from '@/assets/images/fmt.webp';
import { Chat } from "../Chat";
import { ChatBox } from "../ChatBox";
import { Preview } from "../Preview";
import IconAndroid from '~icons/mdi/android-debug-bridge';
import IconClose from '~icons/mdi/close';
import IconFullscreen from '~icons/mdi/fullscreen';
import { ArtifactEditor } from "../ArtifactEditor";

const examples = [
  {
    label: '横幅',
    value: '开发一个酷炫点首屏横幅，展示标题“今天买了么？”，小标题“百年不遇的黄金坑亟待发掘”。',
  },
  {
    label: '图表卡片',
    value: '开发一个卡片，展示标题：2024年哪些水果最受欢迎、正文：下面是2024年的销售数据，卖哪个你考虑清楚🤔。、还有一个echart图表。图表展示2024年销售数据，苹果：1000，橘子400，榴莲222，西瓜3000。',
  },
];

export function AiCoding({ id, onCreate }: {
  id?: string;
  onCreate: (args: { id: string }) => void;
  onChange: () => void;
}) {
  const {isOpen, onOpen, onOpenChange} = useDisclosure();
  const [playgroundId, setPlaygroundId] = useState<string>('');
  const [full, setFull] = useState(false);
  const store = useMemo(() => {
    if (playgroundId) {
      return createPlaygroundStore(playgroundId);
    }
  }, [playgroundId]);


  useEffect(() => {
    if (id) {
      setPlaygroundId(id);
    } else {
      setPlaygroundId('');
    }
  }, [id]);

  useEffect(() => {
    if (store) {
      store.getState().start();
    }
  }, [store]);

  let preview, content;
  if (playgroundId) {
    preview = (
      <Preview autoHeight />
    );

    content = (
      <PanelGroup className="flex-1 flex flex-row" direction="horizontal">
        <Panel className="flex" defaultSize={ full ? 30 : 100 }>
          <Chat id={playgroundId} />
        </Panel>
        {
          full ? (
            <>
              <PanelResizeHandle />
              <Panel>
                <ArtifactEditor />
              </Panel>
            </>
          ) : null
        }
      </PanelGroup>
    );
  } else {
    preview = (
      <div className="flex justify-center items-center min-h-[200px]">
        <img width={100} height={100} src={img} alt="AI开发" />
      </div>
    );
    content = (
      <ChatBox
        onCreate={(id) => {
          setPlaygroundId(id);
          onCreate({ id });
        }}
        examples={examples}
      />
    );
  }

  return (
    <PlaygroundContext.Provider value={ store }>
      <Card className="min-w-[400px]">
        <CardBody>
          { preview }
          <Button className="absolute top-2 right-2" size="sm" color="primary" isIconOnly onPress={onOpen}>
            <IconAndroid />
          </Button>
        </CardBody>
      </Card>
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        scrollBehavior="inside"
        size={full ? 'full' : 'lg'}
        hideCloseButton
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-row gap-1 border-b-1">
                <span className="flex-1">AI组件开发</span>
                <Button size="sm" variant="light" isIconOnly onPress={() => setFull(!full)}>
                  <IconFullscreen />
                </Button>
                <Button size="sm" variant="light" isIconOnly onPress={onClose}>
                  <IconClose />
                </Button>
              </ModalHeader>
              <ModalBody>
                { content }
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </PlaygroundContext.Provider>
  );
}
