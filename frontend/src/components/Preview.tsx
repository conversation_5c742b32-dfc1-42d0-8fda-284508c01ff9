import { <PERSON><PERSON> } from "@heroui/alert";
import { Autocomplete, AutocompleteItem } from "@heroui/autocomplete";
import { But<PERSON> } from "@heroui/button";
import { Mo<PERSON>, ModalContent, ModalHeader, ModalBody, ModalFooter } from "@heroui/modal";
import { Spinner } from "@heroui/spinner";
import { Tooltip } from "@heroui/tooltip";
import { CSSProperties, useContext, useEffect, useRef, useState } from "react";
import { PlaygroundContext } from "../stores/playground";
// import ProcessManagerModal from "./ProcessManagerModal";
import {
  baseUrl,
  startCustomPreviewHeartbeat,
  sendCustomPreviewHeartbeat,
  stopCustomPreviewHeartbeat,
  getCustomPreviewStatus
} from "@/apis";
import { eventBus } from "@/utils/eventBus";
import IconCheck from "~icons/mdi/check";
import IconClose from "~icons/mdi/close";
import IconEdit from "~icons/mdi/edit";
import IconLaunch from "~icons/mdi/launch";
import IconReload from "~icons/mdi/reload";
import IconRestart from "~icons/mdi/restart";

export function Preview({ autoWidth, autoHeight }: { autoWidth?: boolean; autoHeight?: boolean }) {
  const useStore = useContext(PlaygroundContext)!;
  const { url, restart, playground, updateUrl } = useStore();
  const [dark, setDark] = useState(false);
  const [errorButtonVisible, setErrorButtonVisible] = useState(false);
  const [lastErrorData, setLastErrorData] = useState<any>(null);
  const [size, setSize] = useState<{
    width: number;
    height: number;
  }>();
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const [isEditingUrl, setIsEditingUrl] = useState(false);
  const [customUrl, setCustomUrl] = useState("");
  const [parsedUrlsFromLogs, setParsedUrlsFromLogs] = useState<string[]>([]);
  const [isCustomRestarting, setIsCustomRestarting] = useState(false);

  // 检查是否支持自定义预览地址（只有enableCustomPreview为true的场景）
  const supportsCustomPreview = playground?.enableCustomPreview === true;

  const isProjectPreview = playground?.tags?.includes('project-preview'); // 是否项目预览模式

  // 使用单一的URL状态源 - store中的url作为唯一真实来源
  const currentPreviewUrl =
    isProjectPreview || supportsCustomPreview ? (url?.startsWith('/api/background-tasks') && !customUrl ? null : url) : url; // store中的URL是唯一真实来源

  // 新增：心跳和生命周期管理状态
  const [sessionId] = useState(() => `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [isAutoStarted, setIsAutoStarted] = useState(false);
  const heartbeatIntervalRef = useRef<number | null>(null);
  const isVisibleRef = useRef(true);
  
  // 加载超时控制
  const [showLoadingTimeout, setShowLoadingTimeout] = useState(false);
  const loadingTimeoutRef = useRef<number | null>(null);

  // 加载超时处理
  useEffect(() => {
    // 组件挂载后启动10秒计时器
    loadingTimeoutRef.current = window.setTimeout(() => {
      if (!currentPreviewUrl) {
        console.log('⏰ [Preview] 10秒超时，停止显示加载中状态');
        setShowLoadingTimeout(true);
      }
    }, 10000); // 10秒

    return () => {
      // 清理计时器
      if (loadingTimeoutRef.current) {
        window.clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
    };
  }, []); // 只在组件挂载时执行一次

  // 当获取到URL时，重置超时状态
  useEffect(() => {
    if (currentPreviewUrl && showLoadingTimeout) {
      setShowLoadingTimeout(false);
    }
  }, [currentPreviewUrl, showLoadingTimeout]);

  // 新增：页面可见性检测和自动启动
  useEffect(() => {
    // 页面可见性检测（不再立即停止服务）
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;
      console.log(`👁️ [Preview] 页面可见性变化: ${isVisibleRef.current ? '可见' : '隐藏'}`);

      // 页面隐藏时不再立即停止服务，依赖后端30分钟心跳超时
      // 只是暂停心跳发送，让后端自然超时处理
      if (document.hidden) {
        console.log('⏸️ [Preview] 页面隐藏，暂停心跳发送（30分钟后自动超时）');
      } else if (isAutoStarted) {
        console.log('▶️ [Preview] 页面显示，恢复心跳发送');
      }
    };

    // 优化：页面卸载检测（只在真正关闭时停止）
    const handleBeforeUnload = (event: Event) => {
      if (isAutoStarted) {
        // 给用户提示，确认是否真的要离开
        console.log('🚪 [Preview] 页面即将卸载，预览服务将在30分钟后自动超时停止');

        // 不再立即停止服务，而是依赖后端的30分钟超时机制
        // 这样用户刷新页面或意外关闭时服务仍然可用

        // 只在组件真正销毁时才立即停止
        // stopPreviewAndHeartbeat('page-unload');
      }
    };

    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 自动启动预览服务
    if (playground && supportsCustomPreview && !isAutoStarted) {
      // FIXME 先禁用自定义预览服务，保障测试环境整体流程走通
      // 先开启预览，测试效果
      autoStartPreview();
    }

    return () => {
      // 清理事件监听器
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // 清理心跳定时器
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }

      // 停止预览服务
      if (isAutoStarted) {
        // stopPreviewAndHeartbeat('component-unmount');
      }
    };
  }, [playground?.id, supportsCustomPreview, isAutoStarted]);

  // 当playground切换时重置地址状态
  useEffect(() => {
    if (playground?.id) {
      console.log('🔄 [Preview] Playground切换，重置地址状态');
      setCustomUrl("");
      // 不需要重置URL，让store管理URL状态
    }
  }, [playground?.id]);

  // 优化：智能启动预览服务（先检查状态再决定是否启动）
  const autoStartPreview = async () => {
    if (!playground || !supportsCustomPreview || isAutoStarted) return;

    try {
      console.log('🔍 [Preview] 检查现有预览服务状态...');
      setIsCustomRestarting(true);

      // 1. 先检查是否已有运行中的预览服务
      const statusResponse: any = await getCustomPreviewStatus({
        playgroundId: playground.id
      });

      const statusResult = statusResponse.data || statusResponse;

      if (statusResult && statusResult.success && statusResult.processes?.length > 0) {
        const runningProcesses = statusResult.processes.filter((p: any) =>
          p.status === 'online' || p.status === 'running'
        );

        if (runningProcesses.length > 0) {
          console.log('✨ [Preview] 发现现有运行中的预览服务，直接连接:', runningProcesses);

          // 直接连接到现有服务，不重新启动
          setIsAutoStarted(true);

          // 绝不自动覆盖用户的预览地址选择
          if (currentPreviewUrl) {
            console.log('👤 [Preview] 用户已有预览地址，严格保持不变:', currentPreviewUrl);
          } else {
            console.log('🔍 [Preview] 用户未设置预览地址，等待日志解析或用户手动选择');
          }

          // 启动心跳，连接到现有服务
          startHeartbeat();

          console.log('✅ [Preview] 已连接到现有预览服务，保持用户现有设置');

          return;
        }
      }

      // 2. 如果没有运行中的服务，则启动新的
      console.log('🚀 [Preview] 没有现有服务，启动新的预览服务');

      const result = await startCustomPreviewHeartbeat({
        playgroundId: playground.id,
        user: playground.user,
        sessionId
      });

      if (result && typeof result === 'object' && 'success' in result && result.success) {
        console.log('✅ [Preview] 新服务启动成功:', result);
        setIsAutoStarted(true);

        // 严格遵循用户意图，不自动覆盖
        if ('url' in result && result.url && !currentPreviewUrl) {
          const normalizedUrl = normalizePreviewUrl(result.url);

          console.log('🔗 [Preview] 初次获取到服务URL，设置为预览地址:', normalizedUrl);
          updateUrl(normalizedUrl);
        } else if (currentPreviewUrl) {
          console.log('👤 [Preview] 用户已有预览地址，绝不覆盖:', currentPreviewUrl);
          console.log('💡 [Preview] 如需切换，请用户手动选择');
        }

        // 启动心跳
        startHeartbeat();
      }
    } catch (error) {
      console.error('❌ [Preview] 智能启动失败:', error);
      // 如果状态检查失败，尝试直接启动
      try {
        console.log('🔄 [Preview] 状态检查失败，尝试直接启动服务...');
        const result = await startCustomPreviewHeartbeat({
          playgroundId: playground.id,
          user: playground.user,
          sessionId
        });

        if (result && typeof result === 'object' && 'success' in result && result.success) {
          console.log('✅ [Preview] 备用启动成功:', result);
          setIsAutoStarted(true);

          // 备用启动时严格遵循用户意图
          if ('url' in result && result.url && !currentPreviewUrl) {
            const normalizedUrl = normalizePreviewUrl(result.url);

            console.log('🔗 [Preview] 备用启动 - 初次设置预览地址:', normalizedUrl);
            updateUrl(normalizedUrl);
          } else if (currentPreviewUrl) {
            console.log('👤 [Preview] 备用启动 - 用户已有预览地址，绝不覆盖:', currentPreviewUrl);
          }

          startHeartbeat();
        }
      } catch (fallbackError) {
        console.error('❌ [Preview] 备用启动也失败:', fallbackError);
      }
    } finally {
      setIsCustomRestarting(false);
    }
  };

  // 优化：启动心跳定时器（页面隐藏时暂停发送）
  const startHeartbeat = () => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = window.setInterval(async () => {
      // 只在页面可见时发送心跳，隐藏时跳过（依赖后端30分钟超时）
      if (!isVisibleRef.current) {
        console.log('⏸️ [Preview] 页面隐藏中，跳过心跳发送');

        return;
      }

      if (!playground || !isAutoStarted) return;

      try {
        await sendCustomPreviewHeartbeat({
          playgroundId: playground.id,
          user: playground.user,
          sessionId
        });
        console.log('💓 [Preview] 心跳发送成功');
      } catch (error) {
        console.error('❌ [Preview] 心跳发送失败:', error);
      }
    }, 30000); // 每30秒发送一次心跳
  };

  // 新增：停止预览服务和心跳
  const stopPreviewAndHeartbeat = async (reason = 'manual') => {
    if (!playground || !isAutoStarted) return;

    try {
      console.log(`🛑 [Preview] 停止预览服务: ${reason}`);

      // 停止心跳定时器
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      // 调用停止API
      await stopCustomPreviewHeartbeat({
        playgroundId: playground.id,
        user: playground.user,
        sessionId
      });

      setIsAutoStarted(false);
      console.log('✅ [Preview] 预览服务已停止');
    } catch (error) {
      console.error('❌ [Preview] 停止预览服务失败:', error);
    }
  };

  // 修改：智能重启方法 - 根据playground类型选择合适的重启策略
  const handleSmartRestart = async () => {
    if (!playground) return;

    try {
      // 检查是否是spec-to-prod-code类型的playground且支持自定义预览 or 自定义项目预览模式
      if ((playground.type === 'spec-to-prod-code' && supportsCustomPreview) || isProjectPreview) {
        console.log('🔄 [Preview] 使用自定义重启逻辑 (spec-to-prod-code)');
        setIsCustomRestarting(true);

        try {
          // // 先停止现有的心跳和预览服务
          await stopPreviewAndHeartbeat('manual-restart');

          // 等待一小段时间确保服务完全停止
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 重新启动
          await autoStartPreview();

        } catch (error) {
          console.error('❌ [Preview] 自定义重启失败:', error);

          // 回退到标准重启
          console.log('🔄 [Preview] 回退到标准重启逻辑');
          await restart();
        } finally {
          setIsCustomRestarting(false);
        }
      } else {
        console.log('🔄 [Preview] 使用标准重启逻辑');
        await restart();
      }
    } catch (error) {
      console.error('❌ [Preview] 重启失败:', error);
      setIsCustomRestarting(false);
    }
  };

  // 新增：预览地址转换方法
  const previewUrl = (port: number) => {
    const hostname = window.location.hostname;

    // 根据hostname规则生成预览地址
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `http://localhost:${port}/`;
    }

    // 处理特定域名格式的转换
    // 例如：ai-coding.fe.htsc -> ai-coding-preview-${port}.fe.htsc
    // 例如：ai-coding-dev.sit.saas.htsc -> ai-coding-dev-preview-${port}.sit.saas.htsc
    const parts = hostname.split('.');

    if (parts.length >= 2) {
      // 取第一个部分（如 ai-coding 或 ai-coding-dev）
      const firstPart = parts[0];
      // 取剩余部分（如 fe.htsc 或 sit.saas.htsc）
      const remainingParts = parts.slice(1).join('.');

      // 构建预览地址：原前缀-preview-端口号.剩余域名
      let previewHostname;

      if (['ai-coding-dev.sit.saas.htsc', 'v0-sit.web.htsc'].includes(hostname)) {
        previewHostname = `v0-preview-${port}-sit.web.htsc`;
      } else {
        previewHostname = `${firstPart}-preview-${port}.${remainingParts}`;
      }
      // 使用与当前页面相同的协议
      const protocol = window.location.protocol;

      return `${protocol}//${previewHostname}/`;
    }

    // 兜底方案：如果域名格式不符合预期，使用localhost
    return `http://localhost:${port}/`;
  };

  // 新增：从URL中提取端口号
  const extractPortFromUrl = (url: string): number | null => {
    try {
      const urlObj = new URL(url);
      const port = urlObj.port;

      return port ? parseInt(port, 10) : (urlObj.protocol === 'https:' ? 443 : 80);
    } catch {
      return null;
    }
  };

  // 新增：标准化URL格式
  const normalizePreviewUrl = (url: string): string => {
    try {
      const urlObj = new URL(url);
      const port = extractPortFromUrl(url);

      if (port) {
        // 精确的本地/内网地址判断
        const hostname = urlObj.hostname;
        const isLocalOrPrivateAddress = 
          hostname === 'localhost' || 
          hostname === '127.0.0.1' || 
          hostname === '0.0.0.0' ||
          // RFC 1918 私有地址段
          /^192\.168\.\d+\.\d+$/.test(hostname) || // ***********/16
          /^10\.\d+\.\d+\.\d+$/.test(hostname) ||   // 10.0.0.0/8
          /^172\.(1[6-9]|2\d|3[01])\.\d+\.\d+$/.test(hostname) || // **********/12
          // 其他常见的内网地址段
          /^169\.254\.\d+\.\d+$/.test(hostname) || // 链路本地地址 ***********/16
          /^127\.\d+\.\d+\.\d+$/.test(hostname) || // 环回地址 *********/8
          // IPv6本地地址
          hostname === '::1' ||
          hostname.startsWith('[::1]') ||
          hostname.startsWith('fe80:') || // IPv6链路本地地址
          hostname.startsWith('[fe80:') ||
          // 本地开发域名（不包含多级域名的简单域名）
          (hostname.includes('.') === false && hostname !== 'localhost') ||
          // 常见的本地开发域名后缀
          hostname.endsWith('.local') ||
          hostname.endsWith('.lan') ||
          hostname.endsWith('.dev') ||
          hostname.endsWith('.test');

        if (isLocalOrPrivateAddress) {
          // 使用previewUrl方法重新构建URL，保持路径和查询参数
          const basePreviewUrl = previewUrl(port);
          const basePath = urlObj.pathname === '/' ? '' : urlObj.pathname;
          const search = urlObj.search;

          return basePreviewUrl.replace(/\/$/, '') + basePath + search;
        }
      }

      return url;
    } catch {
      return url;
    }
  };

  // 新增：参LogView实现的日志读取逻辑
  const logs = useRef([] as string[]);

  useEffect(() => {
    if (!playground?.id || !supportsCustomPreview) return;

    logs.current = [];
    console.log('🔍 [Preview] 开始读取日志，使用LogView相同的接口');

    // 严格按照LogView的实现
    const es = new EventSource(`${baseUrl}${playground.id}/@logs`);

    es.onmessage = ({ data }) => {
      logs.current.push(data);

      // 解析最新的日志消息，查找所有预览地址
      try {
        const allLogsContent = logs.current.map(log => JSON.parse(log).message).join('\n');

        console.log('🔍 [Preview] 原始日志内容长度:', allLogsContent.length);
        console.log('🔍 [Preview] 最新50条日志:', allLogsContent.slice(-2000)); // 显示最新2000字符

        const parsedUrls = parsePreviewUrlsFromLogs(allLogsContent);

        console.log('🔍 [Preview] 解析结果:', parsedUrls);
        console.log('🔍 [Preview] 当前已有地址:', parsedUrlsFromLogs);
        console.log('🔍 [Preview] 当前地址状态:', { currentPreviewUrl, customUrl });

        if (parsedUrls.length > 0 && JSON.stringify(parsedUrls) !== JSON.stringify(parsedUrlsFromLogs)) {
          console.log('🔍 [Preview] 发现新的预览地址，更新状态');

          // 标准化所有解析到的URL
          const normalizedUrls = parsedUrls.map(normalizePreviewUrl);


          const urls = Array.from(new Set([currentPreviewUrl, ...(normalizedUrls || [])])).filter(url => !!url)

          setParsedUrlsFromLogs(urls as string[]);

          // 堆栈结构：自动选择最新的地址（数组最后一个）
          const latestUrl = normalizedUrls[normalizedUrls.length - 1];

          // 只有在没有当前预览地址或者发现了真正新的地址时才自动切换
          if (!currentPreviewUrl || !normalizedUrls.includes(latestUrl)) {
            console.log('🎯 [Preview] 自动选择最新预览地址:', latestUrl);
            updateUrl(latestUrl);
          } else {
            console.log('👤 [Preview] 用户已有预览地址，仅更新候选列表');
          }
        } else if (parsedUrls.length === 0) {
          console.log('🔍 [Preview] 未解析到任何预览地址');
        }
      } catch (error) {
        console.error('❌ [Preview] 解析日志失败:', error);
      }
    };

    return () => {
      console.log('🔄 [Preview] 关闭EventSource连接');
      es.close();
    };
  }, [playground?.id, supportsCustomPreview, currentPreviewUrl]);

  function refresh() {
    const iframe = iframeRef.current;
    const targetUrl = currentPreviewUrl;

    if (iframe && targetUrl) {
      iframe.src = "about:blank";
      setTimeout(() => {
        iframe.src = targetUrl;
      }, 0);
    }
  }

  // 新增：从日志解析多个预览地址 - 增强版：使用堆栈结构管理预览地址
  const parsePreviewUrlsFromLogs = (logContent: string): string[] => {
    if (!logContent) {
      return [];
    }

    console.log('🔍 [Preview] 开始解析日志中的预览地址');

    // 使用Map记录"端口+路径"组合和其最后出现的位置，实现精确去重
    const portPathPositionMap = new Map<string, { url: string; position: number }>();
    const logLines = logContent.split('\n');

    // 更全面的URL匹配模式
    const urlPatterns = [
      // 完整HTTP/HTTPS URL
      /https?:\/\/(?:localhost|127\.0\.0\.1|\d+\.\d+\.\d+\.\d+):(\d+)(?:\/[^\s\]"'<>]*)?/g,

      // 服务器启动消息中的URL
      /(?:Server (?:is )?listening (?:at|on)?:?\s*|Local:\s*|Network:\s*|running (?:at|on)?:?\s*)(https?:\/\/[^\s\]"'<>]+)/gi,

      // 开发服务器常见输出格式
      /(?:➜|›)\s+(?:Local|Network):\s+(https?:\/\/[^\s\]"'<>]+)/gi,

      // 各种框架特有格式
      /(?:ready - started server on|App running at|Development server running at|ready on)\s+(https?:\/\/[^\s\]"'<>]+)/gi,

      // PM2日志中的URL解析标记
      /\[PM2-URL-PARSER\].*?预览地址\d*:\s*(https?:\/\/[^\s\]"'<>]+)/gi,
    ];

    // 遍历每一行日志，记录URL及其出现位置
    for (let lineIndex = 0; lineIndex < logLines.length; lineIndex++) {
      const line = logLines[lineIndex];

      // 对每种模式进行匹配
      for (const pattern of urlPatterns) {
        pattern.lastIndex = 0; // 重置正则表达式状态
        let match;

        while ((match = pattern.exec(line)) !== null) {
          let url = match[0];

          // 如果匹配包含分组，使用第一个分组作为URL
          if (match[1] && match[1].startsWith('http')) {
            url = match[1];
          }

          // 清理URL
          url = cleanAndNormalizeUrl(url);

          if (url && isValidPreviewUrl(url)) {
            // 提取端口号和路径进行组合去重
            const port = extractPortFromUrl(url);

            if (port) {
              try {
                const urlObj = new URL(url);
                const pathname = urlObj.pathname || '/';
                // 创建"端口+路径"的唯一标识
                const portPathKey = `${port}${pathname}`;
                
                // 标准化URL，优先使用当前页面的预览地址格式
                const normalizedUrl = normalizePreviewUrl(url);
                
                // 使用"端口+路径"作为key，后出现的会覆盖前面的（保持最新的URL格式）
                if (!portPathPositionMap.has(portPathKey) || portPathPositionMap.get(portPathKey)!.position < lineIndex) {
                  portPathPositionMap.set(portPathKey, { url: normalizedUrl, position: lineIndex });
                }
              } catch (error) {
                console.warn('URL解析失败:', url, error);
              }
            }
          }
        }
      }

      // 额外的直接URL提取
      const directUrlPattern = /https?:\/\/[^\s\]"'<>]+/g;
      let directMatch;

      while ((directMatch = directUrlPattern.exec(line)) !== null) {
        let url = directMatch[0];

        url = cleanAndNormalizeUrl(url);

        if (url && isValidPreviewUrl(url)) {
          const port = extractPortFromUrl(url);

          if (port) {
            try {
              const urlObj = new URL(url);
              const pathname = urlObj.pathname || '/';
              const portPathKey = `${port}${pathname}`;
              
              const normalizedUrl = normalizePreviewUrl(url);
              
              if (!portPathPositionMap.has(portPathKey) || portPathPositionMap.get(portPathKey)!.position < lineIndex) {
                portPathPositionMap.set(portPathKey, { url: normalizedUrl, position: lineIndex });
              }
            } catch (error) {
              console.warn('URL解析失败:', url, error);
            }
          }
        }
      }
    }

    // 将Map转换为数组，按出现位置排序（最后出现的在数组最后）
    const sortedUrls = Array.from(portPathPositionMap.values())
      .sort((a, b) => a.position - b.position) // 按位置升序排序（最后出现的在数组最后）
      .map(({ url }) => url);

    console.log('🎯 [Preview] 解析完成，共找到', sortedUrls.length, '个唯一的端口+路径组合预览地址');

    return sortedUrls;
  };

  /**
   * 清理和标准化URL
   */
  const cleanAndNormalizeUrl = (rawUrl: string): string => {
    if (!rawUrl) return '';

    // 移除ANSI颜色代码
    let url = rawUrl.replace(/\[[0-9;]*m/g, '');

    // 移除常见的前缀文本
    url = url.replace(/^(?:Server (?:is )?listening (?:at|on)?:?\s*|Local:\s*|Network:\s*|running (?:at|on)?:?\s*|➜\s+(?:Local|Network):\s+|ready - started server on\s+|App running at:\s*|Development server running at:\s*|ready on\s+)/gi, '');

    // 移除末尾的标点符号和特殊字符
    url = url.replace(/[,.\])}'"<>;\s]+$/, '');

    // 确保URL以http开头
    if (url && !url.startsWith('http')) {
      // 如果只是端口号，添加http://localhost前缀
      if (/^\d+$/.test(url.trim())) {
        url = `http://localhost:${url.trim()}`;
      }
    }

    try {
      // 验证URL格式并标准化
      const urlObj = new URL(url);

      return urlObj.toString();
    } catch (error) {
      // URL格式无效
      return '';
    }
  };

  /**
   * 验证是否为有效的预览URL
   */
  const isValidPreviewUrl = (url: string): boolean => {
    if (!url) return false;

    try {
      const urlObj = new URL(url);

      // 只接受HTTP/HTTPS协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // 检查主机名是否为本地地址
      const validHosts = ['localhost', '127.0.0.1', '0.0.0.0'];
      const isLocalHost = validHosts.includes(urlObj.hostname) ||
        /^192\.168\.\d+\.\d+$/.test(urlObj.hostname) || // 局域网地址
        /^10\.\d+\.\d+\.\d+$/.test(urlObj.hostname) ||   // 内网地址
        /^172\.(1[6-9]|2\d|3[01])\.\d+\.\d+$/.test(urlObj.hostname); // 内网地址

      if (!isLocalHost) {
        return false;
      }

      // 检查端口范围
      const port = parseInt(urlObj.port);

      if (port && (port < 3000 || port > 9999)) {
        return false;
      }

      return true;

    } catch (error) {
      return false;
    }
  };

  // 新增：处理URL编辑
  const handleUrlEdit = () => {
    if (isEditingUrl) {
      // 确认编辑
      const newUrl = validateAndGetCustomUrl(customUrl);

      setIsEditingUrl(false);

      // 如果验证成功，更新store中的预览地址
      if (newUrl) {
        const normalizedUrl = normalizePreviewUrl(newUrl);

        updateUrl(normalizedUrl);
        console.log('✅ [Preview] 预览地址已更新到store:', normalizedUrl);

        // 立即刷新iframe到新地址
        const iframe = iframeRef.current;

        if (iframe) {
          iframe.src = "about:blank";
          setTimeout(() => {
            iframe.src = normalizedUrl;
          }, 0);
        }
      }
    } else {
      // 开始编辑
      setIsEditingUrl(true);
    }
  };

  // 新增：验证并获取自定义URL（返回验证后的URL而不是设置状态）
  const validateAndGetCustomUrl = (input: string): string | null => {
    if (!input.trim()) {
      return null;
    }

    try {
      let targetUrl = "";
      const baseUrlObj = parsedUrlsFromLogs.length > 0
        ? new URL(parsedUrlsFromLogs[0])
        : new URL(previewUrl(3000)); // 使用previewUrl方法获取默认URL

      if (input.match(/^https?:\/\//)) {
        // 弱化：完整URL - 允许任何有效的HTTP/HTTPS URL
        try {
          new URL(input); // 只验证URL格式是否正确
          targetUrl = input;
        } catch {
          throw new Error("URL格式不正确");
        }
      } else if (input.match(/^:\d+/)) {
        // :端口格式 - 如 ":3001/page"
        const match = input.match(/^:(\d+)(.*)/);

        if (match) {
          const port = parseInt(match[1], 10);
          const path = match[2] || '';
          const basePreviewUrl = previewUrl(port);

          targetUrl = basePreviewUrl.replace(/\/$/, '') + path;
        }
      } else if (input.match(/^\d+/)) {
        // 端口格式 - 如 "3001/page"
        const match = input.match(/^(\d+)(.*)/);

        if (match) {
          const port = parseInt(match[1], 10);
          const path = match[2] || '';
          const basePreviewUrl = previewUrl(port);

          targetUrl = basePreviewUrl.replace(/\/$/, '') + path;
        }
      } else if (input.startsWith('/')) {
        // 路径格式 - 如 "/page"
        const port = extractPortFromUrl(baseUrlObj.toString()) || 3000;
        const basePreviewUrl = previewUrl(port);

        targetUrl = basePreviewUrl.replace(/\/$/, '') + input;
      } else {
        // 弱化：尝试智能解析，而不是直接报错
        // 如果输入看起来像一个域名或IP，自动加上http://前缀
        if (input.match(/^[\w.-]+\.[a-z]{2,}(:\d+)?(\/.*)?$/i) ||
          input.match(/^\d+\.\d+\.\d+\.\d+(:\d+)?(\/.*)?$/)) {
          try {
            targetUrl = `http://${input}`;
            new URL(targetUrl); // 验证构造的URL是否有效
          } catch {
            throw new Error("无法解析输入的地址格式");
          }
        } else {
          // 最后尝试：假设是相对于当前基础URL的路径
          try {
            const currentPort = extractPortFromUrl(baseUrlObj.toString()) || 3000;
            const basePreviewUrl = previewUrl(currentPort);

            targetUrl = basePreviewUrl.replace(/\/$/, '') + '/' + input.replace(/^\/+/, '');
          } catch {
            throw new Error("无法解析输入格式，请输入有效的URL、端口号或路径");
          }
        }
      }

      console.log('✅ [Preview] URL验证成功:', targetUrl);

      return targetUrl;
    } catch (error) {
      console.warn('⚠️ [Preview] URL验证失败:', error);
      // 弱化：使用更温和的提示方式，而不是弹窗
      console.warn(`URL格式提示: ${error instanceof Error ? error.message : '未知错误'}`);

      // 尝试最宽松的处理：直接使用输入值
      if (input.trim()) {
        console.log('🔄 [Preview] 使用宽松模式，直接使用输入值:', input);

        return input.trim();
      }

      return null;
    }
  };

  // 新增：取消编辑
  const handleCancelEdit = () => {
    setIsEditingUrl(false);
    setCustomUrl("");
  };

  // 新增：处理回车确认
  const handleKeyPress = (e: React.KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.key === 'Enter') {
      handleUrlEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const [cursorColor, setCursorColor] = useState<string | undefined>();
  const [enabled, setEnabled] = useState(false);
  const [processManagerVisible, setProcessManagerVisible] = useState(false);

  useEffect(() => {
    const iframe = iframeRef.current;

    if (iframe) {
      iframe.contentWindow?.postMessage({ enabled }, "*");
    }
  }, [enabled]);


  useEffect(() => {
    function handleMessage(event: MessageEvent) {
      const file: string = event.data?.info?.file;
      const line: string = event.data?.info?.line;
      const column: string = event.data?.info?.column;
      const prompt: string = event.data?.prompt;

      if (file && line && column) {
        const index = file.indexOf("src/");
        const chatFile: string = index !== -1 ? file.slice(index + 4) : file; // +4 是为了跳过 "src/"
        const message: string = `${prompt}，位置在 ${chatFile} 文件的第 ${line} 行，第 ${column} 列`;

        eventBus.emit("append-chat", message);
        setCursorColor(undefined); // 切换颜色
        setEnabled(false);
      }

      if (event.data?.error) {
        setLastErrorData(event.data);
        sendErrorMsgBystrategy(event.data);
      }
    }

    // 🔧 新增：WebSocket连接失败处理
    function handleWebSocketError(event: Event) {
      console.warn('⚠️ [Preview] WebSocket连接失败，可能是服务启动延迟');
      
      // 如果是iframe中的WebSocket连接失败，尝试延迟重试
      if (iframeRef.current && currentPreviewUrl) {
        console.log('🔄 [Preview] 3秒后尝试重新加载预览页面...');
        setTimeout(() => {
          if (iframeRef.current) {
            iframeRef.current.src = currentPreviewUrl;
          }
        }, 3000);
      }
    }

    window.addEventListener("message", handleMessage);
    window.addEventListener("error", handleWebSocketError);

    return () => {
      window.removeEventListener("message", handleMessage);
      window.removeEventListener("error", handleWebSocketError);
    };
  }, [currentPreviewUrl]);

  function sendErrorMsgBystrategy(data: any) {
    const MAX_AI_AUTO_FIX_NUM = 3;

    // 使用本地计数器而不是state
    const currentErrorCount = (window as any).__errorCount || 0;
    const newCount = currentErrorCount + 1;

    (window as any).__errorCount = newCount;

    if (newCount > MAX_AI_AUTO_FIX_NUM) {
      setErrorButtonVisible(true);
    } else {
      sendErrorMsg(data);
    }
  }

  function sendErrorMsg(data: any) {
    const stack: string = data?.error?.stack;
    const prompt: string = data?.prompt;
    const message: string = `出错了，${prompt},该错误的堆栈信息是 ${stack}`;

    eventBus.emit("append-chat", {
      text: message,
      annotations: [
        {
          type: "js-error",
          stack,
        },
      ],
    });
  }

  function select() {
    const iframe = iframeRef.current;
    const targetUrl = currentPreviewUrl;

    if (!iframe || !targetUrl) return;

    setCursorColor(cursorColor ? undefined : "blue"); // 切换颜色
    setEnabled((prevEnabled) => !prevEnabled);
  }

  const type = playground?.type;
  const supportSelection = type === "react" || type === "vue";

  useEffect(() => {
    window?.addEventListener?.("message", (evt) => {
      const { sender, value, type } = evt.data ?? {};

      if (sender === "story-app") {
        if (type === "resize") {
          setSize(value);
        }
      }
    });
  }, []);

  useEffect(() => {
    const iframe = iframeRef.current;

    if (iframe) {
      iframe.addEventListener("load", () => {
        iframe.contentWindow?.postMessage({ enabled: true }, '*');
        iframe.contentWindow?.postMessage({ enabled: false }, '*');
      })
    }
  }, [iframeRef.current]);

  const iframeSize: CSSProperties = {};

  if (size) {
    if (autoWidth) {
      iframeSize.minWidth = `${size.width}px`;
    }
    if (autoHeight) {
      iframeSize.minHeight = `${size.height}px`;
    }
  }

  const displayUrl = currentPreviewUrl;

  // 如果没有URL且未超时，显示加载中
  if (!displayUrl && !showLoadingTimeout) {
    return (
      <div className="flex-1 flex flex-col h-full items-center justify-center">
        <Spinner label="加载中..." />
      </div>
    );
  }

  return (
    <>
      {supportsCustomPreview && <Alert isClosable color="warning" description={
        <>
          系统会自动将常见Web框架的预览服务host设置为0.0.0.0。如果您使用自定义项目且不支持标准环境变量，需要先切换到代码编辑器将该项目构建配置中的host改为0.0.0.0，然后再点击重启预览服务按钮进行重启，重启后再观察项目预览效果。
          <br />
          <span style={
            {
              color: "#6b7280",
              fontSize: "12px",
              fontWeight: "bold",
            }
          }>备注：云桌面外访问时若出现域名解析错误，可尝试配置本机hosts文件。</span>
        </>
      } />}
      <div className="flex-1 flex flex-col h-full">
        <Modal
          backdrop={"transparent"}
          isDismissable={false}
          isKeyboardDismissDisabled={true}
          isOpen={errorButtonVisible}
          onClose={() => setErrorButtonVisible(false)}
        >
          <ModalContent>
            <ModalHeader>错误提示</ModalHeader>
            <ModalBody>出错了，是否需要AI分析并自动修复？</ModalBody>
            <ModalFooter>
              <Button
                color="primary"
                onPress={() => {
                  setErrorButtonVisible(false);
                  if (lastErrorData) {
                    sendErrorMsg(lastErrorData);
                  }
                }}
              >
                Start
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 优化：预览地址选择区域 - 支持直接切换和编辑 */}
        {supportsCustomPreview && (
          <div className="flex items-center px-2 py-1 bg-gray-50 dark:bg-gray-800 border-b">
            <div className="flex items-center flex-1 gap-2">
              <span className="text-sm text-gray-600">预览地址:</span>

              {/* 当有多个地址时显示Autocomplete，否则显示输入框 */}
              {parsedUrlsFromLogs.length > 1 ? (
                <Autocomplete
                  allowsCustomValue={isEditingUrl ? true : false}
                  className="flex-1 max-w-md"
                  inputValue={customUrl || displayUrl || ""}
                  isClearable={false}
                  placeholder={parsedUrlsFromLogs.length > 0 ? "从下拉列表选择或输入自定义地址" : "输入任意URL、端口号、路径"}
                  selectedKey={displayUrl}
                  size="sm"
                  onInputChange={(value) => {
                    if (isEditingUrl) {
                      setCustomUrl(value);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      e.stopPropagation();
                      const inputValue = customUrl || displayUrl || "";
                      const newUrl = validateAndGetCustomUrl(inputValue);

                      if (newUrl) {
                        const normalizedUrl = normalizePreviewUrl(newUrl);

                        updateUrl(normalizedUrl);
                        setCustomUrl(""); // 清空临时输入状态


                        const iframe = iframeRef.current;

                        if (iframe) {
                          iframe.src = "about:blank";
                          setTimeout(() => {
                            iframe.src = normalizedUrl;
                          }, 0);
                        }
                      }
                    }
                  }}
                  onSelectionChange={(key) => {
                    if (key) {
                      const selectedUrl = String(key);
                      const normalizedUrl = normalizePreviewUrl(selectedUrl);

                      updateUrl(normalizedUrl);
                      setCustomUrl(""); // 清空临时输入状态


                      const iframe = iframeRef.current;

                      if (iframe) {
                        iframe.src = "about:blank";
                        setTimeout(() => {
                          iframe.src = normalizedUrl;
                        }, 0);
                      }
                    }
                  }}
                >
                  {/* 堆栈结构：将最新的地址排在前面，方便用户选择 */}
                  {[...parsedUrlsFromLogs].reverse().map((urlOption, index) => (
                    <AutocompleteItem key={urlOption}>
                      {urlOption}
                    </AutocompleteItem>
                  ))}
                </Autocomplete>
              ) : (
                // 只有一个或没有解析到地址时，显示编辑模式
                <>
                  {isEditingUrl ? (
                    <Autocomplete
                      allowsCustomValue
                      className="flex-1 max-w-md"
                      inputValue={customUrl || ''}
                      isClearable={false}
                      placeholder="输入任意URL、端口号或路径"
                      size="sm"
                      onInputChange={setCustomUrl}
                      onKeyDown={handleKeyPress}
                      onSelectionChange={(key) => {
                        if (key) {
                          const selectedUrl = String(key);

                          setCustomUrl(selectedUrl);
                        }
                      }}
                    >
                      {/* 堆栈结构：将最新的地址排在前面 */}
                      {[...parsedUrlsFromLogs].reverse().map((urlOption, index) => (
                        <AutocompleteItem key={urlOption}>
                          {urlOption}
                        </AutocompleteItem>
                      ))}
                    </Autocomplete>
                  ) : (
                    <span
                      className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded flex-1 max-w-md truncate cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600"
                      onClick={handleUrlEdit}
                    >
                      {displayUrl || '暂无解析到对应预览地址'}
                    </span>   
                  )}
                </>
              )}

              {/* 地址计数标识和新地址提示 */}
              {parsedUrlsFromLogs.length > 1 && (
                <span className="text-xs text-blue-600 bg-blue-50 dark:bg-blue-900/20 px-1 py-0.5 rounded">
                  {parsedUrlsFromLogs.length}个地址
                </span>
              )}

              {/* 新增：当有新地址但用户未使用时的提示 */}
              {parsedUrlsFromLogs.length > 0 &&
                currentPreviewUrl &&
                !parsedUrlsFromLogs.includes(currentPreviewUrl) && (
                  <span className="text-xs text-amber-600 bg-amber-50 dark:bg-amber-900/20 px-1 py-0.5 rounded">
                    有新地址
                  </span>
                )}

              {/* 编辑按钮 */}
              {isEditingUrl ? (
                <>
                  <Button
                    isIconOnly
                    color="success"
                    size="sm"
                    variant="flat"
                    onPress={handleUrlEdit}
                  >
                    <IconCheck height={16} width={16} />
                  </Button>
                  <Button
                    isIconOnly
                    color="danger"
                    size="sm"
                    variant="flat"
                    onPress={handleCancelEdit}
                  >
                    <IconClose height={16} width={16} />
                  </Button>
                </>
              ) : (
                <Tooltip content="编辑预览地址">
                  <Button
                    isIconOnly
                    size="sm"
                    variant="flat"
                    onPress={handleUrlEdit}
                  >
                    <IconEdit height={16} width={16} />
                  </Button>
                </Tooltip>
              )}
            </div>
          </div>
        )}

        <header className="flex items-center justify-end px-2 py-1">
          {/* <Tooltip content="进程管理">
          <Button
            isIconOnly
            className="ml-1"
            size="sm"
            variant="bordered"
            onPress={() => setProcessManagerVisible(true)}
          >
            <IconCog height={20} width={20} />
          </Button>
        </Tooltip> */}
          <Tooltip content="重启预览服务" placement="bottom">
            <Button
              isIconOnly
              className="ml-1"
              isDisabled={isCustomRestarting}
              isLoading={isCustomRestarting}
              size="sm"
              variant="light"
              onPress={handleSmartRestart}
            >
              <IconRestart height={16} width={16} />
            </Button>
          </Tooltip>
          <Tooltip content="新窗口打开" placement="bottom">
            <Button isIconOnly className="ml-1" size="sm" variant="light" onPress={() => displayUrl && window.open(displayUrl, "_blank")}>
              <IconLaunch height={16} width={16} />
            </Button>
          </Tooltip>
          <Tooltip content="刷新页面" placement="bottom">
            <Button isIconOnly className="ml-1" size="sm" variant="light" onPress={() => refresh()}>
              <IconReload height={16} width={16} />
            </Button>
          </Tooltip>
          {/* <Tooltip content="切换主题">
          <Button isIconOnly className="ml-1" size="sm" variant="light" onPress={() => setDark((prev) => !prev)}>
            <IconInvertColors height={16} width={16} />
          </Button>
        </Tooltip> */}
          {/* {supportSelection ? (
          <Tooltip content="选择节点">
            <Button isIconOnly className="ml-1" size="sm" variant="light" onPress={() => select()}>
              <MdiCursorDefault height={16} style={{ color: cursorColor }} width={16} />
            </Button>
          </Tooltip>
        ) : null} */}
        </header>
        <iframe
          key={displayUrl} // 当URL变化时强制重新渲染iframe
          ref={iframeRef}
          className="flex-1"
          sandbox="allow-modals allow-forms allow-popups allow-scripts allow-same-origin"
          src={displayUrl}
          style={{
            ...iframeSize,
            backgroundColor: dark ? "black" : "white",
          }}
        />
        {/* <ProcessManagerModal isOpen={processManagerVisible} onClose={() => setProcessManagerVisible(false)} /> */}
      </div>
    </>
  );
}