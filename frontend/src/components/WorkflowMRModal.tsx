import { Modal, Table, Button, Tag, message, Spin, Typography } from 'antd';
import React, { useState, useEffect } from 'react';

// 导入MDI图标
import IconCheckCircle from '~icons/mdi/check-circle';
import IconClockOutline from '~icons/mdi/clock-outline';
import IconCloseCircle from '~icons/mdi/close-circle';
import IconGitlab from '~icons/mdi/gitlab';
import IconBranch from '~icons/mdi/source-branch';
import IconMerge from '~icons/mdi/source-merge';

const { Text } = Typography;

interface MergeRequest {
  id: number;
  iid: number;
  title: string;
  description: string;
  state: 'opened' | 'closed' | 'merged';
  web_url: string;
  source_branch: string;
  target_branch: string;
  created_at: string;
  updated_at: string;
  merged_at?: string;
  author: {
    name: string;
    username: string;
    avatar_url?: string;
  };
  pageName: string;
  workflowId?: string;
  gitlabEnv: string;
  projectInfo: {
    namespace: string;
    projectName: string;
  };
}

interface WorkflowMRModalProps {
  visible: boolean;
  onCancel: () => void;
  projectId: string;
  workflowId?: string; // 用于应用级工作流
  pageId?: string; // 用于页面级工作流
  title?: string;
}

const WorkflowMRModal: React.FC<WorkflowMRModalProps> = ({
  visible,
  onCancel,
  projectId,
  workflowId,
  pageId,
  title = '生码结果MR列表'
}) => {
  const [loading, setLoading] = useState(false);
  const [mergeRequests, setMergeRequests] = useState<MergeRequest[]>([]);

  // 获取MR列表
  const fetchMergeRequests = async () => {
    if (!visible || (!workflowId && !pageId)) return;

    setLoading(true);
    try {
      let url: string;

      if (workflowId) {
        // 应用级工作流
        url = `/api/design-project/${projectId}/workflows/${workflowId}/merge-requests`;
      } else if (pageId) {
        // 页面级工作流
        url = `/api/design-project/${projectId}/pages/${pageId}/merge-requests`;
      } else {
        throw new Error('缺少必要的参数');
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      setMergeRequests(data || []);
    } catch (error) {
      console.error('获取MR列表失败:', error);
      message.error(`获取MR列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
      setMergeRequests([]);
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开时获取数据
  useEffect(() => {
    if (visible) {
      fetchMergeRequests();
    }
  }, [visible, workflowId, pageId, projectId]);

  // 状态图标组件
  const StatusIcon: React.FC<{ state: string }> = ({ state }) => {
    switch (state) {
      case 'opened':
        return <IconClockOutline style={{ color: '#1890ff' }} />;
      case 'merged':
        return <IconCheckCircle style={{ color: '#52c41a' }} />;
      case 'closed':
        return <IconCloseCircle style={{ color: '#ff4d4f' }} />;
      default:
        return <IconClockOutline style={{ color: '#d9d9d9' }} />;
    }
  };

  // 状态标签组件
  const StatusTag: React.FC<{ state: string }> = ({ state }) => {
    const getStatusConfig = () => {
      switch (state) {
        case 'opened':
          return { color: 'blue', text: '开放中' };
        case 'merged':
          return { color: 'green', text: '已合并' };
        case 'closed':
          return { color: 'red', text: '已关闭' };
        default:
          return { color: 'default', text: '未知' };
      }
    };

    const config = getStatusConfig();

    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 表格列配置
  const columns = [
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 100,
      render: (state: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <StatusIcon state={state} />
          <StatusTag state={state} />
        </div>
      ),
    },
    {
      title: 'MR信息',
      key: 'mrInfo',
      render: (record: MergeRequest) => (
        <div>
          <div style={{ fontWeight: '500', marginBottom: '4px', fontSize: '14px' }}>
            {record.title}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            #{record.iid} • 页面: {record.pageName}
          </div>
        </div>
      ),
    },
    {
      title: '分支信息',
      key: 'branchInfo',
      render: (record: MergeRequest) => (
        <div style={{ fontSize: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '2px' }}>
            <IconBranch style={{ fontSize: '14px', color: '#666' }} />
            <Text code style={{ fontSize: '11px' }}>{record.source_branch}</Text>
          </div>
          <div style={{ color: '#666' }}>
            → <Text code style={{ fontSize: '11px' }}>{record.target_branch}</Text>
          </div>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {formatTime(time)}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record: MergeRequest) => (
        <Button
          size="small"
          style={{
            borderRadius: '6px',
            fontSize: '12px',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}
          type="primary"
          onClick={() => window.open(record.web_url, '_blank')}
        >
          <IconGitlab style={{ fontSize: '14px' }} />
          查看MR
        </Button>
      ),
    },
  ];

  return (
    <Modal
      footer={
        <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
          <Button 
            style={{
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              color: '#374151',
              background: '#fff'
            }}
            onClick={onCancel}
          >
                          关闭
          </Button>
          <Button 
            loading={loading} 
            style={{
              borderRadius: '6px',
              background: '#000',
              borderColor: '#000'
            }} 
            type="primary"
            onClick={fetchMergeRequests}
          >
            刷新
          </Button>
        </div>
      }
      open={visible}
      styles={{
        content: {
          padding: 0,
          borderRadius: '12px',
          overflow: 'hidden',
          maxHeight: '90vh'
        },
        header: {
          padding: '20px 24px 16px',
          borderBottom: '1px solid #e5e7eb',
          margin: 0,
          flexShrink: 0
        },
        body: {
          padding: '20px 24px',
          maxHeight: 'calc(90vh - 140px)', // 减去header和footer的高度
          overflowY: 'auto'
        },
        footer: {
          padding: '16px 24px 20px',
          margin: 0,
          borderTop: '1px solid #e5e7eb',
          flexShrink: 0
        }
      }}
      title={
        <div style={{ 
          fontSize: '18px', 
          fontWeight: '600', 
          color: '#000',
          padding: '4px 0'
        }}>
          {title}
        </div>
      }
      width={900}
      onCancel={onCancel}
    >
      {loading ? (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '300px' 
        }}>
          <Spin size="large" />
        </div>
      ) : mergeRequests.length === 0 ? (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '300px',
          color: '#6b7280'
        }}>
          <IconMerge style={{ fontSize: '48px', color: '#d1d5db', marginBottom: '16px' }} />
          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
            暂无相关的MR记录
          </div>
          <div style={{ fontSize: '14px', color: '#9ca3af' }}>
            当前工作流还没有生成任何Merge Request
          </div>
        </div>
      ) : (
        <>
          <div style={{ 
            marginBottom: '16px', 
            padding: '12px 16px', 
            backgroundColor: '#f9fafb', 
            borderRadius: '8px',
            fontSize: '14px',
            color: '#374151',
            border: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <IconMerge style={{ fontSize: '16px', color: '#6b7280' }} />
              <span>找到 <strong>{mergeRequests.length}</strong> 个相关的Merge Request</span>
            </div>
          </div>
          
          <Table
            columns={columns}
            dataSource={mergeRequests}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showQuickJumper: false,
              showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
              style: { marginTop: '16px' }
            }}
            rowKey="id"
            scroll={{ x: true, y: 'calc(90vh - 320px)' }}
            size="middle"
            style={{
              background: '#fff'
            }}
          />
        </>
      )}
    </Modal>
  );
};

export default WorkflowMRModal; 