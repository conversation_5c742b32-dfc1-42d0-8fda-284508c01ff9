import { useContext, FC } from 'react';
import { useStore } from 'zustand';
import { useChatPolling } from '@/hooks/useChatPolling';
import { PlaygroundContext } from '@/stores/playground';
import IconAndroid from '~icons/mdi/android-debug-bridge';
import IconChat from '~icons/mdi/chat';

interface IHeaderProps {
  enableStepByStep: boolean;
  chatPolling: ReturnType<typeof useChatPolling>;
}

const Header: FC<IHeaderProps> = (props) => {
  const { enableStepByStep, chatPolling } = props;
  const store = useContext(PlaygroundContext)!;
  const pgStore = useStore(store);

  // 检查是否应该显示模型信息
  const shouldShowModel = pgStore.playground?.model && 
    !pgStore.playground.model.startsWith('openrouter::') && 
    !pgStore.playground.model.startsWith('siliconflow::');


  return (
    <header className="h-12 flex items-center px-6 border-b border-default-100 gap-2">
      <IconChat />
      <div className="flex-1 flex items-center gap-2 overflow-hidden">
        <span className="truncate">{ pgStore.playground?.desc }</span>
        {enableStepByStep && (
          <span className="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full">
            分步执行
          </span>
        )}
        {chatPolling.isPolling && (
          <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full animate-pulse">
            执行中
          </span>
        )}
      </div>

      {shouldShowModel && (
        <div className="flex items-center gap-1">
          <IconAndroid />
          <span className="text-sm">{pgStore.playground?.model}</span>
        </div>
      )}
    </header>
  );
};

export default Header;
