import { addToast } from '@heroui/react';
import { useEffect, useRef, useState, FC } from 'react';
import { useParams } from 'react-router-dom';
import Message, { ERuleType, IMessage } from './components/Message';
import useScanStyle from './hooks/useScanStyle';
import { getChat } from '@/apis';
import { getAsyncImgToCodeTaskByChatId, getAsyncImgToCodeStatus, ETaskStatus } from '@/apis';

interface IAsyncImgToCodeProps {
  loadChat: () => Promise<void>;
}

enum ESplitImgTaskNodeType {
  IMG_TO_CODE = 'img-to-code', // 图转码(html)
  SPLIT_IMG_TO_CODE = 'split-img-to-code', // 拆的单图转码(html)
}

const AsyncImgToCode: FC<IAsyncImgToCodeProps> = (props) => {
  const { loadChat } = props;
  const { id } = useParams();
  const asyncImgToCodeTaskRef = useRef<number>();
  const chatRef = useRef<number>();
  const [taskId, setTaskId] = useState<string>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [messageList, setMessageList] = useState<IMessage[]>([]);

  // 添加扫描线动画的CSS
  useScanStyle();

  const intervalTime = 5000;

  const startGetChatInterval = async () => {
    if (!id) {
      return;
    }
    const messages = await getChat({ id, sort: 'asc' });
    const status = messages[0]?.status;

    if (status === 'completed') {
      clearInterval(chatRef.current);
      loadChat();
    }
    setIsProcessing(false);
  };

  const getImgToCodeQuery = (node: any) => {
    const { id, metadata } = node;
    const itemsMeta = metadata.items?.[0]?.metadata;

    return {
      id,
      rule: ERuleType.QUERY,
      content: `请对以下图片实现图转码`,
      imgList: [itemsMeta.imageContent],
    };
  };

  const getSplitImgToCodeQuery = (node: any) => {
    const { id, metadata } = node;
    const itemsMeta = metadata.items?.[0]?.metadata;
    const coordinatesText = itemsMeta?.coordinates
      ?.map((item: any) => {
        const { x1, y1, x2, y2 } = item;

        return `    [${x1}, ${y1}, ${x2}, ${y2}]`;
      })
      .join('\n');

    return {
      id,
      rule: ERuleType.QUERY,
      content: `根据下列组件坐标信息生成html代码：\n${coordinatesText}`,
      imgList: metadata?.imgSplitItems?.map((item: any) => {
        return item.metadata.imageUrl;
      }),
    };
  };

  const handleNodeList = (nodeList: any) => {
    const len = nodeList?.length;
    const newMessageList: IMessage[] = [];

    for (let i = 0; i < len; i++) {
      const node = nodeList[i];
      const { status, id, type, result } = node;

      if (status === ETaskStatus.COMPLETED) {
        if (type === ESplitImgTaskNodeType.IMG_TO_CODE) {
          const file = result?.files?.[0];

          newMessageList.unshift(getImgToCodeQuery(node));

          newMessageList.unshift({
            id,
            rule: ERuleType.ANSWER,
            content: `根据图片生成的html内容`,
            htmlList: [
              {
                name: file?.name,
                content: file?.content,
              },
            ],
          });
        } else if (type === ESplitImgTaskNodeType.SPLIT_IMG_TO_CODE) {
          newMessageList.unshift(getSplitImgToCodeQuery(node));
          newMessageList.unshift({
            id,
            rule: ERuleType.ANSWER,
            content: `根据组件坐标生成的html代码`,
            htmlList: result?.files.map((item: any) => {
              return {
                name: item?.name,
                content: item?.content,
              };
            }),
          });
        }
      } else if (status === ETaskStatus.PROCESSING || status === ETaskStatus.PENDING) {
        if (type === ESplitImgTaskNodeType.IMG_TO_CODE) {
          newMessageList.unshift(getImgToCodeQuery(node));
        } else if (type === ESplitImgTaskNodeType.SPLIT_IMG_TO_CODE) {
          newMessageList.unshift(getSplitImgToCodeQuery(node));
        }
        break; // 只显示第一个正在处理的消息
      }
    }

    setMessageList(newMessageList);
  };

  const handleAsyncImgToCodeTaskFail = () => {
    if (asyncImgToCodeTaskRef.current) {
      clearInterval(asyncImgToCodeTaskRef.current);
    }
    setIsProcessing(false);
    addToast({
      color: 'danger',
      title: '任务失败，请重试！',
    });
  };

  // 轮训图片转代码任务
  const startAsyncImgToCodeTaskInterval = async () => {
    if (!taskId) {
      return;
    }
    const params = {
      taskId,
    };
    const ret = await getAsyncImgToCodeStatus(params).catch(() => null);
    const res = ret?.data;
    const taskStatus = res?.status;
    const nodeList = res?.nodes?.[0] || [];
    const isTaskSettled = [ETaskStatus.FAILED, ETaskStatus.COMPLETED].includes(taskStatus);
    const isTaskCompleted = taskStatus === ETaskStatus.COMPLETED;
    const isTaskFailed = taskStatus === ETaskStatus.FAILED;

    // 处理节点列表
    handleNodeList(nodeList);

    if (isTaskSettled) {
      clearInterval(asyncImgToCodeTaskRef.current);
    }
    if (isTaskCompleted) {
      if (res.error) {
        handleAsyncImgToCodeTaskFail();

        return;
      }
      // 开启轮询chat
      chatRef.current = setInterval(startGetChatInterval, intervalTime);
    }
    if (isTaskFailed) {
      handleAsyncImgToCodeTaskFail();
    }
  };

  const fetchAsyncImgToCodeTaskByChatId = async () => {
    if (!id) {
      return;
    }
    const params = {
      chatId: id,
    };
    const res = await getAsyncImgToCodeTaskByChatId(params).catch(() => null);

    const asyncImgToCodeTaskRes = res[0];

    if (asyncImgToCodeTaskRes) {
      setTaskId(asyncImgToCodeTaskRes?.id);
    }
  };

  useEffect(() => {
    if (!taskId) {
      return;
    }

    setIsProcessing(true);
    startAsyncImgToCodeTaskInterval();
    // 开始轮询异步转码任务
    asyncImgToCodeTaskRef.current = setInterval(startAsyncImgToCodeTaskInterval, intervalTime);

    return () => {
      if (asyncImgToCodeTaskRef.current) {
        clearInterval(asyncImgToCodeTaskRef.current);
      }
    };
  }, [taskId]);

  useEffect(() => {
    fetchAsyncImgToCodeTaskByChatId();

    return () => {
      if (chatRef.current) {
        clearInterval(chatRef.current);
      }
    };
  }, []);

  // 如果没有任务ID或消息列表为空，不显示任何内容
  if (!taskId || messageList.length === 0) {
    return null;
  }

  return messageList.map((message, index) => {
    return <Message key={index} data={message} isProcessing={index === 0 && isProcessing} />;
  });
};

export default AsyncImgToCode;
