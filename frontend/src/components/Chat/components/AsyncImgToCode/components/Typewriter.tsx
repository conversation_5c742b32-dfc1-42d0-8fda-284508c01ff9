import { useEffect, useState, FC } from 'react';

// 打字效果组件
interface TypewriterProps {
  text: string;
  speed?: number;
  onComplete?: () => void;
}

const Typewriter: FC<TypewriterProps> = ({ text, speed = 100, onComplete }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  

  useEffect(() => {
    if (currentIndex < text.length) {
      const time = Math.floor(Math.random() * speed)

      const timer = setTimeout(() => {
        setDisplayText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, time);

      return () => clearTimeout(timer);
    } else if (currentIndex === text.length && onComplete) {
      // 打字完成时调用回调
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  useEffect(() => {
    // 重置状态当文本改变时
    setDisplayText('');
    setCurrentIndex(0);
  }, [text]);

  return <span>{displayText}</span>;
};

export default Typewriter;
