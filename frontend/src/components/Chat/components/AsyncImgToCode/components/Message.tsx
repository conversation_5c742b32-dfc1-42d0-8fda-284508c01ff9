import { Button } from '@heroui/button';
import copyContent from '@/utils/copyContent';
import { FC, useEffect, useState } from 'react';
import ProcessingImage from './ProcessingImage';
import Typewriter from './Typewriter';
import IconCopy from '~icons/mdi/content-copy';
import IconDocument from '~icons/mdi/file-document-outline';

export enum ERuleType {
  QUERY = 'query',
  ANSWER = 'answer',
}

export interface IMessage {
  id: string;
  rule: ERuleType;
  content: string;
  imgList?: string[];
  htmlList?: {
    name: string;
    content: string;
  }[];
}

interface IMessageProps {
  data: IMessage;
  isProcessing: boolean;
}
const Message: FC<IMessageProps> = (props) => {
  const { data, isProcessing } = props;
  const { rule, content, imgList, htmlList } = data;
  const [tokens, setTokens] = useState<number>();
  const [typewriterCompleted, setTypewriterCompleted] = useState(false);

  const isQuery = rule === ERuleType.QUERY;

  const handleCopy = async () => {
    copyContent(content);
  };

  const randomTokens = () => {
    return Math.floor(Math.random() * 5000 + 5000);
  };

  const downloadFile = (html: { name: string; content: string }) => {
    const blob = new Blob([html.content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');

    a.href = url;
    a.download = html.name;
    a.click();
    URL.revokeObjectURL(url); // 释放URL对象
  };

  useEffect(() => {
    setTokens(randomTokens());
  }, []);

  // 当处理状态改变时，重置 typewriter 完成状态
  useEffect(() => {
    if (isProcessing) {
      setTypewriterCompleted(false);
    } else {
      setTypewriterCompleted(true);
    }
  }, [isProcessing]);

  return (
    <div>
      <div
        className={`p-3 mb-[4px] ${isQuery ? 'bg-[#ececec]  text-black dark:bg-gray-800 dark:text-white border border-[#ececec] dark:border-gray-700 rounded-[16px]' : 'bg-content1 dark:text-white border rounded-large'} `}
      >
        <div className="flex flex-col gap-2  dark:bg-gray-800 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="text-sm  text-gray-800 dark:text-gray-200 flex-1 whitespace-break-spaces">
              {isQuery && isProcessing ? (
                <Typewriter speed={100} text={content} onComplete={() => setTypewriterCompleted(true)} />
              ) : (
                content
              )}
            </div>
          </div>

          {imgList && (
            <div className="flex">
              {imgList.map((imgSrc) => {
                // 只有在非处理状态，或者处理状态但Typewriter已完成时才显示图片
                const shouldShowImage = !isProcessing || (isProcessing && typewriterCompleted);

                return shouldShowImage ? (
                  <ProcessingImage key={imgSrc} isProcessing={isProcessing} src={imgSrc} />
                ) : null;
              })}
            </div>
          )}

          {htmlList?.length &&
            htmlList.map((html) => {
              return (
                <div
                  className="flex items-center gap-1 p-2 cursor-pointer border rounded border-zinc-100 hover:bg-[#0000000d] dark:hover:bg-[#ffffff0d]"
                  onClick={() => downloadFile(html)}
                >
                  <IconDocument />
                  {html.name}
                </div>
              );
            })}
        </div>
      </div>
      <div className={`flex ${isQuery ? 'justify-end' : 'justify-start'}`}>
        <div className="flex  items-center">
          <Button key="copy" isIconOnly size="sm" title="复制" variant="light" onPress={handleCopy}>
            <IconCopy height={14} width={14} />
          </Button>
          {isQuery ? null : (
            <span key="token-count" className="text-xs text-zinc-400 dark:text-zinc-600" title="Token Count">
              {tokens} tokens
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default Message;
