import { FC, useState, useEffect } from 'react';

// 图片处理动效组件
interface ProcessingImageProps {
  src: string;
  isProcessing: boolean;
  className?: string;
}

const ProcessingImage: FC<ProcessingImageProps> = ({ src, className = '', isProcessing }) => {
  const [blurLevel, setBlurLevel] = useState(0);

  useEffect(() => {
    if (isProcessing) {
      // 处理中：创建从模糊到清晰的循环动画
      const startTime = Date.now();
      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const cycle = (elapsed % 4000) / 4000; // 4秒循环，0-1之间

        // 创建一个波浪形的模糊效果：模糊 -> 清晰 -> 模糊 -> 清晰
        const blurValue = Math.abs(Math.sin(cycle * Math.PI * 2)) * 6; // 0-6px的模糊范围

        setBlurLevel(blurValue);
      }, 50); // 每50ms更新一次，创建平滑动画

      return () => clearInterval(interval);
    } else {
      // 处理完成：立即变清晰
      setBlurLevel(0);
    }
  }, [isProcessing]);

  return (
    <div className={`relative overflow-hidden rounded-lg ${className} mr-4`}>
      <img
        className="transition-all duration-300 scale-100"
        src={src}
        style={{
          filter: `blur(${blurLevel}px)`,
        }}
      />

      {/* 扫描线效果 - 使用灰色调 */}
      {isProcessing && (
        <div className="absolute inset-0 overflow-hidden">
          <div
            className="absolute top-0 w-0.5 h-full bg-gradient-to-b from-transparent via-gray-400 to-transparent opacity-70 shadow-sm shadow-gray-400/30"
            style={{
              animation: 'scan 4s ease-in-out infinite',
              left: '0%',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ProcessingImage;
