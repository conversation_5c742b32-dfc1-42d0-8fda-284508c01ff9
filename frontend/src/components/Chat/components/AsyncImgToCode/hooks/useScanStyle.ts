import { useEffect } from 'react';
// 添加扫描线动画的CSS
const useScanStyle = () => {
  useEffect(() => {
    const style = document.createElement('style');

    style.textContent = `
      @keyframes scan {
        0% {
          left: -2px;
          opacity: 0;
        }
        5% {
          opacity: 1;
        }
        45% {
          left: 100%;
          opacity: 1;
        }
        50% {
          left: 100%;
          opacity: 0;
        }
        55% {
          opacity: 1;
        }
        95% {
          left: -2px;
          opacity: 1;
        }
        100% {
          left: -2px;
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);
};

export default useScanStyle;
