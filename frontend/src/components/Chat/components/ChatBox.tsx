import { ChatRequestOptions } from '@ai-sdk/ui-utils';
import { Button } from '@heroui/button';
import { Textarea } from '@heroui/input';
import { Image } from '@heroui/react';
import { Tooltip } from '@heroui/tooltip';
import { AnimatePresence, motion } from 'framer-motion';
import { useCallback, FC, useContext, useMemo } from 'react';
import { useStore } from 'zustand';
import { isVisionEnabled } from '@/config/models';
import { useFileUpload } from '@/hooks/fileUpload';
import { useHtmlOptimizationIterator } from '@/hooks/useHtmlOptimizationIterator';
import { useHtmlStepByStepExecutor } from '@/hooks/useHtmlStepByStepExecutor';
import { PlaygroundContext } from '@/stores/playground';
import { eventBus } from '@/utils/eventBus';
import IconAttachment from '~icons/mdi/attachment';
import IconClose from '~icons/mdi/close';
import IconStop from '~icons/mdi/stop';

interface IChatBoxProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => void;
  enableStepByStep: boolean;
  htmlIterator: ReturnType<typeof useHtmlOptimizationIterator>;
  stepByStepExecutor: ReturnType<typeof useHtmlStepByStepExecutor>;
  status: 'submitted' | 'streaming' | 'ready' | 'error';
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  setUserInitiatedChat: (value: boolean) => void;
}

const ChatBox: FC<IChatBoxProps> = (props) => {
  const {
    input,
    handleInputChange,
    enableStepByStep,
    htmlIterator,
    stepByStepExecutor,
    status,
    handleSubmit,
    setUserInitiatedChat,
  } = props;
  const store = useContext(PlaygroundContext)!;
  const pgStore = useStore(store);
  const { iptRef, onFileChange, files, removeFile, clearFiles } = useFileUpload();

  // 优化handleSend函数
  const handleSend = useCallback(() => {
    if (status === 'streaming') {
      stop();
    } else {
      // 用户手动发送消息时，标记为用户主动发起的对话，重置轮询逻辑
      if (input.trim()) {
        console.log('🔄 [Chat] 用户主动发送消息，重置轮询状态');
        setUserInitiatedChat(true);
      }

      // 用户手动发送消息时，重置迭代器状态（仅在非分步执行模式下）
      if (pgStore.playground?.type === 'html' && input.trim() && !enableStepByStep) {
        htmlIterator.resetIteration();
      }

      // 如果启用了分步执行，在发送新消息前重置执行状态
      if (enableStepByStep && input.trim()) {
        stepByStepExecutor.resetExecution();
        // 清除已解析消息记录，允许重新解析新消息
        // 注意：这里无法直接访问attemptedParseMessageIdsRef，但resetExecution会处理状态重置
      }

      handleSubmit(
        undefined,
        files
          ? {
              experimental_attachments: files,
            }
          : undefined,
      );
      clearFiles();
    }
  }, [
    status,
    stop,
    pgStore.playground?.type,
    input,
    enableStepByStep,
    htmlIterator,
    stepByStepExecutor,
    handleSubmit,
    files,
    clearFiles,
  ]);

  // 使用useMemo优化文件相关组件
  const { filesSection, fileInput } = useMemo(() => {
    let filesSection, fileInput;

    if (isVisionEnabled(pgStore.playground?.model ?? '')) {
      fileInput = (
        <>
          <input
            ref={iptRef}
            accept=".html,image/*"
            className="hidden"
            multiple={false}
            type="file"
            onChange={onFileChange}
          />
          <Tooltip showArrow content="上传图片">
            <Button
              isIconOnly
              size="sm"
              variant="flat"
              onPress={() => {
                iptRef.current?.click();
              }}
            >
              <IconAttachment />
            </Button>
          </Tooltip>
        </>
      );
      if (files.length) {
        filesSection = (
          <motion.section
            animate={{ height: 'auto' }}
            className="flex items-center flex-wrap gap-2 mt-2 overflow-hidden"
            exit={{ height: 0 }}
            initial={{ height: 0 }}
          >
            {files.map((e, i) => {
              return (
                <div key={i} className="relative w-[100px] h-[100px] flex items-center border rounded-lg p-1">
                  <Image
                    height={100}
                    src={e.url}
                    width={100}
                    onClick={() => {
                      eventBus.emit('show-lightbox', [{ src: e.url }]);
                    }}
                  />
                  <Button
                    isIconOnly
                    className="absolute z-10 top-0 right-0 scale-75 origin-top-right"
                    color="danger"
                    radius="full"
                    size="sm"
                    onPress={() => removeFile(e)}
                  >
                    <IconClose />
                  </Button>
                </div>
              );
            })}
          </motion.section>
        );
      }
    }

    return { filesSection, fileInput };
  }, [pgStore.playground?.model, iptRef, onFileChange, files, removeFile]);

  return (
    <div className="bg-white dark:bg-zinc-800 rounded-lg m-2 mt-0 border">
      <form
        className="flex flex-col gap-2 p-2"
        onSubmit={(e) => {
          e.preventDefault();
          handleSend();
        }}
      >
        <Textarea
          classNames={{
            inputWrapper: 'border-none shadow-none',
          }}
          minRows={1}
          placeholder="你还有什么想法？"
          value={input}
          variant="bordered"
          onChange={handleInputChange}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              handleSend();
            }
          }}
        />
        <AnimatePresence>{filesSection}</AnimatePresence>
        <footer className="flex items-center justify-end gap-2">
          {fileInput}
          <Button
            isIconOnly
            size="sm"
            type="submit"
            variant="light"
          >
            { status === 'streaming' ?
              <IconStop height={20} width={20} /> :
              <svg height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M2.01 21L23 12L2.01 3L2 10l15 2l-15 2z" fill="currentColor" /></svg>
            }
          </Button>
        </footer>
      </form>
    </div>
  );
};

export default ChatBox;
