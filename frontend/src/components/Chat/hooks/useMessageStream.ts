import { UIMessage } from '@ai-sdk/ui-utils';
import { useEffect, useRef, useCallback } from 'react';
import { baseUrl } from '@/apis';
import { useHtmlOptimizationIterator } from '@/hooks/useHtmlOptimizationIterator';
import { useHtmlStepByStepExecutor } from '@/hooks/useHtmlStepByStepExecutor';
import { eventBus } from '@/utils/eventBus';

function useMessageStream(
  messages: UIMessage[],
  status: 'submitted' | 'streaming' | 'ready' | 'error',
  htmlIterator: ReturnType<typeof useHtmlOptimizationIterator>,
  stepByStepExecutor: ReturnType<typeof useHtmlStepByStepExecutor>,
  enableStepByStep: boolean,
  id: string,
) {
  const lastMessage = messages[messages.length - 1];

  // 提取稳定的引用，避免依赖整个对象
  const initializeSteps = stepByStepExecutor.initializeSteps;
  const checkAndExecuteNextStep = stepByStepExecutor.checkAndExecuteNextStep;
  const resetExecution = stepByStepExecutor.resetExecution;
  const stepsLength = stepByStepExecutor.steps.length;
  const isExecuting = stepByStepExecutor.isExecuting;
  const isCompleted = stepByStepExecutor.isCompleted;

  const shouldStartIteration = htmlIterator.shouldStartIteration;
  const startIteration = htmlIterator.startIteration;
  const resetIteration = htmlIterator.resetIteration;

  // 使用ref来存储上一次的状态，避免不必要的重新执行
  const lastStatusRef = useRef<string>('');
  const lastMessageIdRef = useRef<string>('');
  const lastStepsLengthRef = useRef<number>(0);
  const incompleteCheckTimeoutRef = useRef<number | null>(null);

  // 检测不完整输出的函数
  const checkIncompleteOutput = useCallback(
    async (message: UIMessage, finishReason?: string, usage?: any) => {
      if (message.role !== 'assistant' || !message.content) return;

      try {
        // 优先检查AI响应中的官方标识
        // 如果finishReason存在且不是因为length限制，说明输出是完整的
        if (finishReason && finishReason !== 'length') {
          return; // 输出完整，无需检测
        }

        // 如果有usage信息且isContinued为false，说明输出完整
        if (usage && typeof usage.isContinued === 'boolean' && !usage.isContinued) {
          return; // 输出完整，无需检测
        }

        // 只有在明确的截断情况下才进行检测
        if (finishReason === 'length' || (usage && usage.isContinued === true)) {
          // 调用检测API
          const response = await fetch(`${baseUrl}${id}/@checkIncompleteOutput`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              content: message.content,
              messageId: message.id,
            }),
          });

          if (response.ok) {
            const result = await response.json();

            if (result.isIncomplete) {
              // 触发消息更新，添加不完整输出注解
              eventBus.emit('message::add-annotation', {
                messageId: message.id,
                annotation: {
                  type: 'incomplete-output-detected',
                  incompleteType: result.incompleteType,
                  incompleteContent: result.incompleteContent,
                  suggestedContinuePrompt: result.suggestedContinuePrompt,
                  canContinue: true,
                },
              });
            }
          }
        }
      } catch (error) {
        console.error('检测不完整输出失败:', error);
      }
    },
    [id],
  );

  useEffect(() => {
    try {
      // 创建当前状态的唯一标识
      const currentStatusId = `${status}-${lastMessage?.id}-${stepsLength}-${isExecuting}-${isCompleted}`;

      // 如果状态没有实际变化，跳过执行
      if (lastStatusRef.current === currentStatusId) {
        return;
      }
      lastStatusRef.current = currentStatusId;

      // 检查是否有all-files-saved注解，无论是在streaming还是ready状态
      if (lastMessage?.role === 'assistant') {
        const allSavedAnnotation = lastMessage.annotations?.find((e: any) => e?.type === 'all-files-saved') as any;

        if (allSavedAnnotation) {
          console.log('🔄 [Chat] 检测到all-files-saved注解，触发版本更新:', {
            versionNumber: allSavedAnnotation.versionNumber,
            status,
            messageId: lastMessage.id,
            timestamp: new Date().toISOString(),
          });
          eventBus.emit('artifact::new', allSavedAnnotation.versionNumber);
        }
      }

      // 新增：当AI完全停止响应后，延迟检测不完整输出
      if (status === 'ready' && lastMessage?.role === 'assistant') {
        // 清除之前的检测计时器
        if (incompleteCheckTimeoutRef.current) {
          clearTimeout(incompleteCheckTimeoutRef.current);
        }

        // 延迟2秒后进行检测，确保AI已完全停止响应
        incompleteCheckTimeoutRef.current = setTimeout(() => {
          // 从消息注解中获取token使用信息
          const tokenAnnotation = lastMessage.annotations?.find(
            (annotation: any) => annotation?.type === 'token-count',
          ) as any;

          // 检查是否有明确的完成状态信息
          const hasCompleteIndicator =
            tokenAnnotation &&
            (tokenAnnotation.isContinued === false ||
              (tokenAnnotation.finishReason && tokenAnnotation.finishReason !== 'length'));

          if (hasCompleteIndicator) {
            return; // 输出完整，跳过检测
          }

          // 只有在没有明确完成标识时才进行检测
          checkIncompleteOutput(lastMessage, tokenAnnotation?.finishReason, tokenAnnotation);
        }, 2000);
      }

      // 分步执行器逻辑
      if (enableStepByStep) {
        // 优先检查window对象中是否有预设步骤（来自HtmlAgent页面）
        if (stepsLength === 0 && window.__remainingSteps && window.__remainingSteps.length > 0) {
          console.log('🔍 发现window对象中的预设步骤，立即初始化');
          // 使用第一条用户消息来初始化步骤
          const firstUserMessage = messages.find((msg) => msg.role === 'user');

          if (firstUserMessage) {
            initializeSteps(firstUserMessage.content, firstUserMessage.id, []);
          }
        }
        // 初始化步骤（如果是用户的初始消息且步骤未初始化）
        else if (lastMessage?.role === 'user' && stepsLength === 0) {
          console.log('🔍 尝试解析用户消息步骤:', lastMessage.id);
          // 传递消息的附件信息
          const attachments = (lastMessage as any).experimental_attachments || [];

          initializeSteps(lastMessage.content, lastMessage.id, attachments);
        }

        // 如果步骤还没有初始化，尝试从历史消息中查找包含步骤的消息
        if (stepsLength === 0 && messages.length > 0) {
          console.log('🔍 步骤未初始化，检查历史消息中的附件...');

          // 遍历所有用户消息，查找包含步骤规划文件的消息
          for (let i = messages.length - 1; i >= 0; i--) {
            const msg = messages[i];

            if (msg.role === 'user') {
              const attachments = (msg as any).experimental_attachments || [];

              // 检查是否有步骤规划文件
              const hasStepsFile = attachments.some(
                (att: any) =>
                  att.name &&
                  (att.name.includes('step') ||
                    att.name.includes('步骤') ||
                    att.name.includes('plan') ||
                    att.name.includes('规划') ||
                    att.name.endsWith('.md') ||
                    att.name.endsWith('.txt')),
              );

              if (hasStepsFile) {
                console.log(
                  '🔍 发现包含步骤文件的历史消息:',
                  msg.id,
                  attachments.map((a: any) => a.name),
                );
                initializeSteps(msg.content, msg.id, attachments);
                break; // 找到后立即退出循环
              }
            }
          }
        }

        // 检查是否需要执行下一步（避免在执行中重复调用）
        if (!isExecuting || status === 'ready') {
          checkAndExecuteNextStep(messages, status);
        }
      } else {
        // 如果分步执行被禁用，清除已解析消息记录
        if (lastStepsLengthRef.current > 0) {
          lastStepsLengthRef.current = 0;
        }

        // 原有的HTML优化自迭代逻辑
        if (shouldStartIteration(messages, status)) {
          startIteration();
        }
      }
    } catch (error) {
      console.error('❌ useMessageStream 执行出错:', error);

      // 只有在严重错误时才重置状态，避免因为轻微错误就清空步骤条
      const isCriticalError =
        error instanceof Error &&
        (error.message.includes('Maximum update depth exceeded') ||
          error.message.includes('Cannot read properties of undefined') ||
          error.message.includes('Failed to fetch'));

      if (isCriticalError) {
        console.warn('⚠️ 检测到严重错误，重置执行状态');
        if (enableStepByStep) {
          resetExecution();
        } else {
          resetIteration();
        }
      } else {
        // 轻微错误只记录日志，不重置状态
        console.warn('⚠️ 检测到轻微错误，继续保持当前状态');
      }
    }

    // 清理函数
    return () => {
      if (incompleteCheckTimeoutRef.current) {
        clearTimeout(incompleteCheckTimeoutRef.current);
      }
    };
  }, [
    // 核心状态变化
    status,
    lastMessage?.id,
    lastMessage?.role,
    enableStepByStep,

    // 分步执行器状态
    stepsLength,
    isExecuting,
    isCompleted,

    // 稳定的函数引用
    initializeSteps,
    checkAndExecuteNextStep,
    resetExecution,
    shouldStartIteration,
    startIteration,
    resetIteration,
    checkIncompleteOutput,
  ]);
}

export default useMessageStream;
