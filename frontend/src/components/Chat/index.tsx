import { useChat } from '@ai-sdk/react';
import { But<PERSON> } from '@heroui/button';
import { Card, CardBody } from '@heroui/card';
import { useContext, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStore } from 'zustand';
import { ChatMessage } from '../Message/ChatMessage';
import { IterationStatusIndicator } from '../Message/IterationStatusIndicator';
import { MaxIterationReachedAlert } from '../Message/MaxIterationReachedAlert';
import { StepByStepIndicator } from '../Message/StepByStepIndicator';
import AsyncImgToCode from './components/AsyncImgToCode';
import ChatBox from './components/ChatBox';
import Header from './components/Header';
import useMessageStream from './hooks/useMessageStream';
import { baseUrl, deleteMessage, deletePlayground, editMessage } from '@/apis';
import { getChat } from '@/apis';
import { useApiKeyCheck } from '@/hooks/useApiKeyCheck';
import { useChatPolling } from '@/hooks/useChatPolling';
import { useHtmlOptimizationIterator } from '@/hooks/useHtmlOptimizationIterator';
import { useHtmlStepByStepExecutor } from '@/hooks/useHtmlStepByStepExecutor';
import { ChatContext } from '@/stores/chat';
import { PlaygroundContext } from '@/stores/playground';
import { eventBus, type AppendChatMessage } from '@/utils/eventBus';
import { confirm } from '@/utils/modal';

export function Chat({ id }: { id: string }) {
  const store = useContext(PlaygroundContext)!;
  const pgStore = useStore(store);
  const [enableAutoIteration, setEnableAutoIteration] = useState(false);
  const [enableStepByStep, setEnableStepByStep] = useState(false);

  // 用于标记是否是用户主动发起的对话（重置轮询逻辑）
  const [userInitiatedChat, setUserInitiatedChat] = useState(false);
    // 新增：用于检测SSE连接状态和智能切换轮询模式
  const [sseConnected, setSseConnected] = useState(false);
  const [hasDetectedSSEStream, setHasDetectedSSEStream] = useState(false);
  
  // 检测是否为项目预览模式
  const isProjectPreview = pgStore.playground?.tags?.includes('project-preview') || false;
  

  const { messages, error, input, reload, handleInputChange, handleSubmit, append, stop, status, setMessages } =
    useChat({
      id,
      api: `${baseUrl}${id}/@chat`,
      experimental_prepareRequestBody({ messages, id }) {
        console.log('🔧 [useChat] 准备请求体:', {
          messagesCount: messages.length,
          lastMessage: messages[messages.length - 1],
          id,
          apiUrl: `${baseUrl}${id}/@chat`,
        });

        // 获取HTML Agent设置，优先从window.__initialMessage获取，其次从状态获取
        let currentEnableAutoIteration = enableAutoIteration;
        let currentEnableStepByStep = enableStepByStep;

        // 如果是初始消息，从window.__initialMessage获取设置
        if (window.__initialMessage) {
          const { enableAutoIteration: autoIterationSetting, enableStepByStep: stepByStepSetting } =
            window.__initialMessage;

          if (typeof autoIterationSetting === 'boolean') {
            currentEnableAutoIteration = autoIterationSetting;
          }
          if (typeof stepByStepSetting === 'boolean') {
            currentEnableStepByStep = stepByStepSetting;
          }
        }

        const requestBody = {
          message: messages[messages.length - 1],
          id,
          // 传递HTML Agent设置到后端
          enableAutoIteration: currentEnableAutoIteration,
          enableStepByStep: currentEnableStepByStep,
        };

        console.log('📤 [useChat] 发送请求体:', requestBody);

        return requestBody;
      },
      onError: (error) => {
        console.error('❌ [useChat] 请求错误:', error);
      },
      onResponse: (response) => {
        console.log('📥 [useChat] 收到响应:', {
          status: response.status,
          headers: Object.fromEntries(response.headers.entries()),
          url: response.url,
        });
      },
      onFinish: (message) => {
        console.log('✅ [useChat] 响应完成:', message);
      },
    });
  const navigate = useNavigate();

    
  // 监听useChat的status状态来检测SSE连接
  useEffect(() => {
    console.log('🔍 [Chat] SSE状态检测:', { status, sseConnected, hasDetectedSSEStream, userInitiatedChat });
    
    // 检测SSE流开始
    if (status === 'streaming') {
      setSseConnected(true);
      setHasDetectedSSEStream(true);
      console.log('📡 [Chat] 检测到SSE流开始');
    }
    
    // 检测SSE流结束
    if (status === 'ready' && sseConnected) {
      setSseConnected(false);
      console.log('📡 [Chat] 检测到SSE流结束');
    }
    
    // 如果SSE流异常断开（error状态），也标记为断开
    if (status === 'error' && sseConnected) {
      setSseConnected(false);
      console.log('📡 [Chat] 检测到SSE流异常断开');
    }
  }, [status, sseConnected, hasDetectedSSEStream, userInitiatedChat]);
  

    // 智能决定是否启用轮询：
  // 1. 非用户主动发起的对话（后台任务等）
  // 2. 或者SSE连接断开但可能有正在进行的AI对话
  // 使用useRef来记录轮询状态，避免频繁重新计算
  const pollingStateRef = useRef<boolean>(false);
  const lastPollingCheckRef = useRef<{
    userInitiatedChat: boolean;
    sseConnected: boolean;
    hasDetectedSSEStream: boolean;
    messagesLength: number;
  } | null>(null);
  
  const shouldEnablePolling = useMemo(() => {
    // 检查是否需要重新计算
    const currentCheck = {
      userInitiatedChat,
      sseConnected,
      hasDetectedSSEStream,
      messagesLength: messages.length
    };
    
    // 如果关键状态没有变化，直接返回之前的结果
    if (lastPollingCheckRef.current && 
        lastPollingCheckRef.current.userInitiatedChat === currentCheck.userInitiatedChat &&
        lastPollingCheckRef.current.sseConnected === currentCheck.sseConnected &&
        lastPollingCheckRef.current.hasDetectedSSEStream === currentCheck.hasDetectedSSEStream &&
        Math.abs(lastPollingCheckRef.current.messagesLength - currentCheck.messagesLength) < 5) {
      return pollingStateRef.current;
    }
    
    lastPollingCheckRef.current = currentCheck;
    
    // 情况1：非用户主动发起的对话，一直启用轮询
    if (!userInitiatedChat) {
      pollingStateRef.current = true;

      return true;
    }
    
    // 情况2：用户主动发起的对话，但SSE连接断开且有过SSE流历史
    // 这种情况通常发生在页面刷新后，SSE连接断开，但AI对话可能还在后台进行
    if (userInitiatedChat && !sseConnected && hasDetectedSSEStream) {
      // 检查是否有正在处理中的消息
      const hasProcessingMessages = messages.some((message: any) => 
        message.role === 'assistant' && 
        (message.status === 'processing' || message.status === 'pending')
      );
      
      if (hasProcessingMessages) {
        pollingStateRef.current = true;

        return true;
      }
    }
    
    // 其他情况不启用轮询（用户主动发起且SSE正常工作）
    pollingStateRef.current = false;

    return false;
  }, [userInitiatedChat, sseConnected, hasDetectedSSEStream, messages.length]);
  

  // 集成聊天轮询hook
  const chatPolling = useChatPolling({
    playgroundId: id,
    messages,
    setMessages,
      enabled: shouldEnablePolling, // 使用智能判断的轮询开关
    pollInterval: 10000, // 10秒轮询间隔
  });

  // API Key 和余额检查
  useApiKeyCheck(pgStore.playground?.model || '');

  // HTML优化自迭代检查hook
  const htmlIterator = useHtmlOptimizationIterator({
    maxIterations: 2,
    playgroundType: pgStore.playground?.type || '',
    isEnabled: enableAutoIteration && !enableStepByStep,
    onAppendMessage: (message: string) => {
      append({
        role: 'user',
        content: message,
      });
    },
  });

  // HTML分步执行器hook
  const stepByStepExecutor = useHtmlStepByStepExecutor({
    isEnabled: enableStepByStep,
    playgroundId: id,
    onAppendMessage: (message: string) => {
      append({
        role: 'user',
        content: message,
      });
    },
  });

  const loadChat = async () => {
    const messages = await getChat({ id, sort: 'asc' });

    setMessages(messages);
  };

  // 使用ref存储最新的函数引用，避免闭包问题
  const appendRef = useRef(append);
  const setMessagesRef = useRef(setMessages);

  // 更新ref中的函数引用
  useEffect(() => {
    appendRef.current = append;
    setMessagesRef.current = setMessages;
  }, [append, setMessages]);

  const handleAddAnnotation = useCallback(({ messageId, annotation }: { messageId: string; annotation: any }) => {
    setMessagesRef.current((prevMessages: any[]) =>
      prevMessages.map((msg) =>
        msg.id === messageId
          ? {
              ...msg,
              annotations: [...(msg.annotations || []), annotation],
            }
          : msg,
      ),
    );
  }, []);

  // 优化初始化逻辑，确保只初始化一次，并使用防抖的 store 初始化
  const hasInitialized = useRef(false);
  const storeInitialized = useRef(false);

  // 独立的 store 初始化逻辑
  useEffect(() => {
    if (storeInitialized.current) return;
    storeInitialized.current = true;

    console.log('🚀 [Chat] 开始初始化 playground store');
    // 使用防抖的 store.start() 方法
    const initStore = async () => {
      try {
        await store.getState().start();
        console.log('✅ [Chat] playground store 初始化完成');
      } catch (error) {
        console.error('❌ [Chat] playground store 初始化失败:', error);
      }
    };

    initStore();
  }, [store]);

  // Chat 组件的初始化逻辑
  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;

    console.log('🚀 [Chat] 开始初始化 Chat 组件');

    const initialMsg = window.__initialMessage;

    if (initialMsg) {
      const {
        desc,
        files,
        enableAutoIteration: autoIterationSetting,
        enableStepByStep: stepByStepSetting,
      } = initialMsg;

      if (typeof autoIterationSetting === 'boolean') {
        setEnableAutoIteration(autoIterationSetting);
      }

      if (typeof stepByStepSetting === 'boolean') {
        setEnableStepByStep(stepByStepSetting);
      }

      append(
        {
          role: 'user',
          content: desc,
        },
        files
          ? {
              experimental_attachments: files,
            }
          : undefined,
      );
      // clean up, initial msg is used once
      delete window.__initialMessage;
    } else {
      loadChat();

      // 从playground数据恢复HTML Agent配置
      if (pgStore.playground) {
        const {
          enableAutoIteration: pgAutoIteration,
          enableStepByStep: pgStepByStep,
          stepByStepData,
        } = pgStore.playground;

        if (typeof pgAutoIteration === 'boolean') {
          setEnableAutoIteration(pgAutoIteration);
        }

        if (typeof pgStepByStep === 'boolean') {
          setEnableStepByStep(pgStepByStep);

          // 如果启用了分步执行且有保存的状态数据，恢复执行状态
          if (pgStepByStep && stepByStepData) {
            stepByStepExecutor.restoreState(stepByStepData);
          }
        }
      }
    }

    console.log('✅ [Chat] Chat 组件初始化完成');
  }, []); // 纯初始化逻辑，不包含事件监听器

  // 独立的事件监听器管理
  useEffect(() => {
    console.log('🔧 [Chat] 设置事件监听器');
    
    // 注册事件监听器 - 使用ref确保始终获取最新的函数引用
    const appendChat = (message: AppendChatMessage) => {
      // console.log('🚀 [Chat] 收到消息:', message);
      if (typeof message === 'string') {
        appendRef.current({
          role: 'user',
          content: message,
        });
      } else {
        appendRef.current({
          role: 'user',
          content: message.text,
          annotations: message.annotations,
        });
      }
    };

    // 注册 append-chat 事件，消息来源于从页面选择节点发送信息
    eventBus.on('append-chat', appendChat);
    eventBus.on('message::add-annotation', handleAddAnnotation);

    return () => {
      console.log('🧹 [Chat] 清理事件监听器');
      eventBus.off('append-chat', appendChat);
      eventBus.off('message::add-annotation', handleAddAnnotation);
    };
  }, [handleAddAnnotation]); // 依赖 handleAddAnnotation，确保事件监听器始终有效

  useEffect(() => {
    if (error) {
      console.error('Received error from backend:', error);
    }
  }, [error]);

  // 添加状态变化的调试信息
  useEffect(() => {
    console.log('🔄 [Chat] 状态变化:', {
      status,
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1],
      error: error?.message,
      userInitiatedChat,
      isPolling: chatPolling.isPolling,
      hasRunningTasks: chatPolling.hasRunningTasks,
    });
  }, [status, messages.length, error, userInitiatedChat, chatPolling.isPolling, chatPolling.hasRunningTasks]);

  useMessageStream(messages, status, htmlIterator, stepByStepExecutor, enableStepByStep, id);

  // 使用useMemo优化messageList渲染
  const messageList = useMemo(
    () => (
      <div className="flex-1 min-h-[400px] flex flex-col-reverse gap-10 overflow-auto px-12 py-4">
        {/* HTML优化迭代状态指示器 - 分步执行时不显示 */}
        {pgStore.playground?.type === 'html' && !enableStepByStep && (
          <IterationStatusIndicator
            isCancelled={htmlIterator.isCancelled}
            isGenerating={status === 'streaming' || status === 'submitted'}
            isIterating={htmlIterator.isIterating}
            iterationCount={htmlIterator.iterationCount}
            maxIterations={2}
            maxIterationsReached={htmlIterator.maxIterationsReached}
            onCancel={htmlIterator.cancelIteration}
            onResume={htmlIterator.resumeIteration}
          />
        )}

        {/* 分步执行状态指示器 */}
        {enableStepByStep && (
          <StepByStepIndicator
            state={stepByStepExecutor}
            onCancel={stepByStepExecutor.cancelExecution}
            onContinue={stepByStepExecutor.continueExecution}
            onStart={stepByStepExecutor.startExecution}
          />
        )}

        {/* 最大迭代次数提示 - 分步执行时不显示 */}
        {pgStore.playground?.type === 'html' &&
          !enableStepByStep &&
          htmlIterator.maxIterationsReached &&
          !htmlIterator.isCancelled && (
            <MaxIterationReachedAlert iterationCount={htmlIterator.iterationCount} maxIterations={2} />
          )}

        {error && (
          <Card>
            <CardBody>
              <h1 className="text-center font-bold">出错了！</h1>
              <p className="my-2">{error.message}</p>
              <Button color="danger" size="sm" onPress={() => reload()}>
                重新发送
              </Button>
            </CardBody>
          </Card>
        )}
        {/* 显示思考中状态 - SSE流式聊天 */}
        {status === 'submitted' && (
          <ChatMessage
            key="_thinking"
            isLoading
            message={{
              id: '_thinking',
              role: 'assistant',
              content: '思考中...',
            }}
          />
        )}
        {[...messages]
          .reverse()
          .map((message, i) => {
            const isAssistantMessage = message.role === 'assistant';
            const content = message.content?.trim() || '';
            const isEmpty = content === '';
            const messageStatus = (message as any).status;

            // 完全过滤掉已完成或失败状态的空消息
            if (isEmpty && isAssistantMessage && (messageStatus === 'completed' || messageStatus === 'failed')) {
              return null;
            }

            // 检测是否需要显示loading效果（处理中且为空的消息）
            const shouldShowLoading =
              isEmpty &&
              isAssistantMessage &&
              (messageStatus === 'processing' || messageStatus === 'pending') &&
              chatPolling.isPolling;

            // 检测当前消息是否正在streaming（最新的助手消息且SSE正在流式传输）
            const isCurrentlyStreaming = 
              message.role === 'assistant' && 
              i === 0 && // 是最新的消息（因为messages已经reverse了）
              status === 'streaming';

            return (
              <ChatMessage 
                key={message.id} 
                isLoading={shouldShowLoading} 
                isStreaming={isCurrentlyStreaming}
                message={message} 
              />
            );
          })
          .filter(Boolean)}
        {/* 异步转码信息，样式使用flex-col-reverse，这里需要reverse一下 */}
        <AsyncImgToCode loadChat={loadChat} />
      </div>
    ),
    [
      pgStore.playground?.type,
      enableStepByStep,
      htmlIterator.iterationCount,
      htmlIterator.isIterating,
      htmlIterator.maxIterationsReached,
      htmlIterator.isCancelled,
      htmlIterator.cancelIteration,
      htmlIterator.resumeIteration,
      stepByStepExecutor,
      status,
      error,
      reload,
      messages,
      chatPolling.isPolling,
    ],
  );

  // 使用useMemo优化ChatContext value
  const chatContextValue = useMemo(
    () => ({
      append,
      stop,
      reload,
      streamFile: (path: string, content: string) => {
        queueMicrotask(() => {
          const { openFiles, openFile, saveFile } = store.getState();

          if (!openFiles.includes(path)) {
            openFile(path);
          }
          saveFile(path, content, false);
        });
      },
      changeVersion: (version: number) => {
        queueMicrotask(() => {
          const { changeVersion } = store.getState();

          changeVersion(`version-${version}`);
        });
      },
      openFile: async (version: number, path: string) => {
        const openFile = () => {
          const { openFile } = store.getState();

          openFile(path);
          eventBus.emit('artifact::set-tab', 'editor');
        };

        if (typeof version === 'number' && pgStore.currentVersion !== `version-${version}`) {
          await pgStore.changeVersion(`version-${version}`);
          openFile();
        } else {
          queueMicrotask(openFile);
        }
      },
      async deleteMessage(messageId: string) {
        if (messageId === messages[0].id) {
          const yes = await confirm({
            title: '删除工作区',
            message: '删除这条消息将删除整个工作区。是否继续？',
          });

          if (yes) {
            await deletePlayground({ id });
            navigate('/');
          }
        } else {
          const yes = await confirm({
            title: '删除消息',
            message: '删除这条消息将删除所有后续消息。是否继续？',
          });

          if (yes) {
            const msg = messages.findIndex((e) => e.id === messageId);

            if (msg !== -1) {
              setMessages(messages.slice(0, msg));
            }
            await deleteMessage({ id, messageId });
            await pgStore.loadVersions();
            eventBus.emit('artifact::update');
          }
        }
      },
      async editMessage(messageId: string, content: string) {
        const msgIdx = messages.findIndex((e) => e.id === messageId);

        if (msgIdx === -1) {
          return;
        }
        await editMessage({
          id,
          messageId,
          content,
        });
        const msg = messages[msgIdx];

        if (msg.parts.length === 1 && msg.parts[0].type === 'text') {
          msg.parts[0].text = content;
        }
        setMessages(
          messages.slice(0, msgIdx).concat({
            ...msg,
            content,
          }),
        );
        reload();
      },
    }),
    [append, stop, reload, store, pgStore.currentVersion, messages, id, navigate, setMessages, pgStore.loadVersions],
  );

  useEffect(() => {
    // 清理函数
    return () => {
      // 清理全局调试函数
    };
  }, []);

  // 项目预览模式的特殊渲染
  if (isProjectPreview) {
    return (
      <ChatContext.Provider value={chatContextValue} />
    );
  }

  // 普通模式的标准渲染
  return (
    <ChatContext.Provider value={chatContextValue}>
      <div className="w-full flex-1 flex flex-col">
        <Header chatPolling={chatPolling} enableStepByStep={enableStepByStep} />
        {messageList}
        <ChatBox
          enableStepByStep={enableStepByStep}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          htmlIterator={htmlIterator}
          input={input}
          setUserInitiatedChat={setUserInitiatedChat}
          status={status}
          stepByStepExecutor={stepByStepExecutor}
        />
      </div>
    </ChatContext.Provider>
  );
}
