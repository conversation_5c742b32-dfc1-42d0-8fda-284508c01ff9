import type { ProcessInfo, ProcessStats } from "@/apis";
import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardHeader, CardBody } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from "@heroui/modal";
import { Spinner } from "@heroui/spinner";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@heroui/table";
import { Tooltip } from "@heroui/tooltip";
import React, { useState, useEffect } from "react";
import store from "store";
import {
  getUserProcesses,
  getRunningProcesses,
  killProcess as apiKillProcess,
  getProcessStats,
  getPlaygroundProcesses,
} from "@/apis";
import IconDelete from "~icons/mdi/delete";
import IconEye from "~icons/mdi/eye";
import IconReload from "~icons/mdi/reload";
// @ts-ignore

// 特权用户ID，可以查看所有进程
const ADMIN_USER_ID = "018465";

interface ProcessManagerProps {
  playgroundId?: string;
  creatorId?: string; // 聊天对话创建者ID
}

const ProcessManager: React.FC<ProcessManagerProps> = ({ playgroundId, creatorId }) => {
  const [processes, setProcesses] = useState<ProcessInfo[]>([]);
  const [stats, setStats] = useState<ProcessStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedProcess, setSelectedProcess] = useState<ProcessInfo | null>(null);
  const [outputModalVisible, setOutputModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);

  // 获取当前用户信息
  const currentUser = store.get("user") || {};
  const userId = currentUser.username || "";
  const isAdmin = userId === ADMIN_USER_ID;
  const isCreator = creatorId && userId === creatorId;
  
  // 检查用户是否有权限查看进程
  const hasPermission = isAdmin || isCreator || (playgroundId && isCreator);

  // 获取进程列表
  const fetchProcesses = async () => {
    if (!hasPermission) {
      setProcesses([]);

      return;
    }
    
    setLoading(true);
    try {
      let data: ProcessInfo[] = [];

      // 如果有指定的playgroundId，只获取该playground的进程
      if (playgroundId) {
        data = (await getPlaygroundProcesses({ playgroundId })) as ProcessInfo[];
      }
      // 管理员用户可以查看所有进程
      else if (isAdmin) {
        data = (await getRunningProcesses()) as ProcessInfo[];
      }
      // 普通用户只能查看自己的进程
      else {
        data = (await getUserProcesses({ userId })) as ProcessInfo[];
      }

      setProcesses(data);
    } catch (error) {
      console.error("获取进程列表失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 获取统计信息
  const fetchStats = async () => {
    if (!hasPermission) {
      setStats(null);

      return;
    }
    
    try {
      const data = (await getProcessStats()) as ProcessStats;

      setStats(data);
    } catch (error) {
      console.error("获取统计信息失败:", error);
    }
  };

  // 杀死进程
  const handleKillProcess = async (processId: string) => {
    if (!hasPermission) return;
    
    try {
      await apiKillProcess({ processId, reason: "manual" });
      console.log("进程已终止");
      fetchProcesses();
      fetchStats();
    } catch (error) {
      console.error("终止进程失败:", error);
    }
  };

  // 批量清理进程
  const cleanupProcesses = async () => {
    if (!hasPermission) return;
    
    try {
      const response = await fetch("/api/process-manager/cleanup", {
        method: "POST",
      });

      if (response.ok) {
        console.log("进程清理完成");
        fetchProcesses();
        fetchStats();
      } else {
        console.error("进程清理失败");
      }
    } catch (error) {
      console.error("进程清理失败:", error);
    }
    setConfirmModalVisible(false);
  };

  // 查看进程输出
  const viewOutput = (process: ProcessInfo) => {
    if (!hasPermission) return;
    
    setSelectedProcess(process);
    setOutputModalVisible(true);
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "primary";
      case "completed":
        return "success";
      case "failed":
        return "danger";
      case "killed":
        return "default";
      default:
        return "default";
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "running":
        return "运行中";
      case "completed":
        return "已完成";
      case "failed":
        return "失败";
      case "killed":
        return "已终止";
      default:
        return "未知";
    }
  };

  useEffect(() => {
    if (hasPermission) {
      fetchProcesses();
      fetchStats();

      // 定时刷新
      const interval = setInterval(() => {
        fetchProcesses();
        fetchStats();
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [hasPermission, playgroundId, userId]);

  // 如果没有权限，不显示进程管理器
  if (!hasPermission) {
    return (
      <div className="process-manager">
        <Card>
          <CardBody className="flex flex-col items-center justify-center py-10 text-center">
            <div className="text-gray-400 mb-4">
              <svg fill="none" height="48" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" width="48" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" />
                <line x1="8" x2="16" y1="12" y2="12" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">无权限查看</h3>
            <p className="text-sm text-gray-500">您没有权限查看此页面的进程信息</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="process-manager">
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div className="flex gap-2">
            <Button
              isIconOnly
              size="sm"
              variant="bordered"
              onPress={() => {
                fetchProcesses();
                fetchStats();
              }}
            >
              <IconReload height={16} width={16} />
            </Button>
            <Button
              isDisabled={!stats || stats.completed + stats.failed + stats.killed === 0}
              size="sm"
              variant="bordered"
              onPress={() => setConfirmModalVisible(true)}
            >
              清理已完成
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {/* 统计信息 */}
          {stats && (
            <div className="flex gap-4 mb-4">
              <Chip color="primary" variant="flat">
                运行中: {stats.running}
              </Chip>
              <Chip color="success" variant="flat">
                已完成: {stats.completed}
              </Chip>
              <Chip color="danger" variant="flat">
                失败: {stats.failed}
              </Chip>
              <Chip color="default" variant="flat">
                已终止: {stats.killed}
              </Chip>
              <Chip color="secondary" variant="flat">
                总计: {stats.total}
              </Chip>
            </div>
          )}

          {/* 进程列表 */}
          <Table aria-label="进程列表">
            <TableHeader>
              <TableColumn>进程ID</TableColumn>
              <TableColumn>命令</TableColumn>
              <TableColumn>状态</TableColumn>
              <TableColumn>PID</TableColumn>
              <TableColumn>用户</TableColumn>
              <TableColumn>开始时间</TableColumn>
              <TableColumn>页面</TableColumn>
              <TableColumn>操作</TableColumn>
            </TableHeader>
            <TableBody
              emptyContent="暂无进程"
              isLoading={loading}
              items={processes}
              loadingContent={<Spinner label="加载中..." />}
            >
              {(process) => (
                <TableRow key={process.id}>
                  <TableCell>
                    <Tooltip content={process.id}>
                      <span className="text-xs">{process.id.substring(0, 8)}...</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Tooltip content={`${process.command} ${process.args.join(" ")}`}>
                      <span>{process.command}</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Chip color={getStatusColor(process.status)} size="sm">
                      {getStatusText(process.status)}
                    </Chip>
                  </TableCell>
                  <TableCell>{process.pid || "-"}</TableCell>
                  <TableCell>{process.user}</TableCell>
                  <TableCell>{new Date(process.startTime).toLocaleString()}</TableCell>
                  <TableCell>{process.metadata?.pageName || "-"}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button isIconOnly size="sm" variant="light" onPress={() => viewOutput(process)}>
                        <IconEye height={16} width={16} />
                      </Button>
                      {process.status === "running" && (
                        <Button
                          isIconOnly
                          color="danger"
                          size="sm"
                          variant="light"
                          onPress={() => handleKillProcess(process.id)}
                        >
                          <IconDelete height={16} width={16} />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* 输出查看模态框 */}
      <Modal
        isOpen={outputModalVisible}
        scrollBehavior="inside"
        size="3xl"
        onClose={() => setOutputModalVisible(false)}
      >
        <ModalContent>
          <ModalHeader>进程输出 - {selectedProcess?.metadata?.pageName || selectedProcess?.command}</ModalHeader>
          <ModalBody>
            {selectedProcess && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>命令:</strong> {selectedProcess.command} {selectedProcess.args.join(" ")}
                  </div>
                  <div>
                    <strong>状态:</strong>{" "}
                    <Chip color={getStatusColor(selectedProcess.status)} size="sm">
                      {getStatusText(selectedProcess.status)}
                    </Chip>
                  </div>
                  <div>
                    <strong>PID:</strong> {selectedProcess.pid || "N/A"}
                  </div>
                  <div>
                    <strong>开始时间:</strong> {new Date(selectedProcess.startTime).toLocaleString()}
                  </div>
                  {selectedProcess.endTime && (
                    <div>
                      <strong>结束时间:</strong> {new Date(selectedProcess.endTime).toLocaleString()}
                    </div>
                  )}
                  {selectedProcess.exitCode !== undefined && (
                    <div>
                      <strong>退出码:</strong> {selectedProcess.exitCode}
                    </div>
                  )}
                </div>

                {selectedProcess.output && (
                  <div>
                    <h4 className="font-semibold mb-2">输出:</h4>
                    <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-sm max-h-60 overflow-auto">
                      {selectedProcess.output}
                    </pre>
                  </div>
                )}

                {selectedProcess.error && (
                  <div>
                    <h4 className="font-semibold mb-2">错误:</h4>
                    <pre className="bg-red-50 dark:bg-red-900/20 p-3 rounded text-sm text-red-600 dark:text-red-400 max-h-40 overflow-auto">
                      {selectedProcess.error}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 确认清理模态框 */}
      <Modal isOpen={confirmModalVisible} size="sm" onClose={() => setConfirmModalVisible(false)}>
        <ModalContent>
          <ModalHeader>确认清理</ModalHeader>
          <ModalBody>确定要清理所有已完成和失败的进程吗？</ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setConfirmModalVisible(false)}>
              取消
            </Button>
            <Button color="primary" onPress={cleanupProcesses}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ProcessManager;
