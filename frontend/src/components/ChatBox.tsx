import { But<PERSON>, Image, Textarea, Chip, Spinner, addToast, Tooltip } from '@heroui/react';
import clsx from 'clsx';
import { AnimatePresence, motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectDropdown } from './Project/Selector';
import { createPlayground, enhancePrompt } from '@/apis';
import { SplitImagePage } from '@/components/DesignToCode/SplitImagePage';
import { ModelDropdown } from '@/components/ModelSelector';
import { defaultModel } from '@/config/models';
import { useFileUpload } from '@/hooks/fileUpload';
import { useUserInfoStore } from '@/hooks/login';
import { useProjectStore } from '@/stores/project';
import { eventBus } from '@/utils/eventBus';
import IconAttachment from '~icons/mdi/attachment';
import IconClose from '~icons/mdi/close';
import IconCube from '~icons/mdi/cube-outline';
import IconStarShootingOutline from '~icons/mdi/star-shooting-outline';

export function ChatBox({
  className,
  examples,
  onCreate,
  showProjectSelector = true,
  projectId: controlledProjectId,
  onProjectChange,
}: {
  className?: string;
  examples?: { label: string; value: string }[];
  onCreate: (id: string) => void;
  showProjectSelector?: boolean;
  projectId?: string | null;
  onProjectChange?: (id: string | undefined) => void;
}) {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const { projects, current: globalCurrent, setCurrent: setGlobalCurrent } = useProjectStore();

  const isControlled = !!onProjectChange;
  const currentProjectId = isControlled ? controlledProjectId : globalCurrent;
  const handleProjectChange = isControlled ? onProjectChange : setGlobalCurrent;

  const [value, setValue] = useState('');
  const [model, setModel] = useState(() => localStorage.getItem('ai-coding-model') || defaultModel);
  const selectProject = projects.find((item) => item.id === currentProjectId);
  const [creating, setCreating] = useState(false);
  const [isPublic, setIsPublic] = useState(true);
  const [showSplitImagePage, setSplitImagePage] = useState(false);

  // Listen for breadcrumb project changes to ensure two-way data binding
  useEffect(() => {
    const handleBreadcrumbProjectChange = (event: CustomEvent) => {
      if (!isControlled) {
        const newProjectId = event.detail.projectId;

        if (handleProjectChange) {
          handleProjectChange(newProjectId);
        }
      }
    };

    window.addEventListener('chatProjectChanged', handleBreadcrumbProjectChange as EventListener);

    return () => {
      window.removeEventListener('chatProjectChanged', handleBreadcrumbProjectChange as EventListener);
    };
  }, [isControlled, handleProjectChange]);

  let exampleSection;

  if (examples) {
    exampleSection = (
      <section className="flex flex-wrap gap-2 mt-4">
        {examples.map((e) => (
          <Button
            key={e.value}
            className="bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700"
            size="sm"
            variant="light"
            onPress={() => setValue(e.value)}
          >
            {e.label}
          </Button>
        ))}
      </section>
    );
  }

  const { iptRef, onFileChange, files, removeFile } = useFileUpload(false);
  const image = files[0];
  let filesSection;

  if (files.length) {
    filesSection = (
      <motion.section
        animate={{ height: 'auto' }}
        className="flex items-center flex-wrap gap-2 mt-2 overflow-hidden"
        exit={{ height: 0 }}
        initial={{ height: 0 }}
      >
        {files.map((e, i) => {
          return (
            <div key={i} className="relative w-[100px] h-[100px] flex items-center border rounded-lg p-1">
              <Image
                height={100}
                src={e.url}
                width={100}
                onClick={() => {
                  eventBus.emit('show-lightbox', [{ src: e.url }]);
                }}
              />
              <Button
                isIconOnly
                className="absolute z-10 top-0 right-0 scale-75 origin-top-right"
                color="danger"
                radius="full"
                size="sm"
                onPress={() => removeFile(e)}
              >
                <IconClose />
              </Button>
            </div>
          );
        })}
      </motion.section>
    );
  }

  async function create(desc: string, model: string) {
    setCreating(true);

    try {
      const response = await createPlayground({
        model,
        desc,
        projectId: currentProjectId || undefined,
        user: userInfo?.username,
        isPublic: isPublic,
      });
      
      const id = typeof response === 'object' && 'id' in response ? response.id : response;

      window.__initialMessage = {
        desc,
        files,
      };
      onCreate(id as string);
    } catch (e) {
      setCreating(false);
      alert(e);
    }
  }

  const [enhancing, setEnhancing] = useState(false);

  const handleCloseSplitImagePage = () => {
    setSplitImagePage(false);
  };

  async function enhance() {
    if (!value) {
      addToast({
        title: '请输入内容',
        description: '输入内容后才能优化',
      });

      return;
    }
    setEnhancing(true);
    try {
      const response = await enhancePrompt({
        desc: value,
      });

      if (typeof response === 'string') {
        setValue(response);
      } else if (response.data) {
        setValue(response.data);
      }
    } catch (e) {
      addToast({
        title: '优化失败',
        description: e instanceof Error ? e.message : '优化失败',
      });
    } finally {
      setEnhancing(false);
    }
  }

  return (
    <form
      className={clsx('mx-auto w-full', className)}
      onSubmit={async (e) => {
        e.preventDefault();
        const text = value.trim();

        if (!text && !files.length) {
          addToast({
            title: '请输入你的需求',
            description: '越详细越容易达到好的效果',
          });

          return;
        }

        if (files.length) {
          setSplitImagePage(true);

          return;
        }

        let desc = text;

        if (files.length && !text) {
          desc = '设计图还原';
        }

        // if (files.length && !isVisionEnabled(model)) {
        //   const yes = await confirm({
        //     title: '是否切换模型?',
        //     message: '图片需要多模态大模型支持',
        //   });
        //   if (yes) {
        //     setModel(defaultVisionModel);
        //     create(desc, defaultVisionModel);
        //   }
        //   return;
        // }

        create(desc, model);
      }}
    >
      <div className="border border-gray-200 dark:border-zinc-700 rounded-lg p-2 pb-0 shadow-lg shadow-black/5">
        <Textarea
          className=""
          classNames={{
            inputWrapper: '!bg-transparent !border-none',
            input: 'text-base',
          }}
          labelPlacement="outside"
          minRows={4}
          placeholder="今天想要开发点什么？"
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
        <AnimatePresence>{filesSection}</AnimatePresence>
        <footer className="flex items-center w-full py-2 gap-1">
          <ModelDropdown
            model={model}
            onChange={(model) => {
              localStorage.setItem('ai-coding-model', model);
              setModel(model);
            }}
          />
          {showProjectSelector ? (
            <ProjectDropdown
              project={currentProjectId || undefined}
              onChange={(project) => {
                if (handleProjectChange) {
                  handleProjectChange(project || undefined);
                }
              }}
            />
          ) : (
            selectProject && (
              <Chip className="h-8" radius="sm" size="sm" variant="flat">
                <span className="flex items-center gap-1">
                  <IconCube />
                  {selectProject.name}
                </span>
              </Chip>
            )
          )}
          {/* <IsPublicDropdown isPublic={isPublic} onChange={setIsPublic} /> */}
          <div className="flex-1" />
          <Tooltip showArrow content={<p className="max-w-[400px] break-words">优化Prompt</p>}>
            <Button isIconOnly isLoading={enhancing} size="sm" variant="light" onPress={enhance}>
              <IconStarShootingOutline />
            </Button>
          </Tooltip>
          <input
            ref={iptRef}
            accept="image/*"
            className="hidden"
            multiple={false}
            type="file"
            onChange={onFileChange}
          />
          <Tooltip showArrow content="上传图片">
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={() => {
                iptRef.current?.click();
              }}
            >
              <IconAttachment />
            </Button>
          </Tooltip>

          {!creating ? (
            <Button isIconOnly type="submit" variant="light">
              <svg height="1em" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M2.01 21L23 12L2.01 3L2 10l15 2l-15 2z" fill="currentColor" />
              </svg>
            </Button>
          ) : (
            <div className="flex items-center w-[40px] h-[40px]">
              <Spinner variant="wave" />
            </div>
          )}
        </footer>
      </div>
      {exampleSection}
      {/* 图片拆分 */}
      {showSplitImagePage && image.imgHeight && image.imgWidth && (
        <SplitImagePage
          isAsync={true}
          model={model}
          nodeData={{
            imageContent: image.url,
            imgWidth: image.imgWidth,
            imgHeight: image.imgHeight,
          }}
          project={selectProject}
          onClose={handleCloseSplitImagePage}
        />
      )}
    </form>
  );
}
