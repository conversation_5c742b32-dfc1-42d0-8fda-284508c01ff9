import { SwitchProps, useSwitch } from "@heroui/switch";
import { VisuallyHidden } from "@react-aria/visually-hidden";
import clsx from "clsx";
import { FC, useState, useEffect } from "react";
import { useThemeStore } from "@/stores/theme";
import "@/assets/styles/toggles.css";

export interface ThemeSwitchProps {
  className?: string;
  classNames?: SwitchProps["classNames"];
}

export const ThemeSwitch: FC<ThemeSwitchProps> = ({
  className,
  classNames,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  const { theme, setTheme } = useThemeStore();

  const {
    Component,
    slots,
    isSelected,
    getBaseProps,
    getInputProps,
    getWrapperProps,
  } = useSwitch({
    isSelected: theme === "light",
    onChange: () => setTheme(theme === "light" ? "dark" : "light"),
  });

  useEffect(() => {
    setIsMounted(true);
  }, [isMounted]);

  // Prevent Hydration Mismatch
  if (!isMounted) return <div className="w-6 h-6" />;

  return (
    <Component
      aria-label={isSelected ? "Switch to dark mode" : "Switch to light mode"}
      {...getBaseProps({
        className: clsx(
          "px-px transition-opacity hover:opacity-80 cursor-pointer",
          className,
          classNames?.base,
        ),
      })}
    >
      <VisuallyHidden>
        <input {...getInputProps()} />
      </VisuallyHidden>
      <div
        {...getWrapperProps()}
        className={slots.wrapper({
          class: clsx(
            [
              "w-auto h-auto",
              "bg-transparent",
              "rounded-lg",
              "flex items-center justify-center",
              "group-data-[selected=true]:bg-transparent",
              "!text-default-500",
              "pt-px",
              "px-0",
              "mx-0",
            ],
            classNames?.wrapper,
            isSelected ? 'theme-toggle' : 'theme-toggle theme-toggle--toggled',
          ),
        })}
      >
        <svg
          aria-hidden="true"
          className={clsx(
            "theme-toggle__around",
            isSelected ? 'text-amber-500' : 'text-blue-500',
          )}
          fill="currentColor"
          height="1.5em"
          viewBox="0 0 32 32"
          width="1.5em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <clipPath id="theme-toggle__around__cutout">
            <path d="M0 0h42v30a1 1 0 00-16 13H0Z" />
          </clipPath>
          <g clipPath="url(#theme-toggle__around__cutout)">
            <circle cx="16" cy="16" r="8.4" />
            <g>
              <circle cx="16" cy="3.3" r="2.3" />
              <circle cx="27" cy="9.7" r="2.3" />
              <circle cx="27" cy="22.3" r="2.3" />
              <circle cx="16" cy="28.7" r="2.3" />
              <circle cx="5" cy="22.3" r="2.3" />
              <circle cx="5" cy="9.7" r="2.3" />
            </g>
          </g>
        </svg>
      </div>
    </Component>
  );
};
