import { Input } from "@heroui/input";
import vueLogo from '@/assets/images/vue.svg';
import reactLogo from '@/assets/images/react.svg';
import litLogo from '@/assets/images/lit.svg';
import { useState } from "react";
import clsx from "clsx";
import styles from './CreateDialog.module.css';
import { Button } from "@heroui/button";

const icons = [{
  logo: reactLogo,
  name: 'React',
}, {
  logo: vueLogo,
  name: 'Vue',
}, {
  logo: litLogo,
  name: 'Lit',
}];

export function CreateProjectForm() {
  const [type, setType] = useState<string>('React');
  const [name, setName] = useState<string>('');

  const content = icons.map(({ logo, name }) => {
    return (
      <div
        key={name}
        className={clsx('p-4 text-center', styles.createItem)}
        data-selected={type === name}
        onClick={() => setType(name)}
      >
        <img className="w-10 h-10 mb-2" src={logo} alt="React logo" />{ name }
      </div>
    );
  });

  return (
    <section className="flex flex-col items-center justify-center gap-4">
      <div className="w-[400px] flex flex-col gap-2">
        <div className='flex justify-evenly gap-4'>
          { content }
        </div>
        <Input placeholder="项目名" value={name} onChange={(e) => setName(e.target.value)} />
        <Button
          className="bg-gradient-to-tr from-pink-500 to-yellow-500 text-white shadow-lg"
          radius="full"
        >
          创建项目
        </Button>
      </div>
    </section>
  );
}