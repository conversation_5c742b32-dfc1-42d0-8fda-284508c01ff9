import { Ava<PERSON>, Button, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/react";
import { Key } from "react";
import { useUserInfoStore } from '@/hooks/login';
import { useSidebarStore } from "@/stores/sidebar";
import IconAvatarOutline from '~icons/mdi/account-outline';
import IconMenu from '~icons/mdi/menu';

export function TopBar() {
  const { userInfo, logOutUser } = useUserInfoStore();
  const { toggleCollapse } = useSidebarStore();

  const handleClick = (key: Key) => {
    if (key === 'logOut') {
      logOutUser();
    }
  }

  return (
    <div className="h-8 flex items-center justify-between gap-3 px-6  dark:bg-zinc-900/50 backdrop-blur-sm">
      <div className="flex items-center gap-3">
        <Button
          isIconOnly
          className="flex-shrink-0 -ml-6"
          size="sm"
          variant="light"
          onPress={toggleCollapse}
        >
          <IconMenu className="w-4 h-4" />
        </Button>
      </div>
      <div className="flex items-center gap-3">
        {/* <ThemeSwitch /> */}
        <Dropdown>
        <DropdownTrigger>
          <Button className="h-6 px-2 min-w-0" size="sm" variant="light">
            <Avatar 
              showFallback 
              className="w-6 h-6" 
              fallback={<IconAvatarOutline />} 
            />
            <span className="ml-2 text-sm font-medium">
              {userInfo?.displayname || userInfo?.username}
            </span>
          </Button>
        </DropdownTrigger>
        <DropdownMenu onAction={key => handleClick(key)}>
          <DropdownItem key="profile" textValue="用户信息">
            <div className="flex flex-col">
              <span className="text-sm font-medium">{userInfo?.displayname || userInfo?.username}</span>
            </div>
          </DropdownItem>
          <DropdownItem key="logOut" textValue="退出登录">退出登录</DropdownItem>
        </DropdownMenu>
        </Dropdown>
      </div>
    </div>
  );
}