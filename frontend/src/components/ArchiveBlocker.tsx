import { unarchive } from "@/apis";
import { addToast, Button } from "@heroui/react";
import { useState } from "react";

export function ArchiveBlocker({ id, onUnarchived }: { id: string, onUnarchived?: () => void }) {
  const [inProgress, setInProgress] = useState(false);
  return (
    <div className="flex-1 flex flex-col h-full items-center justify-center">
      <div>工作区已存档，是否恢复？</div>
      <Button className="mt-4" color="primary" isLoading={inProgress} onPress={async () => {
        setInProgress(true);
        try {
          await unarchive({ id });
        } catch(e) {
          addToast({
            color: 'danger',
            title: '恢复失败',
            description: String(e),
          });
          return;
        } finally {
          setInProgress(false);
        }
        addToast({
          color: 'success',
          title: '恢复成功',
          description: '🎉🎉🎉',
        });
        onUnarchived?.();
      }}>恢复</Button>
    </div>
  );
}
