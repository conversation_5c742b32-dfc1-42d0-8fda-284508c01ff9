import type { ColumnsType } from 'antd/es/table';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ody,
  ModalFooter,
  Input,
  addToast,
  Tooltip,
} from '@heroui/react';
import { Table } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DesignProject, DesignPage, updateDesignPage, createDesignPage } from '@/apis';
import IconCheck from '~icons/mdi/check';
import IconLink from '~icons/mdi/link';
import IconEdit from '~icons/mdi/pencil';

interface Props {
  isOpen: boolean;
  project?: DesignProject;
  onClose: () => void;
  onProjectUpdate?: () => void;
}

export function PageListModal({ isOpen, project, onClose, onProjectUpdate }: Props) {
  const navigate = useNavigate();
  const [editingPageId, setEditingPageId] = useState<string | null>(null);
  const [editingValues, setEditingValues] = useState<{
    name: string;
    englishName: string;
    description: string;
  }>({
    name: '',
    englishName: '',
    description: '',
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [newPageValues, setNewPageValues] = useState<{
    name: string;
    englishName: string;
    description: string;
  }>({
    name: '',
    englishName: '',
    description: '',
  });
  const displayPages = project?.pages;

  const columns: ColumnsType<DesignPage> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string, record: DesignPage) => {
        const isEditing = editingPageId === record.id;
        const isNew = record.id === 'new';

        return isEditing || isNew ? (
          <Input
            placeholder="请输入页面名称"
            size="sm"
            value={isNew ? newPageValues.name : editingValues.name}
            onChange={(e) => {
              if (isNew) {
                setNewPageValues(prev => ({ ...prev, name: e.target.value }));
              } else {
                setEditingValues(prev => ({ ...prev, name: e.target.value }));
              }
            }}
          />
        ) : (
          text
        );
      },
    },
    {
      title: '英文名',
      dataIndex: 'englishName',
      key: 'englishName',
      width: 150,
      render: (text: string, record: DesignPage) => {
        const isEditing = editingPageId === record.id;
        const isNew = record.id === 'new';

        return isEditing || isNew ? (
          <Input
            placeholder="请输入英文名称"
            size="sm"
            value={isNew ? newPageValues.englishName : editingValues.englishName}
            onChange={(e) => {
              if (isNew) {
                setNewPageValues(prev => ({ ...prev, englishName: e.target.value }));
              } else {
                setEditingValues(prev => ({ ...prev, englishName: e.target.value }));
              }
            }}
          />
        ) : (
          text
        );
      },
    },
    {
      title: '原型',
      dataIndex: 'prototypes',
      key: 'prototypes',
      width: 150,
      render: (prototypes: any[]) => prototypes?.map(prototype => prototype.prototypeName).join(', '),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string, record: DesignPage) => {
        const isEditing = editingPageId === record.id;
        const isNew = record.id === 'new';

        return isEditing || isNew ? (
          <Input
            placeholder="请输入页面描述"
            size="sm"
            value={isNew ? newPageValues.description : editingValues.description}
            onChange={(e) => {
              if (isNew) {
                setNewPageValues(prev => ({ ...prev, description: e.target.value }));
              } else {
                setEditingValues(prev => ({ ...prev, description: e.target.value }));
              }
            }}
          />
        ) : (
          text
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
      width: 150,
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '更新时间',
      dataIndex: 'updated',
      key: 'updated',
      width: 150,
      render: (text: string) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_, record: DesignPage) => {
        const isEditing = editingPageId === record.id;
        const isNew = record.id === 'new';
        
        return (
          <div className="flex items-center gap-2">
            {isEditing || isNew ? (
              <>
                <Tooltip content={isNew ? "确认创建" : "确认更新"}>
                  <span 
                    className="text-lg text-primary cursor-pointer active:opacity-50"
                    onClick={() => isNew ? handleCreatePage() : handleUpdatePage(record)}
                  >
                    <IconCheck />
                  </span>
                </Tooltip>
                <Tooltip content={isNew ? "取消创建" : "取消编辑"}>
                  <span 
                    className="text-lg text-default-400 cursor-pointer active:opacity-50"
                    onClick={isNew ? handleCancelCreate : handleCancelEdit}
                  >
                    ×
                  </span>
                </Tooltip>
              </>
            ) : (
              <Tooltip content="编辑页面">
                <span 
                  className="text-lg text-default-400 cursor-pointer active:opacity-50"
                  onClick={() => handleEditPage(record)}
                >
                  <IconEdit />
                </span>
              </Tooltip>
            )}
            {record.playgroundId && (
              <Tooltip content="查看转码结果">
                <span 
                  className="text-lg text-default-400 cursor-pointer active:opacity-50"
                  onClick={() => handleViewResult(record.playgroundId!)}
                >
                  <IconLink />
                </span>
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ];

  const handleViewResult = (playgroundId: string) => {
    navigate(`/chat/${playgroundId}`);
  };

  const handleEditPage = (page: DesignPage) => {
    setEditingPageId(page.id);
    setEditingValues({
      name: page.name,
      englishName: page.englishName,
      description: page.description,
    });
  };

  const handleCancelEdit = () => {
    setEditingPageId(null);
    setEditingValues({
      name: '',
      englishName: '',
      description: '',
    });
  };

  const handleCancelCreate = () => {
    setIsAdding(false);
    setNewPageValues({
      name: '',
      englishName: '',
      description: '',
    });
  };

  const handleCreatePage = async () => {
    if (!project) return;

    setIsUpdating(true);
    try {
      await createDesignPage({
        projectId: project.id,
        name: newPageValues.name,
        englishName: newPageValues.englishName,
        description: newPageValues.description,
        prototypes: [],
      });

      addToast({
        title: '页面创建成功',
        color: 'success',
      });

      setIsAdding(false);
      setNewPageValues({
        name: '',
        englishName: '',
        description: '',
      });
      onProjectUpdate?.();
    } catch (error) {
      console.error('创建页面失败:', error);
      addToast({
        title: '页面创建失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleUpdatePage = async (page: DesignPage) => {
    if (!project) return;

    setIsUpdating(true);
    try {
      await updateDesignPage({
        projectId: project.id,
        pageId: page.id,
        name: editingValues.name,
        englishName: editingValues.englishName,
        description: editingValues.description,
      });

      addToast({
        title: '页面更新成功',
        color: 'success',
      });

      setEditingPageId(null);
      onProjectUpdate?.();
    } catch (error) {
      console.error('更新页面失败:', error);
      addToast({
        title: '页面更新失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader>
          <h3 className="text-base font-semibold">页面属性设置</h3>
        </ModalHeader>
        <ModalBody>
          <div className="mb-4">
            <Button 
              color="primary" 
              disabled={isAdding}
              onPress={() => setIsAdding(true)}
            >
              添加页面
            </Button>
          </div>
          <Table
            columns={columns}
            dataSource={isAdding ? [{
              id: 'new',
              name: '',
              englishName: '',
              description: '',
              designProjectId: project?.id || '',
              prototypes: [],
              created: '',
              updated: '',
            }, ...displayPages || []] : displayPages}
            pagination={false}
            rowKey="id"
            scroll={{ x: 1200 }}
            size="small"
          />
        </ModalBody>
        <ModalFooter>
          <Button variant="light" onPress={onClose}>
            关闭
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}