import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  addToast,
} from '@heroui/react';
import React, { useState } from 'react';
import {
  DesignProject,
  getProjectCodeGenerationHistory,
  createProjectPreview,
  ProjectPreviewResult,
  ProjectCodeGenerationHistory,
} from '@/apis';
import { useUserInfoStore } from '@/hooks/login';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  project: DesignProject | undefined;
}

export function ProjectPreviewModal({ isOpen, onClose, project }: Props) {
  const { userInfo } = useUserInfoStore();
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewHistory, setPreviewHistory] = useState<ProjectCodeGenerationHistory | null>(null);
  const [previewResult, setPreviewResult] = useState<ProjectPreviewResult | null>(null);

  /**
   * 处理项目预览
   */
  const handleProjectPreview = async () => {
    if (!project) return;
    
    setPreviewResult(null);
    
    try {
      setPreviewLoading(true);
      
      // 1. 先查询项目的代码生成历史
      const historyResponse = await getProjectCodeGenerationHistory({ projectId: project.id });
      const history = historyResponse.data || historyResponse;

      setPreviewHistory(history);
      
      if (history.pageGenerations.length === 0) {
        addToast({
          title: '项目预览',
          description: '该项目还没有已完成的代码生成记录，无法创建预览',
          color: 'warning',
        });

        return;
      }
      
      // 2. 创建项目预览
      const previewResponse = await createProjectPreview({
        projectId: project.id,
        user: userInfo?.username || 'current-user',
      });
      const result = (previewResponse as any).data || previewResponse;
      
      setPreviewResult(result);
      
      if (result.success) {
        addToast({
          title: '项目预览创建成功',
          description: result.message,
          color: 'success',
        });
      } else {
        addToast({
          title: '项目预览创建失败',
          description: result.message,
          color: 'danger',
        });
      }
      
    } catch (error) {
      console.error('项目预览失败:', error);
      addToast({
        title: '项目预览失败',
        description: '创建项目预览时发生错误，请重试',
        color: 'danger',
      });
    } finally {
      setPreviewLoading(false);
    }
  };

  // 当模态框打开时自动开始预览
  React.useEffect(() => {
    if (isOpen && project) {
      handleProjectPreview();
    }
  }, [isOpen, project]);

  // 当模态框关闭时重置状态
  React.useEffect(() => {
    if (!isOpen) {
      setPreviewLoading(false);
      setPreviewHistory(null);
      setPreviewResult(null);
    }
  }, [isOpen]);

  return (
    <Modal 
      isOpen={isOpen} 
      scrollBehavior="inside"
      size="2xl"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3>项目预览</h3>
          {project && (
            <p className="text-sm text-default-600">{project.name}</p>
          )}
        </ModalHeader>
        <ModalBody>
          <div className="text-sm text-gray-600">
            注意：系统会自动将常见Web框架的预览服务host设置为0.0.0.0。如果您使用自定义项目且不支持标准环境变量，需要前往预览页面切换到代码编辑器将该项目构建配置中的host改为0.0.0.0，然后再点击重启预览服务按钮进行重启，重启后再观察项目预览效果。
          </div>

          {previewLoading && (
            <div className="flex flex-col items-center justify-center py-8">
              <Spinner size="lg" />
              <p className="text-sm text-default-600 mt-4">正在创建项目预览...</p>
            </div>
          )}
          
          {previewHistory && !previewLoading && (
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">项目信息</h4>
                <div className="bg-default-100 p-3 rounded-lg">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">项目名称:</span>
                      <span className="text-sm font-medium">{project?.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Git仓库:</span>
                      {project?.gitUrl && (
                        <a 
                          className="text-sm text-blue-600 hover:text-blue-800 underline" 
                          href={previewResult?.originalRepoUrl || project.gitUrl} 
                          rel="noopener noreferrer"
                          target="_blank"
                        >
                          {project.gitUrl}
                        </a>
                      )}
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">目标分支:</span>
                      {previewResult?.originalRepoBranchUrl ? (
                        <a 
                          className="text-sm text-blue-600 hover:text-blue-800 underline" 
                          href={previewResult.originalRepoBranchUrl} 
                          rel="noopener noreferrer"
                          target="_blank"
                        >
                          {project?.gitBranch}
                        </a>
                      ) : (
                        <span className="text-sm font-medium">{project?.gitBranch}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">代码生成历史 ({previewHistory.pageGenerations.length} 个页面)</h4>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {previewHistory.pageGenerations.map((page, index) => {
                    // 从预览结果中查找对应页面的实际分支信息
                    const mergeResult = previewResult?.mergeResults?.find(r => r.pageId === page.pageId);
                    const actualBranch = mergeResult?.sourceBranch || page.branchName;
                    const branchUrl = mergeResult?.sourceBranchUrl;
                    
                    return (
                      <div key={page.pageId} className="bg-default-50 p-3 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{page.pageName}</p>
                            <p className="text-sm text-default-600">
                              fork分支: {branchUrl ? (
                                <a 
                                  className="text-blue-600 hover:text-blue-800 underline" 
                                  href={branchUrl} 
                                  rel="noopener noreferrer"
                                  target="_blank"
                                >
                                  {actualBranch}
                                </a>
                              ) : actualBranch || '待查找'}
                            </p>
                          </div>
                          <span className="text-xs bg-success-100 text-success-700 px-2 py-1 rounded">
                            #{index + 1}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              
              {previewResult && (
                <div>
                  <h4 className="font-semibold mb-2">预览分支生成结果</h4>
                  <div className={`p-3 rounded-lg ${previewResult.success ? 'bg-success-50' : 'bg-danger-50'}`}>
                    <p className={`font-medium ${previewResult.success ? 'text-success-700' : 'text-danger-700'}`}>
                      {previewResult.message}
                    </p>
                    
                    {previewResult.forkUrl && (
                      <p className="text-sm mt-2">
                        <span className="font-medium">Fork仓库:</span> {previewResult.forkProjectUrl ? (
                          <a 
                            className="text-blue-600 hover:text-blue-800 underline ml-1" 
                            href={previewResult.forkProjectUrl} 
                            rel="noopener noreferrer"
                            target="_blank"
                          >
                            {previewResult.forkUrl}
                          </a>
                        ) : previewResult.forkUrl}
                      </p>
                    )}
                    
                    {previewResult.previewBranch && (
                      <p className="text-sm mt-2">
                        <span className="font-medium">预览分支:</span> {previewResult.previewBranchUrl ? (
                          <a 
                            className="text-blue-600 hover:text-blue-800 underline ml-1" 
                            href={previewResult.previewBranchUrl} 
                            rel="noopener noreferrer"
                            target="_blank"
                          >
                            {previewResult.previewBranch}
                          </a>
                        ) : previewResult.previewBranch}
                      </p>
                    )}
                    
                    {previewResult.mergeResults && (
                      <div className="mt-3">
                        <p className="text-sm font-medium mb-2">在fork仓库中的合并结果:</p>
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                          {previewResult.mergeResults.map((result, index) => (
                            <div key={index} className="flex items-center justify-between text-xs">
                              <span className="flex-1">{result.pageName}</span>
                              <div className="flex items-center gap-2">
                                {result.sourceBranchUrl ? (
                                  <a 
                                    className="text-xs text-blue-600 hover:text-blue-800 underline truncate max-w-24" 
                                    href={result.sourceBranchUrl} 
                                    rel="noopener noreferrer"
                                    target="_blank" 
                                    title={result.sourceBranch}
                                  >
                                    {result.sourceBranch}
                                  </a>
                                ) : (
                                  <span className="text-xs text-gray-500 truncate max-w-24" title={result.sourceBranch}>
                                    {result.sourceBranch}
                                  </span>
                                )}
                                <span className={`px-2 py-1 rounded text-xs min-w-12 text-center ${
                                  result.success 
                                    ? 'bg-success-100 text-success-700' 
                                    : 'bg-danger-100 text-danger-700'
                                }`}>
                                  {result.success ? '成功' : '失败'}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        {/* 统计信息 */}
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <div className="flex justify-between text-xs text-gray-600">
                            <span>总计: {previewResult.mergeResults.length} 个页面</span>
                            <span>
                              成功: {previewResult.mergeResults.filter(r => r.success).length}, 
                              失败: {previewResult.mergeResults.filter(r => !r.success).length}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button variant="light" onPress={onClose}>
            关闭
          </Button>
          {/* {previewResult?.webideUrl && (
            <Button 
              color="primary" 
              onPress={() => {
                // 从webideUrl中提取参数，跳转到我们的启动页面
                const webideUrl = previewResult?.webideUrl || '';
                const urlParams = new URLSearchParams(webideUrl.split('?')[1] || '');
                const projectId = urlParams.get('projectId') || project?.id || 'unknown';
                const branch = urlParams.get('branch') || 'master';

                window.open(`/webide-start?projectId=${encodeURIComponent(projectId)}&branch=${encodeURIComponent(branch)}`, '_blank');
              }}
            >
              启动WebIDE
            </Button>
          )} */}
          {/**启动pm2预览 */}
          {previewResult?.previewBranchUrl && previewResult?.pm2PreviewUrl && (
            <Button 
              color="primary" 
              onPress={() => {
                // 直接跳转到PM2预览的chat页面
                const pm2PreviewUrl = previewResult.pm2PreviewUrl;

                window.open(pm2PreviewUrl, '_blank');
              }}
            >
              去预览
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 