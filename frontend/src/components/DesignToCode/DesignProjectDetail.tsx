import { 
  <PERSON><PERSON>, 
  Card, 
  CardBody, 
  CardHeader,
  Chip,
  Modal,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Checkbox,
  Divider,
  Progress,
  useDisclosure,
  addToast
} from '@heroui/react';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  DesignProject, 
  DesignPage,
  getDesignProject, 
  listDesignPages, 
  createDesignPage,
  updateDesignPage,
  deleteDesignPage,
  listProjects,
  Project,
  deletePagePrototype,
  updatePagePrototype,
  createPagePrototype,
  createBackgroundTranscodeTask,
  getTranscodeTask,
  getProjectTranscodeTasks,
  updateTranscodeTaskStatus
} from '@/apis';
import { useUserInfoStore } from '@/hooks/login';
import IconClock from '~icons/mdi/clock';
import IconDelete from '~icons/mdi/delete';
import IconDownload from '~icons/mdi/download';
import IconEye from '~icons/mdi/eye';
import IconMinus from '~icons/mdi/minus';
import IconEdit from '~icons/mdi/pencil';
import IconPlay from '~icons/mdi/play';
import IconPlus from '~icons/mdi/plus';
import IconRefresh from '~icons/mdi/refresh';

interface Props {
  projectId: string;
}

// 临时表单数据结构（用于文件上传）
interface PagePrototypeForm {
  id?: string; // 现有原型的ID
  prototypeName: string;
  htmlFile: File | null;
  cssFile: File | null;
  currentHtmlFileName?: string; // 当前文件名（编辑时显示）
  currentCssFileName?: string;
  isExisting?: boolean; // 是否为现有原型
}

interface TranscodeProgress {
  prototypeId: string;
  pageName: string;
  prototypeName: string;
  status: 'waiting' | 'processing' | 'completed' | 'failed';
  progress: number;
  stage?: string;
}

interface TranscodeTask {
  id: string;
  status: string;
  progress: number;
  model: string;
  enableAutoIteration: boolean;
  enableStepByStep: boolean;
  created: string;
  updated: string;
  items: TranscodeTaskItem[];
}

interface TranscodeTaskItem {
  id: string;
  status: string;
  progress: number;
  stage: string;
  playgroundId?: string;
  error?: string;
  prototype: {
    id: string;
    prototypeName: string;
    designPage: {
      name: string;
    };
  };
}

export function DesignProjectDetail({ projectId }: Props) {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isTranscodeOpen, onOpen: onTranscodeOpen, onClose: onTranscodeClose } = useDisclosure();
  const { isOpen: isProjectCodeGenOpen, onOpen: onProjectCodeGenOpen, onClose: onProjectCodeGenClose } = useDisclosure();
  
  const [project, setProject] = useState<DesignProject | null>(null);
  const [pages, setPages] = useState<DesignPage[]>([]);
  const [selectedPages, setSelectedPages] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [editingPage, setEditingPage] = useState<DesignPage | null>(null);
  
  // 转码任务管理
  const [isTranscoding, setIsTranscoding] = useState(false);
  const [taskPollingInterval, setTaskPollingInterval] = useState<number | null>(null);
  const [currentTask, setCurrentTask] = useState<TranscodeTask | null>(null);
  
  // 页面状态轮询管理
  const [statusPollingInterval, setStatusPollingInterval] = useState<number | null>(null);
  
  // 转码历史悬浮气泡状态
  const [showHistoryBubble, setShowHistoryBubble] = useState(false);
  const [transcodeHistory, setTranscodeHistory] = useState<TranscodeTask[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  
  // 拖拽状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [bubblePosition, setBubblePosition] = useState({ x: 24, y: 24 }); // 默认右下角位置 (right: 24px, bottom: 24px)

  // 项目级代码生成相关状态
  const [projectCodeGenPages, setProjectCodeGenPages] = useState<Set<string>>(new Set());
  const [isStartingProjectCodeGen, setIsStartingProjectCodeGen] = useState(false);

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    prototypes: [{ prototypeName: 'A', htmlFile: null, cssFile: null, isExisting: false }] as PagePrototypeForm[]
  });

  // 项目模板列表
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    loadProject();
    loadPages();
    loadProjects();
    loadTranscodeHistory();
  }, [projectId]);

  // 清理轮询
  useEffect(() => {
    return () => {
      if (taskPollingInterval) {
        clearInterval(taskPollingInterval);
      }
      if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
      }
    };
  }, [taskPollingInterval, statusPollingInterval]);

  const loadProjects = async () => {
    try {
      const result = await listProjects();

      setProjects(Array.isArray(result) ? result : []);
    } catch (error) {
      console.error('加载项目模板失败:', error);
    }
  };

  const loadProject = async () => {
    try {
      const data = await getDesignProject({ id: projectId });

      if (data && typeof data === 'object' && 'id' in data) {
        setProject(data as DesignProject);
      }
    } catch (error) {
      console.error('加载设计稿转码工程失败:', error);
    }
  };

  const loadPages = async () => {
    try {
      setLoading(true);
      const data = await listDesignPages({ projectId });

      setPages(Array.isArray(data) ? data : []);
      
      // 检查是否有处理中的任务，如果有则启动状态轮询
      checkAndStartStatusPolling(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('加载设计稿页面失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 检查是否有处理中的任务
  const hasProcessingTasks = (pages: DesignPage[]): boolean => {
    return pages.some(page => 
      page.prototypes.some(prototype => prototype.status === 'processing')
    );
  };

  // 检查并启动状态轮询
  const checkAndStartStatusPolling = (pages: DesignPage[]) => {
    const hasProcessing = hasProcessingTasks(pages);
    
    if (hasProcessing && !statusPollingInterval) {
      // 有处理中的任务且未启动轮询，则启动轮询
      startStatusPolling();
    } else if (!hasProcessing && statusPollingInterval) {
      // 没有处理中的任务且正在轮询，则停止轮询
      stopStatusPolling();
    }
  };

  // 启动状态轮询
  const startStatusPolling = () => {
    console.log('🔄 启动页面状态轮询 (15秒间隔)');
    
    const pollInterval = setInterval(async () => {
      try {
        console.log('📊 轮询页面状态...');
        const data = await listDesignPages({ projectId });
        const latestPages = Array.isArray(data) ? data : [];
        
        // 统计处理中的原型数量
        const processingCount = latestPages.reduce((count, page) => {
          return count + page.prototypes.filter(p => p.status === 'processing').length;
        }, 0);
        
        console.log(`📈 页面状态更新完成，处理中的原型数量: ${processingCount}`);
        setPages(latestPages);
        
        // 检查是否还有处理中的任务
        const stillHasProcessing = hasProcessingTasks(latestPages);

        if (!stillHasProcessing) {
          console.log('✅ 所有任务已完成，停止状态轮询');
          stopStatusPolling();
        }
      } catch (error) {
        console.error('❌ 状态轮询失败:', error);
        // 轮询失败时不要停止轮询，继续尝试
      }
    }, 15000); // 15秒间隔
    
    setStatusPollingInterval(pollInterval);
  };

  // 停止状态轮询
  const stopStatusPolling = () => {
    if (statusPollingInterval) {
      console.log('⏹️ 停止页面状态轮询');
      clearInterval(statusPollingInterval);
      setStatusPollingInterval(null);
    }
  };

  const handleFileRead = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = reject;
      reader.readAsText(file);
    });
  };

  // 添加新版本
  const addVersion = () => {
    const nextVersionName = String.fromCharCode(65 + formData.prototypes.length); // A, B, C, ...

    setFormData({
      ...formData,
      prototypes: [...formData.prototypes, { 
        prototypeName: nextVersionName, 
        htmlFile: null, 
        cssFile: null,
        isExisting: false // 新添加的原型标记为非现有
      }]
    });
  };

  // 移除版本
  const removeVersion = (index: number) => {
    if (formData.prototypes.length <= 1) {
      if (!editingPage) {
        // 新建页面模式：必须至少保留一个原型
        addToast({
          title: '新建页面时至少需要保留一个原型',
          color: 'danger',
        });

        return;
      } else {
        // 编辑页面模式：警告但允许删除
        const confirmed = confirm('删除后该页面将没有任何原型，确定要删除吗？');

        if (!confirmed) {
          return;
        }
      }
    }
    setFormData({
      ...formData,
      prototypes: formData.prototypes.filter((_, i) => i !== index)
    });
  };

  // 更新版本数据
  const updateVersion = (index: number, field: keyof PagePrototypeForm, value: any) => {
    const newVersions = [...formData.prototypes];

    newVersions[index] = { ...newVersions[index], [field]: value };
    setFormData({ ...formData, prototypes: newVersions });
  };

  const handleCreatePage = async () => {
    if (!formData.name.trim()) {
      addToast({
        title: '请输入页面名称',
        color: 'danger',
      });

      return;
    }
    
    // 验证至少有一个原型
    if (formData.prototypes.length === 0) {
      addToast({
        title: '至少需要添加一个页面原型',
        color: 'danger',
      });

      return;
    }
    
    // 验证所有原型都有文件
    for (let i = 0; i < formData.prototypes.length; i++) {
      const prototype = formData.prototypes[i];

      if (!prototype.htmlFile || !prototype.cssFile) {
        addToast({
          title: `原型 ${prototype.prototypeName} 需要上传HTML和CSS文件`,
          color: 'danger',
        });

        return;
      }
    }

    try {
      // 读取所有版本的文件内容
      const prototypes = await Promise.all(
        formData.prototypes.map(async (prototype) => {
          if (!prototype.htmlFile || !prototype.cssFile) {
            throw new Error('文件为空');
          }
          const htmlContent = await handleFileRead(prototype.htmlFile);
          const cssContent = await handleFileRead(prototype.cssFile);

          return {
            prototypeName: prototype.prototypeName,
            htmlContent,
            cssContent,
            htmlFileName: prototype.htmlFile.name,
            cssFileName: prototype.cssFile.name,
          };
        })
      );
      
      await createDesignPage({
        projectId,
        name: formData.name,
        description: formData.description,
        prototypes,
      });
      
      setFormData({ 
        name: '', 
        description: '', 
        prototypes: [{ prototypeName: 'A', htmlFile: null, cssFile: null, isExisting: false }]
      });
      onClose();
      loadPages();
      
      addToast({
        title: '页面创建成功',
        color: 'success',
      });
    } catch (error) {
      console.error('创建设计稿页面失败:', error);
      addToast({
        title: '创建失败，请重试',
        color: 'danger',
      });
    }
  };

  const handleEditPage = (page: DesignPage) => {
    setEditingPage(page);
    setFormData({
      name: page.name,
      description: page.description || '',
      prototypes: page.prototypes.map(prototype => ({
        id: prototype.id,
        prototypeName: prototype.prototypeName,
        htmlFile: null,
        cssFile: null,
        currentHtmlFileName: prototype.htmlFileName,
        currentCssFileName: prototype.cssFileName,
        isExisting: true,
      }))
    });
    onEditOpen();
  };

  const handleUpdatePage = async () => {
    if (!editingPage || !formData.name.trim()) {
      addToast({
        title: '请输入页面名称',
        color: 'danger',
      });

      return;
    }

    // 验证至少有一个原型
    if (formData.prototypes.length === 0) {
      addToast({
        title: '页面至少需要保留一个原型',
        color: 'danger',
      });

      return;
    }

    try {
      // 更新页面基本信息
      await updateDesignPage({
        projectId,
        pageId: editingPage.id,
        name: formData.name,
        description: formData.description,
      });

      // 处理原型的增删改
      const existingPrototypes = editingPage.prototypes;
      const formPrototypes = formData.prototypes;

      // 删除不在表单中的原型
      for (const existing of existingPrototypes) {
        const stillExists = formPrototypes.find(p => p.id === existing.id);

        if (!stillExists) {
          await deletePagePrototype({ 
            projectId, 
            pageId: editingPage.id, 
            prototypeId: existing.id 
          });
        }
      }

      // 更新或创建原型
      for (const formPrototype of formPrototypes) {
        if (formPrototype.isExisting && formPrototype.id) {
          // 更新现有原型
          const updateData: any = {
            prototypeName: formPrototype.prototypeName,
          };

          // 如果有新文件，读取并更新
          if (formPrototype.htmlFile) {
            updateData.htmlContent = await handleFileRead(formPrototype.htmlFile);
            updateData.htmlFileName = formPrototype.htmlFile.name;
          }
          if (formPrototype.cssFile) {
            updateData.cssContent = await handleFileRead(formPrototype.cssFile);
            updateData.cssFileName = formPrototype.cssFile.name;
          }

          await updatePagePrototype({
            projectId,
            pageId: editingPage.id,
            prototypeId: formPrototype.id,
            ...updateData,
          });
        } else {
          // 创建新原型
          if (!formPrototype.htmlFile || !formPrototype.cssFile) {
            addToast({
              title: `新原型 ${formPrototype.prototypeName} 需要上传HTML和CSS文件`,
              color: 'danger',
            });

            return;
          }

          const htmlContent = await handleFileRead(formPrototype.htmlFile);
          const cssContent = await handleFileRead(formPrototype.cssFile);

          await createPagePrototype({
            projectId,
            pageId: editingPage.id,
            prototypeName: formPrototype.prototypeName,
            htmlContent,
            cssContent,
            htmlFileName: formPrototype.htmlFile.name,
            cssFileName: formPrototype.cssFile.name,
          });
        }
      }
      
      setFormData({ 
        name: '', 
        description: '', 
        prototypes: [{ prototypeName: 'A', htmlFile: null, cssFile: null, isExisting: false }]
      });
      setEditingPage(null);
      onEditClose();
      loadPages();
      
      addToast({
        title: '页面更新成功',
        color: 'success',
      });
    } catch (error) {
      console.error('更新设计稿页面失败:', error);
      addToast({
        title: '更新失败，请重试',
        color: 'danger',
      });
    }
  };

  const handleDeletePage = async (pageId: string) => {
    if (!confirm('确定要删除这个页面吗？这将删除页面的所有原型。')) {
      return;
    }

    try {
      await deleteDesignPage({ projectId, pageId });
      loadPages();
    } catch (error) {
      console.error('删除设计稿页面失败:', error);
      addToast({
        title: '删除失败，请重试',
        color: 'danger',
      });
    }
  };

  // 加载转码历史
  const loadTranscodeHistory = async () => {
    setHistoryLoading(true);
    try {
      const data = await getProjectTranscodeTasks({ projectId });

      setTranscodeHistory(data || []);
    } catch (error) {
      console.error('加载转码历史失败:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // 切换转码历史气泡显示
  const toggleHistoryBubble = () => {
    if (!showHistoryBubble) {
      loadTranscodeHistory(); // 打开时加载数据
    }
    setShowHistoryBubble(!showHistoryBubble);
  };

  // 拖拽事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    
    const rect = (e.target as HTMLElement).closest('.draggable-bubble')?.getBoundingClientRect();

    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const bubbleWidth = 384; // w-96 = 24rem = 384px
    const bubbleHeight = 384; // max-h-96 = 24rem = 384px
    
    // 计算新位置（从右下角开始计算）
    const newX = Math.max(0, Math.min(viewportWidth - bubbleWidth, viewportWidth - (e.clientX - dragOffset.x + bubbleWidth)));
    const newY = Math.max(0, Math.min(viewportHeight - bubbleHeight, viewportHeight - (e.clientY - dragOffset.y + bubbleHeight)));
    
    setBubblePosition({ x: newX, y: newY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none'; // 防止拖拽时选中文本
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging, dragOffset]);

  // 开始转码任务
  const handleStartTranscode = async () => {
    if (selectedPages.size === 0) {
      addToast({
        title: '请选择要转码的页面',
        color: 'danger',
      });

      return;
    }

    // 收集选中页面的所有原型ID
    const selectedPrototypeIds: string[] = [];
    const prototypeInfos: TranscodeProgress[] = [];
    
    pages.forEach(page => {
      if (selectedPages.has(page.id)) {
        page.prototypes.forEach(prototype => {
          selectedPrototypeIds.push(prototype.id);
          prototypeInfos.push({
            prototypeId: prototype.id,
            pageName: page.name,
            prototypeName: prototype.prototypeName,
            status: 'waiting',
            progress: 0,
            stage: '等待开始...',
          });
        });
      }
    });
    
    if (selectedPrototypeIds.length === 0) {
      addToast({
        title: '选中的页面没有可转码的原型',
        color: 'danger',
      });

      return;
    }
    
    try {
      // 立即将选中的原型状态重置为processing
      const updatedPages = pages.map(page => ({
        ...page,
        prototypes: page.prototypes.map(prototype => {
          if (selectedPrototypeIds.includes(prototype.id)) {
            return { ...prototype, status: 'processing' as const };
          }

          return prototype;
        })
      }));

      setPages(updatedPages);

      // 启动状态轮询（如果还未启动）
      if (!statusPollingInterval) {
        startStatusPolling();
      }

      // 创建临时转码任务对象用于弹窗显示
      const tempTask: TranscodeTask = {
        id: 'temp-task', // 临时ID，稍后会被真实ID替换
        status: 'processing',
        progress: 0,
        // model: 'htsc::saas-deepseek-v3',
        enableAutoIteration: false,
        enableStepByStep: false,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        items: prototypeInfos.map(info => ({
          id: 'item-' + info.prototypeId,
          status: 'processing',
          progress: 0,
          stage: '正在创建任务...',
          prototype: {
            id: info.prototypeId,
            prototypeName: info.prototypeName,
            designPage: {
              name: info.pageName,
            },
          },
        })),
      };

      // 立即设置任务并打开弹窗
      setCurrentTask(tempTask);
      setIsTranscoding(true);
      onTranscodeOpen();

      // 启动后台转码任务，使用默认工号018465
      console.log('🚀 创建后台转码任务...');
      const result = await createBackgroundTranscodeTask({
        projectId,
        prototypeIds: selectedPrototypeIds,
        user: userInfo?.username || '018465', // 临时使用018465作为默认工号
        // model: 'htsc::saas-deepseek-v3',
        enableAutoIteration: false,
        enableStepByStep: false,
      });

      console.log('✅ 后台转码任务创建成功:', result);

      // 使用后端返回的真实任务ID更新当前任务并开始轮询
      if (result && result.taskId) {
        const updatedTask = { ...tempTask, id: result.taskId };

        setCurrentTask(updatedTask);
        
        // 开始轮询任务进度，使用真实的任务ID
        startTaskPolling(selectedPrototypeIds, result.taskId);
      } else {
        throw new Error('未获取到任务ID');
      }
      
      // 清空选中状态
      setSelectedPages(new Set());
      
      addToast({
        title: `转码任务已启动，正在后台处理 ${selectedPrototypeIds.length} 个原型`,
        color: 'success',
      });
      
    } catch (error) {
      console.error('❌ 启动后台转码任务失败:', error);
      addToast({
        title: '启动转码失败，请重试',
        color: 'danger',
      });
      
      // 恢复原型状态
      loadPages();
      
      // 清理任务状态
      setCurrentTask(null);
      setIsTranscoding(false);
      onTranscodeClose();
    }
  };

  // 启动任务轮询
  const startTaskPolling = (prototypeIds: string[], taskId: string) => {
    console.log('🔄 启动任务进度轮询，任务ID:', taskId);
    
    // 清理之前的轮询
    if (taskPollingInterval) {
      clearInterval(taskPollingInterval);
    }

    const pollInterval = setInterval(async () => {
      try {
        console.log('📊 轮询任务状态，任务ID:', taskId);
        
        // 获取转码任务的最新状态
        const taskData = await getTranscodeTask({ taskId });
        
        if (!taskData) {
          console.error('❌ 获取转码任务失败，任务ID:', taskId);

          return;
        }

        console.log('📈 获取到任务状态:', {
          taskId: taskData.id,
          status: taskData.status,
          progress: taskData.progress,
          itemCount: taskData.items?.length || 0
        });

        // 更新任务进度
        setCurrentTask(prevTask => {
          if (!prevTask) return null;
          
          return {
            ...prevTask,
            id: taskData.id, // 确保使用真实的任务ID
            status: taskData.status,
            progress: taskData.progress,
            updated: taskData.updated,
            items: (taskData.items || []).map((item: any) => ({
              id: item.id,
              status: item.status,
              progress: item.progress,
              stage: item.stage || '处理中...',
              playgroundId: item.playgroundId,
              error: item.error,
              prototype: {
                id: item.prototype?.id || item.prototypeId,
                prototypeName: item.prototype?.prototypeName || '未知原型',
                designPage: {
                  name: item.prototype?.designPage?.name || '未知页面',
                },
              },
            })),
          };
        });
        
        // 如果任务完成了（成功或失败），停止轮询
        if (taskData.status === 'completed' || taskData.status === 'failed' || taskData.status === 'cancelled') {
          console.log('✅ 任务已完成，停止轮询，状态:', taskData.status);
          clearInterval(pollInterval);
          setTaskPollingInterval(null);
          setIsTranscoding(false);
          
          // 重新加载页面数据以获取最新的原型状态
          console.log('🔄 重新加载页面数据...');
          const pagesData = await listDesignPages({ projectId });
          const latestPages = Array.isArray(pagesData) ? pagesData : [];

          setPages(latestPages);
          
          // 检查是否需要继续状态轮询
          checkAndStartStatusPolling(latestPages);
          
          const completedItems = (taskData.items || []).filter((item: any) => item.status === 'completed');
          const failedItems = (taskData.items || []).filter((item: any) => item.status === 'failed');
          
          if (taskData.status === 'completed') {
            addToast({
              title: `转码任务完成！${completedItems.length} 个原型处理完毕`,
              color: 'success',
            });
          } else if (taskData.status === 'failed') {
            addToast({
              title: `转码任务失败！${failedItems.length} 个原型处理失败`,
              color: 'danger',
            });
          } else if (taskData.status === 'cancelled') {
            addToast({
              title: '转码任务已取消',
              color: 'warning',
            });
          }
        }
      } catch (error) {
        console.error('❌ 轮询任务状态失败:', error);
        // 轮询失败时不要停止轮询，继续尝试
      }
    }, 5000); // 每5秒轮询一次，增加间隔时间减少服务器压力

    setTaskPollingInterval(pollInterval);

    // 5分钟后强制停止轮询（防止无限轮询）
    setTimeout(() => {
      if (pollInterval) {
        console.log('⏰ 轮询超时，强制停止');
        clearInterval(pollInterval);
        setTaskPollingInterval(null);
        setIsTranscoding(false);
      }
    }, 300000); // 5分钟超时
  };

  // 关闭转码进度弹窗（不取消任务）
  const handleCloseTranscodeModal = () => {
    onTranscodeClose();
  };

  // 取消转码任务
  const handleCancelTranscode = async () => {
    if (!currentTask || !isTranscoding) return;

    const confirmed = confirm('确定要取消转码任务吗？已完成的原型将保留结果。');

    if (!confirmed) return;

    try {
      await updateTranscodeTaskStatus({
        taskId: currentTask.id,
        status: 'cancelled',
      });

      if (taskPollingInterval) {
        clearInterval(taskPollingInterval);
        setTaskPollingInterval(null);
      }
      
      setIsTranscoding(false);
      setCurrentTask(null);
      onTranscodeClose();

      addToast({
        title: '转码任务已取消',
        color: 'warning',
      });
    } catch (error) {
      console.error('取消转码任务失败:', error);
      addToast({
        title: '取消任务失败',
        color: 'danger',
      });
    }
  };

  // 重新打开转码进度查看
  const handleReopenTranscodeProgress = () => {
    if (currentTask) {
      onTranscodeOpen();
    }
  };

  // 查看转码结果
  const handleViewResult = (playgroundId: string) => {
    navigate(`/chat/${playgroundId}`);
  };

  // 启动页面级代码生成工作流
  const handleStartWorkflow = async (page: DesignPage) => {
    if (!userInfo?.workNumber) {
      addToast({
        title: '用户信息不完整',
        color: 'danger',
      });

      return;
    }

    try {
      // 使用通用工具函数调用工作流接口
      const result = await retrySinglePageWorkflow(
        projectId,
        page.id,
        userInfo.workNumber,
        {
          enableAutoIteration: true,
          enableStepByStep: false,
        }
      );
      
      addToast({
        title: '工作流启动成功',
        description: `工作流ID: ${result.taskId}`,
        color: 'success',
      });

      // 跳转到工作流可视化页面
      const workflowUrl = `/workflow/${projectId}/${result.taskId}`;

      window.open(workflowUrl, '_blank');
      
    } catch (error) {
      console.error('启动工作流失败:', error);
      addToast({
        title: '启动工作流失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    }
  };

  // 启动多页面工作流
  const handleStartMultiPageWorkflow = async () => {
    if (!userInfo?.workNumber) {
      addToast({
        title: '用户信息不完整',
        color: 'danger',
      });

      return;
    }

    if (selectedPages.size === 0) {
      addToast({
        title: '请选择要启动工作流的页面',
        color: 'danger',
      });

      return;
    }

    const pageIds = Array.from(selectedPages);

    try {
      // 使用通用工具函数调用多页面工作流接口
      const result = await retryMultiPageWorkflow(
        projectId,
        pageIds,
        userInfo.workNumber,
        {
          enableAutoIteration: true,
          enableStepByStep: false,
        }
      );
      
      addToast({
        title: '多页面工作流启动成功',
        description: `工作流ID: ${result.taskId}`,
        color: 'success',
      });

      // 跳转到工作流可视化页面
      const workflowUrl = `/workflow/${projectId}/${result.taskId}`;

      window.open(workflowUrl, '_blank');
      
    } catch (error) {
      console.error('启动多页面工作流失败:', error);
      addToast({
        title: '启动多页面工作流失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    }
  };

  // 启动项目级代码生成工作流
  const handleStartProjectCodeGenWorkflow = async () => {
    if (!userInfo?.workNumber) {
      addToast({
        title: '用户信息不完整',
        color: 'danger',
      });

      return;
    }

    if (projectCodeGenPages.size === 0) {
      addToast({
        title: '请选择要生成代码的页面',
        color: 'danger',
      });

      return;
    }

    const pageIds = Array.from(projectCodeGenPages);

    setIsStartingProjectCodeGen(true);

    try {
      const gitUrl = 'git@*************:codebox-test/zhangyu-test/eam-web-app.git';
      const branch = 'dev';

      if (pageIds.length === 1) {
        // 单页面工作流
        const response = await fetch(`/api/design-project/${projectId}/tasks/page-code-generation-workflow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pageId: pageIds[0],
            user: userInfo.workNumber,
            gitUrl,
            branch,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        addToast({
          title: '页面级代码生成工作流启动成功',
          description: `工作流ID: ${result.taskId}`,
          color: 'success',
        });

        // 跳转到工作流可视化页面
        window.open(`/workflow/${projectId}/${result.taskId}`, '_blank');
      } else {
        // 多页面工作流
        const response = await fetch(`/api/design-project/${projectId}/tasks/multi-page-code-generation-workflow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pageIds,
            user: userInfo.workNumber,
            gitUrl,
            branch,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        addToast({
          title: '多页面代码生成工作流启动成功',
          description: `工作流ID: ${result.taskId}，共${pageIds.length}个页面`,
          color: 'success',
        });

        // 跳转到工作流可视化页面
        window.open(`/multi-workflow/${projectId}/${result.taskId}`, '_blank');
      }

      // 清空选择并关闭弹窗
      setProjectCodeGenPages(new Set());
      onProjectCodeGenClose();
      
    } catch (error) {
      console.error('启动项目级代码生成工作流失败:', error);
      addToast({
        title: '启动项目级代码生成工作流失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsStartingProjectCodeGen(false);
    }
  };

  // 下载文件功能
  const handleDownloadFile = (content: string, fileName: string, contentType: string) => {
    try {
      // 创建Blob对象
      const blob = new Blob([content], { type: contentType });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');

      link.href = url;
      link.download = fileName;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      addToast({
        title: `文件 ${fileName} 下载成功`,
        color: 'success',
      });
    } catch (error) {
      console.error('下载文件失败:', error);
      addToast({
        title: '下载失败，请重试',
        color: 'danger',
      });
    }
  };

  // 获取任务状态颜色
  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'processing': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'danger';
      case 'partial-failed': return 'warning';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  // 获取任务状态文本
  const getTaskStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'partial-failed': return '部分失败';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  // 统计选中的页面数和原型数
  const selectedPagesCount = selectedPages.size;
  const selectedPrototypesCount = pages.reduce((count, page) => {
    if (selectedPages.has(page.id)) {
      return count + page.prototypes.length;
    }

    return count;
  }, 0);
  const totalPagesCount = pages.length;
  const totalPrototypesCount = pages.reduce((sum, page) => sum + page.prototypes.length, 0);

  if (!project) {
    return <div>加载中...</div>;
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* 页面列表 */}
        <div>
          <Button
            color="default"
            startContent={<IconClock />}
            variant="light"
            onPress={toggleHistoryBubble}
          >
            转码历史
          </Button>
          <Button
            color="default"
            startContent={<IconPlay />}
            variant="light"
            onPress={() => navigate(`/project-preview/${projectId}?tab=results`)}
          >
            工作流历史
          </Button>
          {statusPollingInterval && (
            <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-sm text-blue-600 dark:text-blue-400">状态同步中</span>
            </div>
          )}
          
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">设计稿页面</h2>
            <div className="flex gap-2">
              <Button
                color="success"
                startContent={<IconPlay />}
                onPress={onProjectCodeGenOpen}
              >
                项目级代码生成
              </Button>
              {selectedPages.size > 0 && (
                <>
                  <Button
                    color="primary"
                    isDisabled={isTranscoding}
                    startContent={<IconPlay />}
                    onPress={handleStartTranscode}
                  >
                    开始转码 ({selectedPrototypesCount} 个原型)
                  </Button>
                  {selectedPages.size > 1 && (
                    <Button
                      color="secondary"
                      startContent={<IconPlay />}
                      onPress={handleStartMultiPageWorkflow}
                    >
                      启动多页面工作流 ({selectedPages.size} 个页面)
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
            </div>
          ) : pages.length === 0 ? (
            <Card>
              <CardBody className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  还没有设计稿页面
                </p>
                <Button color="primary" onPress={onOpen}>
                  添加第一个页面
                </Button>
              </CardBody>
            </Card>
          ) : (
      <div className="space-y-4">
        {pages.map((page) => (
                <Card key={page.id} className="hover:shadow-md transition-shadow">
                  <CardBody>
                    <div className="flex justify-between items-start mb-3">
              <div className="flex items-center gap-3">
                <Checkbox
                  isSelected={selectedPages.has(page.id)}
                  onValueChange={(checked) => {
                            const newSelected = new Set(selectedPages);

                    if (checked) {
                              newSelected.add(page.id);
                    } else {
                              newSelected.delete(page.id);
                    }
                            setSelectedPages(newSelected);
                  }}
                />
                <div>
                          <h3 className="font-semibold text-lg">{page.name}</h3>
                  {page.description && (
                            <p className="text-gray-600 dark:text-gray-400 text-sm">
                              {page.description}
                            </p>
                  )}
                </div>
              </div>
                      <div className="flex gap-2">
                <Button
                  size="sm"
                  startContent={<IconEdit />}
                  variant="light"
                          onPress={() => handleEditPage(page)}
                >
                          编辑
                </Button>
                <Button
                  color="primary"
                  size="sm"
                  startContent={<IconPlay />}
                  variant="light"
                          onPress={() => handleStartWorkflow(page)}
                >
                          启动工作流
                </Button>
                <Button
                  color="danger"
                  size="sm"
                  startContent={<IconDelete />}
                  variant="light"
                          onPress={() => handleDeletePage(page.id)}
                >
                          删除
                </Button>
              </div>
                    </div>

                    {/* 页面原型列表 */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {page.prototypes.map((prototype) => (
                        <div
                          key={prototype.id}
                          className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                        >
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">
                              原型 {prototype.prototypeName}
                            </span>
                          <Chip
                              color={getTaskStatusColor(prototype.status)}
                            size="sm"
                              variant="flat"
                          >
                              {getTaskStatusText(prototype.status)}
                          </Chip>
                        </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <div className="flex items-center justify-between">
                              <span>HTML: {prototype.htmlFileName}</span>
                              <Button
                                isIconOnly
                                className="min-w-unit-6 w-unit-6 h-unit-6"
                                color="primary"
                                size="sm"
                                title="下载HTML文件"
                                variant="light"
                                onPress={() => handleDownloadFile(
                                  prototype.htmlContent,
                                  prototype.htmlFileName,
                                  'text/html'
                                )}
                              >
                                <IconDownload className="w-3 h-3" />
                              </Button>
                            </div>
                            <div className="flex items-center justify-between">
                              <span>CSS: {prototype.cssFileName}</span>
                              <Button
                                isIconOnly
                                className="min-w-unit-6 w-unit-6 h-unit-6"
                                color="primary"
                                size="sm"
                                title="下载CSS文件"
                                variant="light"
                                onPress={() => handleDownloadFile(
                                  prototype.cssContent,
                                  prototype.cssFileName,
                                  'text/css'
                                )}
                              >
                                <IconDownload className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          {prototype.status === 'completed' && prototype.playgroundId && (
                            <Button
                              className="w-full"
                              color="primary"
                              size="sm"
                              startContent={<IconEye />}
                              variant="flat"
                              onPress={() => handleViewResult(prototype.playgroundId!)}
                            >
                              查看转码结果
                            </Button>
                          )}
                          </div>
                    ))}
                  </div>
            </CardBody>
          </Card>
        ))}
            </div>
          )}
        </div>

        {/* 转码历史 */}
        {showHistoryBubble && (
          <div 
            className={`draggable-bubble fixed z-40 w-96 max-h-96 overflow-hidden ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
            style={{
              right: `${bubblePosition.x}px`,
              bottom: `${bubblePosition.y}px`,
              transition: isDragging ? 'none' : 'all 0.2s ease-out',
            }}
          >
            <Card className="shadow-lg border">
              <CardHeader 
                className="pb-2 cursor-grab active:cursor-grabbing"
                onMouseDown={handleMouseDown}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <IconClock className="w-4 h-4" />
                    <span className="font-medium text-sm">转码历史</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      isIconOnly
                      className="min-w-6 w-6 h-6"
                      isLoading={historyLoading}
                      size="sm"
                      variant="flat"
                      onPress={loadTranscodeHistory}
                    >
                      <IconRefresh className="w-3 h-3" />
                    </Button>
                    <Button
                      isIconOnly
                      className="min-w-6 w-6 h-6"
                      size="sm"
                      variant="flat"
                      onPress={() => setShowHistoryBubble(false)}
                    >
                      ×
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardBody className="pt-0 max-h-80 overflow-y-auto">
                {transcodeHistory.length === 0 ? (
                  <div className="text-center py-6">
                    <IconClock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm text-gray-500">还没有转码历史</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {transcodeHistory.map((task) => (
                      <div key={task.id} className="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Chip
                              color={getTaskStatusColor(task.status)}
                              size="sm"
                              variant="flat"
                            >
                              {getTaskStatusText(task.status)}
                            </Chip>
                            <span className="text-xs text-gray-600">
                              {new Date(task.created).toLocaleString('zh-CN')}
                            </span>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium">进度</span>
                            <span className="text-xs text-gray-600">
                              {task.items.filter(item => item.status === 'completed').length}/
                              {task.items.length}
                            </span>
                          </div>
                          <Progress
                            color={getTaskStatusColor(task.status)}
                            size="sm"
                            value={task.progress}
                          />
                        </div>
                        
                        {/* 任务条目简要信息 */}
                        <div className="mt-2 space-y-1">
                          {task.items.slice(0, 2).map((item) => ( // 只显示前2个
                            <div key={item.id} className="flex items-center justify-between text-xs">
                              <span className="truncate flex-1">
                                {item.prototype.designPage.name} - {item.prototype.prototypeName}
                              </span>
                              <div className="flex items-center gap-1 ml-2">
                                <Chip
                                  className="text-xs h-4"
                                  color={getTaskStatusColor(item.status)}
                                  size="sm"
                                  variant="flat"
                                >
                                  {getTaskStatusText(item.status)}
                                </Chip>
                                {item.playgroundId && item.status === 'completed' && (
                                  <Button
                                    className="h-4 min-h-0 px-1 text-xs cursor-default"
                                    color="primary"
                                    size="sm"
                                    variant="light"
                                    onPress={() => handleViewResult(item.playgroundId!)}
                                  >
                                    查看
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                          {task.items.length > 2 && (
                            <div className="text-xs text-gray-500 text-center">
                              还有 {task.items.length - 2} 个原型...
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardBody>
            </Card>
          </div>
        )}
      </div>

      {/* 添加页面模态框 */}
      <Modal isOpen={isOpen} scrollBehavior="inside" size="4xl" onClose={onClose}>
        <ModalContent>
          <ModalHeader>添加设计稿页面</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                isRequired
                label="页面名称"
                placeholder="请输入页面名称"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
              
              <Textarea
                label="页面描述"
                placeholder="请输入页面描述（可选）"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />

              <Divider />
              
              <div className="flex justify-between items-center">
                <h4 className="text-lg font-semibold">页面原型</h4>
                <Button
                  color="primary"
                  size="sm"
                  startContent={<IconPlus />}
                  onPress={addVersion}
                >
                  添加原型
                </Button>
              </div>
              
              <div className="space-y-4">
                {formData.prototypes.map((prototype, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="font-semibold">原型 {prototype.prototypeName}</h5>
                      {formData.prototypes.length > 1 ? (
                        <Button
                          isIconOnly
                          color="danger"
                          size="sm"
                          variant="light"
                          onPress={() => removeVersion(index)}
                        >
                          <IconMinus />
                        </Button>
                      ) : editingPage ? (
                        <Button
                          isIconOnly
                          color="danger"
                          size="sm"
                          title="删除后该页面将没有任何原型"
                          variant="light"
                          onPress={() => removeVersion(index)}
                        >
                          <IconMinus />
                        </Button>
                      ) : null}
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <Input
                        label="原型名称"
                        size="sm"
                        value={prototype.prototypeName}
                        onChange={(e) => updateVersion(index, 'prototypeName', e.target.value)}
                      />
                      <div />
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">HTML文件</label>
                        <input
                          accept=".html,.htm"
                          className="w-full p-2 border border-default-300 rounded-lg text-sm"
                          type="file"
                          onChange={(e) => updateVersion(index, 'htmlFile', e.target.files?.[0] || null)}
                        />
                        {prototype.htmlFile && (
                          <p className="text-xs text-default-500 mt-1">
                            已选择: {prototype.htmlFile.name}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">CSS文件</label>
                        <input
                          accept=".css"
                          className="w-full p-2 border border-default-300 rounded-lg text-sm"
                          type="file"
                          onChange={(e) => updateVersion(index, 'cssFile', e.target.files?.[0] || null)}
                        />
                        {prototype.cssFile && (
                          <p className="text-xs text-default-500 mt-1">
                            已选择: {prototype.cssFile.name}
                          </p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              
              <div className="text-sm text-default-500">
                <p>每个原型需要上传对应的HTML和CSS文件。您可以为同一个页面创建多个设计原型（如A、B、C）进行对比转码。</p>
                <p className="text-danger-500 mt-1">注意：新建页面时至少需要添加一个原型。</p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              取消
            </Button>
            <Button color="primary" onPress={handleCreatePage}>
              添加页面
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 编辑页面模态框（完整版，支持原型编辑） */}
      <Modal isOpen={isEditOpen} scrollBehavior="inside" size="4xl" onClose={onEditClose}>
        <ModalContent>
          <ModalHeader>编辑设计稿页面</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                isRequired
                label="页面名称"
                placeholder="请输入页面名称"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
              />
              
              <Textarea
                label="页面描述"
                placeholder="请输入页面描述（可选）"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
              />

              <Divider />
              
              <div className="flex justify-between items-center">
                <h4 className="text-lg font-semibold">页面原型</h4>
                <Button
                  color="primary"
                  size="sm"
                  startContent={<IconPlus />}
                  onPress={addVersion}
                >
                  添加原型
                </Button>
              </div>
              
              <div className="space-y-4">
                {formData.prototypes.map((prototype, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center gap-2">
                        <h5 className="font-semibold">原型 {prototype.prototypeName}</h5>
                        {prototype.isExisting && (
                          <Chip color="default" size="sm" variant="flat">
                            现有原型
                          </Chip>
                        )}
                      </div>
                      {formData.prototypes.length > 1 ? (
                        <Button
                          isIconOnly
                          color="danger"
                          size="sm"
                          variant="light"
                          onPress={() => removeVersion(index)}
                        >
                          <IconMinus />
                        </Button>
                      ) : editingPage ? (
                        <Button
                          isIconOnly
                          color="danger"
                          size="sm"
                          title="删除后该页面将没有任何原型"
                          variant="light"
                          onPress={() => removeVersion(index)}
                        >
                          <IconMinus />
                        </Button>
                      ) : null}
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <Input
                        label="原型名称"
                        size="sm"
                        value={prototype.prototypeName}
                        onChange={(e) => updateVersion(index, 'prototypeName', e.target.value)}
                      />
                      <div />
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          HTML文件 {prototype.isExisting && '(可选择新文件替换)'}
                        </label>
                        <input
                          accept=".html,.htm"
                          className="w-full p-2 border border-default-300 rounded-lg text-sm"
                          type="file"
                          onChange={(e) => updateVersion(index, 'htmlFile', e.target.files?.[0] || null)}
                        />
                        {prototype.htmlFile ? (
                          <p className="text-xs text-success-600 mt-1">
                            新文件: {prototype.htmlFile.name}
                          </p>
                        ) : prototype.isExisting && prototype.currentHtmlFileName ? (
                          <p className="text-xs text-default-500 mt-1">
                            当前文件: {prototype.currentHtmlFileName}
                          </p>
                        ) : (
                          <p className="text-xs text-danger-500 mt-1">
                            请选择HTML文件
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium mb-1">
                          CSS文件 {prototype.isExisting && '(可选择新文件替换)'}
                        </label>
                        <input
                          accept=".css"
                          className="w-full p-2 border border-default-300 rounded-lg text-sm"
                          type="file"
                          onChange={(e) => updateVersion(index, 'cssFile', e.target.files?.[0] || null)}
                        />
                        {prototype.cssFile ? (
                          <p className="text-xs text-success-600 mt-1">
                            新文件: {prototype.cssFile.name}
                          </p>
                        ) : prototype.isExisting && prototype.currentCssFileName ? (
                          <p className="text-xs text-default-500 mt-1">
                            当前文件: {prototype.currentCssFileName}
                          </p>
                        ) : (
                          <p className="text-xs text-danger-500 mt-1">
                            请选择CSS文件
                          </p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
              
              <div className="text-sm text-default-500">
                <p>
                  对于现有原型，如果不上传新文件，将保持原有文件不变。
                  新添加的原型必须上传HTML和CSS文件。
                </p>
                <p className="text-warning-600 mt-1">
                  注意：删除所有原型后，该页面将无法进行转码操作。
                </p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onEditClose}>
              取消
            </Button>
            <Button color="primary" onPress={handleUpdatePage}>
              保存更改
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 项目级代码生成弹窗 */}
      <Modal
        isOpen={isProjectCodeGenOpen}
        size="4xl"
        onClose={onProjectCodeGenClose}
      >
        <ModalContent>
          <ModalHeader>项目级代码生成</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                请选择要生成代码的页面，系统将根据选择的页面数量自动调用对应的工作流接口。
              </div>
              
              <div className="space-y-2">
                <h3 className="font-medium">可选页面列表：</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {pages.map((page) => (
                    <div key={page.id} className="flex items-center space-x-2">
                      <Checkbox
                        isSelected={projectCodeGenPages.has(page.id)}
                        onValueChange={(isSelected) => {
                          const newSet = new Set(projectCodeGenPages);

                          if (isSelected) {
                            newSet.add(page.id);
                          } else {
                            newSet.delete(page.id);
                          }
                          setProjectCodeGenPages(newSet);
                        }}
                      />
                      <span className="text-sm">{page.name}</span>
                      <span className="text-xs text-gray-500">
                        ({page.prototypes.length} 个原型)
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {projectCodeGenPages.size > 0 && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="text-sm">
                    <strong>已选择：</strong> {projectCodeGenPages.size} 个页面
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {projectCodeGenPages.size === 1 
                      ? '将调用单页面代码生成工作流' 
                      : '将调用多页面代码生成工作流'
                    }
                  </div>
                </div>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={onProjectCodeGenClose}
            >
              取消
            </Button>
            <Button
              color="primary"
              isDisabled={projectCodeGenPages.size === 0 || isStartingProjectCodeGen}
              isLoading={isStartingProjectCodeGen}
              startContent={!isStartingProjectCodeGen && <IconPlay />}
              onPress={handleStartProjectCodeGenWorkflow}
            >
              {isStartingProjectCodeGen ? '启动中...' : '启动生码工作流'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 转码任务进度气泡 */}
      {currentTask && !isTranscodeOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <Card className="w-80 shadow-lg border">
            <CardBody className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {isTranscoding ? (
                    <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" />
                  ) : (
                    <div className={`w-2 h-2 rounded-full ${
                      currentTask.status === 'completed' ? 'bg-green-500' : 
                      currentTask.status === 'failed' ? 'bg-red-500' : 
                      currentTask.status === 'partial-failed' ? 'bg-yellow-500' : 
                      'bg-gray-500'
                    }`} />
                  )}
                  <span className="font-medium text-sm">
                    {isTranscoding ? '转码进行中' : `转码${getTaskStatusText(currentTask.status)}`}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    isIconOnly
                    className="min-w-6 w-6 h-6"
                    size="sm"
                    variant="flat"
                    onPress={handleReopenTranscodeProgress}
                  >
                    <IconEye className="w-3 h-3" />
                  </Button>
                  {isTranscoding ? (
                    <Button
                      isIconOnly
                      className="min-w-6 w-6 h-6"
                      color="danger"
                      size="sm"
                      variant="flat"
                      onPress={handleCancelTranscode}
                    >
                      ×
                    </Button>
                  ) : (
                    <Button
                      isIconOnly
                      className="min-w-6 w-6 h-6"
                      color="default"
                      size="sm"
                      variant="flat"
                      onPress={() => setCurrentTask(null)}
                    >
                      ×
                    </Button>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">总进度</span>
                  <span className="text-xs text-gray-600">
                    {currentTask.items.filter(item => item.status === 'completed').length}/
                    {currentTask.items.length}
                  </span>
                </div>
                <Progress
                  color={getTaskStatusColor(currentTask.status)}
                  size="sm"
                  value={currentTask.progress}
                />
              </div>
              
              {/* 最新处理的原型信息 */}
              {(() => {
                const processingItem = currentTask.items.find(item => item.status === 'processing');
                const latestItem = processingItem || currentTask.items[currentTask.items.length - 1];

                return latestItem && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="text-xs text-gray-600 mb-1">
                      {latestItem.prototype.designPage.name} - {latestItem.prototype.prototypeName}
                    </div>
                    <div className="text-xs text-gray-500">
                      {latestItem.stage || getTaskStatusText(latestItem.status)}
                    </div>
                  </div>
                );
              })()}
            </CardBody>
          </Card>
        </div>
      )}

      {/* 转码任务详细进度对话框 */}
      <Modal 
        closeButton 
        isOpen={isTranscodeOpen} 
        size="2xl"
        onClose={handleCloseTranscodeModal}
      >
        <ModalContent>
          <ModalHeader className="flex items-center justify-between pr-12">
            <span>转码任务进度</span>
            {isTranscoding && (
              <Chip
                className="ml-4"
                color="primary"
                size="sm"
                startContent={<div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" />}
                variant="flat"
              >
                进行中
              </Chip>
            )}
          </ModalHeader>
          <ModalBody>
            {currentTask && (
              <div className="space-y-4">
                {/* 任务概览 */}
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">任务状态</span>
                    <Chip
                      color={getTaskStatusColor(currentTask.status)}
                      size="sm"
                      variant="flat"
                    >
                      {getTaskStatusText(currentTask.status)}
                    </Chip>
                  </div>
                  <Progress
                    className="mb-2" 
                    color={getTaskStatusColor(currentTask.status)}
                    value={currentTask.progress}
                  />
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    总进度: {currentTask.progress}% ({currentTask.items.filter(item => item.status === 'completed').length}/{currentTask.items.length} 个原型完成)
                  </div>
                </div>

                {/* 任务条目详情 */}
                <div className="space-y-3">
                  <div className="font-medium text-sm">原型处理详情</div>
                  {currentTask.items.map((item) => (
                    <div key={item.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-sm">
                          {item.prototype.designPage.name} - {item.prototype.prototypeName}
                        </div>
                        <div className="flex items-center gap-2">
                          <Chip
                            color={getTaskStatusColor(item.status)}
                            size="sm"
                            variant="flat"
                          >
                            {getTaskStatusText(item.status)}
                          </Chip>
                          {item.playgroundId && item.status === 'completed' && (
                            <Button
                              color="primary"
                              size="sm"
                              variant="flat"
                              onPress={() => handleViewResult(item.playgroundId!)}
                            >
                              查看结果
                            </Button>
                          )}
                        </div>
                      </div>
                      <Progress
                        className="mb-2"
                        color={getTaskStatusColor(item.status)}
                        size="sm"
                        value={item.progress}
                      />
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {item.stage}
                        {item.error && (
                          <div className="text-red-600 dark:text-red-400 mt-1">
                            错误: {item.error}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light" 
              onPress={handleCloseTranscodeModal}
            >
              关闭
            </Button>
            {isTranscoding && (
              <Button
                color="danger"
                variant="flat"
                onPress={handleCancelTranscode}
              >
                取消任务
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
} 