import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Progress,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Divider,
} from '@heroui/react';
import {
  getProjectTranscodeTasks,
  getTranscodeTask,
} from '@/apis';
import IconRefresh from '~icons/mdi/refresh';
import IconEye from '~icons/mdi/eye';
import IconClock from '~icons/mdi/clock';

interface TranscodeTask {
  id: string;
  status: string;
  progress: number;
  model: string;
  enableAutoIteration: boolean;
  enableStepByStep: boolean;
  created: string;
  updated: string;
  items: TranscodeTaskItem[];
}

interface TranscodeTaskItem {
  id: string;
  status: string;
  progress: number;
  stage: string;
  playgroundId?: string;
  error?: string;
  prototype: {
    id: string;
    prototypeName: string;
    designPage: {
      name: string;
    };
  };
}

interface Props {
  projectId: string;
}

export function TranscodeHistoryView({ projectId }: Props) {
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [tasks, setTasks] = useState<TranscodeTask[]>([]);
  const [selectedTask, setSelectedTask] = useState<TranscodeTask | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadTasks();
    
    // 设置自动刷新 - 每10秒刷新一次
    const interval = setInterval(() => {
      loadTasks();
    }, 10000);

    // 清理定时器
    return () => {
      clearInterval(interval);
    };
  }, [projectId]);

  const loadTasks = async () => {
    setLoading(true);
    try {
      const tasksData = await getProjectTranscodeTasks({ projectId });
      setTasks(tasksData || []);
    } catch (error) {
      console.error('加载转码历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewTaskDetail = async (taskId: string) => {
    try {
      const taskDetail = await getTranscodeTask({ taskId });
      setSelectedTask(taskDetail);
      onOpen();
    } catch (error) {
      console.error('获取任务详情失败:', error);
    }
  };

  const handleViewResult = (playgroundId: string) => {
    navigate(`/chat/${playgroundId}`);
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'processing': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'danger';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getTaskStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">转码历史</h3>
        <Button
          size="sm"
          variant="flat"
          startContent={<IconRefresh />}
          onPress={loadTasks}
          isLoading={loading}
        >
          刷新
        </Button>
      </div>

      {tasks.length === 0 ? (
        <Card>
          <CardBody className="text-center py-8">
            <IconClock className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-500">还没有转码历史</p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-3">
          {tasks.map((task) => (
            <Card key={task.id} className="hover:shadow-md transition-shadow">
              <CardBody className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <Chip
                      color={getTaskStatusColor(task.status)}
                      variant="flat"
                      size="sm"
                    >
                      {getTaskStatusText(task.status)}
                    </Chip>
                    <span className="text-sm text-gray-600">
                      {formatDateTime(task.created)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="flat"
                      startContent={<IconEye />}
                      onPress={() => handleViewTaskDetail(task.id)}
                    >
                      查看详情
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">转码进度</span>
                    <span className="text-sm text-gray-600">
                      {task.items.filter(item => item.status === 'completed').length}/
                      {task.items.length} 个原型完成
                    </span>
                  </div>
                  <Progress
                    value={task.progress}
                    color={getTaskStatusColor(task.status)}
                    size="sm"
                  />
                </div>

                <div className="mt-3 flex flex-wrap gap-2">
                  {task.items.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center gap-2 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs"
                    >
                      <span>{item.prototype.designPage.name} - {item.prototype.prototypeName}</span>
                      <Chip
                        color={getTaskStatusColor(item.status)}
                        size="sm"
                        variant="flat"
                      >
                        {getTaskStatusText(item.status)}
                      </Chip>
                      {item.playgroundId && (
                        <Button
                          size="sm"
                          color="primary"
                          variant="light"
                          onPress={() => handleViewResult(item.playgroundId!)}
                          className="h-5 min-h-0 px-2 text-xs"
                        >
                          查看
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}

      {/* 任务详情模态框 */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            转码任务详情
          </ModalHeader>
          <ModalBody>
            {selectedTask && (
              <div className="space-y-4">
                {/* 任务概览 */}
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">任务状态:</span>
                      <Chip
                        color={getTaskStatusColor(selectedTask.status)}
                        variant="flat"
                        size="sm"
                        className="ml-2"
                      >
                        {getTaskStatusText(selectedTask.status)}
                      </Chip>
                    </div>
                    <div>
                      <span className="font-medium">使用模型:</span>
                      <span className="ml-2">{selectedTask.model}</span>
                    </div>
                    <div>
                      <span className="font-medium">创建时间:</span>
                      <span className="ml-2">{formatDateTime(selectedTask.created)}</span>
                    </div>
                    <div>
                      <span className="font-medium">更新时间:</span>
                      <span className="ml-2">{formatDateTime(selectedTask.updated)}</span>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">总体进度</span>
                      <span className="text-sm text-gray-600">
                        {selectedTask.progress}%
                      </span>
                    </div>
                    <Progress
                      value={selectedTask.progress}
                      color={getTaskStatusColor(selectedTask.status)}
                    />
                  </div>
                </div>

                <Divider />

                {/* 任务条目详情 */}
                <div className="space-y-2">
                  <h4 className="font-medium">原型处理详情</h4>
                  {selectedTask.items.map((item) => (
                    <div key={item.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">
                          {item.prototype.designPage.name} - {item.prototype.prototypeName}
                        </span>
                        <div className="flex items-center gap-2">
                          <Chip
                            color={getTaskStatusColor(item.status)}
                            variant="flat"
                            size="sm"
                          >
                            {getTaskStatusText(item.status)}
                          </Chip>
                          {item.playgroundId && (
                            <Button
                              size="sm"
                              color="primary"
                              variant="flat"
                              onPress={() => handleViewResult(item.playgroundId!)}
                            >
                              查看结果
                            </Button>
                          )}
                        </div>
                      </div>
                      <Progress
                        value={item.progress}
                        color={getTaskStatusColor(item.status)}
                        size="sm"
                        className="mb-1"
                      />
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {item.stage}
                        {item.error && (
                          <div className="text-red-600 dark:text-red-400 mt-1">
                            错误: {item.error}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              关闭
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
} 