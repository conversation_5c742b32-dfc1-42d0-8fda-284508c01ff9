import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Input,
  Textarea,
} from '@heroui/react';
import { useEffect, useState } from 'react';
import { DesignPage } from '@/apis';

interface EditPageModalProps {
  isOpen: boolean;
  pageData: DesignPage
  onClose: () => void;
  onSubmitEditPage: (pageData: {name: string, description: string}) => void;
}

export function EditPageModal({ isOpen, pageData, onClose, onSubmitEditPage }: EditPageModalProps) {
  const [pageName, setPageName] = useState('');
  const [pageDescription, setPageDescription] = useState('');

  useEffect(()=>{
    setPageName(pageData?.name);
    setPageDescription(pageData?.description || '');
  }, [pageData?.name, pageData?.description])
  

  const handleSubmit = () => {
    if (!pageName) {
      return;
    }

    onSubmitEditPage({
      name: pageName,
      description: pageDescription,
    });

    // 重置表单
    setPageName('');
    setPageDescription('');
    onClose();
  };

  const handleCancel = () => {
    // 重置表单
    setPageName('');
    setPageDescription('');
    onClose();
  };

  return (
    <Modal 
      classNames={{
        base: "rounded-md",
        backdrop: "rounded-md",
      }} 
      isOpen={isOpen} 
      size="md"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">编辑页面</h3>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">页面名称</label>
              <Input
                classNames={{
                  inputWrapper: "rounded-md",
                }}
                placeholder="请输入页面名称"
                value={pageName}
                variant="bordered"
                onChange={(e) => setPageName(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">页面描述</label>
              <Textarea
                classNames={{
                  inputWrapper: "rounded-md",
                }}
                minRows={3}
                placeholder="请输入页面描述（可选）"
                value={pageDescription}
                variant="bordered"
                onChange={(e) => setPageDescription(e.target.value)}
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button className="rounded-md" color="default" variant="light" onPress={handleCancel}>
            取消
          </Button>
          <Button
            className="bg-black text-white hover:bg-gray-800 rounded-md"
            color="primary"
            isDisabled={!pageName}
            onPress={handleSubmit}
          >
            更新页面
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}