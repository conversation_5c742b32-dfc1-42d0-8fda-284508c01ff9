import {
  Button,
  useDisclosure,
  Modal,
  Modal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  ModalFooter,
} from '@heroui/react';
import { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectSettingsDrawer } from './ProjectSettingsDrawer';
import { DesignProject } from '@/apis';
import { isProjectConfigured, isValidGitLabSshUrl } from '@/utils/projectUtils';
import IconAlertCircle from '~icons/mdi/alert-circle-outline';

interface ProjectConfigCheckProps {
  project: DesignProject | undefined;
  onProjectUpdated: () => void;
}

export function ProjectConfigCheck({ project, onProjectUpdated }: ProjectConfigCheckProps) {
  const navigate = useNavigate();
  const { isOpen: isConfigModalOpen, onOpen: onConfigModalOpen, onClose: onConfigModalClose } = useDisclosure();
  const { isOpen: isSettingsDrawerOpen, onOpen: onSettingsDrawerOpen, onClose: onSettingsDrawerClose } = useDisclosure();
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const isInitialMount = useRef(true);

  useEffect(() => {
    if (project) {
      setIsUpdating(false); // Reset updating flag when new project data arrives
      const missing: string[] = [];
      
      if (!isProjectConfigured(project)) {
        if (!project.gitUrl) {
          missing.push('GitLab 仓库地址');
        } else {
          // 校验是否为SSH协议格式
          if (!isValidGitLabSshUrl(project.gitUrl)) {
            missing.push('GitLab 仓库地址：必须使用Git协议格式地址');
          }
        }
        if (!project.gitBranch) {
          missing.push('GitLab 开发分支');
        }
        if (!project.gitCodeDir) {
          missing.push('GitLab 开发目录');
        }
        if (!project.lanhuProjectId) {
          missing.push('蓝湖项目 ID');
        }
      }

      if (missing.length > 0) {
        setMissingFields(missing);
        onConfigModalOpen();
      } else {
        setMissingFields([]);
      }
    }
  }, [project, onConfigModalOpen]);

  // Re-check config when the settings drawer is closed, in case the user cancelled.
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;

      return;
    }

    if (!isSettingsDrawerOpen && !isUpdating && project) {
      if (!isProjectConfigured(project)) {
        onConfigModalOpen(); // Re-open the modal if config is still missing
      }
    }
  }, [isSettingsDrawerOpen, isUpdating, project, onConfigModalOpen]);

  const handleOpenSettings = () => {
    onConfigModalClose();
    onSettingsDrawerOpen();
  };

  const handleProjectUpdated = () => {
    setIsUpdating(true);
    onSettingsDrawerClose();
    onProjectUpdated();
  }

  return (
    <>
      <Modal hideCloseButton isKeyboardDismissDisabled isDismissable={false} isOpen={isConfigModalOpen} onClose={onConfigModalClose}>
        <ModalContent className="dark:bg-zinc-900 rounded-md">
          <ModalHeader>
            <div className="flex items-center gap-2 font-semibold">
              <IconAlertCircle className="w-5 h-5 text-red-500" />
              <span>项目配置不完整</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="text-sm">
              <p className="mb-3 text-zinc-600 dark:text-zinc-400">为了保证功能正常运行，请先完善以下缺失的配置项：</p>
              <ul className="space-y-2 p-3 bg-zinc-100 dark:bg-zinc-800 rounded-md border dark:border-zinc-700">
                {missingFields.map(field => (
                  <li key={field} className="flex items-center gap-2">
                    <span className="w-1.5 h-1.5 bg-red-500 rounded-full shrink-0" />
                    <span className="font-medium text-zinc-800 dark:text-zinc-200">{field}</span>
                  </li>
                ))}
              </ul>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button className="rounded-md" size="sm" variant="light" onPress={() => navigate('/project-list')}>
             返回项目列表
            </Button>
            <Button className="rounded-md" size="sm" variant="light" onPress={() => navigate(`/prototype/${project?.id}`)}>
              原型代码生成
            </Button>
            <Button className="rounded-md" color="primary" size="sm" onPress={handleOpenSettings}>
              完善配置
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {project && (
        <ProjectSettingsDrawer
          isOpen={isSettingsDrawerOpen}
          project={project}
          onClose={onSettingsDrawerClose}
          onProjectUpdated={handleProjectUpdated}
        />
      )}
    </>
  );
} 