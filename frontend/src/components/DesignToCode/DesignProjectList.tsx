import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Chip,
  useDisclosure,
  addToast,
} from '@heroui/react';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectSettingsDrawer } from './ProjectSettingsDrawer';
import {
  DesignProject,
  DesignPage,
  listDesignProjects,
  deleteDesignProject,
} from '@/apis';
import IconDelete from '~icons/mdi/delete';
import IconEye from '~icons/mdi/eye';
import IconFolder from '~icons/mdi/folder';
import IconEdit from '~icons/mdi/pencil';
import IconPlus from '~icons/mdi/plus';

export function DesignProjectList() {
  const navigate = useNavigate();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();

  const [projects, setProjects] = useState<DesignProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingProject, setEditingProject] = useState<DesignProject | null>(null);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      // const data = await listDesignProjects({ user: userInfo?.username });
      const data = await listDesignProjects();

      setProjects(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('加载设计稿转码工程失败:', error);
      addToast({
        title: '加载设计稿转码工程失败',
        color: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditProject = (project: DesignProject) => {
    setEditingProject(project);
    onEditOpen();
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!confirm('确定要删除这个设计稿转码工程吗？')) return;

    try {
      await deleteDesignProject({ id: projectId });
      addToast({
        title: '工程删除成功',
        color: 'success',
      });
      loadProjects();
    } catch (error) {
      console.error('删除工程失败:', error);
      addToast({
        title: '删除失败，请重试',
        color: 'danger',
      });
    }
  };

  const handleEnterProject = (projectId: string) => {
    navigate(`/project/${projectId}`);
  };

  const getProjectStats = (project: DesignProject) => {
    const pageCount = project.pages?.length || 0;
    const prototypeCount = project.pages?.reduce((total, page) => total + (page.prototypes?.length || 0), 0) || 0;
    const completedCount =
      project.pages?.reduce(
        (total, page) => total + (page.prototypes?.filter((p) => p.status === 'completed').length || 0),
        0,
      ) || 0;

    return { pageCount, prototypeCount, completedCount };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'failed':
        return '失败';
      default:
        return '待处理';
    }
  };

  const getPageOverallStatus = (page: DesignPage): string => {
    if (!page.prototypes || page.prototypes.length === 0) {
      return 'pending';
    }

    const statuses = page.prototypes.map((p: any) => p.status);

    // 如果有任何原型失败，整体为失败
    if (statuses.includes('failed')) {
      return 'failed';
    }

    // 如果有任何原型在处理中，整体为处理中
    if (statuses.includes('processing')) {
      return 'processing';
    }

    // 如果所有原型都完成，整体为完成
    if (statuses.every((status: string) => status === 'completed')) {
      return 'completed';
    }

    // 否则为待处理
    return 'pending';
  };

  return (
    <div>
      {/* 顶部操作栏 */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">设计稿转码工程</h2>
        <Button color="primary" startContent={<IconPlus />} onPress={onOpen}>
          新建工程
        </Button>
      </div>

      {/* 工程列表 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <Card key={project.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex justify-between">
              <div className="flex items-center gap-2">
                <IconFolder className="text-blue-500" />
                <h3 className="font-semibold">{project.name}</h3>
              </div>
              <div className="flex gap-1">
                <Button isIconOnly color="primary" size="sm" variant="light" onPress={() => handleEditProject(project)}>
                  <IconEdit />
                </Button>
                <Button
                  isIconOnly
                  color="danger"
                  size="sm"
                  variant="light"
                  onPress={() => handleDeleteProject(project.id)}
                >
                  <IconDelete />
                </Button>
              </div>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-default-600 mb-3">{project.description}</p>

              {project.pages && project.pages.length > 0 && (
                <div className="mb-3">
                  <p className="text-sm text-default-500 mb-2">页面: {project.pages.length} 个</p>
                  <div className="flex flex-wrap gap-1">
                    {project.pages.slice(0, 3).map((page) => (
                      <Chip key={page.id} color={getStatusColor(getPageOverallStatus(page))} size="sm" variant="flat">
                        {getStatusText(getPageOverallStatus(page))}
                      </Chip>
                    ))}
                    {project.pages.length > 3 && (
                      <Chip size="sm" variant="flat">
                        +{project.pages.length - 3}
                      </Chip>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center">
                <p className="text-xs text-default-400">{new Date(project.created).toLocaleDateString()}</p>
                <Button
                  size="sm"
                  startContent={<IconEye />}
                  variant="flat"
                  onPress={() => handleEnterProject(project.id)}
                >
                  查看详情
                </Button>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {projects.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-default-400 mb-4">还没有设计稿转码工程</p>
          <Button color="primary" startContent={<IconPlus />} onPress={onOpen}>
            创建第一个工程
          </Button>
        </div>
      )}

      {/* 新建工程抽屉 */}
      <ProjectSettingsDrawer
        isOpen={isOpen}
        mode="create"
        onClose={onClose}
        onProjectUpdated={loadProjects}
      />

      {/* 编辑工程抽屉 */}
      <ProjectSettingsDrawer
        isOpen={isEditOpen}
        mode="edit"
        project={editingProject || undefined}
        onClose={onEditClose}
        onProjectUpdated={loadProjects}
      />
    </div>
  );
}
