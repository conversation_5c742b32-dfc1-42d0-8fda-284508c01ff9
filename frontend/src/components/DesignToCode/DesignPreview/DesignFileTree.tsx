import { Tree, TreeDataNode } from 'antd';
import { useState, useMemo } from 'react';
import { DesignProject } from '@/apis';
import IconCode from '~icons/mdi/code-tags';
import IconFolder from '~icons/mdi/folder';
import IconImage from '~icons/mdi/image';
import IconWeb from '~icons/mdi/web';

interface ExtendedTreeDataNode extends TreeDataNode {
  type?: string;
  children?: ExtendedTreeDataNode[];
  segment?: any;
  sliceData?: any;
  nodeData?: any;
  renderType?: string;
}

// 节点类型枚举
export enum NodeType {
  Page = 'page',
  DEFAULT = 'default',
  Original = 'original',
  Html = 'html',
  Css = 'css',
  Img = 'img',
  Sliced = 'sliced',
}

interface Props {
  project: DesignProject | undefined;
  onNodeSelect?: (nodeInfo: { renderType: string; type: string; id: string; data?: any }) => void;
}

export const DesignFileTree = ({ project, onNodeSelect }: Props) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  // 生成树形数据：页面 -> 原型 -> 资产目录 -> 具体文件
  const treeData: ExtendedTreeDataNode[] = useMemo(() => {
    if (!project || !project.pages) return [];

    // 第一层：页面级别
    return project.pages.map((page) => ({
      title: page.name,
      key: `page-${page.id}`,
      type: NodeType.Page,
      renderType: NodeType.Page,
      icon: <IconFolder className="text-gray-500" />,
      ...page,
      children:
        page.prototypes?.map((prototype) => {
          // 分离 slicedAssets 和其他字段
          const { slicedAssets, ...otherFields } = prototype;

          // 第二层：原型级别
          const prototypeChildren: ExtendedTreeDataNode[] = [];

          // // HTML 文件
          // if (otherFields.htmlContent && otherFields.htmlFileName) {
          //   prototypeChildren.push({
          //     title: otherFields.htmlFileName,
          //     key: `html-${prototype.id}`,
          //     type: NodeType.Html,
          //     renderType: NodeType.Html,
          //     icon: <IconWeb className="text-orange-500" />,
          //     isLeaf: true,
          //     nodeData: prototype,
          //   });
          // }

          // // CSS 文件
          // if (otherFields.cssContent && otherFields.cssFileName) {
          //   prototypeChildren.push({
          //     title: otherFields.cssFileName,
          //     key: `css-${prototype.id}`,
          //     type: NodeType.Css,
          //     renderType: NodeType.Css,
          //     icon: <IconStylesheet className="text-blue-500" />,
          //     isLeaf: true,
          //     nodeData: prototype,
          //   });
          // }
          // // 图片文件
          // if (otherFields.imgFileName && otherFields.imgFileLink) {
          //   prototypeChildren.push({
          //     title: otherFields.imgFileName,
          //     key: `img-${prototype.id}`,
          //     type: NodeType.Img,
          //     renderType: NodeType.Img,
          //     icon: <IconImage className="text-green-500" />,
          //     isLeaf: true,
          //     nodeData: prototype,
          //   });
          // }

          // 智能切图目录
          if (slicedAssets) {
            const slicedChildren: ExtendedTreeDataNode[] = [];
            const segmentChildren: ExtendedTreeDataNode[] = [];

            // HTML 文件（来自 slicedAssets）
            if (slicedAssets.html?.name) {
              slicedChildren.push({
                title: slicedAssets.html.name,
                key: `sliced-html-${prototype.id}`,
                type: NodeType.Sliced,
                renderType: NodeType.Html,
                icon: <IconWeb className="text-orange-500" />,
                isLeaf: true,
                sliceData: {
                  htmlContent: slicedAssets.html.content,
                },
              });
            }

            // 完整图片
            if (slicedAssets.fullImgUrl) {
              slicedChildren.push({
                title: '原始图片',
                key: `sliced-full-img-${prototype.id}`,
                type: NodeType.Sliced,
                renderType: NodeType.Img,
                icon: <IconImage className="text-green-500" />,
                isLeaf: true,
                sliceData: slicedAssets,
              });
            }

            // 切片组件
            if (slicedAssets.segment && slicedAssets.segment.length > 0) {
              slicedAssets.segment.forEach((segment, index: number) => {
                segmentChildren.push({
                  title: segment.name,
                  key: `sliced-segment-${prototype.id}-${segment.id || index}`,
                  type: NodeType.Sliced,
                  renderType: NodeType.Img,
                  icon: <IconImage className="text-green-500" />,
                  isLeaf: true,
                  sliceData: segment,
                });
              });
            }
          }

          return {
            title: prototype.prototypeName,
            key: `prototype-${prototype.id}`,
            type: NodeType.DEFAULT,
            renderType: NodeType.Html, // 设置为 Html 类型，与 HTML 节点保持一致
            icon: <IconCode className="text-gray-500" />,
            nodeData: prototype, // 添加原型数据
            children: prototypeChildren,
          };
        }) || [],
    }));
  }, [project]);

  // 自动展开所有节点
  // useMemo(() => {
  //   if (project && project.pages && project.pages.length > 0) {
  //     const allKeys: React.Key[] = [];

  //     project.pages.forEach((page) => {
  //       // 添加页面节点
  //       allKeys.push(`page-${page.id}`);

  //       page.prototypes?.forEach((prototype) => {
  //         // 添加原型节点
  //         allKeys.push(`prototype-${prototype.id}`);

  //         // 添加智能切图目录节点
  //         if (prototype.slicedAssets) {
  //           allKeys.push(`sliced-${prototype.id}`);
  //         }
  //       });
  //     });

  //     setExpandedKeys(allKeys);
  //   }
  // }, [project]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const onSelect = (selectedKeysValue: React.Key[], info: any) => {
    setSelectedKeys(selectedKeysValue);

    // 处理节点选择事件
    const selectedKey = selectedKeysValue[0] as string;

    if (selectedKey && onNodeSelect) {
      const keyParts = selectedKey.split('-');
      const [type, id] = keyParts;

      console.log(`选中了 ${type}: ${id}`, info.node);

      let nodeData: any = null;

      // 根据不同类型的节点获取对应数据
      switch (type) {
        case 'html':
        case 'css':
        case 'img':
        case 'prototype':
          nodeData = info.node?.nodeData;
          break;
        case 'sliced':
          nodeData = info.node.sliceData;
          break;
        default:
          nodeData = info.node;
      }

      // 调用回调函数
      onNodeSelect({
        renderType: info.node.renderType,
        type,
        id,
        data: nodeData,
      });
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      <div className="py-2 whitespace-normal overflow-auto">
        {project ? (
          <Tree
            blockNode
            showIcon
            className="design-tree [&_.ant-tree-iconEle]:!inline-flex [&_.ant-tree-iconEle]:!items-center"
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            treeData={treeData}
            onExpand={onExpand}
            onSelect={onSelect}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            <IconFolder className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>加载工程数据中...</p>
          </div>
        )}
      </div>
    </div>
  );
};
