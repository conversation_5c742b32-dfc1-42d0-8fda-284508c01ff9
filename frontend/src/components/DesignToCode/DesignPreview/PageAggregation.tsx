import {
  Card,
  CardBody,
  CardHeader,
  CheckboxGroup,
  Checkbox,
  Button,
  Chip,
  addToast,
  Select,
  SelectItem
} from '@heroui/react';
import { Image } from 'antd';
import { useState, useEffect } from 'react';
import { DesignProject, DesignPage, batchBindPrototypes } from '@/apis';

interface Props {
  project: DesignProject;
  onPageAdded?: () => void;
  onCancel?: () => void;
  onResetSelection?: () => void; // 重置选择状态的回调
  lanhuImages: ImageItem[];
  lanhuImagesLoading: boolean;
  selectedPageId?: string; // 预选的页面ID
  autoSelectImages?: boolean; // 是否自动选中传入的图片
}

interface ImageItem {
  id: string;
  name: string;
  url: string;
  width?: number;
  height?: number;
  latest_version?: string;
  [key: string]: any;
}

export function PageAggregation({ project, onPageAdded, onCancel, onResetSelection, lanhuImages, lanhuImagesLoading, selectedPageId, autoSelectImages = true }: Props) {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [currentSelectedPageId, setCurrentSelectedPageId] = useState<string>('');
  const [pages, setPages] = useState<DesignPage[]>([]);
  const [isBinding, setIsBinding] = useState(false);
  // 移除创建页面相关的状态

  // 设置页面列表（从现有项目数据）
  useEffect(() => {
    if (project?.pages) {
      setPages(project.pages);
    }
  }, [project]);

  // 自动选中传入的页面ID
  useEffect(() => {
    if (selectedPageId) {
      setCurrentSelectedPageId(selectedPageId);
    }
  }, [selectedPageId]);

  // 根据autoSelectImages参数决定是否自动选中传入的图片
  useEffect(() => {
    if (autoSelectImages && lanhuImages && lanhuImages.length > 0) {
      const imageIds = lanhuImages.map(img => img.id);

      setSelectedImages(imageIds);
    }
  }, [lanhuImages, autoSelectImages]);

  // 处理图片选择变化
  const handleImageSelectionChange = (values: string[]) => {
    setSelectedImages(values);
  };

  // 移除选中的图片
  const handleRemoveImage = (imageId: string) => {
    setSelectedImages(prev => prev.filter(id => id !== imageId));
  };

  // 处理页面选择变化
  const handlePageSelectionChange = (keys: any) => {
    const selectedKey = Array.from(keys)[0] as string;

    if (selectedKey) {
      setCurrentSelectedPageId(selectedKey);
    }
  };

  // 移除页面模式变化处理函数

  // 处理绑定操作
  const handleBind = async () => {
    if (selectedImages.length === 0) {
      addToast({
        title: '请先选择要绑定的图片',
        color: 'warning',
      });

      return;
    }

    // 验证页面选择
    if (!currentSelectedPageId) {
      addToast({
        title: '请选择一个页面',
        color: 'warning',
      });

      return;
    }

    setIsBinding(true); // 开始绑定，设置 loading 状态

    try {
      // 绑定到现有页面
      const existingPage = pages.find(page => page.id === currentSelectedPageId);

      if (!existingPage) {
        addToast({
          title: '页面不存在',
          color: 'danger',
        });

        return;
      }
      
      // 批量绑定接口
      const selectedImageItems = getSelectedImageItems();

      try {
        const response = await batchBindPrototypes({
          projectId: project.id,
          pageId: existingPage.id,
          selectedImages: selectedImageItems as any,
        }) as any;

        if (response.success) {
          const { successCount, failedCount } = response.results;

          addToast({
            title: '原型添加成功',
            description: `已向页面 "${existingPage.name}" 添加 ${successCount} 个新原型${failedCount > 0 ? `，${failedCount} 个失败` : ''}`,
            color: 'success',
          });

          // 通知父组件页面已更新
          onPageAdded?.();
        } else {

          addToast({
            title: '原型添加失败',
            description: response.message || '批量绑定失败',
            color: 'danger',
          });

          return;
        }
      } catch (error) {

        addToast({
          title: '原型添加失败',
          description: error instanceof Error ? error.message : '网络请求失败',
          color: 'danger',
        });

        return;
      }

      // 绑定成功后清空选择
      setSelectedImages([]);
      setCurrentSelectedPageId('');

      // 通知父组件重置选择状态
      onResetSelection?.();

    } catch (error) {
      addToast({
        title: '绑定失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsBinding(false);
    }
  };

  // 获取选中图片的详细信息
  const getSelectedImageItems = () => {
    return lanhuImages.filter((item: ImageItem) => selectedImages.includes(item.id));
  };




  return (
    <div className="flex gap-6 h-full">
      {/* 左侧图片选择区域 */}
      <Card className="min-w-[400px] max-w-[500px] w-[40%] flex flex-col rounded-md">
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold">图片选择</h3>
        </CardHeader>
        <CardBody className="flex-1 pt-0 max-h-[70vh] overflow-y-auto">
          <CheckboxGroup
            className="gap-3"
            classNames={{
              wrapper: "gap-3",
            }}
            value={selectedImages}
            onValueChange={handleImageSelectionChange}
          >
            {lanhuImagesLoading ? (
              <div className="text-center py-8 text-gray-500">
                <p>正在加载图片...</p>
              </div>
            ) : (
              lanhuImages.map((item, index) => (
                <div key={`${item.id}-${index}`} className="flex items-center gap-3 p-2 border rounded-md hover:bg-gray-50 dark:hover:bg-gray-800">
                  <Checkbox 
                    classNames={{
                      wrapper: "rounded-md",
                    }} 
                    value={item.id}
                  />
                  <Image
                    alt={item.name}
                    className="object-cover rounded border cursor-pointer"
                    height={64}
                    preview={{
                      mask: <div className="text-white text-xs">点击放大</div>
                    }}
                    src={item.url.replace('http://lanhu.htsc.com.cn:8089', 'http://168.63.17.71:8089')}
                    width={64}
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{item.name}</p>
                  </div>
                </div>
              ))
            )}
          </CheckboxGroup>

          {!lanhuImagesLoading && lanhuImages.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>暂无可用图片</p>
              <p className="text-sm mt-2">请同步蓝湖项目并选择原型图片</p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* 右侧表单区域 */}
      <Card className="flex-1 flex flex-col rounded-md">
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold">页面绑定</h3>
        </CardHeader>
        <CardBody className="flex-1 pt-0">
          {/* 选中的图片标签展示区域 */}
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-3">已选择的图片 ({selectedImages.length})</h4>
            <div className="min-h-[60px] max-h-[120px] p-3 border rounded-md bg-gray-50 dark:bg-gray-800 overflow-y-auto">
              <div className="flex flex-wrap gap-2">
                {getSelectedImageItems().map((item, index) => (
                  <Chip
                    key={`${item.id}-${index}`}
                    className="max-w-[200px] rounded-md"
                    color="primary"
                    variant="flat"
                    onClose={() => handleRemoveImage(item.id)}
                  >
                    <span className="truncate">{item.name}</span>
                  </Chip>
                ))}
                {selectedImages.length === 0 && (
                  <p className="text-gray-500 text-sm">请在左侧选择要绑定的图片</p>
                )}
              </div>
            </div>
          </div>

          {/* 页面选择 */}
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-3">选择页面</h4>
            <Select
              className="w-full"
              classNames={{
                trigger: "rounded-md",
              }}
              placeholder="请选择一个页面"
              selectedKeys={currentSelectedPageId ? [currentSelectedPageId] : []}
              selectionMode="single"
              onSelectionChange={handlePageSelectionChange}
            >
              {pages.map((page) => (
                <SelectItem key={page.id}>
                  {page.name}
                </SelectItem>
              ))}
            </Select>
            <p className="text-xs text-gray-500 mt-2">
              💡 选择一个现有页面来添加原型
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3">
            <Button
              className="rounded-md"
              variant="flat"
              onPress={onCancel}
            >
              取消
            </Button>
            <Button
              className="rounded-md"
              color="primary"
              isDisabled={selectedImages.length === 0 || !currentSelectedPageId || isBinding}
              isLoading={isBinding}
              onPress={handleBind}
            >
              {isBinding ? '添加中...' : '添加到页面'}
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
