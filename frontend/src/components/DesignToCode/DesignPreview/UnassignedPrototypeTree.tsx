import { Tree, TreeDataNode } from 'antd';
import { useState, useMemo, useImperativeHandle, forwardRef } from 'react';
import IconFolder from '~icons/mdi/folder';
import IconImage from '~icons/mdi/image';

interface UnassignedPrototype {
  id: string;
  name: string;
  type: string;
  children?: UnassignedPrototype[];
  data?: any;
}

interface ExtendedTreeDataNode extends TreeDataNode {
  type?: string;
  children?: ExtendedTreeDataNode[];
  nodeData?: any;
  renderType?: string;
}

interface Props {
  prototypes: UnassignedPrototype[];
  onNodeSelect: (nodeInfo: { renderType: string; type: string; id: string; data?: any }) => void;
  onNodesCheck?: (checkedNodes: { renderType: string; type: string; id: string; data?: any }[]) => void;
}

export interface UnassignedPrototypeTreeRef {
  resetCheckedKeys: () => void;
}

export const UnassignedPrototypeTree = forwardRef<UnassignedPrototypeTreeRef, Props>(({ prototypes, onNodeSelect, onNodesCheck }, ref) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);

  // 暴露重置方法给父组件
  useImperativeHandle(ref, () => ({
    resetCheckedKeys: () => {
      setCheckedKeys([]);
      // 同时通知父组件选中状态已清空
      onNodesCheck?.([]);
    }
  }));

  // 递归生成树形数据
  const buildTreeData = (nodes: UnassignedPrototype[]): ExtendedTreeDataNode[] => {
    return nodes.map((node) => {
      const isImageNode = node.type === 'img';
      
      return {
        title: node.name,
        key: `${node.type}-${node.id}`,
        type: node.type,
        renderType: node.type,
        icon: isImageNode ? <IconImage className="text-green-500" /> : <IconFolder className="text-gray-500" />,
        isLeaf: isImageNode,
        nodeData: isImageNode ? node.data : undefined,
        children: node.children && node.children.length > 0 ? buildTreeData(node.children) : undefined,
      };
    });
  };

  // 生成树形数据
  const treeData: ExtendedTreeDataNode[] = useMemo(() => {
    if (!prototypes || prototypes.length === 0) return [];
    
    return buildTreeData(prototypes);
  }, [prototypes]);

  // 默认展开第一级节点
  useMemo(() => {
    if (treeData && treeData.length > 0) {
      const firstLevelKeys = treeData
        .filter(node => !node.isLeaf) // 只展开非叶子节点
        .map(node => node.key);
      
      setExpandedKeys(firstLevelKeys);
    }
  }, [treeData]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const onSelect = (selectedKeysValue: React.Key[], info: any) => {
    setSelectedKeys(selectedKeysValue);

    // 处理节点选择事件
    const selectedKey = selectedKeysValue[0] as string;

    if (selectedKey && onNodeSelect) {
      const keyParts = selectedKey.split('-');
      const type = keyParts[0];
      const id = keyParts.slice(1).join('-'); // 处理可能包含连字符的ID

      // 调用回调函数
      onNodeSelect({
        renderType: info.node.renderType || type,
        type,
        id,
        data: info.node.nodeData,
      });
    }
  };

  // 处理节点check事件
  const onCheck = (checkedKeysValue: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    const keys = Array.isArray(checkedKeysValue) ? checkedKeysValue : checkedKeysValue.checked;

    setCheckedKeys(keys);

    if (onNodesCheck) {
      // 获取选中节点的详细信息
      const checkedNodes: { renderType: string; type: string; id: string; data?: any }[] = [];

      keys.forEach((key) => {
        const keyStr = key as string;
        const keyParts = keyStr.split('-');
        const type = keyParts[0];
        const id = keyParts.slice(1).join('-'); // 处理可能包含连字符的ID

        // 查找对应的树节点数据
        const findNodeData = (nodes: ExtendedTreeDataNode[], targetKey: string): ExtendedTreeDataNode | null => {
          for (const node of nodes) {
            if (node.key === targetKey) {
              return node;
            }
            if (node.children) {
              const found = findNodeData(node.children, targetKey);

              if (found) return found;
            }
          }

          return null;
        };

        const nodeData = findNodeData(treeData, keyStr);

        if (nodeData && type === 'img') { // 只处理图片节点，不处理文件夹
          checkedNodes.push({
            renderType: nodeData.renderType || type,
            type,
            id,
            data: nodeData.nodeData,
          });
        }
      });

      onNodesCheck(checkedNodes);
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      <div className="py-2 whitespace-normal overflow-auto">
        {prototypes && prototypes.length > 0 ? (
          <Tree
            blockNode
            checkable
            showIcon
            checkedKeys={checkedKeys}
            className="unassigned-tree [&_.ant-tree-iconEle]:!inline-flex [&_.ant-tree-iconEle]:!items-center"
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            treeData={treeData}
            onCheck={onCheck}
            onExpand={onExpand}
            onSelect={onSelect}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            <IconFolder className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>无数据</p>
            <p className='text-xs'>「同步蓝湖」拉取最新资源</p>
          </div>
        )}
      </div>
    </div>
  );
});
