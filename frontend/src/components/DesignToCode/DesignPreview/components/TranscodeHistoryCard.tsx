import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ody, <PERSON><PERSON>, Chip, Progress } from '@heroui/react';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import IconClock from '~icons/mdi/clock';
import IconRefresh from '~icons/mdi/refresh';

// 数据类型定义（与后端 BackgroundTask 模型匹配）
interface BackgroundTask {
  id: string;
  taskType: string;
  taskName: string;
  user: string;
  status: string;
  progress: number;
  metadata?: any;
  designProjectId?: string;
  model?: string;
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
  items: BackgroundTaskItem[];
  createdAt: string;
  updatedAt: string;
}

interface BackgroundTaskItem {
  id: string;
  backgroundTaskId: string;
  itemId: string;
  itemName: string;
  status: string;
  progress: number;
  stage: string;
  result?: any;
  error?: string;
  playgroundId?: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

// 扩展的任务条目类型，包含父任务信息
interface BackgroundTaskItemWithParent extends BackgroundTaskItem {
  parentTask: BackgroundTask;
}

interface TranscodeHistoryCardProps {
  isVisible: boolean;
  onClose: () => void;
  transcodeHistory: any[];
  historyLoading: boolean;
  onRefresh: () => void;
}

// 任务状态颜色映射
const TASK_STATUS_COLOR_MAP: Record<string, 'default' | 'primary' | 'success' | 'danger' | 'warning' | 'secondary'> = {
  pending: 'default',
  processing: 'primary',
  completed: 'success',
  'partial-success': 'warning',
  failed: 'danger',
  'partial-failed': 'warning',
  cancelled: 'warning',
};

// 任务状态文本映射
const TASK_STATUS_TEXT_MAP: Record<string, string> = {
  pending: '等待中',
  processing: '处理中',
  completed: '已完成',
  'partial-success': '部分成功',
  failed: '失败',
  'partial-failed': '部分失败',
  cancelled: '已取消',
};

// 获取任务状态颜色
const getTaskStatusColor = (status: string): 'default' | 'primary' | 'success' | 'danger' | 'warning' | 'secondary' => {
  return TASK_STATUS_COLOR_MAP[status] || 'default';
};

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  return TASK_STATUS_TEXT_MAP[status] || status;
};

// 任务类型中文名称映射
const TASK_TYPE_TEXT_MAP: Record<string, string> = {
  'img-to-code': '图片转代码',
  'img-visual-split': '图片视觉分割',
  'img-split-to-code': '分割图转代码',
  'coords-to-layout': '坐标转布局',
  'spec-to-prod-code': '根据应用规范生成生产级代码',
  'design-transcode': '设计稿转码',
  'design-merge': '设计稿合并',
  custom: '自定义任务',
};

// 获取任务类型中文名称
const getTaskTypeText = (taskType: string) => {
  return TASK_TYPE_TEXT_MAP[taskType] || taskType;
};

export const TranscodeHistoryCard: React.FC<TranscodeHistoryCardProps> = ({
  isVisible,
  onClose,
  transcodeHistory,
  historyLoading,
  onRefresh,
}) => {
  const navigate = useNavigate();

  if (!isVisible) return null;

  // 处理查看结果
  const handleViewResult = (playgroundId: string) => {
    navigate(`/chat/${playgroundId}`);
  };

  console.log(transcodeHistory.flatMap((task) => task.items.map((item) => ({ ...item, parentTask: task }))));

  return (
    <div className="fixed z-40 w-96 max-h-96 overflow-hidden right-6 bottom-6">
      <Card className="shadow-lg border">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <IconClock className="w-4 h-4" />
              <span className="font-medium text-sm">转码任务</span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                isIconOnly
                className="min-w-6 w-6 h-6"
                isLoading={historyLoading}
                size="sm"
                variant="flat"
                onPress={onRefresh}
              >
                <IconRefresh className="w-3 h-3" />
              </Button>
              <Button isIconOnly className="min-w-6 w-6 h-6" size="sm" variant="flat" onPress={onClose}>
                ×
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody className="pt-0 max-h-80 overflow-y-auto">
          {transcodeHistory.length === 0 || transcodeHistory.every((task) => task.items.length === 0) ? (
            <div className="text-center py-6">
              <IconClock className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-500">还没有任务历史</p>
              <p className="text-xs text-gray-400 mt-1">执行任务后会在这里显示历史记录</p>
            </div>
          ) : (
            <div className="space-y-2">
              {transcodeHistory
                .flatMap((task) => task.items.map((item) => ({ ...item, parentTask: task })))
                .map((item: BackgroundTaskItemWithParent) => (
                  <div
                    key={item.id}
                    className="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Chip color={getTaskStatusColor(item.status)} size="sm" variant="flat">
                          {getTaskStatusText(item.status)}
                        </Chip>
                        <div className="flex flex-col">
                          <span className="text-xs font-medium text-gray-800 dark:text-gray-200">
                            {item.metadata?.taskName ? `${item.metadata.taskName} - ${item.itemName}` : item.itemName}
                          </span>
                          <span className="text-xs text-gray-500">{getTaskTypeText(item.parentTask.taskType)}</span>
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(item.parentTask.createdAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                    {/* 错误信息显示 */}
                    {item.status === 'failed' && item.error && (
                      <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                        <div className="flex items-start gap-2">
                          <span className="text-xs font-medium text-red-600 dark:text-red-400 flex-shrink-0">
                            错误:
                          </span>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs text-red-700 dark:text-red-300 break-words overflow-hidden">
                              <span
                                className="block max-h-16 overflow-y-auto scrollbar-thin scrollbar-thumb-red-300 dark:scrollbar-thumb-red-600"
                                title={item.error}
                              >
                                {item.error}
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    {(item.metadata?.progress || item.progress) && (
                      <div className="space-y-1">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium">进度</span>
                          <span className="text-xs text-gray-600">{item.metadata?.progress || item.progress}%</span>
                        </div>
                        <Progress
                          color={getTaskStatusColor(item.status)}
                          size="sm"
                          value={item.metadata?.progress || item.progress}
                        />
                      </div>
                    )}

                    {/* 查看结果按钮 */}
                    {(item.metadata?.playgroundId || item.playgroundId) && item.status === 'completed' && (
                      <div className="mt-2 flex justify-end">
                        <Button
                          className="text-xs"
                          color="primary"
                          size="sm"
                          variant="light"
                          onPress={() => handleViewResult(item.metadata?.playgroundId || item.playgroundId!)}
                        >
                          查看结果
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};
