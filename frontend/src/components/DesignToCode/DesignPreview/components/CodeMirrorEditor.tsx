import { useEffect, useRef } from 'react';
import { EditorView, basicSetup } from 'codemirror';
import { html } from '@codemirror/lang-html';
import { css } from '@codemirror/lang-css';
import { oneDark } from '@codemirror/theme-one-dark';
import { useThemeStore } from '@/stores/theme';

interface CodeMirrorEditorProps {
  content: string;
  language: 'html' | 'css';
  className?: string;
  readOnly?: boolean;
  onChange?: (content: string) => void;
}

export function CodeMirrorEditor({ 
  content, 
  language, 
  className = '', 
  readOnly = true,
  onChange 
}: CodeMirrorEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView>();
  const { theme } = useThemeStore();

  useEffect(() => {
    if (!editorRef.current) return;

    // 清空容器
    editorRef.current.innerHTML = '';

    const extensions = [
      basicSetup,
      language === 'html' ? html() : css(),
      EditorView.editable.of(!readOnly),
      EditorView.theme({
        '&': {
          height: '100%',
        },
        '.cm-scroller': {
          overflow: 'auto',
          height: '100%',
        },
        '.cm-focused': {
          outline: 'none',
        },
      }),
    ];

    // 如果有 onChange 回调，添加更新监听器
    if (onChange && !readOnly) {
      extensions.push(
        EditorView.updateListener.of(update => {
          if (update.docChanged) {
            onChange(update.state.doc.toString());
          }
        })
      );
    }

    // 根据主题添加暗色主题
    if (theme === 'dark') {
      extensions.push(oneDark);
    }

    const view = new EditorView({
      doc: content,
      extensions,
      parent: editorRef.current,
    });

    viewRef.current = view;

    return () => {
      view.destroy();
    };
  }, [language, readOnly, theme, onChange]);

  // 当内容变化时更新编辑器
  useEffect(() => {
    const view = viewRef.current;
    if (!view) return;

    const currentContent = view.state.doc.toString();
    if (currentContent !== content) {
      view.dispatch({
        changes: {
          from: 0,
          to: view.state.doc.length,
          insert: content
        }
      });
    }
  }, [content]);

  return (
    <div
      ref={editorRef}
      className={`h-full border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${className}`}
    />
  );
}
