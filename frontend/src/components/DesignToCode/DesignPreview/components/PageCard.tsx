import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Button,
  Card,
  CardBody,
  Chip,
  Checkbox
} from '@heroui/react';
import {
  DesignPage,
  createMergeTask
} from '@/apis';
import { useUserInfoStore } from '@/hooks/login';
import { addToast } from '@heroui/react';
import IconMerge from '~icons/mdi/merge';
import IconDownload from '~icons/mdi/download';
import IconEye from '~icons/mdi/eye';
import IconImage from '~icons/mdi/image';

interface PageCardProps {
  page: DesignPage;
  projectId: string;
  isPageSelected: boolean;
  onPageSelectionChange: (pageId: string, checked: boolean) => void;
  onMergeTaskCreated?: () => void;
}

export const PageCard: React.FC<PageCardProps> = ({
  page,
  projectId,
  isPageSelected,
  onPageSelectionChange,
  onMergeTaskCreated
}) => {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const [selectedPrototypes, setSelectedPrototypes] = useState<Set<string>>(new Set());

  // 获取任务状态颜色
  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'processing': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'danger';
      case 'partial-completed': return 'warning';
      case 'partial-failed': return 'warning';
      default: return 'default';
    }
  };

  // 获取任务状态文本
  const getTaskStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'processing': return '处理中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'partial-completed': return '部分完成';
      case 'partial-failed': return '部分失败';
      default: return '未知';
    }
  };

  // 查看转码结果
  const handleViewResult = (playgroundId: string) => {
    navigate(`/chat/${playgroundId}`);
  };

  // 查看图片
  const handleViewImage = (imageUrl: string) => {
    window.open(imageUrl, '_blank');
  };

  // 下载文件功能
  const handleDownloadFile = (content: string, fileName: string, contentType: string) => {
    try {
      // 创建Blob对象
      const blob = new Blob([content], { type: contentType });
      
      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      
      // 触发下载
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载文件失败:', error);
    }
  };

  // 处理合并原型（仅限当前页面的原型）
  const handleMergePrototypes = async () => {
    if (selectedPrototypes.size < 1) {
      addToast({
        title: '请选择至少1个原型进行合并',
        color: 'warning'
      });
      return;
    }

    if (!projectId) {
      addToast({
        title: '项目信息不存在',
        color: 'danger'
      });
      return;
    }

    if (!userInfo?.username) {
      addToast({
        title: '用户信息不存在',
        color: 'danger'
      });
      return;
    }

    try {
      const result = await createMergeTask({
        projectId: projectId,
        prototypeIds: Array.from(selectedPrototypes),
        user: userInfo.username,
      });

      addToast({
        title: '合并任务创建成功',
        description: `已选择 ${selectedPrototypes.size} 个原型进行合并`,
        color: 'success'
      });

      // 清空选择
      setSelectedPrototypes(new Set());

      // 通知父组件任务已创建（可能需要启动轮询）
      onMergeTaskCreated?.();

      console.log('合并任务结果:', result);
    } catch (error) {
      console.error('创建合并任务失败:', error);
      addToast({
        title: '创建合并任务失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger'
      });
    }
  };

  return (
    <Card
      className={`
        transition-all duration-200
        ${isPageSelected
          ? 'shadow-lg'
          : 'hover:shadow-md'
        }
      `}
      style={{
        backgroundColor: isPageSelected
          ? 'hsl(var(--heroui-secondary) / 0.1)'
          : ''
      }}
    >
      <CardBody>
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-3">
            <Checkbox
              isSelected={isPageSelected}
              onValueChange={(checked) => {
                // 如果要选中页面，检查该页面是否有resultHtml
                if (checked) {
                  if (!page.resultHtml) {
                    addToast({
                      title: '请先在预览页生成结果',
                      description: `页面 ${page.name} 还没有生成结果，请先在预览页面进行生成`,
                      color: 'warning'
                    });
                    return;
                  }
                }
                onPageSelectionChange(page.id, checked);
              }}
            />
            <div>
              <h3 className="font-semibold text-lg">{page.name}</h3>
              {page.description && (
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {page.description}
                </p>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="flat"
              color="primary"
              onPress={handleMergePrototypes}
              startContent={<IconMerge />}
              isDisabled={selectedPrototypes.size < 1}
            >
              合并原型 ({selectedPrototypes.size})
            </Button>
          </div>
        </div>

        {/* 页面原型列表 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {page.prototypes.map((prototype) => (
            <div
              key={prototype.id}
              className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 transition-colors"
            >
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Checkbox
                    size="sm"
                    isSelected={selectedPrototypes.has(prototype.id)}
                    onValueChange={(checked) => {
                      // 如果要选中原型，检查是否有resultHtml
                      if (checked && !prototype.resultHtml) {
                        addToast({
                          title: '请先在预览页生成结果',
                          description: `原型 ${prototype.prototypeName} 还没有生成结果，请先在预览页面进行生成`,
                          color: 'warning'
                        });
                        return;
                      }

                      const newSelected = new Set(selectedPrototypes);
                      if (checked) {
                        newSelected.add(prototype.id);
                      } else {
                        newSelected.delete(prototype.id);
                      }
                      setSelectedPrototypes(newSelected);
                    }}
                  />
                  <span className="font-medium">
                    原型 {prototype.prototypeName}
                  </span>
                </div>
                <Chip
                  color={getTaskStatusColor(prototype.status)}
                  variant="flat"
                  size="sm"
                >
                  {getTaskStatusText(prototype.status)}
                </Chip>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {/* 只有当存在HTML内容时才显示HTML行 */}
                {prototype.htmlContent && prototype.htmlFileName && (
                  <div className="flex items-center justify-between">
                    <span>HTML: {prototype.htmlFileName}</span>
                    <Button
                      size="sm"
                      variant="light"
                      color="primary"
                      isIconOnly
                      onPress={() => handleDownloadFile(
                        prototype.htmlContent,
                        prototype.htmlFileName,
                        'text/html'
                      )}
                      title="下载HTML文件"
                      className="min-w-unit-6 w-unit-6 h-unit-6"
                    >
                      <IconDownload className="w-3 h-3" />
                    </Button>
                  </div>
                )}

                {/* 只有当存在CSS内容时才显示CSS行 */}
                {prototype.cssContent && prototype.cssFileName && (
                  <div className="flex items-center justify-between">
                    <span>CSS: {prototype.cssFileName}</span>
                    <Button
                      size="sm"
                      variant="light"
                      color="primary"
                      isIconOnly
                      onPress={() => handleDownloadFile(
                        prototype.cssContent,
                        prototype.cssFileName,
                        'text/css'
                      )}
                      title="下载CSS文件"
                      className="min-w-unit-6 w-unit-6 h-unit-6"
                    >
                      <IconDownload className="w-3 h-3" />
                    </Button>
                  </div>
                )}

                {/* 只有当存在图片链接时才显示图片行 */}
                {prototype.imgFileLink && (
                  <div className="flex items-center justify-between">
                    <span>IMG: {prototype.imgFileName || '图片文件'}</span>
                    <Button
                      size="sm"
                      variant="light"
                      color="primary"
                      isIconOnly
                      onPress={() => handleViewImage(prototype.imgFileLink!)}
                      title="查看图片"
                      className="min-w-unit-6 w-unit-6 h-unit-6"
                    >
                      <IconImage className="w-3 h-3" />
                    </Button>
                  </div>
                )}
              </div>
              {prototype.status === 'completed' && prototype.playgroundId && (
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onPress={() => handleViewResult(prototype.playgroundId!)}
                  startContent={<IconEye />}
                  className="w-full"
                >
                  查看转码结果
                </Button>
              )}
            </div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};
