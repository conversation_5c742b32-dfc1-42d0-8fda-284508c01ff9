import { Button, Tabs, Tab, addToast } from '@heroui/react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { CodeMirrorEditor } from './CodeMirrorEditor';
import { ImgPreview } from './ImgPreview';
import { DesignPagePrototype, DesignProject } from '@/apis';
import { updatePrototypeContent } from '@/utils/designProjectUtils';
import { processHtmlContent } from '@/utils/file';
import IconContentSave from '~icons/mdi/content-save';

interface HtmlPreviewProps {
  id: string;
  nodeData?: DesignPagePrototype; // 从树节点传递的完整数据，包含原型信息
  width?: number; // iframe 预览窗口宽度，默认 375px
  project?: DesignProject; // 项目数据，用于保存功能
  onProjectUpdate?: (project: DesignProject) => void;
  initialTab?: 'html' | 'css' | 'img'; // 初始显示的 tab
}

export function HtmlPreview({
  nodeData,
  width = 375,
  project,
  onProjectUpdate,
  initialTab = 'html',
}: HtmlPreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [activeTab, setActiveTab] = useState<string>(initialTab);
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [cssContent, setCssContent] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // 防抖相关状态
  const debounceTimerRef = useRef<number>();
  const currentBlobUrlRef = useRef<string>();

  // 监听 initialTab 变化，更新 activeTab
  useEffect(() => {
    if (!nodeData?.htmlContent && nodeData?.imgFileLink) {
      setActiveTab('img');
    } else {
      setActiveTab(initialTab);
    }
  }, [initialTab]);

  // 校验和清理内容，防止脏数据导致页面崩溃
  const sanitizeContent = (content: any, contentType: string = 'content'): string => {
    // 如果是字符串，直接返回
    if (typeof content === 'string') {
      return content;
    }

    // 其他类型（包括 null、undefined、对象等），统一处理
    if (content != null && typeof content === 'object') {
      console.warn(`检测到 ${contentType} 脏数据:`, content);
    }

    // 统一转换为字符串，如果是 null/undefined 则返回空字符串
    return content ? String(content) : '';
  };

  // 初始化内容状态
  useEffect(() => {
    if (nodeData) {
      const cleanHtmlContent = sanitizeContent(nodeData.htmlContent, 'htmlContent');
      const cleanCssContent = sanitizeContent(nodeData.cssContent, 'cssContent');

      setHtmlContent(cleanHtmlContent);
      setCssContent(cleanCssContent);

      // 只有当 HTML 和 CSS 内容都存在时才更新 iframe（CSS 可以为空）
      if (cleanHtmlContent && cleanCssContent && iframeRef.current) {
        const iframe = iframeRef.current;

        // 清理之前的 Blob URL
        if (currentBlobUrlRef.current) {
          URL.revokeObjectURL(currentBlobUrlRef.current);
        }

        // 处理 HTML 内容，注入 CSS 样式（CSS 可以为空字符串）
        const processedHtml = processHtmlContent(cleanHtmlContent, cleanCssContent || '');

        // 创建新的 Blob URL
        const blob = new Blob([processedHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        // 保存当前 URL 引用
        currentBlobUrlRef.current = url;

        // 设置 iframe 源
        iframe.src = url;
      }
    }
  }, [nodeData]);

  // 防抖更新预览函数
  const debouncedUpdatePreview = useCallback((htmlContent: string, cssContent: string) => {
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的防抖定时器
    debounceTimerRef.current = window.setTimeout(() => {
      if (!iframeRef.current || !htmlContent) return;

      const iframe = iframeRef.current;

      // 清理之前的 Blob URL
      if (currentBlobUrlRef.current) {
        URL.revokeObjectURL(currentBlobUrlRef.current);
      }

      // 处理 HTML 内容，注入 CSS 样式（CSS 可以为空字符串）
      const processedHtml = processHtmlContent(htmlContent, cssContent || '');

      // 创建新的 Blob URL
      const blob = new Blob([processedHtml], { type: 'text/html' });
      const url = URL.createObjectURL(blob);

      // 保存当前 URL 引用
      currentBlobUrlRef.current = url;

      // 设置 iframe 源
      iframe.src = url;
    }, 300); // 300ms 防抖延迟
  }, []);

  // 自动预览：监听编辑器内容变化（只有当有 HTML 内容时才更新预览）
  useEffect(() => {
    if (!htmlContent) return;

    debouncedUpdatePreview(htmlContent, cssContent);
  }, [htmlContent, cssContent, debouncedUpdatePreview]);

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      // 清理 Blob URL
      if (currentBlobUrlRef.current) {
        URL.revokeObjectURL(currentBlobUrlRef.current);
      }
    };
  }, []);



  // 保存按钮点击事件
  const handleSave = async () => {
    if (!project || !onProjectUpdate || !nodeData) {
      addToast({
        title: '保存失败',
        description: '缺少必要的项目信息',
        color: 'danger',
      });

      return;
    }

    setIsSaving(true);
    try {
      await updatePrototypeContent(project, nodeData.id, htmlContent, cssContent, onProjectUpdate);

      addToast({
        title: '保存成功',
        description: 'HTML 和 CSS 内容已更新',
        color: 'success',
      });
    } catch (error) {
      console.error('保存失败:', error);
      addToast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsSaving(false);
    }
  };



  if (!nodeData) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-default-500">请选择一个文件节点来查看内容</p>
      </div>
    );
  }

  // 检查是否有任何内容可以显示
  if (!nodeData.htmlContent && !nodeData.cssContent && !nodeData.imgFileLink) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-default-500">该文件没有可预览的内容</p>
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* 左侧 iframe 预览窗口 - 只有当有 HTML 内容时才显示 */}
      {nodeData.htmlContent ? (
        <div className="flex-shrink-0" style={{ width: `${width}px` }}>
          <div className="h-full box-border pt-3">
            <iframe
              ref={iframeRef}
              className="w-full h-full border border-gray-200 dark:border-gray-700 rounded-lg bg-white"
              loading="lazy"
              referrerPolicy="no-referrer"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
              style={{
                minHeight: '200px',
              }}
              title="HTML Preview"
            />
          </div>
        </div>
      ) : (
        <div className="flex-shrink-0" style={{ width: `${width}px` }}>
          <div className="h-full box-border border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-4xl mb-2">📄</div>
              <p className="text-sm">没有 HTML 内容</p>
              <p className="text-xs mt-1">无法显示预览</p>
            </div>
          </div>
        </div>
      )}

      {/* 右侧代码展示区 */}
      <div className="flex-1 flex flex-col min-w-96 ml-4">
        {/* Tabs 和按钮区域 */}
        <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 px-4 py-2">
          <Tabs
            selectedKey={activeTab}
            size="sm"
            variant="underlined"
            onSelectionChange={(key) => setActiveTab(key as string)}
          >
            {nodeData.htmlFileName && <Tab key="html" title={nodeData.htmlFileName} />}
            {nodeData.cssFileName && <Tab key="css" title={nodeData.cssFileName} />}
            {nodeData.imgFileLink && <Tab key="img" title="设计图" />}
          </Tabs>

          <div className="flex gap-2">
            <Button
              color="default"
              isDisabled={!project || !onProjectUpdate}
              isLoading={isSaving}
              size="sm"
              startContent={<IconContentSave />}
              variant="ghost"
              onPress={handleSave}
            >
              保存
            </Button>
          </div>
        </div>

        {/* 代码编辑器区域 */}
        <div className="p-4" style={{ height: 'calc(100% - 60px)', maxHeight: 'calc(100vh - 200px)' }}>
          {activeTab === 'html' && nodeData.htmlContent && (
            <CodeMirrorEditor content={htmlContent} language="html" readOnly={false} onChange={setHtmlContent} />
          )}
          {activeTab === 'css' && nodeData.cssContent && (
            <CodeMirrorEditor content={cssContent} language="css" readOnly={false} onChange={setCssContent} />
          )}
          {activeTab === 'css' && !nodeData.cssContent && (
            <div className="h-full flex items-center justify-center">
              <p className="text-default-500">该文件没有 CSS 内容</p>
            </div>
          )}
          {activeTab === 'img' && nodeData.imgFileLink && (
            <div className="h-full">
              <ImgPreview
                handleGenerateCode={() => {}}
                id={nodeData.id}
                selectedNode={{ 
                  type: 'prototype', 
                  data: nodeData 
                }}
              />
            </div>
          )}
          {activeTab === 'img' && !nodeData.imgFileLink && (
            <div className="h-full flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-2">🖼️</div>
                <p className="text-default-500">该原型没有图片内容</p>
                <p className="text-xs mt-1 text-gray-400">无法显示图片预览</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
