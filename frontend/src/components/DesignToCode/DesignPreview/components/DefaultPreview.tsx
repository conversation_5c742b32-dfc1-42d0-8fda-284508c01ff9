
import { Card, CardBody } from '@heroui/react';

interface DefaultPreviewProps {
  type: string;
  id: string;
}

export function DefaultPreview({ type, id }: DefaultPreviewProps) {

  return (
    <Card className="m-4">
      <CardBody>
        <div className="flex items-center gap-3 mb-3">
          <div className="text-2xl">📋</div>
          <h3 className="text-lg font-semibold">节点信息</h3>
        </div>
        <p className="text-default-600 mb-2">
          类型: {type}
        </p>
        <p className="text-default-600 mb-2">
          ID: {id}
        </p>
      </CardBody>
    </Card>
  );
} 