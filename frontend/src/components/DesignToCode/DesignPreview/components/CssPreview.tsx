import { useEffect, useRef } from 'react';
import { EditorView, basicSetup } from 'codemirror';
import { css } from '@codemirror/lang-css';
import { oneDark } from '@codemirror/theme-one-dark';
import { useThemeStore } from '@/stores/theme';

interface CssPreviewProps {
  id: string;
  nodeData?: any; // 从树节点传递的完整数据，包含原型信息
}

export function CssPreview({ nodeData }: CssPreviewProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const { theme } = useThemeStore();

  useEffect(() => {
    if (!editorRef.current || !nodeData?.cssContent) return;

    // 清空容器
    editorRef.current.innerHTML = '';

    const extensions = [
      basicSetup,
      css(),
      EditorView.editable.of(false), // 只读模式
      EditorView.theme({
        '&': {
          height: '100%',
        },
        '.cm-scroller': {
          overflow: 'auto',
          height: '100%',
        },
        '.cm-focused': {
          outline: 'none',
        },
      }),
    ];

    // 根据主题添加暗色主题
    if (theme === 'dark') {
      extensions.push(oneDark);
    }

    const view = new EditorView({
      doc: nodeData.cssContent,
      extensions,
      parent: editorRef.current,
    });

    return () => {
      view.destroy();
    };
  }, [nodeData?.cssContent, theme]);

  return (
    <div className="h-full flex flex-col">
      {nodeData && nodeData.cssContent ? (
        <div
          ref={editorRef}
          className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
          style={{
            minHeight: '200px',
            height: 'calc(100% - 20px)', // 减去可能的边距
            maxHeight: 'calc(100vh - 150px)', // 确保不超过视口高度
          }}
        />
      ) : nodeData ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="text-default-500">
            该文件没有 CSS 内容
          </p>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <p className="text-default-500">
            请选择一个 CSS 文件节点来查看内容
          </p>
        </div>
      )}
    </div>
  );
}