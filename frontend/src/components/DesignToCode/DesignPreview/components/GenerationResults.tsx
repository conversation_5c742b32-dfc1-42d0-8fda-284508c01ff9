import { <PERSON><PERSON>, <PERSON> } from '@heroui/react';
import { addToast } from '@heroui/react';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageCard } from './PageCard';
import { TranscodeHistoryCard } from './TranscodeHistoryCard';
import { DesignProject, DesignPage, getDesignProject, createSpecToProdCodeTask, getProjectTasks } from '@/apis';
import { useUserInfoStore } from '@/hooks/login';
import IconMerge from '~icons/mdi/merge';
import IconPlay from '~icons/mdi/play';

interface GenerationResultsProps {
  projectId: string;
}

// 后台任务相关类型定义
interface BackgroundTask {
  id: string;
  taskType: string;
  taskName: string;
  user: string;
  status: string;
  progress: number;
  metadata?: any;
  designProjectId?: string;
  model?: string;
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
  items: BackgroundTaskItem[];
  created: string;
  updated: string;
}

interface BackgroundTaskItem {
  id: string;
  backgroundTaskId: string;
  itemId: string;
  itemName: string;
  status: string;
  progress: number;
  stage: string;
  result?: any;
  error?: string;
  playgroundId?: string;
  metadata?: any;
  created: string;
  updated: string;
}

export const GenerationResults: React.FC<GenerationResultsProps> = ({ projectId }) => {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const [project, setProject] = useState<DesignProject | undefined>();
  const [loading, setLoading] = useState(true);
  const [selectedPages, setSelectedPages] = useState<Set<string>>(new Set());
  const [statusPollingInterval, setStatusPollingInterval] = useState<ReturnType<typeof setInterval> | null>(null);

  // 转码历史相关状态
  const [showHistoryCard, setShowHistoryCard] = useState(false);
  const [transcodeHistory, setTranscodeHistory] = useState<BackgroundTask[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  // 加载项目数据
  const loadProject = async () => {
    try {
      setLoading(true);
      const data = await getDesignProject({ id: projectId });

      console.log('loadProject', data);

      if (data && typeof data === 'object' && 'id' in data) {
        const projectData = data as DesignProject;

        setProject(projectData);

        // 检查是否需要启动轮询
        checkAndStartStatusPolling(projectData.pages || []);
      }
    } catch (error) {
      console.error('加载设计稿转码工程失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载项目数据
  useEffect(() => {
    if (projectId) {
      // 生成跳转过来，需要等待任务处理一会
      setTimeout(() => {
        loadProject();
      }, 2000);
    }
  }, [projectId]);

  // 清理轮询
  useEffect(() => {
    return () => {
      if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
      }
    };
  }, [statusPollingInterval]);

  // 检查是否有处理中的任务
  const hasProcessingTasks = (pages: DesignPage[]): boolean => {
    return pages.some((page) => page.prototypes.some((prototype) => prototype.status === 'processing'));
  };

  // 检查并启动状态轮询
  const checkAndStartStatusPolling = (pages: DesignPage[]) => {
    const hasProcessing = hasProcessingTasks(pages);

    if (hasProcessing && !statusPollingInterval) {
      // 有处理中的任务且未启动轮询，则启动轮询
      startStatusPolling();
    } else if (!hasProcessing && statusPollingInterval) {
      // 没有处理中的任务且正在轮询，则停止轮询
      stopStatusPolling();
    }
  };

  // 启动状态轮询
  const startStatusPolling = () => {
    if (!projectId) return;

    console.log('🔄 启动生成结果状态轮询 (15秒间隔)');

    const pollInterval = setInterval(async () => {
      try {
        console.log('📊 轮询生成结果状态...');
        const data = await getDesignProject({ id: projectId });

        if (data && typeof data === 'object' && 'id' in data) {
          const latestProject = data as DesignProject;
          const latestPages = latestProject.pages || [];

          // 统计处理中的原型数量
          const processingCount = latestPages.reduce((count, page) => {
            return count + page.prototypes.filter((p) => p.status === 'processing').length;
          }, 0);

          console.log(`📈 生成结果状态更新完成，处理中的原型数量: ${processingCount}`);
          setProject(latestProject);

          // 检查是否还有处理中的任务
          const stillHasProcessing = hasProcessingTasks(latestPages);

          if (!stillHasProcessing) {
            console.log('✅ 所有任务已完成，停止状态轮询');
            // 直接清理interval，避免异步状态更新导致的问题
            clearInterval(pollInterval);
            setStatusPollingInterval(null);
          }
        }
      } catch (error) {
        console.error('❌ 状态轮询失败:', error);
        // 轮询失败时不要停止轮询，继续尝试
      }
    }, 15000); // 15秒间隔

    setStatusPollingInterval(pollInterval);
  };

  // 停止状态轮询
  const stopStatusPolling = () => {
    if (statusPollingInterval) {
      console.log('⏹️ 停止生成结果状态轮询');
      clearInterval(statusPollingInterval);
      setStatusPollingInterval(null);
    }
  };

  // 加载转码历史
  const loadTranscodeHistory = async () => {
    setHistoryLoading(true);
    try {
      const data = await getProjectTasks({ projectId });

      console.log(data);

      setTranscodeHistory(data || []);
    } catch (error) {
      console.error('加载转码历史失败:', error);
    } finally {
      setHistoryLoading(false);
    }
  };

  // 处理转码历史按钮点击
  const handleTranscodeHistoryClick = () => {
    if (!showHistoryCard) {
      loadTranscodeHistory(); // 打开时加载数据
    }
    setShowHistoryCard(!showHistoryCard);
  };

  // 处理页面选择变化
  const handlePageSelectionChange = (pageId: string, checked: boolean) => {
    const newSelected = new Set(selectedPages);

    if (checked) {
      newSelected.add(pageId);
    } else {
      newSelected.delete(pageId);
    }
    setSelectedPages(newSelected);
  };

  // 处理合并任务创建后的回调
  const handleMergeTaskCreated = () => {
    // 启动状态轮询（如果还未启动）
    if (!statusPollingInterval) {
      startStatusPolling();
    }
  };

  // 处理生成项目级代码
  const handleGenerateProjectCode = async () => {
    if (!project || !userInfo) {
      addToast({
        title: '错误',
        description: '项目信息或用户信息不完整',
        color: 'danger',
      });

      return;
    }

    // 获取选中的页面
    const selectedPagesData = pages.filter((page) => selectedPages.has(page.id));

    if (selectedPagesData.length === 0) {
      addToast({
        title: '请选择页面',
        description: '请先选择要生成项目级代码的页面',
        color: 'warning',
      });

      return;
    }

    try {
      // 构建页面数据 - 只处理选中的页面
      const pageData = selectedPagesData.map((page) => ({
        name: page.name,
        htmlContent: page.resultHtml || '',
        description: page.description || '',
      }));

      const taskName = `${project.name}代码生成任务`;
      const gitUrl = 'git@*************:codebox-test/zhangyu-test/eam-web-app.git';
      // todo lz mock

      await createSpecToProdCodeTask({
        projectId,
        taskName,
        gitUrl,
        branch: 'feature/d2c-test',
        pages: pageData,
        user: userInfo.username,
      });

      addToast({
        title: '任务创建成功[git 地址和分支为mock 数据]',
        description: `已创建项目级代码生成任务：${taskName}`,
        color: 'success',
      });

      // 启动状态轮询
      if (!statusPollingInterval) {
        startStatusPolling();
      }
    } catch (error) {
      console.error('创建项目级代码生成任务失败:', error);
      addToast({
        title: '任务创建失败',
        description: (error as Error)?.message || '创建项目级代码生成任务时发生错误',
        color: 'danger',
      });
    }
  };

  if (loading || !project) {
    return <div>加载中...</div>;
  }

  const pages = project.pages || [];

  return (
    <div className="space-y-6">
      {/* 项目级操作按钮区域 */}
      <div className="flex items-center justify-between p-3">
        {/* 左侧项目信息 */}
        <div className="">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-2xl font-bold">{project?.name || '设计稿转码工程'}</h1>
            <Button color="default" size="sm" variant="bordered" onPress={handleTranscodeHistoryClick}>
              查看转码历史
            </Button>
            <Button
              color="default"
              startContent={<IconPlay />}
              variant="bordered"
              onPress={() => navigate(`/project-preview/${projectId}?tab=results`)}
            >
              工作流历史
            </Button>
          </div>
          <p className="text-gray-600 dark:text-gray-400">{project?.description || '管理设计稿页面和转码任务'}</p>
        </div>

        {/* 右侧按钮交互区域 */}
        <div className="flex items-center">
          <Chip color="primary" size="sm" variant="flat">
            已选择 {selectedPages.size} 个页面
          </Chip>
          <Button
            className="ml-3 bg-gradient-to-tr from-pink-500 to-yellow-500 shadow-lg"
            color="default"
            size="md"
            startContent={<IconMerge />}
            variant="solid"
            onPress={handleGenerateProjectCode}
          >
            生成项目级代码
          </Button>
        </div>
      </div>

      {/* 页面列表 */}
      <div className="space-y-4">
        {pages.map((page) => (
          <PageCard
            key={page.id}
            isPageSelected={selectedPages.has(page.id)}
            page={page}
            projectId={projectId}
            onMergeTaskCreated={handleMergeTaskCreated}
            onPageSelectionChange={handlePageSelectionChange}
          />
        ))}
      </div>

      {/* 转码历史卡片 */}
      <TranscodeHistoryCard
        historyLoading={historyLoading}
        isVisible={showHistoryCard}
        transcodeHistory={transcodeHistory}
        onClose={() => setShowHistoryCard(false)}
        onRefresh={loadTranscodeHistory}
      />
    </div>
  );
};
