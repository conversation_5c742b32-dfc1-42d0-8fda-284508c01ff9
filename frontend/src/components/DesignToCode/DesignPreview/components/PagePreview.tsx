import { Card, CardBody, CardHeader, Button, addToast, Modal, ModalContent, ModalHeader, ModalBody, useDisclosure } from '@heroui/react';
import { Image } from 'antd';
import { PageAggregation } from '../PageAggregation';
import { deleteDesignPage, deletePagePrototype, DesignPage } from '@/apis';
import IconDelete from '~icons/mdi/delete';
import IconFolderOutline from '~icons/mdi/folder-outline';
import IconEdit from '~icons/mdi/pencil';
import IconPlus from '~icons/mdi/plus';

interface PagePreviewProps {
  id: string;
  nodeData?: any; // 从树节点传递的完整数据，包含原型信息
  onPageUpdated?: () => void; // 页面更新后的回调
  onEditPage?: (page: DesignPage) => void; // 编辑页面的回调
  onPrototypeDeleted?: () => void; // 原型删除后的回调（不清空选中节点）
  onPrototypeAdded?: () => void; // 添加原型后的回调（不清空选中节点但更新数据）
  // 新增用于添加原型的属性
  project?: any; // 项目数据，用于传递给PageAggregation
  unassignedImages?: any[]; // 未分配的蓝湖图片数据
  unassignedImagesLoading?: boolean; // 图片加载状态
}

export function PagePreview({ id, nodeData, onPageUpdated, onEditPage, onPrototypeDeleted, onPrototypeAdded, project, unassignedImages, unassignedImagesLoading }: PagePreviewProps) {
  // 添加原型模态框的状态管理
  const { isOpen: isAddPrototypeOpen, onOpen: onAddPrototypeOpen, onClose: onAddPrototypeClose } = useDisclosure();
  const handleEditPage = (page: any) => {
    if (onEditPage) {
      // 使用传入的编辑回调函数
      onEditPage(page);
    }
  };

  const handleDeletePage = async () => {
    if (!confirm('确定要删除这个页面吗？这将删除页面的所有原型。')) {
      return;
    }

    if (!id || !nodeData?.designProjectId) {
      addToast({
        title: '缺少页面ID或项目ID，无法删除页面',
        color: 'danger',
      });

      return;
    }

    try {
      // projectId 从 nodeData.designProjectId 获取，pageId 使用 props 中的 id
      await deleteDesignPage({
        projectId: nodeData.designProjectId,
        pageId: id
      });
      addToast({
        title: '页面删除成功',
        color: 'success',
      });
      // 调用回调函数通知父组件更新
      onPageUpdated?.();
    } catch (error) {
      console.error('删除设计稿页面失败:', error);
      addToast({
        title: '删除失败，请重试',
        color: 'danger',
      });
    }
  };

  const handleDeletePrototype = async (prototypeId: string, prototypeName: string) => {
    if (!confirm(`确定要删除原型"${prototypeName}"吗？`)) {
      return;
    }

    if (!id || !nodeData?.designProjectId) {
      addToast({
        title: '缺少页面ID或项目ID，无法删除原型',
        color: 'danger',
      });

      return;
    }

    try {
      await deletePagePrototype({
        projectId: nodeData.designProjectId,
        pageId: id,
        prototypeId: prototypeId
      });
      addToast({
        title: '原型删除成功',
        color: 'success',
      });
      // 调用原型删除专用回调函数（不清空选中节点）
      onPrototypeDeleted?.();
    } catch (error) {
      console.error('删除原型失败:', error);
      addToast({
        title: '删除原型失败，请重试',
        color: 'danger',
      });
    }
  };

  // 处理添加原型成功后的回调
  const handlePrototypeAdded = () => {
    onAddPrototypeClose(); // 关闭模态框
    onPrototypeAdded?.(); // 使用专门的添加原型回调（不清空选中节点）
  };

  // 创建一个修改过的项目对象，将当前页面设置为默认选中
  const projectWithPreSelectedPage = project ? {
    ...project,
    pages: project.pages?.map((page: any) => ({
      ...page,
      // 如果是当前页面，标记为预选
      isPreSelected: page.id === id
    }))
  } : null;

  // 计算页面统计数据
  const prototypeCount = nodeData?.prototypes?.length || 0;
  const createdDate = nodeData?.created ? new Date(nodeData.created).toLocaleDateString('zh-CN') : '';

  return (
    <div className="h-full p-3">
      <Card className="shadow-none">
        <CardHeader className="flex items-center justify-between pb-3">
          <div className="flex items-center gap-3">
            <IconFolderOutline className="text-xl" />
            <h2 className="text-lg font-semibold">{nodeData?.name || nodeData?.prototypeName}</h2>
          </div>
          <div className="flex gap-2">
            <Button
              className="rounded-md"
              size="sm"
              startContent={<IconEdit />}
              variant="light"
              onPress={() => handleEditPage(nodeData)}
            >
              编辑
            </Button>
            <Button
              className="rounded-md"
              color="danger"
              size="sm"
              startContent={<IconDelete />}
              variant="light"
              onPress={handleDeletePage}
            >
              删除
            </Button>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* 描述信息 */}
          {nodeData?.description && (
            <p className="text-default-600 mb-4">
              {nodeData.description}
            </p>
          )}

          {/* 页面属性 */}
          <div className="mb-6">
            <h3 className="text-base font-semibold mb-4">页面属性</h3>
            <div className="flex justify-evenly items-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-1">{prototypeCount}</div>
                <div className="text-sm text-default-600">原型数量</div>
              </div>
              {/* <div className="text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">{boundCount}</div>
                <div className="text-sm text-default-600">已绑定</div>
              </div> */}
              <div className="text-center">
                <div className="text-lg font-bold text-default-800 mb-1">{createdDate}</div>
                <div className="text-sm text-default-600">创建日期</div>
              </div>
            </div>
          </div>

          {/* 包含的原型 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base font-semibold">原型</h3>
              <Button
                className="rounded-md"
                color="primary"
                size="sm"
                startContent={<IconPlus />}
                variant="light"
                onPress={onAddPrototypeOpen}
              >
                添加原型
              </Button>
            </div>
            {nodeData?.prototypes && nodeData.prototypes.length > 0 && (
              <div className="flex flex-wrap gap-3 justify-start">
                {nodeData.prototypes.map((prototype: any, index: number) => (
                  <Card 
                    key={prototype.id || index} 
                    className="shadow-none border-1 dark:border-zinc-700 rounded-md w-72 flex-shrink-0"
                  >
                    <CardBody className="p-3">
                      <div className="flex flex-col gap-3">
                        {/* 缩略图 */}
                        <div className="w-full h-24 rounded-md overflow-hidden">
                          {prototype.imgFileLink ? (
                            <Image
                              alt={prototype.prototypeName || `原型 ${index + 1}`}
                              className="w-full h-full object-cover"
                              fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA2NCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAxNkgyNFYyMEgyMFYxNlpNMjggMTZIMzJWMjBIMjhWMTZaTTM2IDE2SDQwVjIwSDM2VjE2Wk0yMCAyNEgyNFYyOEgyMFYyNFpNMjggMjRIMzJWMjhIMjhWMjRaTTM2IDI0SDQwVjI4SDM2VjI0Wk0yMCAzMkgyNFYzNkgyMFYzMlpNMjggMzJIMzJWMzZIMjhWMzJaTTM2IDMySDQwVjM2SDM2VjMyWiIgZmlsbD0iI0QxRDVEM0EiLz4KPC9zdmc+"
                              preview={{
                                mask: '点击预览',
                                maskClassName: 'rounded-md'
                              }}
                              src={prototype.imgFileLink}
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center">
                              <div className="text-gray-400 text-sm">📄</div>
                            </div>
                          )}
                        </div>
                        
                        {/* 原型信息 */}
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div className="font-medium text-sm truncate flex-1" title={prototype.prototypeName || `原型 ${index + 1}`}>
                              {prototype.prototypeName}
                            </div>
                            {/* 删除按钮 */}
                            <Button
                              className="w-6 h-6 min-w-6 rounded-md flex-shrink-0"
                              color="danger"
                              size="sm"
                              variant="light"
                              onPress={() => handleDeletePrototype(prototype.id, prototype.prototypeName || `原型 ${index + 1}`)}
                            >
                              删除
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}
            {(!nodeData?.prototypes || nodeData.prototypes.length === 0) && (
              <div className="text-center py-8 text-gray-500">
                <p className="text-sm">暂无原型</p>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* 添加原型模态框 */}
      <Modal
        classNames={{
          base: "rounded-md",
          header: "rounded-t-md",
          body: "rounded-b-md",
        }}
        isOpen={isAddPrototypeOpen}
        isDismissable={false}
        placement="center"
        scrollBehavior="inside"
        size="5xl"
        onClose={onAddPrototypeClose}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h3 className="text-lg font-semibold">页面绑定</h3>
          </ModalHeader>
          <ModalBody className="p-6">
            {projectWithPreSelectedPage && (
              <PageAggregation
                autoSelectImages={false} // 从PagePreview进入不自动选中图片
                lanhuImages={unassignedImages || []}
                lanhuImagesLoading={unassignedImagesLoading || false}
                project={projectWithPreSelectedPage}
                selectedPageId={id} // 传递当前页面ID作为默认选中
                onCancel={onAddPrototypeClose}
                onPageAdded={handlePrototypeAdded}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
}