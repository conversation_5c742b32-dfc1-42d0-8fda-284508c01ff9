import { useRef, useEffect, useState } from 'react';
import { NodeType } from '../../DesignFileTree';
import { ISegment } from '@/apis';
import SelectImageSplitArea from '@/components/SplitImage/ImageSelector/SelectImageSplitArea';

interface ImgPreviewProps {
  id: string;
  handleGenerateCode: () => void;
  selectedNode?: any; // 从树节点传递的完整数据，包含原型信息
}

export function ImgPreview(props: ImgPreviewProps) {
  const { selectedNode, handleGenerateCode } = props;
  const imageRef = useRef<any>();
  const [selectAreaMaxWidth, setSelectAreaMaxWidth] = useState();
  const { type, data: nodeData } = selectedNode;

  const isSliced = type === NodeType.Sliced; // 是否是切图的资源

  const getInitialRectsPosition = (nodeData: any) => {
    return (
      nodeData?.slicedAssets?.segment?.map((item: ISegment) => {
        const { position } = item;

        return {
          position,
        };
      }) || []
    );
  };

  useEffect(() => {
    const { width } = imageRef.current?.getBoundingClientRect();

    setSelectAreaMaxWidth(width);
  }, []);

  return (
    <div className="h-full flex flex-col">
      {/* 固定在顶部的按钮区域 */}
      {/* {!isSliced && (
        <div className="flex justify-end p-3 flex-shrink-0 border-b border-divider">
          <Button color="primary" size="sm" onPress={handleGenerateCode}>
            生成代码
          </Button>
        </div>
      )} */}

      {/* 可滚动的图片预览区域 */}
      <div
        ref={imageRef}
        className="flex-1 flex justify-center overflow-auto"
        style={{ minHeight: 0 }}
      >
        {selectAreaMaxWidth && (
          <SelectImageSplitArea
            imageUrl={nodeData.imgFileLink || nodeData.fullImgUrl || nodeData.imgUrl}
            initialRectsPosition={getInitialRectsPosition(nodeData)}
            initialScale={1}
            isPreview={true}
          />
        )}
      </div>
    </div>
  );
}
