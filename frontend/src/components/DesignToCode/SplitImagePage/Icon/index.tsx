export const SplitIcon = () => {
  return (
    <svg
      viewBox="64 64 896 896"
      focusable="false"
      data-icon="scissor"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M567.1 512l318.5-319.3c5-5 1.5-13.7-5.6-13.7h-90.5c-2.1 0-4.2.8-5.6 2.3l-273.3 274-90.2-90.5c12.5-22.1 19.7-47.6 19.7-74.8 0-83.9-68.1-152-152-152s-152 68.1-152 152 68.1 152 152 152c27.7 0 53.6-7.4 75.9-20.3l90 90.3-90.1 90.3A151.04 151.04 0 00288 582c-83.9 0-152 68.1-152 152s68.1 152 152 152 152-68.1 152-152c0-27.2-7.2-52.7-19.7-74.8l90.2-90.5 273.3 274c1.5 1.5 3.5 2.3 5.6 2.3H880c7.1 0 10.7-8.6 5.6-13.7L567.1 512zM288 370c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80zm0 444c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"></path>
    </svg>
  );
};

export const SmartSplitIcon = () => {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19V21ZM7 11H17V13H7V11ZM7 15H17V17H7V15ZM7 19H14V21H7V19Z" />
      <circle cx="18" cy="6" r="2" fill="#3b82f6" />
      <path d="M16.5 4.5L17.5 5.5L19.5 3.5" stroke="white" strokeWidth="0.8" fill="none" />
    </svg>
  );
};

export const CodeIcon = () => {
  return (
    <svg width="1em" height="1em" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg" className="text-[14px]">
      <path
        d="m225.6 113.6-36-36c-3.976-3.976-10.424-3.976-14.4 0-3.976 3.976-3.976 10.424 0 14.4l36 36-36 36c-3.976 3.976-3.976 10.424 0 14.4 3.976 3.976 10.424 3.976 14.4 0l36-36c7.951-7.954 7.951-20.846 0-28.8Zm-159.2-36-36 36c-7.951 7.954-7.951 20.846 0 28.8l36 36A10.182 10.182 0 1 0 80.8 164l-36-36 36-36c3.976-3.976 3.976-10.424 0-14.4-3.976-3.976-10.424-3.976-14.4 0Zm27.392 136.016 48.768-176.64a10.182 10.182 0 0 1 19.632 5.408l-48.768 176.64a10.184 10.184 0 1 1-19.632-5.424v.016Z"
        fill="currentColor"
        fillRule="nonzero"
      ></path>
    </svg>
  );
};

export const ClearIcon = () => {
  return (
    <svg
      viewBox="64 64 896 896"
      focusable="false"
      data-icon="delete"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
    </svg>
  );
};

export const UndoIcon = () => {
  return (
    <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <path id="1815803851a" d="M0 0h20v20H0z"></path>
      </defs>
      <g fill="none" fillRule="evenodd">
        <path
          d="m6.28 8.978.018.015a.781.781 0 0 1-1.009 1.193L2.073 7.544l-.022-.018a1.406 1.406 0 0 1 .022-2.156l3.216-2.642.018-.014a.781.781 0 0 1 1.082.122l.014.018a.781.781 0 0 1-.122 1.082L4.413 5.469H12.4c3.704 0 6.212 2.367 6.212 5.625 0 3.225-2.458 5.577-6.101 5.624H8.602a.781.781 0 0 1-.023-1.561h3.822c2.86 0 4.65-1.69 4.65-4.063 0-2.35-1.754-4.029-4.564-4.062H12.4L3.91 7.03l2.37 1.947Z"
          fill="currentColor"
          fillRule="nonzero"
          mask="url(#1815803851b)"
        ></path>
      </g>
    </svg>
  );
};

export const RedoIcon = () => {
  return (
    <svg width="1em" height="1em" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <path id="3101216855a" d="M0 0h20v20H0z"></path>
      </defs>
      <g transform="matrix(-1 0 0 1 20 0)" fill="none" fillRule="evenodd">
        <path
          d="m6.28 8.978.018.015a.781.781 0 0 1-1.009 1.193L2.073 7.544l-.022-.018a1.406 1.406 0 0 1 .022-2.156l3.216-2.642.018-.014a.781.781 0 0 1 1.082.122l.014.018a.781.781 0 0 1-.122 1.082L4.413 5.469H12.4c3.704 0 6.212 2.367 6.212 5.625 0 3.225-2.458 5.577-6.101 5.624H8.602a.781.781 0 0 1-.023-1.561h3.822c2.86 0 4.65-1.69 4.65-4.063 0-2.35-1.754-4.029-4.564-4.062H12.4L3.91 7.03l2.37 1.947Z"
          fill="currentColor"
          fillRule="nonzero"
          mask="url(#3101216855b)"
        ></path>
      </g>
    </svg>
  );
};

export const ZoomOutIcon = () => {
  return (
    <svg
      viewBox="64 64 896 896"
      focusable="false"
      data-icon="minus"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"></path>
    </svg>
  );
};

export const ZoomInIcon = () => {
  return (
    <svg
      viewBox="64 64 896 896"
      focusable="false"
      data-icon="plus"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path>
      <path d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"></path>
    </svg>
  );
};
