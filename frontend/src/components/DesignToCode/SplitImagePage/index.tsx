import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, Input, Spinner, addToast } from '@heroui/react';
import { useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SplitIcon, CodeIcon, ClearIcon, UndoIcon, RedoIcon, ZoomOutIcon, ZoomInIcon } from './Icon';
import {
  SlicedAssets,
  imgVisualSplitApi,
  Project,
  imgToCodeApi,
  splitImgToCodeApi,
  coordsToLayoutApi,
  asyncImgToCodeTaskCreate,
  IAsyncImgToCodeTaskCreateParam,
  ISplitImgToCodeItem,
  IImgToCodeItem,
  IImgVisualSplitItem,
  ETaskType,
  IAsyncImgToCodeItem,
} from '@/apis';
import SelectImageSplitArea, {
  SelectAreaRef,
  RectData,
} from '@/components/SplitImage/ImageSelector/SelectImageSplitArea';
import { useUserInfoStore } from '@/hooks/login';

interface ISplitImagePageProps {
  project?: Project; // 项目数据，用于保存功能
  nodeData: {
    imgFileLink?: string;
    imageContent?: string;
    imgWidth: string;
    imgHeight: string;
    id?: string;
  };
  onClose?: () => void;
  handleUpdateSlicedAssets?: (prototypeId: string, slicedAssets: SlicedAssets) => Promise<any>;
  isAsync?: boolean;
  model?: string;
}

interface ICoordinate {
  name: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

interface IUploadedRect extends RectData {
  id: string;
  imgUrl: string;
  name: string;
}

export function SplitImagePage({
  project,
  nodeData,
  onClose,
  handleUpdateSlicedAssets,
  isAsync, // 是否异步生码
  model,
}: ISplitImagePageProps) {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const selectAreaRef = useRef<SelectAreaRef>(null);
  const modalBodyRef = useRef<HTMLDivElement>(null);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [scale, setScale] = useState(1);
  const [initialWidth, setInitialWidth] = useState(0);
  const [showSpinner, setShowSpinner] = useState(false);
  const [isGenerating, setGenerating] = useState(false);
  const [rectsPosition, setRectsPosition] = useState<RectData[]>([]);
  // 缩放相关常量
  const MIN_ZOOM = 0.1; // 最小缩放10%
  const MAX_ZOOM = 2; // 最大缩放500%
  const ZOOM_STEP = 0.1; // 缩放步长10%
  const { imgFileLink, imageContent, imgWidth, imgHeight } = nodeData;
  const btnDisable = showSpinner || isGenerating;
  const prototypeId = nodeData.id || '';
  const projectId = project?.id || '';

  const handleClose = () => {
    onClose && onClose();
  };

  useEffect(() => {
    if (!modalBodyRef.current) {
      return;
    }
    const percent = 2 / 3;
    const modalBodyWidth = modalBodyRef.current.offsetWidth;

    const contentInitialWidth = modalBodyWidth * percent;

    setInitialWidth(contentInitialWidth);
  }, []);

  // 定期检查撤销重做状态
  useEffect(() => {
    const checkUndoRedoState = () => {
      if (selectAreaRef.current) {
        setCanUndo(selectAreaRef.current.canUndo());
        setCanRedo(selectAreaRef.current.canRedo());
      }
    };

    const interval = setInterval(checkUndoRedoState, 100);

    return () => clearInterval(interval);
  }, []);

  const handleUndo = () => {
    selectAreaRef.current?.undo();
  };

  const handleRedo = () => {
    selectAreaRef.current?.redo();
  };

  const handleClearSelection = () => {
    selectAreaRef.current?.clearSelection();
  };

  // 上传图片到 MinIO 的函数
  const uploadImageToMinIO = async (file: File): Promise<string> => {
    const fd = new FormData();

    fd.append('file', file);

    try {
      const response = await fetch('/api/ai-coding/@uploadImage', {
        method: 'POST',
        body: fd,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const res = await response.json();

      // 响应格式: {"code":0,"message":"success","data":[{"data":"http://...","success":true}]}
      if (res && res.code === 0 && res.data && Array.isArray(res.data) && res.data.length > 0) {
        const uploadResult = res.data[0];

        if (uploadResult.success && uploadResult.data) {
          return uploadResult.data;
        }
      }
      throw new Error('上传响应中没有找到文件URL或上传失败');
    } catch (error) {
      throw new Error('图片上传失败');
    }
  };

  // 生成base64的hash值并截取前5位
  const generateBase64Hash = (base64: string): string => {
    let hash = 0;
    const str = base64.replace(/^data:image\/[a-z]+;base64,/, ''); // 移除data URL前缀

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);

      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }

    // 转换为正数并转为16进制，然后截取前5位
    const hashStr = Math.abs(hash).toString(16);

    return hashStr.substring(0, 5).padStart(5, '0');
  };

  // 将 base64 转换为 File 对象
  const base64ToFile = (base64: string, filename: string): File => {
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, { type: mime });
  };

  const handleTaskError = (title = '拆分失败，请重试！') => {
    addToast({
      color: 'danger',
      title,
    });
  };

  const buildImgToCodeItem = () => {
    const item: IImgToCodeItem = {
      prototypeId, // 原型ID
      metadata: {},
    };

    if (imgFileLink) {
      item.metadata.imageUrl = imgFileLink;
    } else if (imageContent) {
      item.metadata.imageContent = imageContent;
    }

    return item;
  };

  const handleImgToCodeApi = async () => {
    const item = buildImgToCodeItem();
    const params = {
      user: userInfo?.username,
      projectId,
      items: [item],
      waitForCompletion: true,
    };
    const result = await imgToCodeApi(params).catch(() => null);

    if (result?.files?.['index.html']) {
      return result.files['index.html'].content;
    }

    if (result?.files?.[0]?.content) {
      return result.files[0].content;
    }

    throw new Error('图片转码失败，请重试！');
  };

  const handleImgToCodeHtml = async (htmlContent: string) => {
    if (handleUpdateSlicedAssets && imgFileLink && prototypeId) {
      // 调用外层的update方法
      await handleUpdateSlicedAssets(prototypeId, {
        html: {
          name: 'index.html',
          content: htmlContent,
        },
        fullImgUrl: imgFileLink,
      });
    }
  };

  // 上传所有切图到 MinIO
  const splitImgUpload = async (rectList: RectData[]): Promise<IUploadedRect[] | undefined> => {
    const uploadPromises = rectList.map(async (rect, index) => {
      const { image } = rect;

      if (image) {
        try {
          const id = generateBase64Hash(image);
          const filename = `${id}_${index}.png`;
          // 将 base64 转换为 File 对象
          const file = base64ToFile(image, filename);

          // 上传到 MinIO
          const imgUrl = await uploadImageToMinIO(file);

          return {
            ...rect,
            imgUrl,
            name: filename,
            id,
          };
        } catch (error) {
          return {
            ...rect,
            uploadError: `上传失败: ${error instanceof Error ? error.message : String(error)}`,
          };
        }
      }

      return rect;
    });

    const uploadedRectList = await Promise.all(uploadPromises);

    // 检查是否有上传失败的项目
    const failedUploads = uploadedRectList.filter((rect) => 'uploadError' in rect);

    if (failedUploads.length > 0) {
      throw new Error(`有 ${failedUploads.length} 个组件图片上传失败，请重新点击按钮重试`);
    }

    return uploadedRectList as IUploadedRect[];
  };
  // 正则表达式匹配body标签内的内容
  const getBodyContent = (htmlString: string) => {
    const bodyContentRegex = /<body[^>]*>([\s\S]*?)<\/body>/i;
    // 提取body内容
    const match = htmlString.match(bodyContentRegex);

    return match ? match[1].trim() : '';
  };

  // 将每个图的HTML内容插入到layoutContent对应位置中
  const insertComponentsIntoLayout = (
    layoutContent: string,
    uploadedRectList: IUploadedRect[],
    bodyContentList: string[],
  ): string => {
    let finalHtml = layoutContent;

    // 为每个组件创建一个占位符到实际内容的映射
    uploadedRectList.forEach((rect, index) => {
      const componentContent = bodyContentList[index] || '';
      const { position } = rect;
      const { left, top, right, bottom } = position;

      // 根据坐标生成容器class名称，格式：container_left_top_right_bottom
      const containerClassName = `container_${left}_${top}_${right}_${bottom}`;

      // console.log(`正在查找容器: ${containerClassName}`);

      // 查找对应的容器元素并替换其内容
      // 匹配格式：<div class="container_left_top_right_bottom"></div>
      const containerPattern = new RegExp(
        `(<div\\s+class=["']${containerClassName}["'][^>]*>)([\\s\\S]*?)(<\\/div>)`,
        'gi',
      );

      const match = finalHtml.match(containerPattern);

      if (match) {
        // 找到匹配的容器，替换其内容
        finalHtml = finalHtml.replace(containerPattern, `$1${componentContent}$3`);
        // console.log(`成功替换容器 ${containerClassName} 的内容`);
      }
    });

    return finalHtml;
  };

  const imgSplitToCode = async (reactItem: IUploadedRect) => {
    const { position, imgUrl } = reactItem;
    const { left, top, right, bottom } = position;
    const height = bottom - top;
    const width = right - left;
    const params = {
      user: userInfo?.username,
      projectId,
      items: [
        {
          prototypeId, // 原型ID
          metadata: {
            // imageContent: image, // base64
            imageUrl: imgUrl,
            imgHeight: height.toString(),
            imgWidth: width.toString(),
          },
        },
      ],
      waitForCompletion: true,
    };

    const result = await splitImgToCodeApi(params).catch(() => null);

    if (result?.files?.['index.html']) {
      return result.files['index.html'].content;
    }

    if (result?.files?.[0]?.content) {
      return result.files[0].content;
    }
    throw new Error('切图生码失败');
  };

  const coordsToLayout = async (reactItemList: IUploadedRect[]) => {
    const params = {
      user: userInfo?.username || '',
      projectId,
      items: [
        {
          prototypeId, // 原型ID
          metadata: {
            imgHeight,
            imgWidth,
            coordinates: reactItemList.map((reactItem) => {
              const { position, name } = reactItem;
              const { left, top, right, bottom } = position;

              return {
                name,
                x1: left,
                y1: top,
                x2: right,
                y2: bottom,
              };
            }),
          },
        },
      ],
      waitForCompletion: true,
    };
    const result = await coordsToLayoutApi(params).catch(() => null);

    if (result?.files?.['index.html']) {
      return result.files['index.html'].content;
    }
    if (result?.files?.[0]?.content) {
      return result.files[0].content;
    }
    throw new Error('布局生成失败');
  };

  const handleSuccessGenerate = () => {
    setGenerating(false);
    handleClose();
  };

  const handleErrorGenerate = () => {
    handleTaskError('图片转码失败，请重试！');
    setGenerating(false);
  };

  // 异步拆图生码
  const handleAsyncSplitImgToCodeTask = async (uploadedRectList: IUploadedRect[]) => {
    const items = [
      {
        prototypeId, // 原型ID
        metadata: {
          imgHeight,
          imgWidth,
          coordinates: uploadedRectList.map((reactItem) => {
            const { position, name } = reactItem;
            const { left, top, right, bottom } = position;

            return {
              name,
              x1: left,
              y1: top,
              x2: right,
              y2: bottom,
            };
          }),
        },
      },
    ];

    const imgSplitItems: ISplitImgToCodeItem[] = uploadedRectList.map((reactItem) => {
      const { position, imgUrl } = reactItem;
      const { left, top, right, bottom } = position;
      const height = bottom - top;
      const width = right - left;

      return {
        prototypeId, // 原型ID
        metadata: {
          imageUrl: imgUrl,
          imgHeight: height.toString(),
          imgWidth: width.toString(),
        },
      };
    });

    await handleAsyncImgToCodeTaskCreate(ETaskType.SPLIT_IMG_TO_CODE, items, imgSplitItems);
    handleSuccessGenerate();
  };

  // 同步拆图生码
  const handleSyncSplitImgToCodeTask = async (uploadedRectList: IUploadedRect[]) => {
    const [layoutContent, ...codeResList] = await Promise.all([
      coordsToLayout(uploadedRectList), // 生成布局
      ...uploadedRectList.map((reactItem) => {
        return imgSplitToCode(reactItem);
      }),
    ]);
    const bodyContentList = codeResList.map((htmlContent) => getBodyContent(htmlContent));
    // console.log('--------------uploadedRectList----------------');
    // console.log(uploadedRectList);
    // console.log(bodyContentList);
    // console.log(layoutContent);

    // 将每个图的HTML内容插入到layoutContent对应位置中
    const finalHtmlContent = insertComponentsIntoLayout(layoutContent, uploadedRectList, bodyContentList);
    // console.log('--------------finalHtmlContent----------------');
    // console.log(finalHtmlContent);

    // 调用外层的update方法
    if (handleUpdateSlicedAssets && imgFileLink && prototypeId) {
      await handleUpdateSlicedAssets(prototypeId, {
        html: {
          name: 'index.html',
          content: finalHtmlContent,
        },
        fullImgUrl: imgFileLink,
        segment: uploadedRectList.map((item) => {
          const { id, imgUrl, name, position } = item;

          return {
            id,
            name,
            imgUrl,
            position,
          };
        }),
      });
    }

    handleSuccessGenerate();
  };

  // 切图生码
  const handleSplitToCode = async (rectList: RectData[]) => {
    const uploadedRectList = await splitImgUpload(rectList); // 上传所有切图到 MinIO

    if (!uploadedRectList) {
      return;
    }

    if (isAsync) {
      await handleAsyncSplitImgToCodeTask(uploadedRectList);

      return;
    }

    await handleSyncSplitImgToCodeTask(uploadedRectList);
  };

  const handleAsyncImgToCodeTaskCreate = async (
    type: ETaskType,
    items: IAsyncImgToCodeItem[],
    imgSplitItems?: ISplitImgToCodeItem[],
  ) => {
    const params: IAsyncImgToCodeTaskCreateParam = {
      user: userInfo?.username,
      items,
      type,
    };

    if (imgSplitItems?.length) {
      params.imgSplitItems = imgSplitItems;
    }
    if (projectId) {
      params.projectId = projectId;
    }

    if (model) {
      params.model = model;
    }

    const res = await asyncImgToCodeTaskCreate(params).catch(() => null);

    const chatId = res?.chatId;

    if (chatId) {
      handleSuccessGenerate();
      navigate(`/chat/${chatId}`);

      return;
    }
    handleTaskError('图片转码失败，请重试！');
  };

  // 异步图生码
  const asyncImgToCode = async () => {
    const item = buildImgToCodeItem();

    await handleAsyncImgToCodeTaskCreate(ETaskType.DIRECT_IMG_TO_CODE, [item]);
    handleSuccessGenerate();
  };

  // 同步图生码
  const syncImgToCode = async () => {
    const htmlContent = await handleImgToCodeApi();

    await handleImgToCodeHtml(htmlContent);
    handleSuccessGenerate();
  };

  const handleImgToCode = async () => {
    try {
      if (isAsync) {
        await asyncImgToCode();

        return;
      }
      await syncImgToCode();
    } catch (error) {
      handleErrorGenerate();
    }
  };

  const handleGenerateCode = async () => {
    const rectList = selectAreaRef.current?.getAllRects();

    if (!rectList || rectList.length === 0) {
      return;
    }

    setGenerating(true);
    if (rectList.length === 1 && !rectList[0]?.image) {
      // 1. 图生码
      await handleImgToCode();

      return;
    }

    try {
      // 2. 切图生码
      await handleSplitToCode(rectList);
    } catch (error) {
      handleErrorGenerate();
    }
  };

  // 缩放相关功能
  const updateZoom = (newZoom: number) => {
    // 限制缩放范围
    const clampedZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, newZoom));
    // 四舍五入到小数点后1位，避免浮点数精度问题
    const roundedZoom = Math.round(clampedZoom * 10) / 10;

    setScale(roundedZoom);
  };

  const handZoomOut = () => {
    const newScale = scale - ZOOM_STEP;

    updateZoom(newScale);
  };

  const handleZoomIn = () => {
    const newScale = scale + ZOOM_STEP;

    updateZoom(newScale);
  };

  const handleCoordinates = (coordinates: ICoordinate[]) => {
    const rectsPositionList = coordinates.map((item) => {
      const { x1, y1, x2, y2 } = item;

      return {
        position: {
          left: x1,
          top: y1,
          right: x2,
          bottom: y2,
        },
      };
    });

    setRectsPosition(rectsPositionList);
  };

  const buildSmartSplitItem = () => {
    const item: IImgVisualSplitItem = {
      prototypeId, // 原型ID
      name: project?.name || '', // 项目名称
      metadata: {
        imgHeight,
        imgWidth,
      },
    };

    if (imgFileLink) {
      item.metadata.imageUrl = imgFileLink;
    } else if (imageContent) {
      item.metadata.imageContent = imageContent;
    }

    return item;
  };

  // 智能拆分功能
  const handleSmartSplit = async () => {
    setShowSpinner(true);

    try {
      const smartSplitItem = buildSmartSplitItem();
      const params = {
        user: userInfo?.username,
        projectId,
        items: [smartSplitItem],
        waitForCompletion: true,
      };

      const result = await imgVisualSplitApi(params).catch(() => null);

      if (result?.coordinates?.length) {
        handleCoordinates(result.coordinates);
      } else {
        handleTaskError();
      }
    } catch (error) {
      handleTaskError();
    } finally {
      setShowSpinner(false);
    }
  };

  return (
    <Modal
      classNames={{
        backdrop: 'bg-black/60',
      }}
      isOpen={true}
      scrollBehavior="inside"
      onClose={handleClose}
    >
      <ModalContent
        className="bg-[#18181b] h-screen"
        style={{
          maxWidth: 'calc(-48px + 100vw)',
          maxHeight: 'calc(-40px + 100vh)',
        }}
      >
        <ModalHeader className="px-10 mt-6 pb-4 flex justify-between items-center relative">
          <div className="flex items-center gap-3">
            <Button color="default" isDisabled={btnDisable} radius="none" size="sm" startContent={<SplitIcon />}>
              第一步：拆分组件
            </Button>
            {/* <Button
              color="primary"
              isDisabled={btnDisable}
              isLoading={showSpinner}
              radius="none"
              size="sm"
              startContent={!showSpinner && <SmartSplitIcon />}
              variant="bordered"
              onPress={handleSmartSplit}
            >
              {showSpinner ? '拆分中...' : '智能拆分'}
            </Button> */}
          </div>
          <div className="flex items-center justify-center absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <Input
              isReadOnly
              className="max-w-[100px]"
              endContent={
                <div className="cursor-pointer" onClick={handleZoomIn}>
                  <ZoomInIcon />
                </div>
              }
              isDisabled={btnDisable}
              radius="none"
              size="sm"
              startContent={
                <div className="cursor-pointer" onClick={handZoomOut}>
                  <ZoomOutIcon />
                </div>
              }
              value={`${(scale * 100).toFixed(0)}%`}
            />
          </div>
          <div className="flex gap-2">
            <Button
              color="default"
              isDisabled={!canUndo || btnDisable}
              radius="none"
              size="sm"
              startContent={<UndoIcon />}
              onPress={handleUndo}
            >
              撤销
            </Button>
            <Button
              color="default"
              isDisabled={!canRedo || btnDisable}
              radius="none"
              size="sm"
              startContent={<RedoIcon />}
              onPress={handleRedo}
            >
              重做
            </Button>
            <Button
              className="ml-5"
              color="default"
              isDisabled={btnDisable}
              radius="none"
              size="sm"
              startContent={<ClearIcon />}
              onPress={handleClearSelection}
            >
              清空组件
            </Button>
            <Button
              color="primary"
              isDisabled={btnDisable}
              isLoading={isGenerating}
              radius="none"
              size="sm"
              startContent={!isGenerating && <CodeIcon />}
              onPress={handleGenerateCode}
            >
              {isGenerating ? '生成中...' : '下一步：生成代码'}
            </Button>
          </div>
          <div className="border-t border-[rgb(63,63,63)] text-white py-2 flex items-center justify-center h-12 absolute -bottom-12 left-0 w-full z-[9] bg-[#18181b] font-normal">
            手动框选拆分组件，默认不拆分
          </div>
        </ModalHeader>
        <ModalBody className="flex items-center py-0">
          <div ref={modalBodyRef} className="flex items-center justify-center w-full mt-12 relative">
            {initialWidth && (
              <SelectImageSplitArea
                ref={selectAreaRef}
                imageContent={imageContent}
                imageUrl={imgFileLink}
                initialRectsPosition={rectsPosition}
                initialWidth={initialWidth}
                isPreview={showSpinner} // 当显示Spinner时禁用操作
                scale={scale}
                setScale={setScale}
              />
            )}
            {btnDisable && (
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center z-10">
                <div className="absolute top-[30vh] bg-white/90 rounded-lg p-6 flex flex-col items-center gap-3">
                  <Spinner color="primary" size="lg" />
                  {showSpinner && <span className="text-sm text-gray-700">智能拆分中，请稍候...</span>}
                  {isGenerating && <span className="text-sm text-gray-700">代码生成中，请稍候...</span>}
                </div>
              </div>
            )}
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
