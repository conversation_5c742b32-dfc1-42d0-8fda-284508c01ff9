import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  Modal<PERSON>ooter,
  Checkbox,
  addToast,
} from '@heroui/react';
import { useState } from 'react';
import { DesignPage, DesignProject } from '@/apis';
import { useUserInfoStore } from '@/hooks/login';
import { startProjectCodeGenWorkflow } from '@/utils/workflowUtils';
import IconPlay from '~icons/mdi/play';

interface ProjectCodeGenModalProps {
  /** 是否显示弹窗 */
  isOpen: boolean;
  /** 关闭弹窗回调 */
  onClose: () => void;
  /** 设计项目数据 */
  project: DesignProject | undefined;
  /** 项目ID */
  projectId: string;
}

export function ProjectCodeGenModal({
  isOpen,
  onClose,
  project,
  projectId,
}: ProjectCodeGenModalProps) {
  const { userInfo } = useUserInfoStore();

  // 项目级代码生成相关状态
  const [projectCodeGenPages, setProjectCodeGenPages] = useState<Set<string>>(new Set());
  const [isStartingProjectCodeGen, setIsStartingProjectCodeGen] = useState(false);

  // 启动项目级代码生成工作流
  const handleStartProjectCodeGenWorkflow = async () => {
    if (!userInfo?.username) {
      addToast({
        title: '用户信息不完整',
        color: 'danger',
      });

      return;
    }

    if (projectCodeGenPages.size === 0) {
      addToast({
        title: '请选择要生成代码的页面',
        color: 'danger',
      });

      return;
    }

    const pageIds = Array.from(projectCodeGenPages);

    setIsStartingProjectCodeGen(true);

    try {
      const result = await startProjectCodeGenWorkflow(
        projectId,
        pageIds,
        userInfo.username,
        {
          enableAutoIteration: true,
          enableStepByStep: false,
        }
      );

      addToast({
        title: `${result.workflowType === 'single-page' ? '页面级' : '多页面'}代码生成工作流启动成功`,
        description: `工作流ID: ${result.taskId}${result.workflowType === 'multi-page' ? `，共${pageIds.length}个页面` : ''}`,
        color: 'success',
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        if (result.workflowType === 'single-page') {
          window.open(`/workflow/${projectId}/${result.taskId}`, '_blank');
        } else {
          window.open(`/multi-workflow/${projectId}/${result.taskId}`, '_blank');
        }
      }, 1500);

      // 清空选择并关闭弹窗
      setProjectCodeGenPages(new Set());
      onClose();

    } catch (error) {
      console.error('启动项目级代码生成工作流失败:', error);
      addToast({
        title: '启动项目级代码生成工作流失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    } finally {
      setIsStartingProjectCodeGen(false);
    }
  };

  // 关闭弹窗时重置状态
  const handleClose = () => {
    setProjectCodeGenPages(new Set());
    setIsStartingProjectCodeGen(false);
    onClose();
  };

  // 检查所有原型是否有对应的html资产
  const checkPageHasHtmlAsset = (page: DesignPage) => {
    const prototypeCount = page.prototypes?.length || 0;

    return prototypeCount > 0 && prototypeCount === page.prototypes?.filter(prototype => prototype?.htmlContent || (prototype?.imgFileName && prototype?.imgFileLink) || (prototype?.slicedAssets as any)?.html?.content)?.length;
  };

  return (
    <Modal
      isOpen={isOpen}
      scrollBehavior="inside"
      size="3xl"
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader>项目级代码生成</ModalHeader>
        <ModalBody className="py-6">
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              请选择要生成代码的页面，系统将根据选择的页面数量自动调用对应的工作流接口。
            </div>

            <div className="text-sm text-gray-600">
              注意：系统目前一期仅支持对有html资产的页面进行生码，如果页面没有html资产，则无法选中该页面进行生码。
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">可选页面列表：</h3>
                <Checkbox
                  isIndeterminate={projectCodeGenPages.size > 0 && project?.pages && projectCodeGenPages.size < project.pages.length}
                  isSelected={project?.pages && project.pages.length > 0 && projectCodeGenPages.size === project.pages.length}
                  onValueChange={(isSelected) => {
                    if (isSelected) {
                      // 全选
                      const allPageIds = new Set(project?.pages?.filter(page => checkPageHasHtmlAsset(page))?.map(page => page.id) || []);

                      setProjectCodeGenPages(allPageIds);
                    } else {
                      // 取消全选
                      setProjectCodeGenPages(new Set());
                    }
                  }}
                >
                  <span className="text-sm text-gray-600">全选</span>
                </Checkbox>
              </div>
              <div className="space-y-3">
                {project?.pages?.map((page) => (
                  <div key={page.id} className="flex items-center space-x-3">
                    <Checkbox
                      isDisabled={!checkPageHasHtmlAsset(page)}
                      isSelected={projectCodeGenPages.has(page.id)}
                      onValueChange={(isSelected) => {
                        const newSet = new Set(projectCodeGenPages);

                        if (isSelected) {
                          newSet.add(page.id);
                        } else {
                          newSet.delete(page.id);
                        }
                        setProjectCodeGenPages(newSet);
                      }}
                    />
                    <span className="text-sm">{page.name}</span>
                    <span className="text-xs text-gray-500">
                      ({page.prototypes?.length || 0} 个原型)
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {projectCodeGenPages.size > 0 && (
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="text-sm">
                  <strong>已选择：</strong> {projectCodeGenPages.size} 个页面
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  {projectCodeGenPages.size === 1
                    ? '将调用单页面代码生成工作流'
                    : '将调用多页面代码生成工作流'
                  }
                </div>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="light"
            onPress={handleClose}
          >
            取消
          </Button>
          <Button
            color="primary"
            isDisabled={projectCodeGenPages.size === 0 || isStartingProjectCodeGen}
            isLoading={isStartingProjectCodeGen}
            startContent={!isStartingProjectCodeGen && <IconPlay />}
            onPress={handleStartProjectCodeGenWorkflow}
          >
            {isStartingProjectCodeGen ? '启动中...' : '启动生码工作流'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 