import {
  Autocomplete,
  AutocompleteI<PERSON>,
  Drawer,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DrawerBody,
  Button,
  Tabs,
  Tab,
  Input,
  Textarea,
  Select,
  SelectItem,
  addToast,
} from '@heroui/react';
import { Image } from 'antd';
import { useState, useEffect } from 'react';
import { DesignProject, createDesignProject, updateDesignProject } from '@/apis';
import lanhuProjectIdImage from '@/assets/images/lanhu-projectid.png';
import { MarkdownEditor } from '@/components/Project/MarkdownEditor';
import { typeMap, libraryMap } from '@/config/library';
import { useUserInfoStore } from '@/hooks/login';
import { isValidGitLabSshUrl } from '@/utils/projectUtils';
import IconInformation from '~icons/mdi/information';

interface ProjectSettingsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  project?: DesignProject;
  onProjectUpdated?: () => void;
  mode?: 'create' | 'edit';
}

interface ProjectFormData {
  name: string;
  description: string;
  gitUrl: string;
  gitBranch: string;
  gitCodeDir: string;
  previewStartCommand: string;
  framework: string;
  componentLibrary: string;
  llmstxt: string;
  lanhuProjectId: string;
  lanhuToken: string;
}

export function ProjectSettingsDrawer({
  isOpen,
  onClose,
  project,
  onProjectUpdated,
  mode = 'edit',
}: ProjectSettingsDrawerProps) {
  const { userInfo } = useUserInfoStore();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    gitUrl: '',
    gitBranch: '',
    gitCodeDir: '',
    previewStartCommand: '',
    framework: '',
    componentLibrary: '',
    llmstxt: '',
    lanhuProjectId: '',
    lanhuToken: '',
  });

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || '',
        gitUrl: project.gitUrl || '',
        gitBranch: project.gitBranch || '',
        gitCodeDir: project.gitCodeDir || '',
        previewStartCommand: project.previewStartCommand || '',
        framework: project.framework || '',
        componentLibrary: project.componentLibrary || '',
        llmstxt: project.llmstxt || '',
        lanhuProjectId: project.lanhuProjectId || '',
        lanhuToken: project.lanhuToken || '',
      });
    }
  }, [project]);

  const handleNext = () => {
    if (activeTab === 'overview') {
      setActiveTab('git');
    } else if (activeTab === 'git') {
      setActiveTab('prompt');
    } else if (activeTab === 'prompt') {
      setActiveTab('lanhu');
    }
  };

  const handlePrev = () => {
    if (activeTab === 'lanhu') {
      setActiveTab('prompt');
    } else if (activeTab === 'prompt') {
      setActiveTab('git');
    } else if (activeTab === 'git') {
      setActiveTab('overview');
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      if (mode === 'create') {
        // 创建新项目
                  await createDesignProject({
            name: formData.name,
            description: formData.description,
            user: userInfo?.username || '',
            framework: formData.framework,
            componentLibrary: formData.componentLibrary,
            llmstxt: formData.llmstxt,
            gitUrl: formData.gitUrl,
            gitBranch: formData.gitBranch,
            gitCodeDir: formData.gitCodeDir,
            previewStartCommand: formData.previewStartCommand,
            lanhuProjectId: formData.lanhuProjectId,
            lanhuToken: formData.lanhuToken,
          });

        addToast({
          title: '项目创建成功',
          color: 'success',
        });
      } else {
        // 更新现有项目
        if (project?.id) {
          await updateDesignProject({
            id: project.id,
            name: formData.name,
            description: formData.description,
            framework: formData.framework,
            componentLibrary: formData.componentLibrary,
            llmstxt: formData.llmstxt,
            gitUrl: formData.gitUrl,
            gitBranch: formData.gitBranch,
            gitCodeDir: formData.gitCodeDir,
            previewStartCommand: formData.previewStartCommand,
            lanhuProjectId: formData.lanhuProjectId,
            lanhuToken: formData.lanhuToken,
          });

          addToast({
            title: '项目更新成功',
            color: 'success',
          });
        }
      }

      onClose();
      onProjectUpdated?.();
    } catch {
      addToast({
        title: mode === 'create' ? '创建失败' : '更新失败',
        color: 'danger',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateFormData = (field: keyof ProjectFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderOverviewTab = () => (
    <div className="space-y-4 pt-4">
      <Input
        isRequired
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        label="项目名称"
        value={formData.name}
        onChange={(e) => updateFormData('name', e.target.value)}
      />
      <Textarea
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        label="项目描述"
        minRows={2}
        value={formData.description}
        onChange={(e) => updateFormData('description', e.target.value)}
      />
      <div className="flex gap-2">
        <Select
          className="flex-1"
          classNames={{
            trigger: 'rounded-sm',
          }}
          label="框架"
          selectedKeys={formData.framework ? [formData.framework] : []}
          onChange={(e) => {
            updateFormData('framework', e.target.value);
            updateFormData('componentLibrary', '');
          }}
        >
          {typeMap.map((framework) => (
            <SelectItem key={framework.value}>{framework.label}</SelectItem>
          ))}
        </Select>
        <Autocomplete
          allowsCustomValue
          className="flex-1"
          classNames={{
            base: 'rounded-sm',
            selectorButton: 'rounded-sm',
          }}
          defaultItems={libraryMap[formData.framework] ?? []}
          inputValue={formData.componentLibrary}
          label="UI 组件库"
          onInputChange={(value) => updateFormData('componentLibrary', value)}
        >
          {(item) => (
            <AutocompleteItem key={item.value}>{item.label}</AutocompleteItem>
          )}
        </Autocomplete>
      </div>
    </div>
  );

  const renderGitTab = () => (
    <div className="space-y-4 pt-4">
      <Input
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        description={
          <div className={`flex items-center gap-1 ${
            !formData.gitUrl 
              ? 'text-red-500 dark:text-red-400' 
              : isValidGitLabSshUrl(formData.gitUrl)
                ? 'text-gray-500 dark:text-gray-400'
                : 'text-red-500 dark:text-red-400'
          }`}>
            <IconInformation className="w-3 h-3" />
            <span className="text-xs">
              {!formData.gitUrl 
                ? '平台将从该git仓库地址拉取代码、生成代码，并提交MR到该代码仓库。'
                : isValidGitLabSshUrl(formData.gitUrl)
                  ? '平台将从该git仓库地址拉取代码、生成代码，并提交MR到该代码仓库。'
                  : 'Git地址格式错误，必须使用Git SSH协议格式且域名为指定域名。'
              }
            </span>
          </div>
        }
        label="Git 地址"
        value={formData.gitUrl}
        onChange={(e) => updateFormData('gitUrl', e.target.value)}
      />
      <Input
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        description={
          <div className={`flex items-center gap-1 ${formData.gitBranch ? 'text-gray-500 dark:text-gray-400' : 'text-red-500 dark:text-red-400'}`}>
            <IconInformation className="w-3 h-3" />
            <span className="text-xs">平台将作为开发人员，从该分支拉取代码、并提交MR到该仓库分支。</span>
          </div>
        }
        label="Git开发分支"
        value={formData.gitBranch}
        onChange={(e) => updateFormData('gitBranch', e.target.value)}
      />
      <Input
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        description={
          <div className={`flex items-center gap-1 ${formData.gitCodeDir ? 'text-gray-500 dark:text-gray-400' : 'text-red-500 dark:text-red-400'}`}>
            <IconInformation className="w-3 h-3" />
            <span className="text-xs">平台会将生成的代码写入该目录</span>
          </div>
        }
        label="Git开发目录"
        placeholder="代码开发的目录，例如：src 或 apps/eam-client/src/projects"
        value={formData.gitCodeDir}
        onChange={(e) => updateFormData('gitCodeDir', e.target.value)}
      />
      <Input
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
        description={
          <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
            <IconInformation className="w-3 h-3" />
            <span className="text-xs">如果不填系统将基于package.json自动解析对应的启动命令，可能不准确，建议还是主动填更好</span>
          </div>
        }
        label="预览服务启动命令"
        placeholder="预览启动命令，例如：npm run start"
        value={formData.previewStartCommand}
        onChange={(e) => updateFormData('previewStartCommand', e.target.value)}
      />
    </div>
  );

  const renderPromptTab = () => (
    <div className="space-y-4 pt-4">
      <div className="bg-zinc-100 dark:bg-zinc-800 p-4 rounded-md">
        <h3 className="text-sm font-medium mb-2 text-zinc-700 dark:text-zinc-300">
          组件描述
        </h3>
        <p className="text-xs text-zinc-500 dark:text-zinc-400 mb-4">
          请提供一个markdown文件，包含组件的说明、使用方法、参数说明等信息。组件描述会加入到系统提示词当中。
        </p>
        <MarkdownEditor
          className="rounded-sm"
          value={formData.llmstxt}
          onChange={(value) => updateFormData('llmstxt', value)}
        />
      </div>
    </div>
  );

  const renderLanhuTab = () => (
    <div className="space-y-4 pt-4">
      <Input
        classNames={{
          inputWrapper: 'rounded-sm',
        }}
         description={
          <div className={`flex items-center gap-1 ${formData.lanhuProjectId ? 'text-gray-500 dark:text-gray-400' : 'text-red-500 dark:text-red-400'}`}>
            <IconInformation className="w-3 h-3" />
            <span className="text-xs">请将蓝湖账号****************加入蓝湖项目团队，并赋予编辑者权限。</span>
          </div>
        }
        label="蓝湖项目 ID"
        value={formData.lanhuProjectId}
        onChange={(e) => updateFormData('lanhuProjectId', e.target.value)}
      />
      
      <div className="space-y-2">
        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">
          蓝湖项目ID配置说明
        </label>
        <div className="bg-zinc-50 dark:bg-zinc-800 p-4 rounded-md">
          <p className="text-xs text-zinc-600 dark:text-zinc-400 mb-3">
            请按照以下步骤配置蓝湖项目ID：
          </p>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">1</span>
              <span className="text-xs text-zinc-600 dark:text-zinc-400">登录蓝湖，进入项目页面</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">2</span>
              <span className="text-xs text-zinc-600 dark:text-zinc-400">复制项目ID（在项目页面的URL中pid=后面的文字，请查看下面的截图）</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">3</span>
              <span className="text-xs text-zinc-600 dark:text-zinc-400">将账号 <EMAIL> 添加为项目成员，并赋予编辑者权限</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">4</span>
              <span className="text-xs text-zinc-600 dark:text-zinc-400">如有疑问，请联系周韬(019270)</span>
            </div>
          </div>
          
          <div className="mt-4">
            <p className="text-xs text-zinc-600 dark:text-zinc-400 mb-2">
              点击下方图片查看项目ID如何获取（示例图片）：
            </p>
            <Image
              alt="蓝湖项目配置指南"
              className="w-full max-w-md rounded-md border border-zinc-200 dark:border-zinc-700 cursor-pointer"
              preview={{
                getContainer: false,
              }}
              src={lanhuProjectIdImage}
            />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <Drawer isOpen={isOpen} placement="right" size="lg" onClose={isLoading ? undefined : onClose}>
        <DrawerContent className="dark:bg-zinc-900 rounded-md">
          <DrawerHeader className="border-b dark:border-zinc-700/50 py-4 px-6">
          <div className="text-lg font-semibold">
            {mode === 'create' ? '创建项目' : '项目配置'}
          </div>
        </DrawerHeader>
        <DrawerBody>
          <Tabs
            className="w-full"
            classNames={{
              tab: 'rounded-sm',
              tabList: 'rounded-sm',
            }}
            selectedKey={activeTab}
            onSelectionChange={(key) => setActiveTab(key as string)}
          >
            <Tab key="overview" title="概览">
              {renderOverviewTab()}
            </Tab>
            <Tab key="git" title="Git和开发配置">
              {renderGitTab()}
            </Tab>
            <Tab key="prompt" title="提示词">
              {renderPromptTab()}
            </Tab>
            <Tab key="lanhu" title="蓝湖配置">
              {renderLanhuTab()}
            </Tab>
          </Tabs>
        </DrawerBody>
        <div className="p-4 border-t dark:border-zinc-700/50 flex justify-between">
          <div>
            {activeTab !== 'overview' && (
              <Button
                className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
                isDisabled={isLoading}
                size="sm"
                onPress={handlePrev}
              >
                上一步
              </Button>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
              isDisabled={isLoading}
              size="sm"
              onPress={onClose}
            >
              取消
            </Button>
            {activeTab !== 'lanhu' ? (
              <Button 
                color="primary" 
                isDisabled={isLoading} 
                size="sm"
                onPress={handleNext}
              >
                下一步
              </Button>
            ) : (
              <Button 
                color="primary" 
                isDisabled={isLoading} 
                isLoading={isLoading}
                size="sm"
                onPress={handleSave}
              >
                {mode === 'create' ? '创建项目' : '保存'}
              </Button>
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
    </>
  );
}
