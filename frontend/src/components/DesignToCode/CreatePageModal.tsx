import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Textarea,
} from '@heroui/react';
import { useState } from 'react';

interface CreatePageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreatePage?: (pageData: { name: string; description: string }) => void;
}

export function CreatePageModal({ isOpen, onClose, onCreatePage }: CreatePageModalProps) {
  const [pageName, setPageName] = useState('');
  const [pageDescription, setPageDescription] = useState('');

  const handleSubmit = () => {
    if (!pageName) {
      return;
    }

    onCreatePage?.({
      name: pageName,
      description: pageDescription,
    });

    // 重置表单
    setPageName('');
    setPageDescription('');
    onClose();
  };

  const handleCancel = () => {
    // 重置表单
    setPageName('');
    setPageDescription('');
    onClose();
  };

  return (
    <Modal 
      classNames={{
        base: "rounded-md",
        backdrop: "rounded-md",
      }} 
      isOpen={isOpen} 
      size="md"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">创建新页面</h3>
          <p className="text-sm text-yellow-800">创建后在「未分配原型」中绑定原型</p>
        </ModalHeader>
        <ModalBody>
          <div className="flex flex-col gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">页面名称</label>
              <Input
                classNames={{
                  inputWrapper: "rounded-md",
                }}
                placeholder="请输入页面名称"
                value={pageName}
                variant="bordered"
                onChange={(e) => setPageName(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">页面描述</label>
              <Textarea
                classNames={{
                  inputWrapper: "rounded-md",
                }}
                minRows={3}
                placeholder="请输入页面描述（可选）"
                value={pageDescription}
                variant="bordered"
                onChange={(e) => setPageDescription(e.target.value)}
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button className="rounded-md" color="default" variant="light" onPress={handleCancel}>
            取消
          </Button>
          <Button
            className="bg-black text-white hover:bg-gray-800 rounded-md"
            color="primary"
            isDisabled={!pageName.trim()}
            onPress={handleSubmit}
          >
            创建页面
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}