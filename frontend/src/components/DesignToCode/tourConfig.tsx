import IconCheckCircle from '~icons/mdi/check-circle';
import IconSync from '~icons/mdi/cloud-sync';
import IconCode from '~icons/mdi/code-braces';
import IconPage from '~icons/mdi/file-document-plus';
import IconTree from '~icons/mdi/file-tree';
import IconHandPointingUp from '~icons/mdi/hand-pointing-up';
import IconPrototype from '~icons/mdi/image-multiple';
import IconLightbulb from '~icons/mdi/lightbulb';
import IconBind from '~icons/mdi/link-variant';
import IconWelcome from '~icons/mdi/rocket-launch';
import IconTarget from '~icons/mdi/target';

export interface TourStep {
  title: React.ReactNode;
  description: React.ReactNode;
  placement?: string;
  target?: (() => HTMLElement) | null;
}

export const createTourSteps = (
  syncButtonRef: React.RefObject<HTMLButtonElement>,
  unassignedPrototypeRef: React.RefObject<HTMLDivElement>,
  createPageButtonRef: React.RefObject<HTMLButtonElement>,
  bindButtonRef: React.RefObject<HTMLButtonElement>,
  pageDirectoryRef: React.RefObject<HTMLDivElement>,
  codeGenButtonRef: React.RefObject<HTMLButtonElement>,
  helpButtonRef: React.RefObject<HTMLButtonElement>
): TourStep[] => [
  {
    title: (
      <div className="flex items-center gap-3">
        <IconWelcome className="w-5 h-5 text-amber-500" />
        <span>欢迎使用项目代码生成功能！</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">让我们通过简单的几步来了解如何使用这个平台生成与管理您的项目。</p>
        <div className="flex items-center gap-2 text-amber-600 text-sm">
          <IconLightbulb className="w-4 h-4" />
          <span>同步蓝湖设计稿 • 创建页面 • 绑定原型 • 生成代码</span>
        </div>
      </div>
    ),
    target: null,
    placement: 'center'
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconSync className="w-5 h-5 text-blue-500" />
        <span>同步蓝湖项目</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">点击这里可以从蓝湖项目中同步最新的设计稿。</p>
        <div className="flex items-center gap-2 text-blue-600 text-sm">
          <IconLightbulb className="w-4 h-4" />
          <span>每个设计稿都会成为一个可用的原型</span>
        </div>
      </div>
    ),
    target: () => syncButtonRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconPrototype className="w-5 h-5 text-purple-500" />
        <span>查看未绑定原型</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">同步后的原型（设计稿）会显示在这里。</p>
        <div className="flex items-center gap-2 text-purple-600 text-sm">
          <IconLightbulb className="w-4 h-4" />
          <span>这些原型还没有绑定到任何页面，需要您手动分配</span>
        </div>
      </div>
    ),
    target: () => unassignedPrototypeRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconPage className="w-5 h-5 text-blue-500" />
        <span>创建页面</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">点击这个按钮可以创建新的页面。</p>
        <div className="flex items-center gap-2 text-blue-600 text-sm">
          <IconCheckCircle className="w-4 h-4" />
          <span>页面用来组织相关的原型（设计稿）</span>
        </div>
      </div>
    ),
    target: () => createPageButtonRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconBind className="w-5 h-5 text-indigo-500" />
        <span>绑定原型到页面</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">在目录树上「选中」相关图片后，点击图标绑定。</p>
        <div className="flex items-center gap-2 text-indigo-600 text-sm">
          <IconTarget className="w-4 h-4" />
          <span>将原型分配到指定的页面中</span>
        </div>
      </div>
    ),
    target: () => bindButtonRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconTree className="w-5 h-5 text-emerald-500" />
        <span>浏览页面结构</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">这里显示您的页面层级结构。</p>
        <div className="flex items-center gap-2 text-emerald-600 text-sm">
          <IconHandPointingUp className="w-4 h-4" />
          <span>点击页面 icon 后在右侧区域可以查看与编辑属性与原型</span>
        </div>
      </div>
    ),
    target: () => pageDirectoryRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconCode className="w-5 h-5 text-red-500" />
        <span>生成项目级代码</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">最后一步：点击开始生成项目代码。</p>
        <div className="flex items-center gap-2 text-red-600 text-sm">
          <IconLightbulb className="w-4 h-4" />
          <span>系统会根据您的页面和原型生成完整的项目代码</span>
        </div>
      </div>
    ),
    target: () => codeGenButtonRef.current!,
  },
  {
    title: (
      <div className="flex items-center gap-3">
        <IconCheckCircle className="w-5 h-5 text-green-500" />
        <span>完成！</span>
      </div>
    ),
    description: (
      <div>
        <p className="mb-3">您已经掌握了基本操作流程。</p>
        <div className="flex items-center gap-2 text-green-600 text-sm">
          <IconLightbulb className="w-4 h-4" />
          <span>可通过点击帮助按钮再次查看引导教程</span>
        </div>
      </div>
    ),
    target: () => helpButtonRef.current!,
  },
]; 