import {
  Button,
  useDisclosure,
  Card,
  CardBody,
  CardHeader,
  <PERSON><PERSON>,
  Tab,
  <PERSON>dal,
  <PERSON>dal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  Tooltip,
  addToast,
  Spinner,
} from '@heroui/react';
import { Tour } from 'antd';
import { useState, useEffect, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { CreatePageModal } from './CreatePageModal';
import {
  PagePreview,
  HtmlPreview,
  ImgPreview,
} from './DesignPreview/components';
import { DesignFileTree } from './DesignPreview/DesignFileTree';
import { PageAggregation } from './DesignPreview/PageAggregation';
import { UnassignedPrototypeTree, UnassignedPrototypeTreeRef } from './DesignPreview/UnassignedPrototypeTree';
import { EditPageModal } from './EditPageModal';
import { PageListModal } from './PageListModal';
import { ProjectCodeGenModal } from './ProjectCodeGenModal';
import { ProjectConfigCheck } from './ProjectConfigCheck';
import { ProjectPreviewModal } from './ProjectPreviewModal';
import { SplitImagePage } from './SplitImagePage';
import { createTourSteps } from './tourConfig';
import {
  DesignProject,
  createDesignPage,
  updateDesignPage,
  SlicedAssets,
  getLanhuProjectImages,
  getLanhuProjectSectors,
} from '@/apis';
import { useProjectContext } from '@/contexts/ProjectContext';
import { Component as WorkflowManager } from '@/pages/WorkflowManager';
import { updatePrototypeSlicedAssets } from '@/utils/designProjectUtils';
import IconFolder from '~icons/mdi/folder';
import IconHelp from '~icons/mdi/help-circle';
import IconLink from '~icons/mdi/link';
import IconMerge from '~icons/mdi/merge';
import IconPreview from '~icons/mdi/monitor';
import IconOpenInNew from '~icons/mdi/open-in-new';
import IconPlus from '~icons/mdi/plus';


interface Props {
  projectId: string;
  // 项目数据和更新方法现在都通过 Context 提供
}

export function DesignPreviewPage({ projectId }: Props) {
  const [searchParams] = useSearchParams();
  const { isOpen, onClose } = useDisclosure();
  
  // 使用 ProjectContext 获取项目数据
  const projectContext = useProjectContext();
  const { isOpen: isBindModalOpen, onOpen: onBindModalOpen, onClose: onBindModalClose } = useDisclosure();
  const { isOpen: isProjectCodeGenOpen, onOpen: onProjectCodeGenOpen, onClose: onProjectCodeGenClose } = useDisclosure();
  const { isOpen: isProjectPreviewOpen, onOpen: onProjectPreviewOpen, onClose: onProjectPreviewClose } = useDisclosure();
  const { isOpen: isCreatePageOpen, onOpen: onCreatePageOpen, onClose: onCreatePageClose } = useDisclosure();
  const { isOpen: isEditPageOpen, onOpen: onEditPageOpen, onClose: onEditPageClose } = useDisclosure();

  // 完全依赖 Context 获取项目数据 - 更简洁和一致
  const project = projectContext?.project;
  const contextOnProjectUpdate = projectContext?.onProjectUpdate;
  const contextSetProject = projectContext?.setProject;
  const [selectedNode, setSelectedNode] = useState<{
    type: string;
    renderType: string;
    id: string;
    data?: any;
    imgFileLink: string;
    imgWidth: string;
    imgHeight: string;
  } | null>(null);

  const [activeTab, setActiveTab] = useState<string>('directory');
  const [lanhuImages, setLanhuImages] = useState<any[]>([]);
  const [lanhuSectors, setLanhuSectors] = useState<any[]>([]);
  const [isSyncing, setIsSyncing] = useState<boolean>(false);

  const [showSplitImagePage, setSplitImagePage] = useState(false);
  const [selectedCheckedNodes, setSelectedCheckedNodes] = useState<{ renderType: string; type: string; id: string; data?: any }[]>([]);
  const isSelectedImgNode = selectedNode?.renderType === 'img';

  // UnassignedPrototypeTree 的 ref
  const unassignedTreeRef = useRef<UnassignedPrototypeTreeRef>(null);

  // Tour 引导相关的 refs
  const syncButtonRef = useRef<HTMLButtonElement>(null);
  const unassignedPrototypeRef = useRef<HTMLDivElement>(null);
  const createPageButtonRef = useRef<HTMLButtonElement>(null);
  const bindButtonRef = useRef<HTMLButtonElement>(null);
  const pageDirectoryRef = useRef<HTMLDivElement>(null);
  const codeGenButtonRef = useRef<HTMLButtonElement>(null);
  const helpButtonRef = useRef<HTMLButtonElement>(null);

  // Tour 引导状态
  const [tourOpen, setTourOpen] = useState(false);
  const [currentTourStep, setCurrentTourStep] = useState(0);

  // Tour 引导步骤配置
  const tourSteps = createTourSteps(
    syncButtonRef,
    unassignedPrototypeRef,
    createPageButtonRef,
    bindButtonRef,
    pageDirectoryRef,
    codeGenButtonRef,
    helpButtonRef
  );

  // 检查是否是首次访问
  const checkFirstVisit = () => {
    const hasVisited = localStorage.getItem(`design-preview-tour-completed`);

    return !hasVisited;
  };

  const markTourCompleted = () => {
    localStorage.setItem(`design-preview-tour-completed`, 'true');
  };

  const startTour = () => {
    setCurrentTourStep(0);
    setTourOpen(true);
  };

  const handleHelpClick = () => {
    startTour();
  };

  // 监听项目数据变化，更新选中页面节点的数据
  useEffect(() => {
    if (selectedNode && selectedNode.renderType === 'page' && project?.pages) {
      const updatedPage = project.pages.find((page: any) => page.id === selectedNode.id);

      if (updatedPage && JSON.stringify(updatedPage) !== JSON.stringify(selectedNode.data)) {
        setSelectedNode(prev => prev ? {
          ...prev,
          data: updatedPage
        } : null);
      }
    }
  }, [project?.pages]); // 只依赖project?.pages，避免无限循环

  // 监听URL查询参数，自动切换标签页
  useEffect(() => {
    const tabParam = searchParams.get('tab');

    if (tabParam === 'results') {
      setActiveTab('results');
    }
  }, [searchParams]);


  // 当项目加载完成且有蓝湖配置时，自动加载蓝湖图片和分区
  useEffect(() => {
    if (project?.lanhuProjectId) {
      handleSyncLanhuProject();
    }
  }, [project?.lanhuProjectId, project?.lanhuToken]);

  // 首次访问自动触发引导
  useEffect(() => {
    if (project && checkFirstVisit()) {
      // 延迟一下确保组件完全渲染
      const timer = setTimeout(() => {
        startTour();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [project]);

  // 当 project 更新时，同步更新 selectedNode 的数据（如果当前选中的是页面节点）
  useEffect(() => {
    if (project && selectedNode && selectedNode.renderType === 'page') {
      const updatedPage = project.pages?.find(page => page.id === selectedNode.id);

      if (updatedPage) {
        setSelectedNode(prevSelectedNode => ({
          ...prevSelectedNode!,
          data: updatedPage
        }));
      }
    }
  }, [project, selectedNode?.id,selectedNode?.data?.description, selectedNode?.renderType, selectedNode?.data?.prototypes?.length]);

  // 统一的项目更新回调 - 直接使用 Context 的更新方法
  const handleProjectUpdate = () => {
    if (contextOnProjectUpdate) {
      contextOnProjectUpdate();
    }
  };

  // 项目设置函数 - 直接使用 Context 的 setter
  const setProjectData = (newProject: DesignProject) => {
    if (contextSetProject) {
      contextSetProject(newProject);
    }
  };

  const loadLanhuImages = async (lanhuProjectId: string, lanhuToken?: string) => {
    try {
      const response = await getLanhuProjectImages({
        project_id: lanhuProjectId,
        lanhu_token: lanhuToken
      });

      if (response?.data?.images) {
        const images = response.data.images.map((img: any) => ({
          ...img,
          name: img.name || '未命名图片',
        }));

        setLanhuImages(images);
      }
    } catch (error) {
       addToast({
        title: '获取蓝湖原型失败',
        description: '网络异常或token已过期',
        color: 'danger',
      });
    }
  };

  const loadLanhuSectors = async (lanhuProjectId: string, lanhuToken?: string) => {
    try {
      const response = await getLanhuProjectSectors({
        project_id: lanhuProjectId,
        lanhu_token: lanhuToken
      });

      if (response?.data?.sectors) {
        const sectors = response.data.sectors.map((sector: any) => ({
          ...sector,
          name: sector.name || '未命名分区',
        })).reverse();

        setLanhuSectors(sectors);
      }
    } catch (error) {
       addToast({
        title: '获取蓝湖项目分区失败',
        description: '网络异常或token已过期',
        color: 'danger',
      });
      console.error('获取蓝湖项目分区失败:', error);
    }
  };

  // 计算未分配原型数据
  const unassignedPrototypes = useMemo(() => {
    if (!lanhuImages || !project?.pages) {
      return [];
    }

    // 收集所有已分配的蓝湖版本ID
    const assignedVersionIds = new Set<string>();

    project.pages.forEach(page => {
      page.prototypes?.forEach(prototype => {
        if (prototype.lanhuVersionId) {
          assignedVersionIds.add(prototype.lanhuVersionId);
        }
      });
    });

    // 筛选未分配的图片
    const unassignedImages = lanhuImages.filter(img => {
      // 使用 img.latest_version 或 img.id 进行对比
      const versionId = img.latest_version;

      return versionId && !assignedVersionIds.has(versionId);
    });

    if (unassignedImages.length === 0) {
      return [];
    }

    // 如果没有sectors或sectors为空，创建一个默认分组
    if (!lanhuSectors || lanhuSectors.length === 0) {
      return [{
        id: 'unassigned-default',
        name: '未分配',
        type: 'folder',
        children: unassignedImages.map(img => ({
          id: `unassigned-${img.id}`,
          name: img.name || '未命名图片',
          type: 'img',
          data: {
            ...img,
            renderType: 'img',
            imgFileLink: img.url,
            imgFileName: img.name || '未命名图片.png',
            url: img.url,
            width: img.width,
            height: img.height,
            latest_version: img.latest_version
          }
        }))
      }];
    }

    // 创建图片映射，便于查找
    const imageMap = new Map();

    unassignedImages.forEach(img => {
      imageMap.set(img.id, img);
    });

    // 构建树结构
    const buildTree = (sectors: any[], parentId: string | null = null): any[] => {
      return sectors
        .filter(sector => sector.parent_id === parentId)
        .map(sector => {
          // 获取当前分区的子分区
          const children = buildTree(sectors, sector.id);

          // 获取当前分区的图片（转换为叶子节点）
          const imageChildren = sector.images
            ?.map((imageId: string) => {
              const image = imageMap.get(imageId);

              if (!image) return null;

              return {
                id: imageId,
                name: image.name || '未命名图片',
                type: 'img',
                data: {
                  ...image,
                  imgFileLink: image.url || image.imgFileLink,
                  width: image.width,
                  height: image.height,
                  url: image.url || image.imgFileLink,
                }
              };
            })
            .filter(Boolean) || [];

          // 如果既有子分区又有图片，或者只有其中一种，都创建节点
          if (children.length > 0 || imageChildren.length > 0) {
            return {
              id: sector.id,
              name: sector.name || '未命名分区',
              type: 'folder',
              children: [...children, ...imageChildren]
            };
          }
          
          return null;
        })
        .filter(Boolean);
    };

    return buildTree(lanhuSectors);
  }, [lanhuImages, lanhuSectors, project?.pages]);

  // 页面更新成功后的回调
  const handlePageUpdated = () => {
    // 通过 Context 触发项目数据更新
    handleProjectUpdate();
    setSelectedNode(null);
  };

  // 原型删除后的回调（不清空选中节点）
  const handlePrototypeDeleted = async () => {
    // 通过 Context 触发项目数据更新
    handleProjectUpdate();
  };

  const handlePrototypeAdded = async () => {
    handleProjectUpdate();
  };

  const handleEditPage = () => {
    onEditPageOpen();
  };

  // 处理节点选择
  const handleNodeSelect = (nodeInfo: { renderType: string; type: string; id: string; data?: any }) => {
    setSelectedNode({
      ...nodeInfo,
      imgFileLink: nodeInfo.data?.imgFileLink || nodeInfo.data?.url || '',
      imgWidth: String(nodeInfo.data?.width || ''),
      imgHeight: String(nodeInfo.data?.height || ''),
    });
  };

  const handleOpenLanhuProject = () => {
    if (project?.lanhuProjectId) {
      const lanhuUrl = `http://************:8089/web/#/item/project/stage?pid=${project.lanhuProjectId}`;

      window.open(lanhuUrl, '_blank');
    }
  };

  const handleSyncLanhuProject = async () => {
    if (!project?.lanhuProjectId) {
      addToast({
        title: '同步失败',
        description: '未找到蓝湖项目ID，请检查项目配置',
        color: 'warning',
      });

      return;
    }

    try {
      setIsSyncing(true);
      await Promise.all([
      loadLanhuImages(project.lanhuProjectId),
      loadLanhuSectors(project.lanhuProjectId)
    ]);
    } catch (error) {
      console.log(error);
    } finally{
      setIsSyncing(false);
    }
  };

  // 处理页面绑定
  const handleBindToPage = () => {
    onBindModalOpen();
  };

  // 处理未分配原型节点check
  const handleUnassignedNodesCheck = (checkedNodes: { renderType: string; type: string; id: string; data?: any }[]) => {
    setSelectedCheckedNodes(checkedNodes);
  };

  // 处理未分配原型节点选择
  const handleUnassignedNodeSelect = (nodeInfo: { renderType:string; type: string; id: string; data?: any }) => {
    setSelectedNode({
      ...nodeInfo,
      imgFileLink: nodeInfo.data?.imgFileLink || nodeInfo.data?.url || '',
      imgWidth: String(nodeInfo.data?.width || ''),
      imgHeight: String(nodeInfo.data?.height || ''),
    });
  };

  // 重置选择状态
  const handleResetSelection = () => {
    // 重置 selectedCheckedNodes
    setSelectedCheckedNodes([]);
    // 重置 UnassignedPrototypeTree 的 checkedKeys
    unassignedTreeRef.current?.resetCheckedKeys();
  };

  // 更新原型的 slicedAssets
  const handleUpdateSlicedAssets = async (prototypeId: string, slicedAssets: SlicedAssets) => {
    if (!project || !selectedNode) {
      console.error('项目数据或选定节点不存在');

      return;
    }

    try {
      const updatedProject = await updatePrototypeSlicedAssets(project, prototypeId, slicedAssets, setProjectData);

      console.log('SlicedAssets 更新成功');
      const newSelectedNode = {
        ...selectedNode,
        data: {
          ...selectedNode.data,
          slicedAssets,
        },
      };

      setSelectedNode(newSelectedNode);

      return updatedProject;
    } catch (error) {
      console.error('更新 SlicedAssets 失败:', error);
      throw error;
    }
  };

  const handleGenerateCode = () => {
    setSplitImagePage(true);
  };

  const handleCloseSplitImagePage = () => {
    setSplitImagePage(false);
  };

  const handleCreatePage = async (pageData: { name: string; description: string }) => {
    try {
      await createDesignPage({
        projectId,
        name: pageData.name,
        description: pageData.description,
        prototypes: [],
      });
      
      addToast({
        title: '页面创建成功',
        color: 'success',
      });
      
      // 创建成功后通过 Context 触发项目数据更新
      handleProjectUpdate();
      
    } catch (error) {
      console.error('创建页面失败:', error);
      addToast({
        title: '页面创建失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    }
  };

  
  const handleSubmitEditPage = async (pageData: { name: string; description: string }) => {
    try {
      await updateDesignPage({
        projectId,
        pageId: selectedNode!.id,
        name: pageData.name,
        description: pageData.description,
      });
      
      addToast({
        title: '页面更新成功',
        color: 'success',
      });
      
      handleProjectUpdate();
      
    } catch (error) {
      console.error('更新页面失败:', error);
      addToast({
        title: '页面更新失败',
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger',
      });
    }
  };


  // 渲染预览内容
  const renderPreviewContent = () => {
    if (!selectedNode) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <div className='flex justify-center items-center'>
              <p className="text-lg">请选择左侧目录树中的节点</p>
              <IconFolder className="size-8 pl-2" />
            </div>
            
            <p className="text-sm mt-2">支持预览HTML、CSS 和图片内容</p>
            <p className="text-sm mt-2">选择页面节点可以编辑</p>
          </div>
        </div>
      );
    }

    const { renderType, type, id, data: nodeData } = selectedNode;

    switch (renderType) {
      case 'page':
        return (
          <PagePreview 
            id={id} 
            nodeData={nodeData} 
            project={project} 
            unassignedImages={unassignedPrototypes.flatMap(proto => 
              proto.children?.flatMap((child: any) => 
                child.type === 'img' ? [child.data] : 
                child.children?.filter((grandChild: any) => grandChild.type === 'img').map((grandChild: any) => grandChild.data) || []
              ) || []
            )}
            unassignedImagesLoading={isSyncing}
            onEditPage={handleEditPage}
            onPageUpdated={handlePageUpdated}
            onPrototypeAdded={handlePrototypeAdded}
            onPrototypeDeleted={handlePrototypeDeleted}
          />
        );
      case 'html':
        return (
          <HtmlPreview
            id={id}
            initialTab="html"
            nodeData={nodeData}
            project={project}
            onProjectUpdate={handleProjectUpdate}
          />
        );
      case 'css':
        return (
          <HtmlPreview
            id={id}
            initialTab="css"
            nodeData={nodeData}
            project={project}
            onProjectUpdate={handleProjectUpdate}
          />
        );
      case 'img':
        return <ImgPreview handleGenerateCode={handleGenerateCode} id={id} selectedNode={selectedNode} />;
      default:
        // return <DefaultPreview id={id} type={type} />;
        return '';
    }
  };

  return (
    <div className=" w-full flex flex-col h-full">
      {/* Tabs 区域 */}
      <div className="flex flex-col h-full">
        {/* 标签行，包含Tabs标签和齿轮图标 */}
        <div className="flex items-center justify-between">
          <Tabs classNames={{
            tabList: 'rounded-md',
          }} selectedKey={activeTab} onSelectionChange={(key) => setActiveTab(key as string)}>
            <Tab key="directory" className='rounded-sm' title="页面规划" />
            <Tab key="results" className='rounded-sm' title="执行记录" />
          </Tabs>
          <div className="flex items-center gap-3">
            <Tooltip content="帮助引导">
              <Button
                ref={helpButtonRef}
                isIconOnly
                className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
                size="sm"
                onPress={handleHelpClick}
              >
                <IconHelp className="w-4 h-4" />
              </Button>
            </Tooltip>
            <Button
              className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
              size="sm"
              startContent={<IconPreview />}
              onPress={onProjectPreviewOpen}
            >
              项目预览
            </Button>
            <Button
              ref={codeGenButtonRef}
              className="ml-3 transition-all duration-200 rounded-md border-1 dark:border-zinc-700"
              color='primary'
              size="sm"
              startContent={<IconMerge />}
              onPress={onProjectCodeGenOpen}
            >
              生成项目级代码
            </Button>
          </div>
        </div>

        {/* Tab内容区域 */}
        <div className="mt-4">
          {activeTab === 'directory' && (
            <div className="flex gap-4" style={{ height: 'calc(100vh - 210px)' }}>
              <div className="min-w-[300px] max-w-[370px] w-[30%] flex flex-col gap-4 h-full">
                <Card className="flex flex-col flex-shrink-0 shadow-none border-1 dark:border-zinc-700 rounded-small">
                  <CardHeader className="p-3 flex flex-row items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h3 className="text-base font-semibold">{project?.name}</h3>
                    </div>
                  </CardHeader>
                  <CardBody className="pt-0 pb-3">
                    <p className="text-sm text-gray-600 mb-3">{project?.description}</p>
                    <div className="flex gap-2">
                      <Button
                        ref={syncButtonRef}
                        className="flex-1 bg-black text-white hover:bg-gray-800 rounded-md"
                        isDisabled={isSyncing}
                        isLoading={isSyncing}
                        size="sm"
                        onPress={handleSyncLanhuProject}
                      >
                        {isSyncing ? '同步中...' : '同步蓝湖'}
                      </Button>
                      <Tooltip content="跳转蓝湖">
                        <Button
                          isIconOnly
                          className="bg-transparent transition-all duration-200 rounded-md border-1 dark:border-zinc-700"
                          size="sm"
                          onPress={handleOpenLanhuProject}
                        >
                          <IconOpenInNew className="w-4 h-4" />
                        </Button>
                      </Tooltip>
                    </div>
                  </CardBody>
                </Card>

                {/* 页面目录 */}
                <Card ref={pageDirectoryRef} className="flex flex-col flex-[0_0_45%] shadow-none border-1 dark:border-zinc-700 rounded-small">
                  <CardHeader className="p-2 px-4 flex flex-row items-center justify-between flex-shrink-0">
                    <h3 className="text-base font-semibold">页面目录</h3>
                    <Tooltip content="创建页面">
                      <Button
                        ref={createPageButtonRef}
                        isIconOnly
                        className="bg-transparent transition-all duration-200 rounded-md border-1 dark:border-zinc-700"
                        size="sm"
                        onPress={onCreatePageOpen}
                      >
                        <IconPlus className="w-4 h-4" />
                      </Button>
                    </Tooltip>
                  </CardHeader>
                  <CardBody className="flex-1 overflow-auto pt-0 min-h-0">
                    <DesignFileTree project={project} onNodeSelect={handleNodeSelect} />
                  </CardBody>
                </Card>

                {/* 未分配原型 */}
                <Card ref={unassignedPrototypeRef} className="flex flex-col flex-1 shadow-none border-1 dark:border-zinc-700 rounded-small">
                  <CardHeader className="py-2 px-4 flex flex-row items-center justify-between flex-shrink-0">
                    <h3 className="text-base font-semibold">未分配原型</h3>
                    <div className="flex gap-1">
                      <Tooltip content="页面绑定">
                        <Button ref={bindButtonRef} isIconOnly  className='bg-transparent transition-all duration-200 rounded-md border-1 dark:border-zinc-700' size="sm" onPress={handleBindToPage}>
                          <IconLink />
                        </Button>
                      </Tooltip>
                    </div>
                  </CardHeader>
                  <CardBody className="flex-1 overflow-auto pt-0 min-h-0 relative">
                    {isSyncing && (
                      <div className="absolute inset-0 bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm z-10 flex items-center justify-center">
                        <div className="flex flex-col items-center gap-3">
                          <Spinner size="lg" />
                          <p className="text-sm text-gray-600 dark:text-gray-400">正在同步蓝湖数据...</p>
                        </div>
                      </div>
                    )}
                    <UnassignedPrototypeTree
                      ref={unassignedTreeRef}
                      prototypes={unassignedPrototypes}
                      onNodeSelect={handleUnassignedNodeSelect}
                      onNodesCheck={handleUnassignedNodesCheck}
                    />
                  </CardBody>
                </Card>
              </div>
              <Card className="flex-1 min-w-[800px] flex flex-col shadow-none border-1 dark:border-zinc-700 rounded-small">
                <CardBody className="preview-content-wrap flex-1 overflow-auto pt-0">{renderPreviewContent()}</CardBody>
              </Card>
            </div>
          )}

          {activeTab === 'results' && (
            <div className="w-full h-full">
              <WorkflowManager projectId={projectId} />
            </div>
          )}
        </div>
      </div>

      {/* 页面列表模态框 */}
      <PageListModal isOpen={isOpen} project={project} onClose={onClose} onProjectUpdate={handleProjectUpdate} />

    

      {/* 图片拆分 */}
      {isSelectedImgNode && showSplitImagePage && project && (
        <SplitImagePage
          handleUpdateSlicedAssets={handleUpdateSlicedAssets}
          nodeData={selectedNode.data}
          project={project as any}
          onClose={handleCloseSplitImagePage}
        />
      )}

      {/* 页面绑定弹窗 */}
      <Modal
        classNames={{
          base: "rounded-md",
          backdrop: "rounded-md",
        }}
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={isBindModalOpen}
        size="5xl"
        onClose={onBindModalClose}
      >
        <ModalContent>
          <ModalHeader className="flex justify-between items-center">
            <h3 className="text-base font-semibold">页面绑定</h3>
          </ModalHeader>
          <ModalBody className="pb-6">
            {project && (
              <PageAggregation
                autoSelectImages={true} // 从UnassignedPrototypeTree进入自动选中图片
                lanhuImages={selectedCheckedNodes.map(node => ({
                  ...node.data,
                  name: node.data?.name || '未命名图片',
                }))}
                lanhuImagesLoading={false}
                project={project}
                onCancel={onBindModalClose}
                onPageAdded={() => {
                  handlePrototypeDeleted();
                  onBindModalClose();
                }}
                onResetSelection={handleResetSelection}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 项目级代码生成弹窗 */}
      <ProjectCodeGenModal
        isOpen={isProjectCodeGenOpen}
        project={project}
        projectId={projectId}
        onClose={onProjectCodeGenClose}
      />

      {/* 项目预览对话框 */}
      <ProjectPreviewModal
        isOpen={isProjectPreviewOpen}
        project={project}
        onClose={onProjectPreviewClose}
      />

      {/* 创建新页面弹窗 */}
      <CreatePageModal
        isOpen={isCreatePageOpen}
        onClose={onCreatePageClose}
        onCreatePage={handleCreatePage}
      />
      <EditPageModal
        isOpen={isEditPageOpen}
        pageData={selectedNode?.data}
        onClose={onEditPageClose}
        onSubmitEditPage={handleSubmitEditPage}
      />
      <ProjectConfigCheck project={project} onProjectUpdated={handleProjectUpdate} />

      {/* Tour 引导组件 */}
      <Tour
        arrow={{
          pointAtCenter: true,
        }}
        current={currentTourStep}
        indicatorsRender={(current, total) => (
          <div className="flex items-center justify-center text-gray-600 text-sm font-medium">
            <span>{current + 1} / {total}</span>
          </div>
        )}
     
        open={tourOpen}
        placement="bottom"
        rootClassName="tour-custom"
        steps={tourSteps}
        type="primary"
        onChange={setCurrentTourStep}
        onFinish={() => {
          setTourOpen(false);
          markTourCompleted();
        }}
        onClose={() => {
          setTourOpen(false);
          markTourCompleted();
        }}
      />

    </div>
  );
}
