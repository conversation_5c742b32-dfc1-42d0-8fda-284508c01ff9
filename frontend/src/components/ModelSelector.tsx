import { Dropdown, DropdownItem, DropdownMenu, DropdownSection, DropdownTrigger } from "@heroui/dropdown";
import { Button } from "@heroui/react";
import { Select, SelectItem, SelectSection } from "@heroui/select";
import { useState } from "react";
import { modelList } from "@/config/models";
import IconAndroid from '~icons/mdi/android-debug-bridge';

export function ModelSelector({ model, onChange }: {
  model: string;
  onChange: (model: string) => void;
}) {
  const pairs = Object.entries(modelList);

  return (
    <Select className="w-[200px]"
      label="模型"
      selectedKeys={new Set([model])}
      size="sm"
      onSelectionChange={(keys) => {
        const selectedKey = Array.from(keys)[0] as string;

        if (selectedKey) {
          onChange(selectedKey);
        }
      }}
    >
      {
        pairs.map(([k, v], i) => (
          <SelectSection key={k} showDivider={i >= pairs.length - 1} title={v.label}>{
            v.models.map((e) => <SelectItem key={`${k}::${e}`} textValue={e}>{e}</SelectItem>)
          }</SelectSection>
        ))
      }
    </Select>
  );
}

export function ModelDropdown({ model, onChange }: {
  model: string;
  onChange: (model: string) => void;
}) {
  const pairs = Object.entries(modelList);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Dropdown isOpen={isOpen} placement="bottom-end" radius="sm" size="sm" onOpenChange={setIsOpen}>
      <DropdownTrigger>
        <Button className="h-8 flex items-center gap-1" size="sm" variant="flat">
          <IconAndroid />
          {model}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        selectedKeys={new Set([model])}
        selectionMode="single"
        variant="flat"
        onSelectionChange={(keys) => {
          const selectedKey = Array.from(keys)[0] as string;

          if (selectedKey) {
            onChange(selectedKey);
          }
        }}
      >
        {
          pairs.map(([k, v], i) => (
            <DropdownSection key={k} showDivider={i < pairs.length - 1} title={v.label}>{
              v.models.map((e) => <DropdownItem key={`${k}::${e}`} textValue={e}>{e}</DropdownItem>)
            }</DropdownSection>
          ))
        }
      </DropdownMenu>
    </Dropdown>
  );
}
