import { useState, useMemo } from 'react';
import { marked } from 'marked';
import { Button } from '@heroui/button';
import IconChevronDown from '~icons/mdi/chevron-down';
import IconChevronUp from '~icons/mdi/chevron-up';

interface ExpandableHtmlContentProps {
  html: string;
  originalText: string;
  maxLines?: number;
}

export function ExpandableHtmlContent({ html, originalText, maxLines = 50 }: ExpandableHtmlContentProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 添加空值检查，提供默认值
  const safeOriginalText = originalText || '';
  const safeHtml = html || '';
  
  // 计算原始文本行数
  const lineCount = useMemo(() => {
    return safeOriginalText.split('\n').length;
  }, [safeOriginalText]);
  
  // 如果行数少于等于最大行数，直接显示全部内容
  if (lineCount <= maxLines) {
    return (
      <div
        className="simple-markdown-body"
        dangerouslySetInnerHTML={{ __html: safeHtml }}
      />
    );
  }
  
  // 截取前指定行数并重新渲染为HTML
  const truncatedHtml = useMemo(() => {
    if (isExpanded) return safeHtml;
    
    const lines = safeOriginalText.split('\n');
    const truncated = lines.slice(0, maxLines).join('\n');
    return marked(truncated, { async: false });
  }, [safeHtml, safeOriginalText, isExpanded, maxLines]);
  
  return (
    <div>
      <div
        className="simple-markdown-body"
        dangerouslySetInnerHTML={{ __html: truncatedHtml }}
      />
      {lineCount > maxLines && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <Button
            size="sm"
            variant="flat"
            className="w-full"
            startContent={isExpanded ? <IconChevronUp /> : <IconChevronDown />}
            onPress={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? '收起' : `展开查看全部内容 (共${lineCount}行)`}
          </Button>
        </div>
      )}
    </div>
  );
} 