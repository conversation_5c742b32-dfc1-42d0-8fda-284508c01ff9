import { type UIMessage } from '@ai-sdk/ui-utils';
import { Alert } from '@heroui/alert';
import { Button } from '@heroui/button';
import { Card, CardBody } from '@heroui/card';
import { Image } from '@heroui/image';
import { Textarea } from '@heroui/react';
import { Spinner } from "@heroui/spinner";
import clsx from 'clsx';
import { concat } from 'lodash';
import { useContext, useMemo, useState, useEffect } from 'react';
import { tv } from 'tailwind-variants';
import { CodeDiff } from './CodeDiff';
import { CodeLink } from './CodeLink';
import { ExpandableHtmlContent } from './ExpandableHtmlContent';
import { ExpandableText } from './ExpandableText';
import { IncompleteOutputIndicator } from './IncompleteOutputIndicator';
import { textParser } from './parser';
import styles from './ripple.module.css';
import { ChatContext } from '@/stores/chat';
import copyContent from '@/utils/copyContent';
import { eventBus } from '@/utils/eventBus';
import { confirm } from '@/utils/modal';
import IconCircleEditOutline from '~icons/mdi/circle-edit-outline';
import IconCopy from '~icons/mdi/content-copy';
import IconDocument from '~icons/mdi/file-document-outline';
import IconHammer from '~icons/mdi/hammer';
import IconRefresh from '~icons/mdi/refresh';
import IconTrashCan from '~icons/mdi/trash-can-outline';
import '@/assets/styles/simple-markdown.css';
import { CodeView } from './CodeView';

const bubble = tv({
  base: 'flex flex-col items-start',
  variants: {
    role: {
      user: 'items-end',
      assistant: 'items-start',
    },
  },
  defaultVariants: {
    role: 'user',
  },
});

const bubbleCard = tv({
  base: 'shadow-none w-full',
  variants: {
    role: {
      user: 'bg-[#ececec] text-black dark:bg-gray-800 dark:text-white border border-[#ececec] dark:border-gray-700 rounded-[16px]',
      assistant: 'text-black dark:text-white border',
    },
    loading: {
      true: styles.ripple,
    },
  },
  defaultVariants: {
    role: 'user',
  },
});

const footerClass = tv({
  base: 'mt-1 flex gap-1 items-center',
  variants: {
    role: {
      user: 'justify-end',
      assistant: 'justify-start',
    },
  },
  defaultVariants: {
    role: 'user',
  },
});

export function ChatMessage({ message, isLoading, isLast, isStreaming }: {
  message: Omit<UIMessage, 'parts'> & Partial<Pick<UIMessage, 'parts'>>;
  isFirst?: boolean;
  isLast?: boolean;
  isLoading?: boolean;
  isStreaming?: boolean;
}) {
  const { reload, deleteMessage, editMessage, streamFile, openFile, changeVersion, append } = useContext(ChatContext)!;
  const [edit, setEdit] = useState<string | undefined>();
  const [isContinuing, setIsContinuing] = useState(false);
  const { role, content, parts, annotations, experimental_attachments } = message;
  const msgRole = role == 'user' ? 'user' : 'assistant';

  // 检测是否有不完整输出注解
  // 检测是否有不完整输出注解
  const incompleteAnnotation = annotations?.find((e: any) => {
    if (typeof e !== 'object' || e.type !== 'incomplete-output-detected') {
      return false;
    }

    // 添加前端验证：检查当前消息内容是否真的不完整
    if (e.incompleteType === 'files') {
      const blocks = textParser(content, true, false);
      const filesBlocks = blocks.filter((block) => block.type === 'files');

      if (filesBlocks.length > 0) {
        const lastFilesBlock = filesBlocks[filesBlocks.length - 1];

        // 如果最后一个files块实际上是完整的，忽略这个注解
        if (lastFilesBlock.closed) {
          return false;
        }
      }
    }

    return true;
  }) as any;

  const downloadFile = (url: string, name: string) => {
    const link = document.createElement('a');

    link.href = url;
    link.download = name;
    link.click();
  };

  // 处理继续输出
  const handleContinueOutput = () => {
    if (!incompleteAnnotation || incompleteAnnotation.canContinue === false) {
      return;
    }

    if (append) {
      append({
        role: 'user',
        content: '继续输出',
      });
    }
  };

  const blocks = useMemo(() => {
    const parseMessagePart = (text: string, parentKey = '') => {
      if (message.role === 'user') {
        return (
          <ExpandableText key={parentKey} content={text}>
            <span>{text}</span>
          </ExpandableText>
        );
      }

      // 检查是否需要截断检测
      // 只有在检测到截断状态时才启用严格的截断检测
      const tokenAnnotation = annotations?.find((e: any) => typeof e === 'object' && e.type === 'token-count') as any;
      const shouldDetectTruncation =
        tokenAnnotation && (tokenAnnotation.finishReason === 'length' || tokenAnnotation.isContinued === true);

      const textBlocks = textParser(text, true, shouldDetectTruncation);

      return textBlocks.map((block, i: number) => {
        const key = `${parentKey}::${i}`;

        if (block.type === 'text') {
          return <ExpandableHtmlContent key={key} data-part={key} html={block.text} originalText={block.text} />;
        } else if (block.type === 'code') {
          return <CodeView key={i} code={block.code} lang={block.lang} />;
        } else if (block.type === 'tool') {
          return (
            <Alert
              key={key}
              className="my-2"
              classNames={{
                base: 'p-2 items-center',
              }}
              endContent={
                <Button
                  onPress={() => {
                    confirm({
                      title: '工具详情',
                      message: JSON.stringify(block.arguments),
                      buttons: [],
                    });
                  }}
                >
                  详情
                </Button>
              }
              icon={<IconHammer />}
              variant="faded"
            >
              {block.name}
            </Alert>
          );
        } else if (block.type === 'files') {
          const { list, files } = block;
          const lastFile = files?.[files.length - 1];
          const done =
            annotations?.filter((e: any) => typeof e === 'object' && e.type === 'file-saved').map((e: any) => e.path) ??
            [];
          const versionAnnotation = annotations?.find(
            (e: any) => typeof e === 'object' && e.type === 'all-files-saved',
          ) as { versionNumber: number } | undefined;
          const isDone = versionAnnotation !== undefined;

          if (!isDone && files && files.length > 0) {
            files.forEach((file) => {
              if (file.content && file.content.trim()) {
                streamFile(file.path, file.content);
              }
            });
          }

          return (
            <CodeLink
              key={key}
              changeVersion={changeVersion}
              id={key}
              list={list}
              openFile={openFile}
              status={{
                done,
                isDone,
                version: versionAnnotation?.versionNumber,
              }}
            />
          );
        } else if (block.type === 'diff') {
          return (
            <CodeDiff
              key={key}
              annotations={annotations}
              diff={block.diff}
              status={{
                isDone: block.closed,
              }}
            />
          );
        }

        return null;
      });
    };

    if (parts) {
      return concat(
        parts.map((part: any, i: number) => {
          if (part.type == 'text') {
            return parseMessagePart(part.text, String(i));
          }

          return null;
        }),
      );
    } else {
      return parseMessagePart(content);
    }
  }, [edit, content, annotations?.length]);

  const blocksArr = Array.isArray(blocks) ? blocks : blocks ? [blocks] : [];

  useEffect(() => {
    if (isLast && message.role === 'assistant') {
      // 为最后一条assistant消息添加全局调试函数
      // 清理函数在组件卸载时执行
      return () => {
        // 清理全局调试函数
      };
    }
  }, [isLast, message.role, incompleteAnnotation, append]);

  if (edit) {
    return (
      <div className={bubble({ role: msgRole })}>
        <Card className={bubbleCard({ role: msgRole, loading: false })}>
          <CardBody className="flex flex-col text-sm">
            <Textarea maxRows={20} minRows={5} value={edit} onValueChange={setEdit} />
            <footer className={clsx('mt-2', footerClass({ role: msgRole }))}>
              <Button size="sm" variant="flat" onPress={() => setEdit(undefined)}>
                取消
              </Button>
              <Button
                size="sm"
                onPress={async () => {
                  await editMessage(message.id, edit);
                  setEdit(undefined);
                }}
              >
                保存
              </Button>
            </footer>
          </CardBody>
        </Card>
      </div>
    );
  }

  let images;

  if (experimental_attachments?.length) {
    images = (
      <section className="mt-1">
        {experimental_attachments?.map((e, i) => {
          if (e.contentType?.startsWith('image')) {
            return (
              <Image
                key={i}
                isZoomed
                className="mt-1"
                height={200}
                src={e.url}
                width={200}
                onClick={() => eventBus.emit('show-lightbox', [{ src: e.url }])}
              />
            );
          } else {
            return (
              <div
                key={i}
                className="flex items-center gap-1 p-2 cursor-pointer border rounded border-zinc-100 hover:bg-[#0000000d] dark:hover:bg-[#ffffff0d]"
                onClick={() => downloadFile(e.url, e.name ?? 'file')}
              >
                <IconDocument />
                {e.name}
              </div>
            );
          }
        })}
      </section>
    );
  }

  const copy = () => {
    copyContent(content);
  };

  const footerButtons = [
    <Button key="copy" isIconOnly size="sm" title="复制" variant="light" onPress={copy}>
      <IconCopy height={14} width={14} />
    </Button>,
  ];

  if (message.role === 'user') {
    footerButtons.push(
      <Button key="delete" isIconOnly size="sm" title="删除" variant="light" onPress={() => deleteMessage(message.id)}>
        <IconTrashCan height={14} width={14} />
      </Button>,
    );
    footerButtons.push(
      <Button key="edit" isIconOnly size="sm" title="编辑" variant="light" onPress={() => setEdit(content)}>
        <IconCircleEditOutline height={14} width={14} />
      </Button>,
    );
  } else if (isLast && message.role === 'assistant') {
    footerButtons.push(
      <Button key="reload" isIconOnly size="sm" title="重新生成" variant="light" onPress={reload}>
        <IconRefresh height={14} width={14} />
      </Button>,
    );
  }
  footerButtons.push(<div key="spacer" className="flex-1" />);
  if (message.role === 'assistant') {
    const tokenAnnotation = annotations?.find((e: any) => e.type === 'token-count') as { totalTokens: number };

    if (tokenAnnotation) {
      footerButtons.push(
        <span key="token-count" className="text-xs text-zinc-400 dark:text-zinc-600" title="Token Count">
          {tokenAnnotation.totalTokens} tokens
        </span>,
      );
    }
  }

  const footer = <footer className={footerClass({ role: msgRole })}>{footerButtons}</footer>;

  const children = <>
    <div className="relative w-full">
      <Card className={bubbleCard({ role: msgRole, loading: false })}>
        <CardBody className={clsx("flex flex-col text-sm py-[20px]", {
          // SSE流消息框保持最小高度与后台任务loading消息框一致
          "min-h-[45px]": message.role === 'assistant' && isStreaming && (!content || content.trim() === '')
        })}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              { (blocksArr.length > 0)
                  ? blocksArr
                  : (content && <ExpandableHtmlContent html={content} originalText={content} />)
              }
              { images }
            </div>
            {/* 消息框内右侧的loading效果 - 任务进行中时显示 */}
            {message.role === 'assistant' && isLoading && (
              <div className="ml-2 -mt-3 flex-shrink-0">
                <Spinner variant="dots" />
              </div>
            )}
          </div>
        </CardBody>
      </Card>
      
      {/* 消息右上角的loading效果 - 仅在助手消息且正在streaming时显示 */}
      {message.role === 'assistant' && isStreaming && (
        <div className="absolute -top-2 right-2 z-10">
          <Spinner variant="dots" />
        </div>
      )}
    </div>
    
    { footer }
  </>;

  return (
    <div className="w-full">
      {/* 消息气泡 */}
      <div className={bubble({ role: msgRole })}>{children}</div>

      {/* 不完整输出指示器 - 在消息下方单独一行显示 */}
      {message.role === 'assistant' && isLast && incompleteAnnotation && !isLoading && (
        <div className="mt-3 px-2">
          <IncompleteOutputIndicator
            canContinue={incompleteAnnotation.canContinue !== false} // 确保默认为true
            incompleteContent={incompleteAnnotation.incompleteContent}
            incompleteType={incompleteAnnotation.incompleteType}
            isLoading={isContinuing}
            suggestedContinuePrompt={incompleteAnnotation.suggestedContinuePrompt}
            onContinue={handleContinueOutput}
          />
        </div>
      )}
    </div>
  );
}
