import React from 'react';
import { Card, CardBody, Button, Chip, Divider } from '@heroui/react';
import { motion } from 'framer-motion';
import IconAlert from '~icons/mdi/alert-circle';
import IconRefresh from '~icons/mdi/refresh';
import IconInfo from '~icons/mdi/information';

interface IncompleteOutputIndicatorProps {
  incompleteType?: 'files' | 'diff' | 'text' | 'mixed';
  incompleteContent?: string;
  suggestedContinuePrompt?: string;
  canContinue?: boolean;
  onContinue: () => void;
  isLoading?: boolean;
}

const typeConfig = {
  files: {
    color: 'warning' as const,
    icon: '📁',
    title: '文件输出不完整',
    description: '代码文件可能没有完整输出',
  },
  diff: {
    color: 'warning' as const,
    icon: '📝',
    title: 'Diff输出不完整',
    description: '代码变更可能没有完整输出',
  },
  text: {
    color: 'primary' as const,
    icon: '✂️',
    title: '回答被截断',
    description: '回答内容可能因长度限制被截断',
  },
  mixed: {
    color: 'danger' as const,
    icon: '🔀',
    title: '混合内容不完整',
    description: '代码和文本内容都可能不完整',
  },
};

export function IncompleteOutputIndicator({
  incompleteType = 'text',
  incompleteContent,
  suggestedContinuePrompt,
  canContinue = true,
  onContinue,
  isLoading = false,
}: IncompleteOutputIndicatorProps) {
  const config = typeConfig[incompleteType];

  const handleContinue = () => {
    if (!canContinue || isLoading) {
      return;
    }

    if (onContinue) {
      onContinue();
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="border-2 border-orange-200 dark:border-orange-700 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20">
        <CardBody className="p-4">
          {/* 标题区域 */}
          <div className="flex items-start gap-3 mb-3">
            <div className="flex-shrink-0 text-2xl">{config.icon}</div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold text-orange-900 dark:text-orange-100">
                  {config.title}
                </h4>
                <Chip 
                  color={config.color} 
                  size="sm" 
                  variant="flat"
                  startContent={<IconAlert className="w-3 h-3" />}
                >
                  需要继续
                </Chip>
              </div>
              <p className="text-sm text-orange-700 dark:text-orange-300">
                {config.description}
              </p>
            </div>
          </div>

          {/* 详细信息 */}
          {incompleteContent && (
            <div className="mb-3 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <div className="flex items-start gap-2">
                <IconInfo className="w-4 h-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-orange-800 dark:text-orange-200">
                  <strong>检测结果：</strong>{incompleteContent}
                </p>
              </div>
            </div>
          )}

          <Divider className="my-3" />

          {/* 建议的继续提示 */}
          {suggestedContinuePrompt && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-600">
              <h5 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                💡 建议的继续提示：
              </h5>
              <p className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed">
                {suggestedContinuePrompt}
              </p>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-600 dark:text-gray-400">
              点击继续按钮让AI完成剩余内容
            </div>
            <Button
              color="primary"
              size="sm"
              startContent={<IconRefresh className="w-4 h-4" />}
              onPress={handleContinue}
              isLoading={isLoading}
              isDisabled={!canContinue}
              className="font-medium"
            >
              {isLoading ? '正在继续...' : '继续输出'}
            </Button>
          </div>

          {/* 使用提示 */}
          <div className="mt-3 pt-3 border-t border-orange-200 dark:border-orange-700">
            <div className="flex items-start gap-2">
              <div className="text-orange-500 text-xs mt-0.5">💡</div>
              <div className="text-xs text-orange-600 dark:text-orange-400 leading-relaxed">
                <strong>使用说明：</strong>
                系统自动检测到输出可能不完整。点击"继续输出"按钮，AI将从中断的地方继续完成回答。
                {incompleteType === 'files' && '特别是代码文件，将确保完整输出所有内容。'}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
} 