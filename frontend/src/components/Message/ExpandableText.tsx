import { useState, useMemo } from 'react';
import { Button } from '@heroui/button';
import IconChevronDown from '~icons/mdi/chevron-down';
import IconChevronUp from '~icons/mdi/chevron-up';

interface ExpandableTextProps {
  children: React.ReactNode;
  content: string;
  maxLines?: number;
}

export function ExpandableText({ children, content, maxLines = 50 }: ExpandableTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 计算文本行数
  const lineCount = useMemo(() => {
    return content.split('\n').length;
  }, [content]);
  
  // 如果行数少于等于最大行数，直接显示全部内容
  if (lineCount <= maxLines) {
    return <div className="whitespace-pre-wrap">{children}</div>;
  }
  
  // 重新渲染截取后的内容
  const truncatedChildren = useMemo(() => {
    if (isExpanded) return children;
    
    const lines = content.split('\n');
    if (lines.length > maxLines) {
      const truncated = lines.slice(0, maxLines).join('\n');
      return <div className="whitespace-pre-wrap">{truncated}</div>;
    }
    return children;
  }, [children, content, isExpanded, maxLines]);
  
  return (
    <div>
      <div className="whitespace-pre-wrap">
        {truncatedChildren}
      </div>
      {lineCount > maxLines && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <Button
            size="sm"
            variant="flat"
            className="w-full"
            startContent={isExpanded ? <IconChevronUp /> : <IconChevronDown />}
            onPress={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? '收起' : `展开查看全部内容 (共${lineCount}行)`}
          </Button>
        </div>
      )}
    </div>
  );
} 