import { <PERSON>, Spin<PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { type UIMessage } from '@ai-sdk/ui-utils';
import { AnimatedCheck } from '../AnimatedCheck';
import { eventBus } from "@/utils/eventBus";

export function CodeDiff({ diff, status, annotations }: {
  diff: string;
  annotations?: UIMessage['annotations'],
  status: {
    isDone: boolean;
  };
 }) {
  const diffError = (annotations as Record<string, any>[])?.find(anno => anno.type === 'apply-diff-error');
  return (
    <>
      <div
        className='my-2 p-2 border border-zinc-300 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 hover:!bg-[#aaaaaa22] cursor-pointer rounded-lg'
        onClick={() => {
          // TODO: show in codemirror
          alert(diff);
        }}
      >
        <div className="flex flex-row items-center px-2 gap-2 h-6 my-1">
          <span className="text-tiny text-foreground-500 flex-1">代码片段</span>
          { status.isDone ? <AnimatedCheck /> : <Spinner variant="dots" /> }
        </div>
      </div>
      { diffError ? (
        <div>
          <div className="flex flex-row gap-2 mt-1">
            <Chip color="danger">合入错误</Chip>
            {diffError.error}
          </div>
          <footer className="mt-2 text-center">
            <Button size="sm" color="primary" onClick={() => {
              eventBus.emit('append-chat', 'Diff合入失败，请避免使用Diff格式');
            }}>修复这个错误</Button>
          </footer>
        </div>
      ): null}
    </>
  );
}
