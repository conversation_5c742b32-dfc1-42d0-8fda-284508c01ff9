import { Alert } from '@heroui/alert';
import IconInformation from '~icons/mdi/information';

interface MaxIterationReachedAlertProps {
  iterationCount: number;
  maxIterations: number;
}

export function MaxIterationReachedAlert({ 
  iterationCount, 
  maxIterations 
}: MaxIterationReachedAlertProps) {
  return (
    <Alert
      className="my-2"
      icon={<IconInformation />}
      variant="bordered"
      color="warning"
      title="已达到最大自迭代检查轮数"
      description={
        <div className="space-y-2">
          <p>
            已经完成了 <strong>{iterationCount}</strong> 轮自动优化检查（最大 {maxIterations} 轮）。
          </p>
          <p>
            如果您认为HTML代码仍需要进一步优化，请手动输入具体的优化要求，AI将根据您的指导继续改进代码。
          </p>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            <p><strong>建议操作：</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>检查当前的HTML和CSS代码效果</li>
              <li>如需继续优化，请描述具体需要改进的方面</li>
              <li>例如："请优化移动端响应式布局"、"请改善颜色搭配"等</li>
            </ul>
          </div>
        </div>
      }
    />
  );
} 