import { Listbox, ListboxItem } from "@heroui/listbox";
import { Spinner } from "@heroui/spinner";
import { Button } from "@heroui/react";
import { AnimatedCheck } from "../AnimatedCheck";
import IconOutline from '~icons/mdi/file-outline';

export function CodeLink({
  id, list, openFile, changeVersion, status,
}: {
  id: string;
  list: string[];
  openFile: (version: number, path: string) => Promise<void>;
  changeVersion: (version: number) => void;
  status: {
    done: string[];
    isDone: boolean;
    version?: number;
  };
}) {
  let extra;
  if (status.isDone) {
    if (status.version !== undefined) {
      extra = (
        <Button size="sm" isIconOnly onPress={() => {
          if (status.isDone) {
            changeVersion(status.version!);
          }
        }}>
          v{status.version}
        </Button>
      );
    }
  } else {
    extra = <Spinner variant="dots" />;
  }
  return (
    <div
      data-part={id}
      className='my-2 p-2 border border-zinc-300 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 rounded-lg'
    >
      <div className="flex flex-row items-center px-2 gap-2 h-6 my-1">
        <span className="text-tiny text-foreground-500">生成文件</span>
        <div className="flex-1"></div>
        {extra}
      </div>
      <Listbox variant="flat" aria-label="输出文件">
        {list.map((file, k) => (
          <ListboxItem
            key={k}
            startContent={<IconOutline />}
            endContent={status.done.includes(file) || status.isDone ? <AnimatedCheck /> : null}
            onPress={() => {
              openFile(status.version!, file);
            }}
          >
            {file}
          </ListboxItem>
        ))}
      </Listbox>
    </div>
  );
}
