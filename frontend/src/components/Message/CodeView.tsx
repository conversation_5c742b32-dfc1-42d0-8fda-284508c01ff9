import { Button } from '@heroui/react';
import { useEffect, useState } from 'react';
import { codeToHtml } from 'shiki';
import { useThemeStore } from '@/stores/theme';
import copyContent from '@/utils/copyContent';
import IconCopy from '~icons/mdi/content-copy';

const langs = [
  'javascript',
  'html',
  'css',
  'json',
  'typescript',
  'javascript',
  'jsx',
  'tsx',
  'sass',
  'scss',
  'less',
  'bash',
];
// const highlighter = await createHighlighter({
//   themes: ['github-light', 'github-dark'],
//   langs,
// })

export function CodeView({ lang, code }: { lang?: string; code: string }) {
  if (lang && !new Set(langs).has(lang)) {
    lang = 'text';
  }

  const { theme } = useThemeStore();

  const [html, setHtml] = useState('');

  useEffect(() => {
    let obsoleted = false;
    const transform = async () => {
      const html = await codeToHtml(code, {
        theme: theme === 'dark' ? 'github-dark' : 'github-light',
        lang: lang ?? 'text',
      });

      if (!obsoleted) {
        setHtml(html);
      }
    };

    transform();

    return () => {
      obsoleted = true;
    };
  }, [lang, code, theme]);

  return (
    <div className="my-2 p-2 border border-zinc-300 dark:border-zinc-700 bg-white dark:bg-[#24292e] rounded-lg relative">
      <Button
        className="absolute right-0 top-0"
        endContent={<IconCopy />}
        isIconOnly={!lang}
        size="sm"
        onPress={() => {
          copyContent(code);
        }}
      >
        {lang}
      </Button>
      <div dangerouslySetInnerHTML={{ __html: html }} className="p-2 overflow-auto w-full text-xs" />
    </div>
  );
}
