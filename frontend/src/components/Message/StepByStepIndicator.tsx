import React from 'react';
import { Card, CardBody, Button, Progress, Chip } from '@heroui/react';
import { ExecutionStep, StepExecutionState } from '@/hooks/useHtmlStepByStepExecutor';
import IconPlay from '~icons/mdi/play';
import IconPause from '~icons/mdi/pause';
import IconStop from '~icons/mdi/stop';
import IconCheck from '~icons/mdi/check';
import IconAlert from '~icons/mdi/alert';
import IconClock from '~icons/mdi/clock';
import IconFileCode from '~icons/mdi/file-code';
import IconFilePlus from '~icons/mdi/file-plus';

interface StepByStepIndicatorProps {
  state: StepExecutionState;
  onStart: () => void;
  onCancel: () => void;
  onContinue: (input: string) => void;
}

export function StepByStepIndicator({
  state,
  onStart,
  onCancel,
  onContinue,
}: StepByStepIndicatorProps) {
  const [userInput, setUserInput] = React.useState('');

  // 分析步骤输出类型
  const analyzeStepOutputType = (stepContent: string): 'incremental' | 'full' | 'mixed' => {
    const content = stepContent.toLowerCase();
    
    const incrementalKeywords = ['添加', '新增', '插入', '修改', '更新', '调整', '优化', '增加', '补充', '完善', '改进', '扩展', '加入'];
    const fullKeywords = ['创建', '生成', '构建', '建立', '制作', '编写', '完整', '全部', '整个', '所有', '从头', '重新'];
    
    const hasIncremental = incrementalKeywords.some(keyword => content.includes(keyword));
    const hasFull = fullKeywords.some(keyword => content.includes(keyword));
    
    if (hasIncremental && hasFull) return 'mixed';
    if (hasIncremental) return 'incremental';
    if (hasFull) return 'full';
    return 'mixed';
  };

  const getStepStatusIcon = (step: ExecutionStep) => {
    switch (step.status) {
      case 'completed':
        return <IconCheck className="w-4 h-4 text-green-600" />;
      case 'executing':
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      case 'failed':
        return <IconAlert className="w-4 h-4 text-red-600" />;
      case 'waiting_user':
        return <IconClock className="w-4 h-4 text-orange-600" />;
      default:
        return <div className="w-4 h-4 border border-gray-300 rounded-full" />;
    }
  };

  const getStepStatusColor = (step: ExecutionStep) => {
    switch (step.status) {
      case 'completed':
        return 'success';
      case 'executing':
        return 'primary';
      case 'failed':
        return 'danger';
      case 'waiting_user':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getOutputTypeIcon = (outputType: 'incremental' | 'full' | 'mixed') => {
    switch (outputType) {
      case 'full':
        return <IconFilePlus className="w-3 h-3 text-red-600" />;
      case 'incremental':
        return <IconFileCode className="w-3 h-3 text-yellow-600" />;
      default:
        return <IconFileCode className="w-3 h-3 text-blue-600" />;
    }
  };

  const getOutputTypeColor = (outputType: 'incremental' | 'full' | 'mixed') => {
    switch (outputType) {
      case 'full':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      case 'incremental':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20';
      default:
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  const completedSteps = state.steps.filter(step => step.status === 'completed').length;
  const progressValue = state.steps.length > 0 ? (completedSteps / state.steps.length) * 100 : 0;

  // 修改渲染条件：即使没有步骤也显示初始化状态
  const hasSteps = state.steps.length > 0;
  
  console.log('🔍 StepByStepIndicator渲染状态:', {
    hasSteps,
    stepsCount: state.steps.length,
    isExecuting: state.isExecuting,
    isCompleted: state.isCompleted,
    isCancelled: state.isCancelled,
  });

  return (
    <Card className="mb-4 border-2 border-purple-200 dark:border-purple-700 min-h-[200px]">
      <CardBody className="p-4 min-h-[160px]">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <IconPlay className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900 dark:text-purple-100">
                自动化分步执行
              </h3>
              <p className="text-sm text-purple-600 dark:text-purple-400">
                {hasSteps 
                  ? `共 ${state.steps.length} 个步骤，已完成 ${completedSteps} 个`
                  : '正在初始化步骤...'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {!hasSteps && (
              <Chip color="default" variant="flat" size="sm">
                初始化中
              </Chip>
            )}
            {state.isCompleted && (
              <Chip color="success" variant="flat" size="sm">
                全部完成
              </Chip>
            )}
            {state.isCancelled && (
              <Chip color="danger" variant="flat" size="sm">
                已取消
              </Chip>
            )}
            {state.isExecuting && (
              <Chip color="primary" variant="flat" size="sm">
                执行中
              </Chip>
            )}
            {state.needsUserInput && (
              <Chip color="warning" variant="flat" size="sm">
                等待用户输入
              </Chip>
            )}
          </div>
        </div>

        {/* 进度条 - 只有当有步骤时才显示 */}
        {hasSteps && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                执行进度
              </span>
              <span className="text-sm font-bold text-purple-600">
                {Math.round(progressValue)}%
              </span>
            </div>
            <Progress
              value={progressValue}
              color="secondary"
              size="sm"
              className="w-full"
            />
          </div>
        )}

        {/* 初始化提示 - 当没有步骤时显示 */}
        {!hasSteps && (
          <div className="mb-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm font-medium text-purple-900 dark:text-purple-100">
                正在解析执行步骤
              </span>
            </div>
            <p className="text-xs text-purple-700 dark:text-purple-300">
              系统正在从您的任务描述中解析出详细的执行步骤，请稍候...
            </p>
          </div>
        )}

        {/* 输出规范说明 - 只有当有步骤时才显示 */}
        {hasSteps && (
          <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 mb-2">
              <IconFileCode className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                输出规范说明
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div className="flex items-center gap-2">
                <IconFilePlus className="w-3 h-3 text-red-600" />
                <span className="text-red-600">全量输出</span>
                <span>- 输出完整代码，不允许省略任何内容</span>
              </div>
              <div className="flex items-center gap-2">
                <IconFileCode className="w-3 h-3 text-yellow-600" />
                <span className="text-yellow-600">增量输出</span>
                <span>- 基于现有代码修改，但仍需输出完整文件</span>
              </div>
              <div className="flex items-center gap-2">
                <IconFileCode className="w-3 h-3 text-blue-600" />
                <span className="text-blue-600">混合输出</span>
                <span>- 根据步骤内容智能判断输出方式</span>
              </div>
            </div>
          </div>
        )}

        {/* 步骤列表 - 只有当有步骤时才显示 */}
        {hasSteps && (
          <div className="space-y-2 mb-4 max-h-48 overflow-y-auto">
            {state.steps.map((step, index) => {
              const outputType = analyzeStepOutputType(step.content);
              return (
                <div
                  key={step.stepNumber}
                  className={`flex items-center gap-3 p-2 rounded-lg border ${
                    index === state.currentStepIndex
                      ? 'border-purple-300 bg-purple-50 dark:bg-purple-900/20'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {getStepStatusIcon(step)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        步骤 {step.stepNumber}
                      </span>
                      <Chip
                        size="sm"
                        variant="flat"
                        color={getStepStatusColor(step) as any}
                      >
                        {step.status === 'pending' && '待执行'}
                        {step.status === 'executing' && '执行中'}
                        {step.status === 'completed' && '已完成'}
                        {step.status === 'failed' && '失败'}
                        {step.status === 'waiting_user' && '等待输入'}
                      </Chip>
                      <div className={`flex items-center gap-1 px-2 py-1 rounded text-xs ${getOutputTypeColor(outputType)}`}>
                        {getOutputTypeIcon(outputType)}
                        <span>
                          {outputType === 'full' && '全量'}
                          {outputType === 'incremental' && '增量'}
                          {outputType === 'mixed' && '混合'}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {step.title}
                    </p>
                    {step.result && (
                      <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                        {step.result}
                      </p>
                    )}
                    {step.error && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {step.error}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 用户输入区域 */}
        {state.needsUserInput && (
          <div className="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-700">
            <div className="flex items-center gap-2 mb-2">
              <IconClock className="w-4 h-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-900 dark:text-orange-100">
                需要用户输入
              </span>
            </div>
            <p className="text-sm text-orange-700 dark:text-orange-300 mb-3">
              {state.userInputPrompt}
            </p>
            <div className="flex gap-2">
              <input
                type="text"
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="请输入相关信息..."
                className="flex-1 px-3 py-2 text-sm border border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && userInput.trim()) {
                    onContinue(userInput.trim());
                    setUserInput('');
                  }
                }}
              />
              <Button
                size="sm"
                color="warning"
                onPress={() => {
                  if (userInput.trim()) {
                    onContinue(userInput.trim());
                    setUserInput('');
                  }
                }}
                isDisabled={!userInput.trim()}
              >
                继续执行
              </Button>
            </div>
          </div>
        )}

        {/* 控制按钮 */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {!hasSteps && '正在初始化分步执行模式...'}
            {hasSteps && state.isExecuting && '正在自动执行步骤，请稍候...'}
            {hasSteps && state.isCompleted && '🎉 所有步骤已完成！'}
            {hasSteps && state.isCancelled && '执行已取消'}
            {hasSteps && !state.isExecuting && !state.isCompleted && !state.isCancelled && !state.needsUserInput && '准备开始执行'}
          </div>
          
          <div className="flex gap-2">
            {hasSteps && !state.isExecuting && !state.isCompleted && !state.needsUserInput && (
              <Button
                size="sm"
                color="secondary"
                startContent={<IconPlay className="w-4 h-4" />}
                onPress={onStart}
              >
                开始执行
              </Button>
            )}
            
            {hasSteps && (state.isExecuting || state.needsUserInput) && !state.isCompleted && (
              <Button
                size="sm"
                color="danger"
                variant="flat"
                startContent={<IconStop className="w-4 h-4" />}
                onPress={onCancel}
              >
                取消执行
              </Button>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
} 