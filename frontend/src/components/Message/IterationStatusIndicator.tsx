import { Progress } from '@heroui/progress';
import { Chip } from '@heroui/chip';
import { Button } from '@heroui/button';
import IconAutorenew from '~icons/mdi/autorenew';
import IconCheckCircle from '~icons/mdi/check-circle';
import IconStop from '~icons/mdi/stop';
import IconPlay from '~icons/mdi/play';
import IconCancel from '~icons/mdi/cancel';

interface IterationStatusIndicatorProps {
  iterationCount: number;
  maxIterations: number;
  isIterating: boolean;
  maxIterationsReached: boolean;
  isGenerating?: boolean; // 是否还在生成中
  isCancelled?: boolean; // 是否被用户取消
  onCancel?: () => void; // 取消回调
  onResume?: () => void; // 恢复回调
}

export function IterationStatusIndicator({
  iterationCount,
  maxIterations,
  isIterating,
  maxIterationsReached,
  isGenerating = false,
  isCancelled = false,
  onCancel,
  onResume,
}: IterationStatusIndicatorProps) {
  // 如果没有开始迭代，不显示
  if (iterationCount === 0 && !isIterating) {
    return null;
  }

  // 进度计算：如果达到最大迭代次数但还在生成，进度显示为95%而不是100%
  const baseProgress = (iterationCount / maxIterations) * 100;
  const progressValue = maxIterationsReached && isGenerating 
    ? Math.min(baseProgress, 95) // 还在生成时最多显示95%
    : baseProgress;
  
  // 真正完成的条件：达到最大迭代次数且不在生成中
  const isReallyCompleted = maxIterationsReached && !isGenerating;

  // 确定状态和颜色
  const getStatusConfig = () => {
    if (isCancelled) {
      return {
        color: 'danger' as const,
        label: '已取消',
        icon: <IconCancel className="w-3 h-3" />,
        animate: false
      };
    }
    if (isReallyCompleted) {
      return {
        color: 'warning' as const,
        label: '已完成',
        icon: <IconCheckCircle className="w-3 h-3" />,
        animate: false
      };
    }
    if (isIterating || isGenerating) {
      return {
        color: 'primary' as const,
        label: '进行中...',
        icon: null,
        animate: true
      };
    }
    return {
      color: 'default' as const,
      label: `${iterationCount}/${maxIterations}`,
      icon: null,
      animate: false
    };
  };

  const statusConfig = getStatusConfig();

  return (
    <div className="my-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <IconAutorenew 
            className={`w-4 h-4 transition-all duration-200 ${
              (isIterating && !isCancelled)
                ? 'animate-spin text-blue-500 drop-shadow-sm' 
                : isCancelled
                ? 'text-red-500'
                : 'text-gray-500 hover:text-gray-600'
            }`} 
            style={(isIterating && !isCancelled) ? {
              animation: 'spin 1s linear infinite, pulse 2s ease-in-out infinite alternate'
            } : undefined}
          />
          <span className="text-sm font-medium">HTML优化自检查进度</span>
        </div>
        
        <div className="flex items-center gap-2">
          <Chip
            size="sm"
            variant="flat"
            color={statusConfig.color}
            startContent={statusConfig.icon}
            className={statusConfig.animate ? 'animate-pulse' : ''}
          >
            {statusConfig.label}
          </Chip>
          
          {/* 控制按钮 */}
          {(isIterating || (!maxIterationsReached && !isCancelled)) && (
            <Button
              size="sm"
              variant="flat"
              color={isCancelled ? 'primary' : 'danger'}
              isIconOnly
              onPress={isCancelled ? onResume : onCancel}
              className="ml-1"
            >
              {isCancelled ? (
                <IconPlay className="w-3 h-3" />
              ) : (
                <IconStop className="w-3 h-3" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      <Progress
        size="sm"
        value={isCancelled ? 0 : progressValue}
        color={isCancelled ? 'danger' : isReallyCompleted ? 'warning' : 'primary'}
        className={`mb-2 ${statusConfig.animate ? 'animate-pulse' : ''}`}
      />
      
      <div className="text-xs text-gray-600 dark:text-gray-400">
        {isCancelled ? (
          <span className="text-red-600 dark:text-red-400">
            🚫 自动优化已取消，点击播放按钮可重新启用
          </span>
        ) : isIterating ? (
          <span className="flex items-center gap-1">
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></span>
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></span>
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></span>
            <span className="ml-1">正在发送自动优化请求...</span>
          </span>
        ) : isGenerating && maxIterationsReached ? (
          <span className="flex items-center gap-1">
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></span>
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></span>
            <span className="inline-block w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></span>
            <span className="ml-1">最后一轮优化生成中...</span>
          </span>
        ) : isReallyCompleted ? (
          '自动检查已完成，如需继续优化请手动指导'
        ) : (
          `已进行了 ${iterationCount} 轮自动检查，最多进行 ${maxIterations} 轮`
        )}
      </div>
    </div>
  );
} 