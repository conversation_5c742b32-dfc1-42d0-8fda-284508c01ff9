import { useEffect, useRef } from "react";
import { Button } from "@heroui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, ModalHeader } from "@heroui/modal";

export function ModalConfirm({
  isOpen,
  onOpenChange,
  title,
  message,
  onConfirm,
  onCancel,
  cancelText = "取消",
  confirmText = "确定",
  confirmColor = "danger",
  buttons = ['cancel', 'confirm'],
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title?: React.ReactNode;
  message?: React.ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  cancelText?: string;
  confirmText?: string;
  confirmColor?: 'default' | 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
  buttons?: React.ReactNode[];
}) {
  const confirmed = useRef(false);
  useEffect(() => {
    if (isOpen) {
      // Reset confirmed when modal opens
      confirmed.current = false;
    }
  }, [isOpen]);
  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={(open) => {
        if (open) {
          // This is not called regardless
          confirmed.current = false;
        } else {
          if (confirmed.current) {
            onConfirm?.();
          } else {
            onCancel?.();
          }
        }
        onOpenChange(open);
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            { title && <ModalHeader className="flex flex-col gap-1">{ title }</ModalHeader> }
            { message && <ModalBody>{ message }</ModalBody> }
            <ModalFooter>
              {buttons.map((button, i) => {
                if (typeof button === 'string') {
                  return (
                    <Button key={i} color={button === 'confirm' ? confirmColor : 'default'} onPress={() => {
                      if (button === 'confirm') {
                        if (!confirmed.current) {
                          confirmed.current = true;
                          onClose();
                        }
                      } else {
                        onClose();
                      }
                    }}>{button === 'confirm' ? confirmText : cancelText}</Button>
                  );
                }
                return button;
              })}
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}