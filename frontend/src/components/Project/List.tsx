import { Button, Listbox, ListboxItem } from "@heroui/react";
import { useNavigate } from "react-router-dom";
import { useProjectStore } from "@/stores/project";
import IconPlus from '~icons/mdi/plus';

export function ProjectList() {
  const { projects, current } = useProjectStore();
  const navigate = useNavigate();

  return (
    <div className="w-full max-w-[260px] border-small px-1 py-2 rounded-small border-default-200 dark:border-default-100">
      <Listbox
        aria-label="Dynamic Actions"
        bottomContent={
          <Button
            fullWidth
            size="sm"
            startContent={<IconPlus />}
            onPress={() => navigate('/project')}
          >创建项目</Button>
        }
        items={projects}
        selectedKeys={current ? [current] : []}
        selectionMode="single"
        onSelectionChange={([id]) => {
          if (id) {
            navigate(`/project/${id}`);
          }
        }}
      >
        {
          (item) => (
            <ListboxItem
              key={ item.id }
            >
              { item.name }
            </ListboxItem>
          )
        }
      </Listbox>
    </div>
  );
}