import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import clsx from "clsx";
import { addToast, Button, Input, Select, SelectItem, Textarea, Tooltip } from "@heroui/react";
import { Autocomplete, AutocompleteItem } from "@heroui/autocomplete";
import { useProjectStore } from "@/stores/project";
import { typeMap, libraryMap } from "@/config/library";
import { useUserInfoStore } from '@/hooks/login';
import { MarkdownEditor } from "./MarkdownEditor";
import IconHelp from '~icons/mdi/help';

export function ProjectForm({
  className,
}: {
  className?: string;
}) {
  const { userInfo } = useUserInfoStore();
  const { addProject, updateProject, current, currentProject } = useProjectStore();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [framework, setFramework] = useState('');
  const [componentLibrary, setComponentLibrary] = useState('');
  const [llmstxt, setLlmstxt] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const project = currentProject();
    if (project) {
      setName(project.name);
      setDescription(project.description);
      setFramework(project.framework);
      setComponentLibrary(project.componentLibrary);
      setLlmstxt(project.llmstxt);
    } else {
      reset();
    }
  }, [current]);

  async function save() {
    const project = currentProject();
    if (project) {
      await updateProject(project.id, {
        name,
        description,
        framework,
        componentLibrary,
        llmstxt,
      });
      addToast({
        title: '保存成功',
        color: 'success',
      });
    } else {
      const { id } = await addProject({
        name,
        description,
        framework,
        componentLibrary,
        llmstxt,
        user: userInfo?.username,
      });
      addToast({
        title: '创建成功',
        color: 'success',
      });
      navigate(`/project/${id}`);
    }
  }

  function reset() {
    setName('');
    setDescription('');
    setFramework('');
    setComponentLibrary('');
    setLlmstxt('');
  }

  return (
    <section className={clsx("flex flex-col gap-4 min-w-0", className)}>
      <Input size="sm" label="项目名称" value={name} onChange={(e) => setName(e.target.value)} />
      <Textarea size="sm" label="描述" value={description} onChange={(e) => setDescription(e.target.value)} minRows={2} />
      <div className="flex flex-row gap-2">
        <Select
          size="sm"
          className="max-w-xs"
          label="框架"
          selectedKeys={[framework]}
          onChange={(e) => {
            setFramework(e.target.value);
            setComponentLibrary('');
          }}
        >
          {typeMap.map((framework) => (
            <SelectItem key={framework.value}>{framework.label}</SelectItem>
          ))}
        </Select>
        <Autocomplete
          size="sm"
          className="max-w-xs"
          label="组件库"
          inputValue={componentLibrary}
          defaultItems={libraryMap[framework] ?? []}
          onInputChange={setComponentLibrary}
          allowsCustomValue
        >
          {(e) => (
            <AutocompleteItem key={e.value}>{e.label}</AutocompleteItem>
          )}
        </Autocomplete>
      </div>
      <div className="bg-zinc-100 dark:bg-zinc-800 p-2 rounded-lg">
        <h1 className="flex items-center text-sm mb-2 text-zinc-500 dark:text-zinc-400">
          <span className="flex-1">自定义组件</span>
          <Tooltip content="请提供一个markdown文件，包含组件的说明、使用方法、参数说明等信息">
            <Button className="ml-2" isIconOnly variant="ghost" radius="full" size="sm">
              <IconHelp />
            </Button>
          </Tooltip>
        </h1>
        <MarkdownEditor value={llmstxt} onChange={setLlmstxt} />
      </div>
      <div className="flex flex-row gap-2 justify-center">
        <Button size="sm" variant="flat" onPress={reset}>重置</Button>
        <Button size="sm" color="primary" onPress={save}>{ current ? '更新' : '创建' }</Button>
      </div>
    </section>
  )
}
