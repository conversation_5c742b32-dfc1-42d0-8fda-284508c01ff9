import { useEffect, useRef } from "react";
import { EditorView, basicSetup } from "codemirror";
import clsx from "clsx";
import { keymap } from "@codemirror/view";
import { Transaction } from "@codemirror/state";
import { indentWithTab } from "@codemirror/commands";
import { oneDark } from '@codemirror/theme-one-dark';
import { useThemeStore } from "@/stores/theme";
import { markdown } from "@codemirror/lang-markdown";
import { languages } from "@codemirror/language-data";
import styles from './MarkdownEditor.module.css';

export function MarkdownEditor({ value, onChange, className }: {
  value: string;
  className?: string;
  onChange: (value: string) => void;
}) {
  const { theme } = useThemeStore();
  const editorRef = useRef<HTMLDivElement>(null);
  const editorViewRef = useRef<EditorView>();
  useEffect(() => {
    const editorEl = editorRef.current!;
    const extensions = [
      basicSetup,
      keymap.of([indentWithTab]),
      markdown({ codeLanguages: languages }),
      EditorView.updateListener.of(update => {
        const isRemoteUpdate = update.transactions.some(a => a.annotation(Transaction.remote));
        if (!update.docChanged || isRemoteUpdate) {
          return;
        }
        if (update.docChanged && update.state.doc.length > 0) {
          onChange(update.state.doc.toString());
        }
      }),
    ];

    if (theme == 'dark') {
      extensions.push(oneDark);
    }
  
    const view = new EditorView({
      extensions,
      parent: editorEl,
      root: document,
    });

    editorViewRef.current = view;

    return () => {
      view.destroy();
    };
  }, []);

  // Updata content when file content changed
  useEffect(() => {
    const view = editorViewRef.current;
    if (!view) return;
    const oldText = view?.state.doc.toString();
    const newText = value;
    if (oldText !== newText) {
      // 保存当前滚动位置
      const scrollTop = view.scrollDOM.scrollTop;
      const scrollLeft = view.scrollDOM.scrollLeft;
      
      view.dispatch({
        annotations: [Transaction.remote.of(true), Transaction.addToHistory.of(false)],
        changes: { from: 0, to: view.state.doc.length, insert: value },
      });
      
      // 恢复滚动位置
      requestAnimationFrame(() => {
        view.scrollDOM.scrollTo({
          top: Math.min(scrollTop, view.scrollDOM.scrollHeight - view.scrollDOM.clientHeight),
          left: scrollLeft
        });
      });
    }
  }, [value]);

  return (
    <div
      className={clsx('flex-1 max-w-full', className, styles.markdownEditor)}
      ref={editorRef}
    />
  );
}
