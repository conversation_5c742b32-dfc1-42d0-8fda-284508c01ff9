import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/dropdown";
import { Button, Tooltip } from "@heroui/react";
import _ from 'lodash';
import { useState } from "react";
import EyeOnIcon from '~icons/mdi/eye';
import EyeOffIcon from '~icons/mdi/eye-off';

const OPTIONS = [
  {
    value: 'Y',
    text: '是',
  },
  {
    value: 'N',
    text: '否',
  },
];

export function IsPublicDropdown({ isPublic, onChange }: {
  isPublic: boolean,
  onChange: (key: boolean) => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const selectedKey = isPublic ? 'Y' : 'N';

  const handleChange = (key: string) => {
    onChange(key === 'Y');
  };

  if (!isOpen) {
    return (
      <Tooltip
        showArrow
        content={`是否公开`}
      >
        <Button isIconOnly size="sm" variant="flat" onPress={() => setIsOpen(true)}>
          { isPublic ? <EyeOnIcon /> : <EyeOffIcon /> }
        </Button>
      </Tooltip>
    );
  }

  return (
    <Dropdown className="w-[200px]" isOpen={isOpen} placement="bottom-end" size="sm" onOpenChange={setIsOpen}>
      <DropdownTrigger>
        <Button isIconOnly size="sm" title="是否公开" variant="flat">
        { isPublic ? <EyeOnIcon /> : <EyeOffIcon /> }
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        selectedKeys={[selectedKey]}
        selectionMode="single"
        variant="flat"
        onSelectionChange={(e) => handleChange(e.currentKey || '')}
      >
        {
          _.map(OPTIONS, option => (
            <DropdownItem key={`${option.value}`} textValue={option.text}>{option.text}</DropdownItem>
          ))
        }
      </DropdownMenu>
    </Dropdown>
  );
}
