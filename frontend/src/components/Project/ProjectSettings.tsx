import { useDisclosure } from "@heroui/react";
import { DesignProject } from "@/apis";
import { ProjectSettingsDrawer } from "@/components/DesignToCode/ProjectSettingsDrawer";

interface ProjectSettingsProps {
  project?: DesignProject;
  onProjectUpdated: () => void;
  children: (onOpen: () => void) => React.ReactNode;
}

export const ProjectSettings = ({ project, onProjectUpdated, children }: ProjectSettingsProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  if (!project) {
    return null;
  }

  return (
    <>
      {children(onOpen)}
      <ProjectSettingsDrawer
        isOpen={isOpen}
        project={project}
        onClose={onClose}
        onProjectUpdated={onProjectUpdated}
      />
    </>
  );
}; 