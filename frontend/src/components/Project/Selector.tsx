import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from "@heroui/dropdown";
import { Button } from "@heroui/react";
import { useState } from "react";
import { useProjectStore } from "@/stores/project";
import IconClose from '~icons/mdi/close';
import IconCube from '~icons/mdi/cube-outline';

export function ProjectDropdown({ project, onChange }: {
  project: string | undefined;
  onChange: (project: string | undefined) => void;
}) {
  const { projects } = useProjectStore();
  const projectObj = projects.find(p => p.id === project);
  const [isOpen, setIsOpen] = useState(false);

  const displayText = projectObj?.name || "";

  return (
    <Dropdown 
      className="w-[200px]" 
      isOpen={isOpen} 
      placement="bottom-end" 
      radius="sm"
      size="sm" 
      onOpenChange={setIsOpen}
    >
      <DropdownTrigger>
        <Button 
          className={`h-8 flex items-center gap-1 ${!project ? 'min-w-8 w-8 px-0' : ''}`} 
          size="sm" 
          variant="flat"
        >
          <IconCube />
          {displayText}
          {project && (
            // eslint-disable-next-line jsx-a11y/click-events-have-key-events
            <div
              className="ml-1 h-4 w-4 min-w-4 flex items-center justify-center cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              onClick={(e) => {
                e.stopPropagation();
                onChange(undefined);
              }}
            >
              <IconClose className="h-3 w-3" />
            </div>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        className="max-h-60 overflow-y-auto"
        selectedKeys={project ? [project] : []}
        selectionMode="single"
        variant="flat"
        onSelectionChange={(e) => onChange(e.currentKey && e.currentKey !== project ? e.currentKey : undefined)}
      >
        {projects.map(p => (
          <DropdownItem key={p.id} textValue={p.name}>{p.name}</DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
