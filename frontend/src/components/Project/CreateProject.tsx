import { useDisclosure } from "@heroui/react";
import { ProjectSettingsDrawer } from "@/components/DesignToCode/ProjectSettingsDrawer";

interface CreateProjectProps {
  onProjectCreated?: () => void;
  children: (onOpen: () => void) => React.ReactNode;
}

export function CreateProject({ onProjectCreated, children }: CreateProjectProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleProjectCreated = () => {
    onClose();
    onProjectCreated?.();
  };

  return (
    <>
      {children(onOpen)}
      <ProjectSettingsDrawer
        isOpen={isOpen}
        mode="create"
        onClose={onClose}
        onProjectUpdated={handleProjectCreated}
      />
    </>
  );
} 