import {
  But<PERSON>,
  Chip,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Input,
} from "@heroui/react";
import moment from "moment";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { CreateProject } from "@/components/Project/CreateProject";
import { useProjectStore } from "@/stores/project";
import { isProjectConfigured } from "@/utils/projectUtils";
import IconDelete from "~icons/mdi/delete-outline";
import IconDotsHorizontal from "~icons/mdi/dots-horizontal";
import IconView from "~icons/mdi/eye-outline";
import IconFolder from '~icons/mdi/folder-outline';
import IconSearch from '~icons/mdi/magnify';
import IconPlus from '~icons/mdi/plus';

export function ProjectGrid() {
  const { projects } = useProjectStore();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");

  // Filter projects based on search term
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (project.description && project.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const navigateToProject = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);

    if (!project) {
      return;
    }
    if (isProjectConfigured(project)) {
      navigate(`/project/${projectId}`);
    } else {
      navigate(`/prototype/${projectId}`);
    }
  };

  const handleProjectCreated = () => {
    // Refresh the project list after creation
    window.location.reload();
  };

  const handleAction = (key: string | number, projectId: string) => {
    switch (key) {
      case 'view':
        navigateToProject(projectId);
        break;
      case 'delete':
        // TODO: 实现删除功能
        console.log('删除项目:', projectId);
        break;
      default:
        break;
    }
  };

  return (
    <div className="w-full space-y-8">
      {/* Search */}
      <div className="border-b border-[rgb(242,242,242)]">
        <Input
          classNames={{
            input: "font-medium text-default-700 text-sm",
            inputWrapper: "h-12 w-full !bg-white shadow-none focus-within:shadow-none px-6",
          }}
          placeholder="搜索项目..."
          startContent={<IconSearch className="w-5 h-5 text-default-500" />}
          value={searchTerm}
          onValueChange={setSearchTerm}
        />
      </div>
      <div className="max-w-7xl mx-auto px-6 space-y-8">
        <h2 className="text-lg font-medium text-default-700">所有项目</h2>

        {/* Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="cursor-pointer group"
              onClick={() => navigateToProject(project.id)}
            >
              {/* Preview Area */}
              <div className="aspect-[1.618] bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center relative overflow-hidden">
                <div className="absolute top-2 right-2 flex gap-2 z-10">
                  <Chip size="sm" variant="flat">
                    {project.framework || '通用'}
                  </Chip>
                  {project.componentLibrary && (
                    <Chip color="secondary" size="sm" variant="flat">
                      {project.componentLibrary}
                    </Chip>
                  )}
                </div>
                {/* TODO: Replace with actual project screenshot using project.screenshotUrl */}
                <span className="text-sm text-neutral-500 dark:text-neutral-400">No screenshot available</span>
                <div className="absolute bottom-2 left-3 z-10 flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-300">
                  <span className="font-medium truncate">{project.user}</span>
                  <span className="text-nowrap">· {moment(project.created).format('YYYY-MM-DD HH:mm')}</span>
                </div>
              </div>

              {/* Content Area */}
              <div className="mt-4 flex items-start justify-between">
                <div className="min-w-0">
                  <h3 className="font-semibold text-default-900 truncate" title={project.name}>{project.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate" title={project.description || ''}>
                    {project.description || ''}
                  </p>
                </div>
                <div
                  className="transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Dropdown placement="bottom-end" radius="sm">
                    <DropdownTrigger>
                      <Button isIconOnly size="sm" variant="light">
                        <IconDotsHorizontal className="w-5 h-5" />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      aria-label="Project Actions"
                      onAction={(key) => handleAction(key, project.id)}
                    >
                      <DropdownItem
                        key="view"
                        startContent={<IconView className="w-4 h-4" />}
                      >
                        查看
                      </DropdownItem>
                      <DropdownItem
                        key="delete"
                        className="text-danger"
                        color="danger"
                        startContent={<IconDelete className="w-4 h-4" />}
                      >
                        删除
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <IconFolder className="w-16 h-16 text-default-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-default-600 mb-2">
              {searchTerm ? '未找到项目' : '暂无项目'}
            </h3>
            <p className="text-default-500 mb-4">
              {searchTerm
                ? '请尝试调整搜索条件'
                : '创建您的第一个开发项目'
              }
            </p>
            {!searchTerm && (
              <CreateProject onProjectCreated={handleProjectCreated}>
                {(onOpen) => (
                  <Button
                    color="primary"
                    radius="sm"
                    startContent={<IconPlus className="w-4 h-4" />}
                    variant="flat"
                    onPress={onOpen}
                  >
                    创建项目
                  </Button>
                )}
              </CreateProject>
            )}
          </div>
        )}
      </div>
      {/* 底部留白 - 确保有足够的空间 */}
      <div className="h-20" />
    </div>
  );
}
