import Ansi from "ansi-to-react";
import { useContext, useEffect, useRef, useState } from "react";
import { baseUrl } from "@/apis";
import { PlaygroundContext } from "@/stores/playground";

export function LogView() {
  const useStore = useContext(PlaygroundContext)!;
  const logs = useRef([] as string[]);
  const [_, setTotal] = useState(0);
  const logContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const es = new EventSource(`${baseUrl}${useStore.getState().id}/@logs`);

    es.onmessage = ({ data }) => {
      logs.current.push(data);
      setTotal(logs.current.length);
    };

    return () => {
      es.close();
    };
  }, []);

  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs.current.length]);

  return (
    <div
      ref={logContainerRef}
      className="flex-1 overflow-y-auto p-2 font-mono text-sm"
      style={{ whiteSpace: "pre-line" }}
    >
      {logs.current.map((log, i) => (
        <Ansi key={i}>{JSON.parse(log).message}</Ansi>
      ))}
    </div>
  );
}
