import { Mo<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, ModalBody } from '@heroui/modal';
import React, { useContext } from 'react';
import { PlaygroundContext } from '../stores/playground';
import ProcessManager from './ProcessManager';

interface ProcessManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProcessManagerModal: React.FC<ProcessManagerModalProps> = ({ isOpen, onClose }) => {
  const playgroundStore = useContext(PlaygroundContext);
  const currentPlaygroundId = playgroundStore?.getState().id;
  const currentPlayground = playgroundStore?.getState().playground;

  return (
    <Modal
      classNames={{
        base: 'max-h-[90vh]',
        body: 'p-0',
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1 px-6 py-4">
          <h2 className="text-xl font-semibold">进程管理</h2>
          <p className="text-sm text-gray-500">管理Chat任务的自定义命令进程</p>
        </ModalHeader>
        <ModalBody className="px-6 pb-6">
          <ProcessManager playgroundId={currentPlaygroundId} />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default ProcessManagerModal;
