import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button, Input } from '@heroui/react';
import { useState, useEffect, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { getDesignProject, type DesignProject } from '@/apis';
import { findMenuItemBySegment } from '@/config/menu';
import { useProjectStore } from '@/stores/project';
import IconChat from '~icons/mdi/chat';
import IconChevronDown from '~icons/mdi/chevron-down';
import IconFileMultiple from '~icons/mdi/file-multiple';
import IconFolder from '~icons/mdi/folder-outline';
import IconSearch from '~icons/mdi/magnify';
import IconMerge from '~icons/mdi/merge';
import IconMonitor from '~icons/mdi/monitor';

export function Breadcrumb() {
  const location = useLocation();
  
  // Never show breadcrumb on the root page
  if (location.pathname === '/') {
    return null;
  }

  const navigate = useNavigate();
  const { currentProject, projects, setCurrent } = useProjectStore();
  const [selectedProjectInChat, setSelectedProjectInChat] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProjectSelectorOpen, setIsProjectSelectorOpen] = useState(false);
  const [designProject, setDesignProject] = useState<DesignProject | null>(null);
  
  // Parse the current path to determine breadcrumb items
  const pathSegments = location.pathname.split('/').filter(Boolean);
  const breadcrumbItems = [];

  // 获取设计项目信息
  useEffect(() => {
    const fetchDesignProject = async () => {
      const firstSegment = pathSegments[0];

      if (['workflow-manager', 'workflow', 'multi-workflow', 'project-preview'].includes(firstSegment) && pathSegments.length > 1) {
        const projectId = pathSegments[1];

        try {
          const data = await getDesignProject({ id: projectId });

          if (data && typeof data === 'object' && 'id' in data) {
            setDesignProject(data as DesignProject);
          }
        } catch (error) {
          console.error('获取设计项目信息失败:', error);
          setDesignProject(null);
        }
      } else {
        setDesignProject(null);
      }
    };

    fetchDesignProject();
  }, [location.pathname]);

  // Check if we're on the root page and if there's a selected project in store or URL
  useEffect(() => {
    const updateSelectedProject = () => {
      // Check for selected project in chat context (could be stored in URL params or store)
      const urlParams = new URLSearchParams(location.search);
      const projectFromUrl = urlParams.get('project');

      if (projectFromUrl) {
        setSelectedProjectInChat(projectFromUrl);
      } else {
        // Use store's current project instead of localStorage
        const currentProjectId = currentProject()?.id;

        setSelectedProjectInChat(currentProjectId || null);
      }
    };

    updateSelectedProject();

    // Listen for chat project changes
    const handleChatProjectChange = (event: CustomEvent) => {
      setSelectedProjectInChat(event.detail.projectId);
    };

    window.addEventListener('chatProjectChanged', handleChatProjectChange as EventListener);
    
    return () => {
      window.removeEventListener('chatProjectChanged', handleChatProjectChange as EventListener);
    };
  }, [location, currentProject]);

  // Filter projects based on search query
  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) {
      return projects;
    }

    return projects.filter(project => 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (project.description && project.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [projects, searchQuery]);

  // Handle project selection from breadcrumb
  const handleProjectSelect = (projectId: string) => {
    const selectedProject = projects.find(p => p.id === projectId);

    if (selectedProject) {
      // 使用store管理项目选择，store会自动处理localStorage
      setCurrent(projectId);
      
      // Update local state
      setSelectedProjectInChat(projectId);
      
      // Different behavior based on current route context
      const isOnRootPage = location.pathname === '/';
      const isOnProjectPages = location.pathname.startsWith('/project');
      const isOnPrototypePages = location.pathname.startsWith('/prototype');
      
      if (isOnRootPage) {
        // 原型代码生成: Only update state, no navigation
        // Just update the breadcrumb and chat context
      } else if (isOnProjectPages) {
        // 应用代码生成: Navigate to project preview
        navigate(`/project/${projectId}`);
      } else if (isOnPrototypePages) {
        navigate(`/prototype/${projectId}`);
      }
      
      // Close dropdown and clear search
      setIsProjectSelectorOpen(false);
      setSearchQuery('');
    }
  };

  // Handle different route patterns
  if (pathSegments.length > 0) {
    const firstSegment = pathSegments[0];
    const menuItem = findMenuItemBySegment(firstSegment);
    
    if (menuItem) {
      breadcrumbItems.push({
        key: menuItem.id,
        label: menuItem.label,
        href: menuItem.breadcrumbPath || menuItem.path,
        icon: <menuItem.icon className="w-4 h-4" />,
      });
    }
    
    // Switch case is now simplified, as the main logic is handled above.
    // We still need it for project-specific parts of the breadcrumb.
    switch (firstSegment) {
      case 'project':
        if (pathSegments.length > 1) {
          const projectId = pathSegments[1];

          if (projectId === 'new') {
            // Creating a new project
            breadcrumbItems.push({
              key: 'new-project',
              label: '新建工程',
              href: '/project/new',
              icon: <IconFolder className="w-4 h-4" />
            });
          } else {
            const project = projects.find(p => p.id === projectId) ?? currentProject();
            const subMenuItem = menuItem?.submenu?.find(sub => sub.id === 'project');
            const Icon = subMenuItem ? subMenuItem.icon : IconFolder;

            // We're in a specific project
            if (project) {
              breadcrumbItems.push({
                key: 'current-project',
                label: project.name || '工程',
                href: `/project/${project.id}`,
                icon: <Icon className="w-4 h-4" />,
                hasSelector: true // 支持下拉选择
              });
            }
          }
        }
        break;
      
      case 'prototype':
        if (pathSegments.length > 1) {
          const projectId = pathSegments[1];
          const project = projects.find(p => p.id === projectId) ?? currentProject();
          const subMenuItem = menuItem?.submenu?.find(sub => sub.id === 'prototype');
          const Icon = subMenuItem ? subMenuItem.icon : IconFolder;

          if (project) {
            breadcrumbItems.push({
              key: 'current-prototype',
              label: project.name || '原型',
              href: `/prototype/${project.id}`,
              icon: <Icon className="w-4 h-4" />,
              hasSelector: true,
            });
          }
        }
        break;

      case 'workflow':
        if (pathSegments.length > 1) {
          const projectId = pathSegments[1];
          const project = projects.find(p => p.id === projectId) ?? currentProject();
          
          if (project) {
            // 第一级：文字"项目"，点击跳转到项目列表
            breadcrumbItems.push({
              key: 'project-list',
              label: '项目',
              href: '/project-list',
              icon: <IconFolder className="w-4 h-4" />
            });
            
            // 第二级：当前项目名称，不支持项目切换
            breadcrumbItems.push({
              key: 'current-project',
              label: project.name || '工程',
              href: `/project/${project.id}`,
              icon: <IconMonitor className="w-4 h-4" />
            });
            
            // 第三级：文字"工作流"
            breadcrumbItems.push({
              key: 'workflow',
              label: '工作流',
              href: `/workflow/${projectId}`,
              icon: <IconMerge className="w-4 h-4" />
            });
          }
        }
        break;

      case 'multi-workflow':
        if (pathSegments.length > 1) {
          const projectId = pathSegments[1];
          const project = projects.find(p => p.id === projectId) ?? currentProject();
          
          if (project) {
            // 第一级：文字"项目"，点击跳转到项目列表
            breadcrumbItems.push({
              key: 'project-list',
              label: '项目',
              href: '/project-list',
              icon: <IconFolder className="w-4 h-4" />
            });
            
            // 第二级：当前项目名称，不支持项目切换
            breadcrumbItems.push({
              key: 'current-project',
              label: project.name || '工程',
              href: `/project/${project.id}`,
              icon: <IconMonitor className="w-4 h-4" />
            });
            
            // 第三级：文字"多页工作流"
            breadcrumbItems.push({
              key: 'multi-workflow',
              label: '多页工作流',
              href: `/multi-workflow/${projectId}`,
              icon: <IconFileMultiple className="w-4 h-4" />
            });
          }
        }
        break;

      case 'chat':
        if (pathSegments.length > 1) {
          breadcrumbItems.push({
            key: 'chat',
            label: 'Chat',
            href: `/chat/${pathSegments[1]}`,
            icon: <IconChat className="w-4 h-4" />
          });
        }
        break;
        
      default:
        // Handle other routes generically. If not found by menu config, use segment.
        if (!menuItem) {
          breadcrumbItems.push({
            key: firstSegment,
            label: firstSegment.charAt(0).toUpperCase() + firstSegment.slice(1),
            href: `/${firstSegment}`,
            icon: <IconFolder className="w-4 h-4" />
          });
        }
    }
  } else if (selectedProjectInChat) {
    // We're on the root page but have a selected project in chat
    const selectedProject = projects.find(p => p.id === selectedProjectInChat);

    if (selectedProject) {
      breadcrumbItems.push({
        key: 'prototype',
        label: '项目',
        href: '/',
        icon: <IconFolder className="w-4 h-4" />
      });
      breadcrumbItems.push({
        key: 'selected-project',
        label: selectedProject.name,
        href: '/',
        icon: <IconFolder className="w-4 h-4" />,
        // 项目名称本身是选择器，以保持一致性
        hasSelector: true,
      });
    }
  }

  // Don't show breadcrumb if we're just on the root or have no items
  if (breadcrumbItems.length === 0) {
    return null;
  }

  const renderProjectSelector = (item: any) => {
    return (
      <Dropdown 
        className="min-w-[320px] rounded-md"
        isOpen={isProjectSelectorOpen}
        onOpenChange={setIsProjectSelectorOpen}
      >
        <DropdownTrigger>
          <Button
            className="h-auto p-1 min-w-0 text-default-600 hover:text-default-900 transition-colors"
            endContent={<IconChevronDown className="w-3 h-3 ml-1" />}
            size="sm"
            variant="light"
          >
            <div className="flex items-center gap-1">
              {item.icon}
              <span className="text-sm">{item.label}</span>
            </div>
          </Button>
        </DropdownTrigger>
        <DropdownMenu
          aria-label="项目选择"
          bottomContent={
            searchQuery === '' && (
              <div className="">
                <div 
                  className="flex items-center gap-3 hover:bg-default-100 rounded-lg p-2 cursor-pointer transition-colors"
                  onClick={() => {
                    navigate('/project-list');
                    setIsProjectSelectorOpen(false);
                  }}
                >
                  <IconFolder className="w-4 h-4 text-default-500" />
                  <div className="flex items-center justify-between w-full">
                    <span className="font-medium text-sm">查看所有工程</span>
                    <span className="text-xs text-default-500">View All</span>
                  </div>
                </div>
              </div>
            )
          }
          className="max-w-[320px] min-w-[280px] rounded-sm p-0"
          classNames={{
            base: "max-h-[400px]",
            list: "gap-0 max-h-[250px] overflow-y-auto"
          }}
          itemClasses={{
            base: "data-[hover=true]:bg-default-100 items-start",
            wrapper: "min-w-0",
            title: "font-medium truncate"
          }}
          topContent={
            <div className="p-1">
              <Input
                classNames={{
                  input: "text-sm",
                  inputWrapper: "h-8"
                }}
                placeholder="搜索项目..."
                size="sm"
                startContent={<IconSearch className="w-4 h-4 text-default-400" />}
                value={searchQuery}
                variant="flat"
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          }
          onAction={(key) => {
            if (key === 'view-all') {
              navigate('/project-list');
              setIsProjectSelectorOpen(false);
            } else if (typeof key === 'string' && key !== 'header') {
              handleProjectSelect(key);
            }
          }}
        >
          {filteredProjects.length > 0 ? (
            filteredProjects.map((project) => (
              <DropdownItem
                key={project.id}
                className="py-2"
                description={
                  project.description ? (
                    <div className="text-xs text-default-400 truncate">
                      {project.description}
                    </div>
                  ) : undefined
                }
                startContent={<IconFolder className="w-4 h-4 text-default-500" />}
                title={project.name}
              />
            ))
          ) : (
            <DropdownItem key="no-results" isDisabled>
              <span className="text-sm text-default-400">未找到匹配的项目</span>
            </DropdownItem>
          )}
        </DropdownMenu>
      </Dropdown>
    );
  };

  const renderBreadcrumbItem = (item: any, index: number, isLast: boolean) => {
    // Show project selector for items that have hasSelector flag
    if (item.hasSelector) {
      return renderProjectSelector(item);
    }

    // Regular breadcrumb item - 所有项目都支持点击跳转
    return (
      <div className="flex items-center gap-2">
        {item.icon}
        {isLast ? (
          <span className="font-medium text-default-900 text-sm">{item.label}</span>
        ) : (
          <Link 
            className="hover:text-primary transition-colors text-sm text-default-600" 
            to={item.href}
          >
            {item.label}
          </Link>
        )}
      </div>
    );
  };

  return (
    <div className="flex items-center">
      <div className="flex items-center gap-1">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          
          return (
            <div key={item.key} className="flex items-center">
              {renderBreadcrumbItem(item, index, isLast)}
              {!isLast && (
                <span className="text-default-400 mx-2 text-sm">/</span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}