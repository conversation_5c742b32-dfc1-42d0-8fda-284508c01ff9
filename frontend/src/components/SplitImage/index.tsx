import DefaultLayout from '@/layouts/default';
import { title } from '@/components/primitives';
import ImageSelector from './ImageSelector';

export function Component() {
  return (
    <DefaultLayout>
      <div className="mb-8">
        <section className="flex flex-col items-center justify-center gap-4 py-6">
          <div className="inline-block max-w-lg text-center">
            <h1 className={title({ color: 'violet' })}>图生码</h1>
            <p className="text-default-600 mt-4">上传图片，选择组件区域，AI智能生成代码</p>
          </div>
        </section>

        <div className="mx-auto max-w-[1200px]">
          <ImageSelector />
        </div>
      </div>
    </DefaultLayout>
  );
}
