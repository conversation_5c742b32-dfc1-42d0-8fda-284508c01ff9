import { addToast } from '@heroui/react';
import * as fabric from 'fabric';
import { useRef, useEffect, useImperativeHandle, forwardRef } from 'react';

interface ISplitImageProps {
  imageUrl?: string;
  imageContent?: string;
  isPreview?: boolean;
  initialRectsPosition?: RectData[];
  scale?: number;
  setScale?: (scale: number) => void;
  initialWidth?: number;
  initialScale?: number;
}

// 定义Rect数据结构
export interface RectData {
  position: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  };
  image?: string; // 可选的图片数据（base64格式）
}

// 定义组件暴露的方法接口
export interface SelectAreaRef {
  undo: () => void;
  redo: () => void;
  clearSelection: () => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
  getAllRects: () => RectData[];
}

// 扩展 fabric.Rect 类型以包含自定义属性
interface ExtendedRect extends fabric.Rect {
  originalFill?: string; // 保存原始填充颜色
  isHighlighted?: boolean; // 标记是否处于高亮状态
}

// 历史记录状态类型
interface HistoryState {
  objects: any[]; // 存储canvas对象的JSON数据
}

const SplitImage = forwardRef<SelectAreaRef, ISplitImageProps>((props, ref) => {
  const {
    imageUrl,
    imageContent,
    initialWidth = 375,
    isPreview,
    initialRectsPosition,
    scale,
    setScale,
    initialScale,
  } = props;
  const canvasDomRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const imgObjRef = useRef<fabric.Image | null>(null);
  const isDrawingRef = useRef(false); // 是否正在选区域
  const isTransformingRef = useRef(false); // 是否正在变换（移动/缩放）
  const drawRectRef = useRef<fabric.Rect | null>(null); // 选中的区域
  const startRef = useRef({
    startX: 0,
    startY: 0,
  });
  const normalFill = 'rgba(255,0,0,0.1)';
  const highLightFill = 'rgba(255,0,0,0.4)';

  // 历史记录相关状态
  const historyRef = useRef<HistoryState[]>([]);
  const historyIndexRef = useRef<number>(-1);
  const isRestoringRef = useRef<boolean>(false); // 防止在恢复状态时触发新的历史记录

  // 保存当前状态到历史记录
  const saveState = () => {
    if (!fabricCanvasRef.current || isRestoringRef.current) return;

    // 获取所有矩形对象（排除背景图片）
    const rects = fabricCanvasRef.current.getObjects('rect');
    const state: HistoryState = {
      objects: rects.map((obj) => obj.toObject()),
    };

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (historyIndexRef.current < historyRef.current.length - 1) {
      historyRef.current = historyRef.current.slice(0, historyIndexRef.current + 1);
    }

    // 添加新状态
    historyRef.current.push(state);
    historyIndexRef.current = historyRef.current.length - 1;

    // 限制历史记录数量（最多50条）
    if (historyRef.current.length > 50) {
      historyRef.current.shift();
      historyIndexRef.current--;
    }
  };

  // 恢复到指定状态
  const restoreState = (state: HistoryState) => {
    if (!fabricCanvasRef.current) return;

    isRestoringRef.current = true;

    // 移除所有矩形对象（保留背景图片）
    const rects = fabricCanvasRef.current.getObjects('rect');

    rects.forEach((rect) => fabricCanvasRef.current!.remove(rect));

    // 恢复对象
    state.objects.forEach((objData) => {
      fabric.Rect.fromObject(objData).then((rect) => {
        // 设置矩形的样式属性
        const fabricRect = rect as fabric.Rect;

        fabricRect.set({
          fill: normalFill,
          selectable: !isPreview, // 预览模式下不可选择
          evented: !isPreview, // 预览模式下不响应事件
          hasBorders: false,
          cornerColor: 'red',
          borderColor: 'red',
          transparentCorners: false,
          borderDashArray: [5, 5],
        });

        // 删除旋转控制点
        delete (fabricRect as any).controls.mtr;

        // 初始化扩展属性
        (fabricRect as ExtendedRect).originalFill = fabricRect.fill as string;
        (fabricRect as ExtendedRect).isHighlighted = false;

        fabricCanvasRef.current!.add(fabricRect);
      });
    });

    fabricCanvasRef.current.requestRenderAll();
    isRestoringRef.current = false;
  };

  // 撤销操作
  const undo = () => {
    if (historyIndexRef.current > 0) {
      historyIndexRef.current--;
      restoreState(historyRef.current[historyIndexRef.current]);
    }
  };

  // 重做操作
  const redo = () => {
    if (historyIndexRef.current < historyRef.current.length - 1) {
      historyIndexRef.current++;
      restoreState(historyRef.current[historyIndexRef.current]);
    }
  };

  // 清空选区
  const clearSelection = () => {
    if (!fabricCanvasRef.current) return;

    // 移除所有矩形对象（保留背景图片）
    const rects = fabricCanvasRef.current.getObjects('rect');

    rects.forEach((rect) => fabricCanvasRef.current!.remove(rect));

    fabricCanvasRef.current.requestRenderAll();

    // 保存清空后的状态
    saveState();
  };

  // 检查是否可以撤销
  const canUndo = () => historyIndexRef.current > 0;

  // 检查是否可以重做
  const canRedo = () => historyIndexRef.current < historyRef.current.length - 1;

  // 将图片URL转换为base64
  const convertImageToBase64 = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.crossOrigin = 'anonymous'; // 设置跨域属性

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            reject(new Error('无法获取canvas context'));

            return;
          }

          canvas.width = img.width;
          canvas.height = img.height;

          ctx.drawImage(img, 0, 0);
          const base64 = canvas.toDataURL('image/png');

          resolve(base64);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('图片加载失败'));
      };

      img.src = url;
    });
  };

  // 获取指定矩形区域的图片数据
  const getImageDataFromRect = (left: number, top: number, width: number, height: number): string => {
    if (!imgObjRef.current || !fabricCanvasRef.current) {
      return '';
    }

    try {
      // 使用 Fabric.js 的 toDataURL 方法，指定裁剪区域
      const currentScale = imgObjRef.current.scaleX || 1;
      const scaledLeft = left * currentScale;
      const scaledTop = top * currentScale;
      const scaledWidth = width * currentScale;
      const scaledHeight = height * currentScale;

      // 临时隐藏所有矩形，只显示背景图片
      const rects = fabricCanvasRef.current.getObjects('rect');

      rects.forEach((rect) => rect.set({ visible: false }));
      fabricCanvasRef.current.renderAll();

      // 使用 Fabric.js 的 toDataURL 方法获取指定区域
      const dataURL = fabricCanvasRef.current.toDataURL({
        format: 'png',
        quality: 1.0,
        multiplier: 1,
        left: scaledLeft,
        top: scaledTop,
        width: scaledWidth,
        height: scaledHeight,
      });

      // 恢复矩形的可见性
      rects.forEach((rect) => rect.set({ visible: true }));
      fabricCanvasRef.current.renderAll();

      return dataURL;
    } catch (error) {
      console.error('获取图片数据失败:', error);

      // 确保恢复矩形的可见性
      const rects = fabricCanvasRef.current?.getObjects('rect') || [];

      rects.forEach((rect) => rect.set({ visible: true }));
      fabricCanvasRef.current?.renderAll();

      // 备用方案：返回坐标信息作为文本标识
      return `data:text/plain;base64,${btoa(`rect-${left}-${top}-${width}-${height}`)}`;
    }
  };

  // 获取所有Rect的方法，包含坐标和对应的图片数据
  const getAllRects = (): RectData[] => {
    // 如果画布或图片对象不存在，返回空数组
    if (!fabricCanvasRef.current || !imgObjRef.current) {
      return [];
    }

    // 获取所有矩形对象（排除背景图片）
    const rects = fabricCanvasRef.current.getObjects('rect') as fabric.Rect[];

    // 如果没有矩形，返回一个覆盖原始图片的坐标
    if (rects.length === 0) {
      return [
        {
          position: {
            left: 0,
            top: 0,
            right: imgObjRef.current.width,
            bottom: imgObjRef.current.height,
          },
        },
      ];
    }

    // 获取当前图片的缩放比例
    const currentScale = imgObjRef.current.scaleX || 1;

    return rects.map((rect) => {
      const { left = 0, top = 0, width = 0, height = 0, scaleX = 1, scaleY = 1 } = rect;

      // 计算矩形的实际尺寸（考虑矩形自身的缩放）
      const actualWidth = width * scaleX;
      const actualHeight = height * scaleY;

      // 将画布坐标转换为相对于原始图片大小的坐标
      const originalLeft = left / currentScale;
      const originalTop = top / currentScale;
      const originalWidth = actualWidth / currentScale;
      const originalHeight = actualHeight / currentScale;

      // 计算整数坐标
      const rectLeft = Math.round(originalLeft);
      const rectTop = Math.round(originalTop);
      const rectWidth = Math.round(originalWidth);
      const rectHeight = Math.round(originalHeight);

      // 获取矩形区域对应的图片数据
      const imageData = getImageDataFromRect(rectLeft, rectTop, rectWidth, rectHeight);

      return {
        position: {
          left: rectLeft,
          top: rectTop,
          right: rectLeft + rectWidth,
          bottom: rectTop + rectHeight,
        },
        image: imageData,
      };
    });
  };

  // 创建预设的矩形区域
  const createInitialRects = () => {
    if (!fabricCanvasRef.current || !initialRectsPosition || initialRectsPosition.length === 0) {
      return;
    }

    // 获取当前图片的缩放比例，用于将原始图片坐标转换为画布坐标
    const currentScale = imgObjRef.current?.scaleX || 1;

    initialRectsPosition.forEach((rectData) => {
      const { left, top, right, bottom } = rectData.position;

      // 将原始图片坐标转换为画布坐标
      const canvasLeft = left * currentScale;
      const canvasTop = top * currentScale;
      const canvasRight = right * currentScale;
      const canvasBottom = bottom * currentScale;

      const canvasWidth = canvasRight - canvasLeft;
      const canvasHeight = canvasBottom - canvasTop;

      // 创建矩形对象
      const rect = new fabric.Rect({
        left: canvasLeft,
        top: canvasTop,
        width: canvasWidth,
        height: canvasHeight,
        fill: normalFill,
        selectable: !isPreview, // 预览模式下不可选择
        evented: !isPreview, // 预览模式下不响应事件
        hasBorders: false,
        cornerColor: 'red',
        borderColor: 'red',
        transparentCorners: false,
        borderDashArray: [5, 5],
      });

      // 删除旋转控制点
      delete (rect as any).controls.mtr;

      // 初始化扩展属性
      (rect as ExtendedRect).originalFill = rect.fill as string;
      (rect as ExtendedRect).isHighlighted = false;

      // 添加到画布
      fabricCanvasRef.current!.add(rect);
    });

    fabricCanvasRef.current.requestRenderAll();
  };

  // 使用 useImperativeHandle 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    undo,
    redo,
    clearSelection,
    canUndo,
    canRedo,
    getAllRects,
  }));

  const findActiveObject = (pointer: fabric.Point) => {
    if (!fabricCanvasRef.current) {
      return;
    }
    const objects = fabricCanvasRef.current.getObjects('rect');
    const len = objects.length;

    for (let i = 0; i < len; i++) {
      const rectItem = objects[i];
      const pointerX = pointer.x;
      const pointerY = pointer.y;
      const { left, top, width, height } = rectItem;

      if (pointerX >= left && pointerX <= left + width && pointerY >= top && pointerY <= top + height) {
        return rectItem;
      }
    }
  };

  const handleMouseDown = (event: any) => {
    if (!fabricCanvasRef.current || !imgObjRef.current) {
      return;
    }

    const pointer = fabricCanvasRef.current.getScenePoint(event);

    if (
      !pointer ||
      pointer.x < 0 ||
      pointer.y < 0 ||
      pointer.x > imgObjRef.current?.getScaledWidth() ||
      pointer.y > imgObjRef.current?.getScaledHeight()
    ) {
      return;
    }

    // 检查是否已经有活动对象，如果有，可能是在操作控制点
    const currentActiveObject = fabricCanvasRef.current.getActiveObject();
    const target = fabricCanvasRef.current.findTarget(event.e);

    if (currentActiveObject && target === currentActiveObject) {
      // 如果点击的是当前活动对象，直接返回，保持选中状态
      // 这样可以避免在点击控制点或对象本身时失去焦点
      return;
    }

    // 如果正在进行变换操作且点击的不是当前活动对象，先重置变换状态
    if (isTransformingRef.current) {
      isTransformingRef.current = false;
    }

    const activeObject = findActiveObject(pointer);

    if (activeObject) {
      // 如果点击的是已存在的对象，设置为可选择和可移动
      delete activeObject.controls.mtr; // 删除旋转控制点
      activeObject.set({
        cornerColor: 'red', // 设置控制点颜色
        hasBorders: false, // 隐藏默认边框，避免双虚线
        transparentCorners: false,
        borderDashArray: [5, 5], // 设置边框为虚线
      });
      drawRectRef.current = activeObject as fabric.Rect;
      fabricCanvasRef.current.setActiveObject(activeObject);
      fabricCanvasRef.current.requestRenderAll();

      return;
    }

    // 如果当前有选中的对象但没有点击到任何对象，则取消选择
    if (fabricCanvasRef.current.getActiveObject()) {
      fabricCanvasRef.current.discardActiveObject();
      fabricCanvasRef.current.requestRenderAll();
    }

    const startX = pointer.x;
    const startY = pointer.y;

    isDrawingRef.current = true;
    startRef.current = {
      startX,
      startY,
    };
    drawRectRef.current = new fabric.Rect({
      left: startX,
      top: startY,
      width: 1,
      height: 1,
      fill: normalFill,
      selectable: !isPreview, // 预览模式下不可选择
      evented: !isPreview, // 预览模式下不响应事件
      // 添加控制点显示属性
      hasBorders: false, // 隐藏默认边框，避免双虚线
      cornerColor: 'red', // 设置控制点颜色
      borderColor: 'red', // 设置控制边框的颜色
      transparentCorners: false,
      borderDashArray: [5, 5], // 设置边框为虚线
    });
    fabricCanvasRef.current.add(drawRectRef.current);
  };

  const handleMouseMove = (event: any) => {
    if (!isDrawingRef.current || !drawRectRef.current || !fabricCanvasRef.current || !imgObjRef.current) {
      return;
    }
    const { startX, startY } = startRef.current;

    const pointer = fabricCanvasRef.current.getScenePoint(event);
    let { x, y } = pointer;

    if (x < 0) {
      x = 0;
    }
    if (x > imgObjRef.current?.getScaledWidth()) {
      x = imgObjRef.current?.getScaledWidth();
    }
    if (y < 0) {
      y = 0;
    }
    if (y > imgObjRef.current?.getScaledHeight()) {
      y = imgObjRef.current?.getScaledHeight();
    }

    const w = x - startX;
    const h = y - startY;

    drawRectRef.current.set({
      width: Math.abs(w),
      height: Math.abs(h),
      left: w < 0 ? x : startX,
      top: h < 0 ? y : startY,
    });

    // 更新坐标（绘制新矩形时不需要检查重叠高亮）
    drawRectRef.current.setCoords();

    fabricCanvasRef.current.requestRenderAll();
  };

  const handleMouseUp = () => {
    if (!isDrawingRef.current || !drawRectRef.current || !fabricCanvasRef.current) {
      return;
    }
    isDrawingRef.current = false;

    // 框太小不保留
    const minWidth = 5;

    if (drawRectRef.current.width < minWidth || drawRectRef.current.height < minWidth) {
      fabricCanvasRef.current.remove(drawRectRef.current);
      fabricCanvasRef.current.requestRenderAll();
      drawRectRef.current = null;

      return;
    }

    // 删除旋转控制点并设置为可移动
    delete drawRectRef.current.controls.mtr;

    // 初始化扩展属性
    const extendedRect = drawRectRef.current as ExtendedRect;

    extendedRect.originalFill = extendedRect.fill as string;
    extendedRect.isHighlighted = false;

    // 处理新绘制元素的重叠逻辑
    handleOverlapProcessing(extendedRect);

    // 检查元素是否还存在（可能在重叠处理中被删除）
    if (fabricCanvasRef.current.getObjects().includes(extendedRect)) {
      // 框选完成后不显示选中状态，取消选择
      fabricCanvasRef.current.discardActiveObject();
      fabricCanvasRef.current.requestRenderAll();
    }

    // 保存状态到历史记录
    saveState();

    drawRectRef.current = null;
  };

  // 边界约束函数 - 根据操作类型处理边界约束
  function applyBoundaryConstraints(activeObject: ExtendedRect, isScaling: boolean = false) {
    if (!activeObject || !imgObjRef.current) return;

    const canvasWidth = imgObjRef.current.getScaledWidth();
    const canvasHeight = imgObjRef.current.getScaledHeight();

    const { left = 0, top = 0, width = 0, height = 0, scaleX = 1, scaleY = 1 } = activeObject;
    const scaledWidth = width * scaleX;
    const scaledHeight = height * scaleY;

    let newLeft = left;
    let newTop = top;
    let newScaleX = scaleX;
    let newScaleY = scaleY;
    let needsUpdate = false;

    if (isScaling) {
      // 缩放操作：需要同时约束位置和尺寸
      // 水平边界检查和约束
      if (left < 0) {
        // 左边界超出：调整位置到边界，同时缩小宽度
        const overflow = -left;

        newLeft = 0;
        // 计算新的缩放比例，确保右边界不变
        const newScaledWidth = scaledWidth - overflow;

        if (newScaledWidth > 5) {
          // 保持最小宽度
          newScaleX = newScaledWidth / width;
        } else {
          newScaleX = 5 / width;
          newLeft = left + scaledWidth - 5; // 保持右边界位置
        }
        needsUpdate = true;
      } else if (left + scaledWidth > canvasWidth) {
        // 右边界超出：只调整尺寸，保持左边界位置不变
        const maxScaledWidth = canvasWidth - left;

        if (maxScaledWidth > 5) {
          // 保持最小宽度
          newScaleX = maxScaledWidth / width;
        } else {
          newScaleX = 5 / width;
          newLeft = canvasWidth - 5; // 调整位置以保持最小宽度
        }
        needsUpdate = true;
      }

      // 垂直边界检查和约束
      if (top < 0) {
        // 上边界超出：调整位置到边界，同时缩小高度
        const overflow = -top;

        newTop = 0;
        // 计算新的缩放比例，确保下边界不变
        const newScaledHeight = scaledHeight - overflow;

        if (newScaledHeight > 5) {
          // 保持最小高度
          newScaleY = newScaledHeight / height;
        } else {
          newScaleY = 5 / height;
          newTop = top + scaledHeight - 5; // 保持下边界位置
        }
        needsUpdate = true;
      } else if (top + scaledHeight > canvasHeight) {
        // 下边界超出：只调整尺寸，保持上边界位置不变
        const maxScaledHeight = canvasHeight - top;

        if (maxScaledHeight > 5) {
          // 保持最小高度
          newScaleY = maxScaledHeight / height;
        } else {
          newScaleY = 5 / height;
          newTop = canvasHeight - 5; // 调整位置以保持最小高度
        }
        needsUpdate = true;
      }
    } else {
      // 移动操作：只约束位置，保持元素大小不变
      // 水平边界检查
      if (left < 0) {
        newLeft = 0;
        needsUpdate = true;
      } else if (left + scaledWidth > canvasWidth) {
        newLeft = canvasWidth - scaledWidth;
        needsUpdate = true;
      }

      // 垂直边界检查
      if (top < 0) {
        newTop = 0;
        needsUpdate = true;
      } else if (top + scaledHeight > canvasHeight) {
        newTop = canvasHeight - scaledHeight;
        needsUpdate = true;
      }
    }

    // 应用边界约束
    if (needsUpdate) {
      if (isScaling) {
        activeObject.set({
          left: newLeft,
          top: newTop,
          scaleX: newScaleX,
          scaleY: newScaleY,
        });
      } else {
        activeObject.set({
          left: newLeft,
          top: newTop,
        });
      }
      activeObject.setCoords();
    }
  }

  // 设置元素高亮状态
  function setElementHighlight(element: ExtendedRect, highlight: boolean) {
    if (!element) return;

    if (highlight && !element.isHighlighted) {
      // 保存原始颜色并设置高亮
      element.originalFill = element.fill as string;
      element.set({
        fill: highLightFill, // 橙色高亮
      });
      element.isHighlighted = true;
    } else if (!highlight && element.isHighlighted) {
      // 恢复原始颜色
      element.set({
        fill: element.originalFill || normalFill,
      });
      element.isHighlighted = false;
    }
  }

  // 检查两个矩形是否真正重叠（不包括相邻情况）
  function hasRealOverlap(rectA: fabric.Rect, rectB: fabric.Rect): boolean {
    const aLeft = rectA.left || 0;
    const aTop = rectA.top || 0;
    const aWidth = rectA.getScaledWidth();
    const aHeight = rectA.getScaledHeight();
    const aRight = aLeft + aWidth;
    const aBottom = aTop + aHeight;

    const bLeft = rectB.left || 0;
    const bTop = rectB.top || 0;
    const bWidth = rectB.getScaledWidth();
    const bHeight = rectB.getScaledHeight();
    const bRight = bLeft + bWidth;
    const bBottom = bTop + bHeight;

    // 使用小的容差值来处理浮点数精度问题
    const tolerance = 0.1;

    // 检查是否只是边界相接（相邻但不重叠）
    const isAdjacentHorizontally = Math.abs(aRight - bLeft) <= tolerance || Math.abs(aLeft - bRight) <= tolerance;
    const isAdjacentVertically = Math.abs(aBottom - bTop) <= tolerance || Math.abs(aTop - bBottom) <= tolerance;

    // 如果水平或垂直方向上只是相邻，则不算重叠
    if (isAdjacentHorizontally || isAdjacentVertically) {
      return false;
    }

    // 检查是否有真正的重叠（内部区域有交集）
    const horizontalOverlap = aLeft < bRight - tolerance && aRight > bLeft + tolerance;
    const verticalOverlap = aTop < bBottom - tolerance && aBottom > bTop + tolerance;

    return horizontalOverlap && verticalOverlap;
  }

  // 检查当前元素是否与其他元素重叠并设置高亮
  function checkAndHighlightOverlap(activeObject: ExtendedRect) {
    if (!fabricCanvasRef.current || !activeObject) return;

    const otherObjects = fabricCanvasRef.current
      .getObjects('rect')
      .filter((obj) => obj !== activeObject) as ExtendedRect[];

    // 检查是否有真正的重叠（不包括相邻情况）
    const hasOverlap = otherObjects.some((obj) => hasRealOverlap(activeObject, obj));

    // 设置高亮状态
    setElementHighlight(activeObject, hasOverlap);

    fabricCanvasRef.current.requestRenderAll();
  }

  // 检查矩形A是否完全在矩形B内部
  function isCompletelyInside(rectA: fabric.Rect, rectB: fabric.Rect): boolean {
    const aLeft = rectA.left || 0;
    const aTop = rectA.top || 0;
    const aWidth = rectA.getScaledWidth();
    const aHeight = rectA.getScaledHeight();
    const aRight = aLeft + aWidth;
    const aBottom = aTop + aHeight;

    const bLeft = rectB.left || 0;
    const bTop = rectB.top || 0;
    const bWidth = rectB.getScaledWidth();
    const bHeight = rectB.getScaledHeight();
    const bRight = bLeft + bWidth;
    const bBottom = bTop + bHeight;

    // A完全在B内部的条件：A的所有边界都在B的内部
    return aLeft >= bLeft && aTop >= bTop && aRight <= bRight && aBottom <= bBottom;
  }

  // 处理元素重叠逻辑
  function handleOverlapProcessing(activeObject: ExtendedRect) {
    if (!fabricCanvasRef.current || !activeObject) return;

    const otherObjects = fabricCanvasRef.current
      .getObjects('rect')
      .filter((obj) => obj !== activeObject) as ExtendedRect[];

    for (const otherObj of otherObjects) {
      // 检查当前元素是否完全在其他元素内部
      if (isCompletelyInside(activeObject, otherObj)) {
        // 如果完全在内部，移除当前元素
        fabricCanvasRef.current.remove(activeObject);
        fabricCanvasRef.current.requestRenderAll();

        // 清空当前选中的对象引用
        if (drawRectRef.current === activeObject) {
          drawRectRef.current = null;
        }

        // 保存状态到历史记录
        saveState();

        return;
      }

      // 检查是否有真正的部分重叠
      if (hasRealOverlap(activeObject, otherObj)) {
        // 处理部分重叠 - 从当前元素中移除重叠部分
        handlePartialOverlap(activeObject, otherObj);
      }
    }

    // 重叠处理完成后，确保当前对象和所有其他对象都使用正常填充色
    if (fabricCanvasRef.current.getObjects().includes(activeObject)) {
      setElementHighlight(activeObject, false);
    }

    // 清除所有其他对象的高亮状态
    otherObjects.forEach((obj) => {
      setElementHighlight(obj, false);
    });
  }

  // 处理部分重叠 - 去除重叠部分，优先保留右侧和下侧部分，确保元素相邻不重叠
  function handlePartialOverlap(activeObject: ExtendedRect, otherObject: ExtendedRect) {
    if (!fabricCanvasRef.current) return;

    const activeLeft = activeObject.left || 0;
    const activeTop = activeObject.top || 0;
    const activeWidth = activeObject.getScaledWidth();
    const activeHeight = activeObject.getScaledHeight();
    const activeRight = activeLeft + activeWidth;
    const activeBottom = activeTop + activeHeight;

    const otherLeft = otherObject.left || 0;
    const otherTop = otherObject.top || 0;
    const otherWidth = otherObject.getScaledWidth();
    const otherHeight = otherObject.getScaledHeight();
    const otherRight = otherLeft + otherWidth;
    const otherBottom = otherTop + otherHeight;

    // 计算重叠区域
    const overlapLeft = Math.max(activeLeft, otherLeft);
    const overlapTop = Math.max(activeTop, otherTop);
    const overlapRight = Math.min(activeRight, otherRight);
    const overlapBottom = Math.min(activeBottom, otherBottom);

    // 如果没有重叠，直接返回
    if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) {
      return;
    }

    // 计算可能的保留区域，确保与其他对象相邻但不重叠
    const rightWidth = activeRight - otherRight; // 右侧剩余宽度（紧贴其他对象右边界）
    const bottomHeight = activeBottom - otherBottom; // 下侧剩余高度（紧贴其他对象下边界）
    const leftWidth = otherLeft - activeLeft; // 左侧剩余宽度（紧贴其他对象左边界）
    const topHeight = otherTop - activeTop; // 上侧剩余高度（紧贴其他对象上边界）

    // 优先级：右侧 > 下侧 > 左侧 > 上侧
    // 选择最大的可保留区域
    type OverlapType = 'right' | 'bottom' | 'left' | 'top';
    const options: Array<{ type: OverlapType; size: number; width: number; height: number }> = [
      { type: 'right', size: rightWidth * activeHeight, width: rightWidth, height: activeHeight },
      { type: 'bottom', size: activeWidth * bottomHeight, width: activeWidth, height: bottomHeight },
      { type: 'left', size: leftWidth * activeHeight, width: leftWidth, height: activeHeight },
      { type: 'top', size: activeWidth * topHeight, width: activeWidth, height: topHeight },
    ];

    // 过滤掉太小的区域（宽度或高度小于5）
    const validOptions = options.filter((option) => option.width > 5 && option.height > 5);

    if (validOptions.length === 0) {
      // 如果没有有效的保留区域，删除整个对象
      fabricCanvasRef.current.remove(activeObject);
      fabricCanvasRef.current.requestRenderAll();

      // 清空当前选中的对象引用
      if (drawRectRef.current === activeObject) {
        drawRectRef.current = null;
      }

      return;
    }

    // 选择面积最大的区域，如果面积相同则按优先级选择
    const bestOption = validOptions.reduce((best, current) => {
      if (current.size > best.size) return current;
      if (current.size === best.size) {
        // 面积相同时按优先级选择：右侧 > 下侧 > 左侧 > 上侧
        const priority: Record<OverlapType, number> = { right: 4, bottom: 3, left: 2, top: 1 };

        return priority[current.type] > priority[best.type] ? current : best;
      }

      return best;
    });

    // 根据选择的区域调整对象，确保与其他对象相邻但不重叠
    let newLeft = activeLeft;
    let newTop = activeTop;
    let newWidth = bestOption.width;
    let newHeight = bestOption.height;

    switch (bestOption.type) {
      case 'right':
        // 保留右侧部分：左边界紧贴其他对象的右边界
        newLeft = otherRight;
        newTop = activeTop;
        newWidth = activeRight - otherRight;
        newHeight = activeHeight;
        break;
      case 'bottom':
        // 保留下侧部分：上边界紧贴其他对象的下边界
        newLeft = activeLeft;
        newTop = otherBottom;
        newWidth = activeWidth;
        newHeight = activeBottom - otherBottom;
        break;
      case 'left':
        // 保留左侧部分：右边界紧贴其他对象的左边界
        newLeft = activeLeft;
        newTop = activeTop;
        newWidth = otherLeft - activeLeft;
        newHeight = activeHeight;
        break;
      case 'top':
        // 保留上侧部分：下边界紧贴其他对象的上边界
        newLeft = activeLeft;
        newTop = activeTop;
        newWidth = activeWidth;
        newHeight = otherTop - activeTop;
        break;
    }

    // 调整对象的位置和大小
    activeObject.set({
      left: newLeft,
      top: newTop,
      width: newWidth / (activeObject.scaleX || 1), // 考虑缩放因子
      height: newHeight / (activeObject.scaleY || 1), // 考虑缩放因子
    });
    activeObject.setCoords();

    // 重叠处理完成后，确保所有对象都使用正常填充色
    setElementHighlight(activeObject, false);
    setElementHighlight(otherObject, false);

    fabricCanvasRef.current.requestRenderAll();
  }

  // 绑定fabric事件
  const bindFabricEvents = () => {
    if (!fabricCanvasRef.current) {
      return;
    }
    // 鼠标按下开始画框
    fabricCanvasRef.current.on('mouse:down', handleMouseDown);
    // 鼠标移动调整框大小
    fabricCanvasRef.current.on('mouse:move', handleMouseMove);
    // 鼠标松开完成框选
    fabricCanvasRef.current.on('mouse:up', handleMouseUp);

    // 在对象修改完成后处理重叠逻辑并重置变换状态
    fabricCanvasRef.current.on('object:modified', (e) => {
      const obj = e.target as ExtendedRect;

      // 处理重叠逻辑
      handleOverlapProcessing(obj);

      // 重置变换状态
      isTransformingRef.current = false;

      // 保存状态到历史记录
      saveState();
    });

    // 当对象被选中时，确保删除旋转控制点并初始化扩展属性
    fabricCanvasRef.current.on('selection:created', (e) => {
      const activeObject = e.selected?.[0] as ExtendedRect;

      if (activeObject && activeObject.type === 'rect') {
        delete activeObject.controls.mtr; // 删除旋转控制点

        // 初始化扩展属性（如果还没有的话）
        if (activeObject.originalFill === undefined) {
          activeObject.originalFill = activeObject.fill as string;
          activeObject.isHighlighted = false;
        }

        fabricCanvasRef.current?.requestRenderAll();
      }
    });

    // 添加对象移动事件监听
    fabricCanvasRef.current.on('object:moving', (e) => {
      isTransformingRef.current = true;

      const activeObject = e.target as ExtendedRect;

      if (activeObject) {
        // 应用边界约束 - 移动操作，只约束位置
        applyBoundaryConstraints(activeObject, false);

        // 检查并设置重叠高亮
        checkAndHighlightOverlap(activeObject);
      }
    });

    // 添加对象缩放事件监听
    fabricCanvasRef.current.on('object:scaling', (e) => {
      isTransformingRef.current = true;

      const activeObject = e.target as ExtendedRect;

      if (activeObject) {
        // 应用边界约束 - 缩放操作，同时约束位置和尺寸
        applyBoundaryConstraints(activeObject, true);

        // 检查并设置重叠高亮
        checkAndHighlightOverlap(activeObject);
      }
    });

    // 添加鼠标按下事件，检测是否开始变换
    fabricCanvasRef.current.on('mouse:down:before', (e) => {
      const activeObject = fabricCanvasRef.current?.getActiveObject();

      if (activeObject) {
        const target = fabricCanvasRef.current?.findTarget(e.e);

        if (target === activeObject) {
          // 如果点击的是活动对象，可能要开始变换
          isTransformingRef.current = true;
        }
      }
    });

    // 添加选择清除事件监听，重置变换状态
    fabricCanvasRef.current.on('selection:cleared', () => {
      isTransformingRef.current = false;
    });

    // 添加鼠标抬起事件监听，确保变换状态重置
    fabricCanvasRef.current.on('mouse:up', () => {
      // 延迟重置，确保 object:modified 事件先触发
      setTimeout(() => {
        if (!fabricCanvasRef.current?.getActiveObject()) {
          isTransformingRef.current = false;
        }
      }, 10);
    });

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);
  };

  const initFabric = () => {
    if (!canvasDomRef.current) {
      return;
    }

    /**
       * 初始化canvas，Fabric.js 使用双层 canvas 结构：
          - 底层 canvas (fabric-pdf-canvas)：渲染静态内容（PDF、图片等）
          - 上层 canvas (upper-canvas)：处理交互和动态内容（如标注框）
      */
    fabricCanvasRef.current = new fabric.Canvas(canvasDomRef.current, {
      selection: false, // 启用选择功能
      preserveObjectStacking: true,
    });

    // 重写边框绘制方法，禁用默认边框渲染
    fabric.FabricObject.prototype.drawBorders = function () {
      // 不调用原始的边框绘制方法，边框将在 after:render 事件中统一处理
      return this;
    };

    // 添加Canvas后期渲染事件，在所有对象渲染完成后绘制固定边框
    fabricCanvasRef.current.on('after:render', () => {
      const canvas = fabricCanvasRef.current;

      if (!canvas) return;

      const ctx = canvas.getContext();
      const vpt = canvas.viewportTransform;

      if (!vpt) return;

      // 获取所有矩形对象
      const rects = canvas.getObjects('rect') as fabric.Rect[];

      rects.forEach((rect) => {
        if (rect.borderColor && rect.borderDashArray) {
          ctx.save();

          // 使用元素的实际位置和尺寸，而不是边界框
          const rectLeft = rect.left || 0;
          const rectTop = rect.top || 0;
          const rectWidth = rect.getScaledWidth();
          const rectHeight = rect.getScaledHeight();

          // 手动计算视口变换后的坐标
          // vpt = [scaleX, skewY, skewX, scaleY, translateX, translateY]
          const left = rectLeft * vpt[0] + rectTop * vpt[2] + vpt[4];
          const top = rectLeft * vpt[1] + rectTop * vpt[3] + vpt[5];
          const width = rectWidth * vpt[0];
          const height = rectHeight * vpt[3];

          // 设置固定的边框样式（不受缩放影响）
          ctx.strokeStyle = rect.borderColor as string;
          ctx.lineWidth = 2; // 固定2像素线宽
          ctx.setLineDash([5, 5]); // 固定5像素虚线和间隙

          // 在画布坐标系中绘制边框，确保边框紧贴元素边缘
          ctx.strokeRect(left, top, width, height);

          ctx.restore();
        }
      });
    });

    isDrawingRef.current = false;
    drawRectRef.current = null;
  };

  const handleFabric = async () => {
    initFabric();

    try {
      // 先将图片URL转换为base64，避免CORS问题
      let base64Image = '';

      if (imageContent) {
        base64Image = imageContent;
      } else if (imageUrl) {
        base64Image = await convertImageToBase64(imageUrl);
      } else {
        return;
      }

      const fabricImage: fabric.FabricImage = await fabric.FabricImage.fromURL(base64Image);
      const scaleValue = initialScale ? initialScale : initialWidth / fabricImage.width;

      if (setScale) {
        setScale(scaleValue);
      }
      fabricImage.set({
        left: 0,
        top: 0,
        selectable: false, //  禁止选择
        lockMovementX: true, // 锁定水平移动
        lockMovementY: true, // 锁定垂直移动
        evented: false, // 禁止所有交互事件
        scaleX: scaleValue,
        scaleY: scaleValue,
      });
      imgObjRef.current = fabricImage;

      fabricCanvasRef.current?.setDimensions({
        width: fabricImage.width * scaleValue,
        height: fabricImage.height * scaleValue,
      });
      fabricCanvasRef.current?.add(fabricImage);

      // console.log('-------------initialRectsPosition--------------');
      // console.log(initialRectsPosition);
      // 创建预设的矩形区域
      createInitialRects();

      if (isPreview) {
        return;
      }
      // 绑定事件
      bindFabricEvents();

      // 保存初始状态（包含预设矩形）
      saveState();
    } catch (error) {
      // console.log('----------------error--------------');
      // console.log(error);
      addToast({
        color: 'danger',
        title: '图片加载失败',
      });
    }
  };

  // 添加键盘事件处理函数
  function handleKeyDown(e: KeyboardEvent) {
    if (!fabricCanvasRef.current) return;

    // 获取当前选中的对象
    const activeObject = fabricCanvasRef.current.getActiveObject() as ExtendedRect;

    // 如果有选中对象且按下了删除键（Delete 或 Backspace）
    if (activeObject && (e.key === 'Delete' || e.key === 'Backspace')) {
      // 从画布中移除对象
      fabricCanvasRef.current.remove(activeObject);
      fabricCanvasRef.current.requestRenderAll();

      // 清空当前选中的对象引用
      if (drawRectRef.current === activeObject) {
        drawRectRef.current = null;
      }

      // 保存状态到历史记录
      saveState();
    }
  }

  // 监听 rectPosition 变化，重新创建矩形
  useEffect(() => {
    if (fabricCanvasRef.current && imgObjRef.current) {
      // 清除现有的矩形
      const existingRects = fabricCanvasRef.current.getObjects('rect');

      existingRects.forEach((rect) => fabricCanvasRef.current!.remove(rect));

      // 创建新的预设矩形
      createInitialRects();

      // 保存状态
      if (!isPreview) {
        saveState();
      }
    }
  }, [initialRectsPosition]);

  useEffect(() => {
    if (!imageUrl && !imageContent) {
      return;
    }
    handleFabric();

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      // 清理 fabric canvas 实例
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, [imageUrl, imageContent]);

  useEffect(() => {
    if (imgObjRef.current && fabricCanvasRef.current && scale) {
      // 获取当前的缩放比例，用于计算矩形的相对位置
      const currentScale = imgObjRef.current.scaleX || 1;
      const scaleRatio = scale / currentScale;

      // 更新图片缩放
      imgObjRef.current.set({
        scaleX: scale,
        scaleY: scale,
      });

      // 更新画布尺寸
      const originalWidth = imgObjRef.current.width || 0;
      const originalHeight = imgObjRef.current.height || 0;

      fabricCanvasRef.current.setDimensions({
        width: originalWidth * scale,
        height: originalHeight * scale,
      });

      // 更新所有矩形的位置和大小以匹配新的缩放比例
      const objects = fabricCanvasRef.current.getObjects();

      objects.forEach((obj) => {
        if (obj !== imgObjRef.current && obj.type === 'rect') {
          const rect = obj as fabric.Rect;
          const currentLeft = rect.left || 0;
          const currentTop = rect.top || 0;
          const currentWidth = rect.width || 0;
          const currentHeight = rect.height || 0;
          const currentScaleX = rect.scaleX || 1;
          const currentScaleY = rect.scaleY || 1;

          // 计算新的位置和大小
          const newLeft = currentLeft * scaleRatio;
          const newTop = currentTop * scaleRatio;
          const newWidth = currentWidth * scaleRatio;
          const newHeight = currentHeight * scaleRatio;

          // 更新矩形属性
          rect.set({
            left: newLeft,
            top: newTop,
            width: newWidth,
            height: newHeight,
            // 保持矩形自身的缩放比例不变
            scaleX: currentScaleX,
            scaleY: currentScaleY,
          });

          // 更新坐标系统
          rect.setCoords();
        }
      });

      fabricCanvasRef.current.requestRenderAll();
    }
  }, [scale]);

  return <canvas ref={canvasDomRef} />;
});

export default SplitImage;
