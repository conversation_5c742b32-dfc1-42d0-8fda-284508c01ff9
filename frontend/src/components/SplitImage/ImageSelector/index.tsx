import { Button } from '@heroui/button';
import { Card, CardBody } from '@heroui/card';
import { useFileUpload } from '@/hooks/fileUpload';
import { useRef, useState, useEffect } from 'react';
import IconUndo from '~icons/mdi/undo';
import IconRedo from '~icons/mdi/redo';
import IconDelete from '~icons/mdi/delete';
import IconUpload from '~icons/mdi/upload';
import SelectImageSplitArea, { SelectAreaRef } from './SelectImageSplitArea';

function ImageSelector() {
  const { iptRef, onFileChange, files } = useFileUpload();
  const image = files[0];
  const selectAreaRef = useRef<SelectAreaRef>(null);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [scale, setScale] = useState(1);
  // 缩放相关常量
  const MIN_ZOOM = 0.1; // 最小缩放10%
  const MAX_ZOOM = 2; // 最大缩放500%
  const ZOOM_STEP = 0.1; // 缩放步长10%

  // 定期检查撤销重做状态
  useEffect(() => {
    const checkUndoRedoState = () => {
      if (selectAreaRef.current) {
        setCanUndo(selectAreaRef.current.canUndo());
        setCanRedo(selectAreaRef.current.canRedo());
      }
    };

    const interval = setInterval(checkUndoRedoState, 100);
    return () => clearInterval(interval);
  }, [image]);

  const handleUndo = () => {
    selectAreaRef.current?.undo();
  };

  const handleRedo = () => {
    selectAreaRef.current?.redo();
  };

  const handleClearSelection = () => {
    selectAreaRef.current?.clearSelection();
  };

  // 缩放相关功能
  const updateZoom = (newZoom: number) => {
    // 限制缩放范围
    const clampedZoom = Math.max(MIN_ZOOM, Math.min(MAX_ZOOM, newZoom));
    // 四舍五入到小数点后1位，避免浮点数精度问题
    const roundedZoom = Math.round(clampedZoom * 10) / 10;
    setScale(roundedZoom);
  };

  const handZoomOut = () => {
    const newScale = scale - ZOOM_STEP;
    updateZoom(newScale);
  };

  const handleZoomIn = () => {
    const newScale = scale + ZOOM_STEP;
    updateZoom(newScale);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">图片组件选择器</h2>
        {image && (
          <div>
            <Button color="primary" onPress={handZoomOut}>
              -
            </Button>
            {(scale * 100).toFixed(0)}%
            <Button color="primary" onPress={handleZoomIn}>
              +
            </Button>
          </div>
        )}
        {image && (
          <div className="flex gap-2">
            <Button isIconOnly variant="flat" onPress={handleUndo} isDisabled={!canUndo} title="撤销">
              <IconUndo />
            </Button>
            <Button isIconOnly variant="flat" onPress={handleRedo} isDisabled={!canRedo} title="重做">
              <IconRedo />
            </Button>
            <Button isIconOnly variant="flat" onPress={handleClearSelection} title="清空选区">
              <IconDelete />
            </Button>
          </div>
        )}
      </div>
      <Card className="w-full">
        <CardBody>
          {image ? (
            <div className="flex justify-center">
              <SelectImageSplitArea ref={selectAreaRef} imageUrl={image.url} scale={scale} setScale={setScale} />
            </div>
          ) : (
            <div
              className="flex flex-col items-center justify-center p-10 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer"
              onClick={() => iptRef.current?.click()}
            >
              <IconUpload className="w-12 h-12 text-gray-400 mb-4" />
              <p className="text-gray-500">点击上传图片或拖拽图片到此处</p>
              <input ref={iptRef} type="file" accept="image/*" className="hidden" onChange={onFileChange} />
            </div>
          )}
        </CardBody>
      </Card>
      {image && (
        <div className="flex justify-between">
          <Button color="primary" onPress={() => iptRef.current?.click()} startContent={<IconUpload />}>
            更换图片
          </Button>
        </div>
      )}
    </div>
  );
}

export default ImageSelector;
