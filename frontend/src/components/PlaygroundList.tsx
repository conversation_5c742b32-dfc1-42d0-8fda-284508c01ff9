import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@heroui/card";
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/dropdown";
import { Image } from "@heroui/image";
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dalB<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, Mo<PERSON>Header } from "@heroui/react";
import moment from 'moment';
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { tv } from "tailwind-variants";
import { deletePlayground, getChat, listPlaygrounds } from "@/apis";
import placeholder from '@/assets/images/placeholder.png';
import { useUserInfoStore } from '@/hooks/login';
import { type Playground } from "@/stores/playground";
import { confirm } from "@/utils/modal";
import { splitModel } from "@/utils/model";
import IconDots from '~icons/mdi/dots-horizontal';

const cardStyle = tv({
  base: 'p-1 text-zinc-100 dark:text-zinc-200',
  variants: {
    type: {
      react: 'bg-blue-500 dark:bg-blue-600',
      vue: 'bg-green-500 dark:bg-green-600',
      lit: 'bg-amber-500 dark:bg-amber-600',
    }
  },
})

function useFirstMessage(id: string) {
  const [firstMessage, setFirstMessage] = useState('');

  useEffect(() => {
    const load = async () => {
      if (!id) {
        setFirstMessage('');
      } else {
        const ret = await getChat({ id, take: 1, sort: 'asc' });

        setFirstMessage(ret[0].content);
      }
    };

    load();
  }, [id]);

  return { firstMessage };
}

export function PlaygroundList() {
  const { userInfo } = useUserInfoStore();
  const navigate = useNavigate();
  const [playgrounds, setPlaygrounds] = useState<Playground[]>([]);
  const [infoOpen, setInfoOpen] = useState('');

  useEffect(() => {
    const load = async () => {
      const ret = await listPlaygrounds({ user: userInfo?.username });

      setPlaygrounds(ret);
    };

    load();
  }, []);

  const onShowInfo = (id: string) => {
    setInfoOpen(id);
  };
  const onCloseInfo = () => {
    setInfoOpen('');
  };

  const { firstMessage } = useFirstMessage(infoOpen);

  return (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(250px,1fr))] gap-2">
      {playgrounds.map(({ id, name, desc, created, type, model }) => (
        <Card
          key={id}
          isPressable
          classNames={{
            base: cardStyle({ type: type as any })
          }}
          onPress={() => {
            navigate(`/chat/${id}`);
          }}
        >
          <CardHeader className="py-2 px-4 flex-col items-start">
            <div className="text-left w-full">
              <p className="text-tiny uppercase font-bold truncate ellipsis">{name}</p>
              <h4 className="font-bold text-large truncate ellipsis">{desc}</h4>
              <small className="text-zinc-300 dark:text-zinc-400">{moment(created).fromNow()}</small>
            </div>
            <Dropdown>
              <DropdownTrigger>
                <div
                  className="absolute top-2 right-2 hover:bg-zinc-600 dark:hover:bg-zinc-800 rounded-full p-1"
                >
                  <IconDots />
                </div>
              </DropdownTrigger>
              <DropdownMenu aria-label="Actions">
                <DropdownItem key="info" onPress={() => onShowInfo(id)}>
                  信息
                </DropdownItem>
                <DropdownItem key="delete" className="text-danger" color="danger" onPress={async () => {
                  const yes = await confirm({
                    title: '确定删除吗?',
                    message: '删除后无法恢复',
                  });

                  if (yes) {
                    await deletePlayground({ id });
                    setPlaygrounds((prev) => prev.filter(e => e.id !== id));
                  }
                }}>
                  删除
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </CardHeader>
          <CardBody className="overflow-visible p-0">
            <Image
              className="object-cover rounded-xl w-full"
              src={placeholder}
              width="100%"  
            />
          </CardBody>
          <CardFooter className="absolute left-0 right-0 bg-white/30 bottom-0 border-t-1 border-zinc-100/50 z-10 justify-between text-zinc-500">
            {!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::') && (
              <p className="text-left">{splitModel(model).modelName}</p>
            )}
          </CardFooter>
        </Card>
      ))}
      {infoOpen && (
        <Modal isOpen={!!infoOpen} size="lg" onClose={onCloseInfo}>
          <ModalContent>
            {(onClose) => {
              const { name, desc, created, type, model } = playgrounds.find(e => e.id === infoOpen)!;
              const fields: Record<string, string> = {
                '名称': name,
                '描述': desc,
                '创建时间': moment(created).format('YYYY-MM-DD HH:mm:ss'),
                '类型': type,
              };
              
              // 只有当模型不以openrouter::或siliconflow::开头时才显示模型字段
              if (!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::')) {
                fields['模型'] = model;
              }

              return (
                <>
                  <ModalHeader className="flex flex-col gap-1">信息</ModalHeader>
                  <ModalBody>
                    <h1>npm包信息</h1>
                    <dl className="grid gap-4 grid-cols-[60px_1fr] border rounded-lg p-4">
                      {Object.entries(fields).map(([key, value]) => (
                        <>
                          <dt className="text-sm font-medium text-muted-foreground">{key}</dt>
                          <dd className="text-base font-semibold">{value}</dd>
                        </>
                      ))}
                    </dl>
                    <h1 className="mt-2">开发需求</h1>
                    <div className="border rounded-lg p-4">
                      <p>{firstMessage}</p>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <Button color="danger" variant="light" onPress={onClose}>
                      关闭
                    </Button>
                  </ModalFooter>
                </>
              );
            }}
          </ModalContent>
        </Modal>
      )}
    </div>
  );
}
