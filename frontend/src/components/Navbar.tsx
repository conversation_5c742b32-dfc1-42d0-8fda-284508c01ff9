import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Nav<PERSON><PERSON><PERSON>,
  Navbar<PERSON>ontent,
  NavbarItem,
  link as linkStyles,
  Avatar,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Button
} from "@heroui/react";
import clsx from "clsx";
import { Key } from "react";
import { Link } from "react-router-dom";
import Logo from "@/assets/images/logo.svg?react";
import { ThemeSwitch } from "@/components/ThemeSwitch";
import { siteConfig } from "@/config/site";
import { useUserInfoStore } from '@/hooks/login';
import IconAvatarOutline from '~icons/mdi/account-outline';

export const Navbar = () => {
  const { userInfo, logOutUser } = useUserInfoStore();

  const handleClick = (key: Key) => {
    if (key === 'logOut') {
      logOutUser();
    }
  }

  return (
    <HeroUINavbar maxWidth="xl" position="sticky">
      <NavbarContent className="basis-1/5 sm:basis-full" justify="start">
        <NavbarBrand className="gap-3 max-w-fit">
          <Link
            className="flex justify-start items-center gap-1"
            to="/"
          >
            <Logo fontSize={24} />
            <p className="font-bold text-inherit">魔方智能生成</p>
          </Link>
        </NavbarBrand>
        <div className="hidden lg:flex gap-4 justify-start ml-2">
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <Link
                className={clsx(
                  linkStyles({ color: "foreground" }),
                  "data-[active=true]:text-primary data-[active=true]:font-medium"
                )}
                to={item.href}
              >
                {item.label}
              </Link>
            </NavbarItem>
          ))}
        </div>
      </NavbarContent>

      <NavbarContent
        justify="end"
      >
        <NavbarItem className="hidden sm:flex gap-2">
          <ThemeSwitch />
        </NavbarItem>
        <Dropdown>
          <DropdownTrigger>
            <Button className="border-none" variant="bordered">
              <Avatar showFallback className="w-6 h-6" fallback={<IconAvatarOutline />} />
              { userInfo?.displayname || userInfo?.username }
            </Button>
          </DropdownTrigger>
          <DropdownMenu onAction={key => handleClick(key)}>
            <DropdownItem key="logOut" textValue="退出登录">退出登录</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </NavbarContent>
    </HeroUINavbar>
  );
};
