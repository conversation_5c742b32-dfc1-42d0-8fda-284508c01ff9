import { Selection } from "@heroui/react";
import { Select, SelectItem } from "@heroui/select";
import { Tabs, Tab } from "@heroui/tabs";
import { Tooltip } from "@heroui/tooltip";
import { map, isSet } from 'lodash';
import { useContext, useEffect, useState, useCallback } from "react";
import { useStore } from "zustand";
import { ArchiveBlocker } from "./ArchiveBlocker";
import { commitFiles } from "@/apis";
import { Editor } from "@/components/Editor/Editor";
import { LogView } from "@/components/LogView";
import { Preview } from "@/components/Preview";
import { PlaygroundContext } from "@/stores/playground";
import { eventBus } from "@/utils/eventBus";
import IconCodeTags from '~icons/mdi/code-tags';
import IconTextLong from '~icons/mdi/text-long';
import IconViewGallery from '~icons/mdi/view-gallery';

export function ArtifactEditor() {
  const [tab, setTab] = useState<'preview' | 'editor' | 'log'>('editor');
  const pgStore = useContext(PlaygroundContext)!;
  const { id, currentVersion, versionList, changeVersion, loadVersions, start, playground, restart } = useStore(pgStore);

  // 使用useCallback稳定化函数，避免无限重新渲染
  const handleVersionUpdate = useCallback(async (versionNumber?: number) => {
    console.log('🔄 [ArtifactEditor] 开始处理版本更新:', { versionNumber, timestamp: new Date().toISOString() });

    // 延迟一下，确保后端已经完成分支切换和缓存清除
    await new Promise(resolve => setTimeout(resolve, 500));

    // 重试机制：最多重试3次，每次间隔递增
    const maxRetries = 3;
    let retryCount = 0;
    let success = false;

    while (retryCount < maxRetries && !success) {
      try {
        console.log(`🔄 [ArtifactEditor] 第${retryCount + 1}次尝试加载版本信息`);

        // 加载最新的版本列表
        console.log('🔄 [ArtifactEditor] 调用loadVersions()...');
        await loadVersions();

        // 等待store状态更新
        await new Promise(resolve => setTimeout(resolve, 300));

        // 动态获取最新的版本列表和当前版本，而不依赖于闭包中的状态
        const { versionList: currentVersionList, currentVersion: storeCurrentVersion } = pgStore.getState();

        console.log('🔄 [ArtifactEditor] 当前版本状态:', {
          currentVersionList,
          storeCurrentVersion,
          targetVersionNumber: versionNumber,
          versionListLength: currentVersionList.length
        });

        if (currentVersionList.length > 0) {
          let targetVersion: string;
          let shouldChangeVersion = false;

          // 如果传入了版本号，尝试切换到该版本
          if (typeof versionNumber === 'number') {
            targetVersion = `version-${versionNumber}`;

            if (currentVersionList.includes(targetVersion)) {
              console.log('🔄 [ArtifactEditor] 找到指定版本:', targetVersion);
              // 检查是否需要切换版本
              if (storeCurrentVersion !== targetVersion) {
                console.log('🔄 [ArtifactEditor] 需要切换到指定版本:', { from: storeCurrentVersion, to: targetVersion });
                shouldChangeVersion = true;
              } else {
                console.log('✅ [ArtifactEditor] 已经是目标版本，无需切换');
                success = true;
              }
            } else {
              console.warn('⚠️ [ArtifactEditor] 指定版本不存在，切换到最新版本:', { targetVersion, currentVersionList });
              // 切换到最新版本
              targetVersion = currentVersionList[currentVersionList.length - 1];
              if (storeCurrentVersion !== targetVersion) {
                console.log('🔄 [ArtifactEditor] 需要切换到最新版本:', { from: storeCurrentVersion, to: targetVersion });
                shouldChangeVersion = true;
              } else {
                console.log('✅ [ArtifactEditor] 已经是最新版本，无需切换');
                success = true;
              }
            }
          } else {
            // 没有指定版本号，切换到最新版本
            targetVersion = currentVersionList[currentVersionList.length - 1];
            if (storeCurrentVersion !== targetVersion) {
              console.log('🔄 [ArtifactEditor] 需要切换到最新版本:', { from: storeCurrentVersion, to: targetVersion });
              shouldChangeVersion = true;
            } else {
              console.log('✅ [ArtifactEditor] 已经是最新版本，无需切换');
              success = true;
            }
          }

          // 如果需要切换版本，执行切换
          if (shouldChangeVersion) {
            console.log('🔄 [ArtifactEditor] 执行版本切换:', targetVersion);
            await changeVersion(targetVersion);

            // 等待版本切换完成
            await new Promise(resolve => setTimeout(resolve, 200));

            // 验证版本切换是否成功
            const { currentVersion: newCurrentVersion } = pgStore.getState();

            console.log('🔄 [ArtifactEditor] 版本切换后状态:', { expected: targetVersion, actual: newCurrentVersion });

            if (newCurrentVersion === targetVersion) {
              console.log('✅ [ArtifactEditor] 版本切换成功:', newCurrentVersion);
              success = true;
            } else {
              console.warn('⚠️ [ArtifactEditor] 版本切换可能失败，但仍然继续');
              // 即使版本切换失败，也认为成功，避免无限重试
              success = true;
            }
          }
        } else {
          console.warn('⚠️ [ArtifactEditor] 版本列表为空，等待重试');
          // 如果版本列表为空，但是没有指定版本号，也认为成功
          if (typeof versionNumber !== 'number') {
            console.log('✅ [ArtifactEditor] 无指定版本且版本列表为空，认为成功');
            success = true;
          }
        }
      } catch (error) {
        console.error(`❌ [ArtifactEditor] 第${retryCount + 1}次尝试失败:`, error);
      }

      if (!success) {
        retryCount++;
        if (retryCount < maxRetries) {
          const delay = retryCount * 1000; // 递增延迟：1s, 2s, 3s

          console.log(`⏳ [ArtifactEditor] 等待${delay}ms后重试`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    if (!success) {
      console.error('❌ [ArtifactEditor] 版本更新失败，已达到最大重试次数');
    } else {
      console.log('✅ [ArtifactEditor] 版本更新完成');
    }
  }, [loadVersions, changeVersion, pgStore]);

  useEffect(() => {
    console.log('🔧 [ArtifactEditor] 组件初始化，设置事件监听');
    start();

    const handleTabChange = (tab: 'preview' | 'editor' | 'log') => {
      console.log('🔄 [ArtifactEditor] 切换Tab:', tab);
      setTab(tab);
    };

    const handleVersionUpdateWithLog = (versionNumber?: number) => {
      console.log('🔄 [ArtifactEditor] 接收到artifact::new事件:', { versionNumber, timestamp: new Date().toISOString() });
      handleVersionUpdate(versionNumber);
    };

    eventBus.on('artifact::set-tab', handleTabChange);
    eventBus.on('artifact::new', handleVersionUpdateWithLog);

    return () => {
      console.log('🔧 [ArtifactEditor] 组件卸载，移除事件监听');
      eventBus.off('artifact::set-tab', handleTabChange);
      eventBus.off('artifact::new', handleVersionUpdateWithLog);
    };
  }, []); // 移除handleVersionUpdate依赖，避免无限循环

  // 监听页面卸载事件
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (playground && !playground.archived) {
        commitFiles({ id });
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [playground]);

  const handleSelectionChange = async (keys: Selection) => {
    if (isSet(keys)) {
      const only = keys.values().next().value as string;

      await changeVersion(only);
    }
  }

  // 检查是否启用预览功能，默认为true（向后兼容）
  // 如果enableCustomPreview为true，则不启用预览功能（自定义生码项目playground先不展示预览tab）
  const isPreviewEnabled = playground?.enablePreview !== false;
  const isProjectPreview = playground?.tags?.includes('project-preview'); // 是否项目预览模式

  // 根据预览功能是否启用，动态调整初始tab
  useEffect(() => {
    if (playground && !isPreviewEnabled && tab === 'preview') {
      setTab('editor'); // 如果预览被禁用且当前在预览tab，切换到编辑器tab
    }
  }, [playground, isPreviewEnabled, tab, isProjectPreview]);


  useEffect(() => {
    if (isProjectPreview && tab !== 'preview') {
      setTab('preview'); // 如果是项目预览模式且当前不在预览tab，切换到预览tab
    }
  }, [isProjectPreview]);

  if (!playground) {
    return null;
  }

  if (playground.archived) {
    return <ArchiveBlocker id={playground.id} onUnarchived={() => {
      start();
    }} />
  }

  let versionSelect;

  if (versionList.length) {
    const selections: { key: string, label: string }[] = map(versionList, ver => ({ key: ver, label: ver }));

    versionSelect = (
      <Select
        className="w-40 absolute right-2 h-10"
        disallowEmptySelection={true}
        items={selections}
        placeholder="选择版本"
        selectedKeys={currentVersion ? new Set([currentVersion]) : new Set()}
        size="sm"
        onSelectionChange={handleSelectionChange}
      >
        {(item) => <SelectItem>{item.label}</SelectItem>}
      </Select>
    );
  }

  return (
    <div className="h-full flex-1 p-2 flex flex-col">
      {versionSelect}
      <Tabs
        classNames={{
          panel: "flex-1 h-full",
          tabList: "p-0 gap-0 bg-gray-100 dark:bg-zinc-800 rounded-md inline-flex",
          cursor: "bg-white dark:bg-zinc-900 rounded-sm shadow-sm",
          tab: "px-3 py-1 text-sm",
          tabContent: "text-gray-500 group-data-[selected=true]:text-black dark:group-data-[selected=true]:text-white"
        }}
        destroyInactiveTabPanel={false}
        selectedKey={tab}
        onSelectionChange={(key) => setTab(key as 'preview' | 'editor' | 'log')}
      >
        {/* 只有当启用预览功能时才显示预览tab */}
        {isPreviewEnabled && (
          <Tab key="preview" title={
            <Tooltip content="预览" placement="bottom">
              <div className="flex items-center">
                <IconViewGallery />
              </div>
            </Tooltip>
          }>
            <Preview />
          </Tab>
        )}
        <Tab key="editor" title={
          <Tooltip content="代码" placement="bottom">
            <div className="flex items-center">
              <IconCodeTags />
            </div>
          </Tooltip>
        }>
          <Editor />
        </Tab>
        <Tab key="log" title={
          <Tooltip content="日志" placement="bottom">
            <div className="flex items-center">
              <IconTextLong />
            </div>
          </Tooltip>
        }>
          <div className="flex flex-col h-full">
            <LogView />
          </div>
        </Tab>
      </Tabs>
    </div>
  );
}