import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
// @ts-ignore
import store from 'store';

interface WebidePodData {
  name: string;
  startTime: string;
  url: string;
}

interface WebideStatusData {
  status: string;
  startLog: string;
  codeServerReady: boolean;
  codeServerVersion: string;
  latestActiveTime: string | null;
  destroyed: boolean;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// TODO 执行参数从项目配置中取
const WebideStart: React.FC = () => {
  const [searchParams] = useSearchParams();
  const projectId = searchParams.get('projectId');
  const branch = searchParams.get('branch');
  const logContainerRef = useRef<HTMLDivElement>(null);

  const [isStarting, setIsStarting] = useState(true);
  const [podData, setPodData] = useState<WebidePodData | null>(null);
  const [statusData, setStatusData] = useState<WebideStatusData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(1);
  const [logs, setLogs] = useState<string[]>([]);
  const [pollingInterval, setPollingInterval] = useState<number | null>(null);
  const [hasAutoJumped, setHasAutoJumped] = useState(false);
  const [isStarted, setIsStarted] = useState(false); // 防止重复启动
  const [displayedLogLength, setDisplayedLogLength] = useState(0); // 记录已显示的日志长度

  // 获取用户信息
  const userInfo = store.get('userInfo') || {};
  const userId = userInfo.userId || userInfo.username || '';

  // 步骤配置
  const steps = [
    { id: 1, title: '检查用户', subtitle: '检查用户身份信息' },
    { id: 2, title: '创建工作目录', subtitle: '准备WebIDE运行环境' },
    { id: 3, title: '创建IDE实例', subtitle: '创建&运行WebIDE实例' },
    { id: 4, title: '完成初始化', subtitle: '完成WebIDE启动' },
  ];

  // 获取环境变量中的基础URL
  const getBaseUrl = () => {
    return '/api/analyzer/auth/api/v1'
  };

  // 添加日志
  const addLog = useCallback((message: string) => {
    setLogs(prev => [...prev, message]);
    
    // 自动滚动到底部
    setTimeout(() => {
      if (logContainerRef.current) {
        logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
      }
    }, 100);
  }, []);

  // 处理启动日志 - 改进日志显示逻辑
  const processStartLog = useCallback((startLog: string) => {
    if (!startLog) return;
    
    const logLines = startLog.split('\n').filter(line => line.trim());
    
    // 只添加新的日志行
    if (logLines.length > displayedLogLength) {
      const newLines = logLines.slice(displayedLogLength);

      newLines.forEach(line => {
        addLog(line);
      });
      setDisplayedLogLength(logLines.length);
    }
  }, [displayedLogLength, addLog]);

  // 启动WebIDE容器
  const startWebidePod = useCallback(async () => {
    if (isStarted) return; // 防止重复启动
    
    if (!projectId || !branch) {
      setError('缺少必要的参数：projectId 或 branch');
      setIsStarting(false);

      return;
    }

    if (!userId) {
      setError('用户信息获取失败，请重新登录');
      setIsStarting(false);

      return;
    }

    setIsStarted(true);

    // 步骤1：检查用户
    setCurrentStep(1);
    setProgress(10);
    addLog('正在检查用户身份信息...');
    addLog(`项目ID: ${projectId}`);
    addLog(`分支: ${branch}`);
    addLog(`用户: ${userId}`);
    
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      // 步骤2：创建工作目录
      setCurrentStep(2);
      setProgress(25);
      addLog('正在创建工作目录...');
      addLog('发送容器启动请求...');

      const response = await fetch(`${getBaseUrl()}/webide-v2/ai-coding/pod/start`, {
        method: 'POST',
        headers: {
          'iv-user': userId,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: projectId,
          unitId: projectId,
          taskId: `ai-coding-${Date.now()}`,
          repository: `git@*************:codebox-test/${projectId}.git`,
          branch: branch,
          userId: userId,
          extras: {
            bashScript: "nvm use 20 && pnpm install && pnpm run start"
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`启动请求失败: ${response.status} ${response.statusText}`);
      }

      const result: ApiResponse<WebidePodData> = await response.json();
      
      if (result.code !== 0) {
        throw new Error(`启动失败: ${result.message}`);
      }

      setPodData(result.data);
      
      // 步骤3：创建IDE实例
      setCurrentStep(3);
      setProgress(50);
      addLog(`容器创建成功: ${result.data.name}`);
      addLog(`访问地址: ${result.data.url}`);
      addLog('正在启动WebIDE实例...');
      
      // 开始轮询状态
      startPolling(result.data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '启动失败';

      setError(errorMessage);
      addLog(`启动失败: ${errorMessage}`);
      setIsStarting(false);
    }
  }, [projectId, branch, userId, isStarted, addLog]);

  // 轮询容器状态
  const startPolling = useCallback((podData: WebidePodData) => {
    const poll = async () => {
      try {
        const response = await fetch(`${getBaseUrl()}/webide-v2/ai-coding/pod`, {
          method: 'POST',
          headers: {
            'iv-user': userId,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            podName: podData.name,
            startTime: podData.startTime,
            userId: userId,
          }),
        });

        if (!response.ok) {
          throw new Error(`状态查询失败: ${response.status} ${response.statusText}`);
        }

        const result: ApiResponse<WebideStatusData> = await response.json();
        
        if (result.code !== 0) {
          throw new Error(`状态查询失败: ${result.message}`);
        }

        const status = result.data;

        setStatusData(status);
        
        // 处理启动日志 - 使用新的日志处理逻辑
        processStartLog(status.startLog);

        // 根据状态更新进度和步骤
        const { newProgress, newStep } = getProgressAndStep(status.status);

        setProgress(newProgress);
        setCurrentStep(newStep);

        // 检查是否完成 - 状态5.0时立即停止轮询
        if (status.status === '5.0') {
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
          setProgress(100);
          setCurrentStep(4);
          addLog('WebIDE已就绪，准备跳转...');
          setIsStarting(false);
          
          // 3秒后自动跳转，只跳转一次
          if (!hasAutoJumped) {
            setHasAutoJumped(true);
            setTimeout(() => {
              addLog('正在打开WebIDE...');
              // 跳转到WebIDE预览页而不是直接打开外部链接
              const previewUrl = `/webide-preview?url=${encodeURIComponent(podData.url)}&projectId=${encodeURIComponent(podData.name)}`;

              window.open(previewUrl, '_blank');
            }, 3000);
          }
        }

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '状态检查失败';

        addLog(`状态检查失败: ${errorMessage}`);
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
        setError(errorMessage);
        setIsStarting(false);
      }
    };

    // 立即执行一次
    poll();
    
    // 设置定时轮询
    const interval = setInterval(poll, 3000) as unknown as number;

    setPollingInterval(interval);

    // 设置超时
    setTimeout(() => {
      if (interval) {
        clearInterval(interval);
        setPollingInterval(null);
        if (isStarting) {
          setError('启动超时，请刷新页面重试');
          addLog('启动超时 (10分钟)');
          setIsStarting(false);
        }
      }
    }, 10 * 60 * 1000);
  }, [userId, pollingInterval, isStarting, hasAutoJumped, addLog, processStartLog]);

  // 根据状态获取进度和步骤
  const getProgressAndStep = (status: string): { newProgress: number; newStep: number } => {
    const statusMap: Record<string, { newProgress: number; newStep: number }> = {
      '1.0': { newProgress: 60, newStep: 3 },
      '2.0': { newProgress: 70, newStep: 3 },
      '3.0': { newProgress: 80, newStep: 3 },
      '4.0': { newProgress: 90, newStep: 3 },
      '5.0': { newProgress: 100, newStep: 4 },
    };

    return statusMap[status] || { newProgress: 50, newStep: 3 };
  };

  // 页面加载时自动启动 - 添加依赖数组防止重复执行
  useEffect(() => {
    if (!isStarted) {
      startWebidePod();
    }
    
    // 清理定时器
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [startWebidePod, isStarted, pollingInterval]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 flex flex-col items-center justify-center p-8">
      {/* 顶部错误提示 */}
      {error && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          请检查启动参数是否正确
        </div>
      )}

      {/* 主要内容容器 */}
      <div className="w-full max-w-4xl mx-auto">
        {/* 步骤指示器 */}
        <div className="flex items-center justify-center mb-16">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              {/* 步骤圆圈 */}
              <div className="flex flex-col items-center w-40">
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg border-2 flex-shrink-0 ${
                    currentStep >= step.id
                      ? 'bg-white bg-opacity-20 border-white'
                      : 'bg-transparent border-gray-400'
                  }`}
                >
                  {step.id}
                </div>
                <div className="mt-4 text-center w-full">
                  <div className={`text-lg font-medium ${
                    currentStep >= step.id ? 'text-white' : 'text-gray-300'
                  }`}>
                    {step.title}
                  </div>
                  <div className={`text-sm ${
                    currentStep >= step.id ? 'text-gray-200' : 'text-gray-400'
                  }`}>
                    {step.subtitle}
                  </div>
                </div>
              </div>
              
              {/* 连接线 */}
              {index < steps.length - 1 && (
                <div
                  className={`h-0.5 w-16 mx-4 mt-[-60px] ${
                    currentStep > step.id ? 'bg-white' : 'bg-gray-400'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* 进度条区域 */}
        <div className="mb-12">
          <div className="bg-gray-300 rounded-full h-3 mb-4">
            <div
              className="bg-gradient-to-r from-blue-400 to-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-center text-white text-xl font-medium">
            Loading
            <span className="loading-dots">
              <span className="dot-1">.</span>
              <span className="dot-2">.</span>
              <span className="dot-3">.</span>
            </span>
          </div>
        </div>

        {/* 启动日志区域 */}
        <div className="mb-8">
          <div className="text-white text-lg font-medium mb-4">启动日志:</div>
          <div
            ref={logContainerRef}
            className="bg-black rounded-lg p-6 h-64 overflow-y-auto font-mono text-sm text-green-400"
          >
            {logs.length === 0 ? (
              <div className="text-gray-500">等待启动...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* 添加CSS动画样式 */}
      <style>
        {`
          .loading-dots {
            display: inline-block;
          }
          .loading-dots span {
            animation: loading 1.4s infinite ease-in-out;
            animation-fill-mode: both;
          }
          .loading-dots .dot-1 {
            animation-delay: -0.32s;
          }
          .loading-dots .dot-2 {
            animation-delay: -0.16s;
          }
          @keyframes loading {
            0%, 80%, 100% {
              opacity: 0;
            }
            40% {
              opacity: 1;
            }
          }
        `}
      </style>
    </div>
  );
};

export default WebideStart; 