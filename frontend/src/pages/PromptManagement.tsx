import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b, Spinner } from '@heroui/react';
import clsx from 'clsx';
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  getPromptTemplateSummary,
  getPromptContent,
  getTargetPromptContent,
  createPromptTemplate,
  updateTargetPrompt,
  resetPromptToDefault,
  resetTargetPromptToDefault,
  type PromptTemplateSummary,
  type PromptContentResponse,
} from '@/apis';
import { useUserInfoStore } from '@/hooks/login';
import IconAlert from '~icons/mdi/alert-circle';
import IconArrowLeft from '~icons/mdi/arrow-left';
import IconCheck from '~icons/mdi/check-circle';
import IconEdit from '~icons/mdi/circle-edit-outline';
import IconClock from '~icons/mdi/clock-outline';
import IconSettings from '~icons/mdi/cog';
import IconSave from '~icons/mdi/content-save';
import IconEye from '~icons/mdi/eye';
import IconFileDocument from '~icons/mdi/file-document-outline';
import IconLock from '~icons/mdi/lock';
import IconReset from '~icons/mdi/restore';

const PromptManagement: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  
  // 权限验证
  const allowedUsers = ['018465', '015265', '019270'];
  const currentUser = userInfo?.workNumber || userInfo?.username || '';
  const hasPermission = allowedUsers.includes(currentUser);

  const [summary, setSummary] = useState<PromptTemplateSummary[]>([]);
  const [selectedTask, setSelectedTask] = useState<string>('');
  const [selectedPromptType, setSelectedPromptType] = useState<'main' | 'check' | 'target'>('main');
  const [promptContent, setPromptContent] = useState<PromptContentResponse | null>(null);
  const [editingContent, setEditingContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // 检查当前任务是否支持target提示词
  const supportsTarget = selectedTask === 'spec-to-prod-code';

  useEffect(() => {
    if (projectId && hasPermission) {
      loadSummary();
    } else if (!hasPermission) {
      setLoading(false);
    }
  }, [projectId, hasPermission]);

  useEffect(() => {
    if (selectedTask && selectedPromptType && hasPermission) {
      loadPromptContent();
    }
  }, [selectedTask, selectedPromptType, hasPermission]);

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    console.log(`${type}: ${message}`);
    if (type === 'error') {
      alert(`错误: ${message}`);
    }
  };

  const loadSummary = async () => {
    if (!projectId) return;
    
    try {
      const response = await getPromptTemplateSummary({ 
        projectId, 
        user: currentUser 
      });
      const data = (response as any)?.data || response;

      setSummary(data);
      if (data.length > 0 && !selectedTask) {
        setSelectedTask(data[0].taskType);
      }
    } catch (error) {
      console.error('加载提示词概览失败:', error);
      showToast('加载提示词概览失败', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadPromptContent = async () => {
    if (!projectId || !selectedTask || !selectedPromptType) return;

    try {
      let response: any;

      if (selectedPromptType === 'target') {
        response = await getTargetPromptContent({
          projectId,
          taskType: selectedTask,
          user: currentUser,
        });
      } else {
        response = await getPromptContent({
          projectId,
          taskType: selectedTask,
          promptType: selectedPromptType,
          user: currentUser,
        });
      }

      const data = response?.data || response;

      setPromptContent(data);
      setEditingContent(data.content || '');
    } catch (error) {
      console.error('加载提示词内容失败:', error);
      showToast('加载提示词内容失败', 'error');
    }
  };

  const savePromptContent = async () => {
    if (!projectId || !selectedTask || !selectedPromptType) return;

    setSaving(true);
    try {
      if (selectedPromptType === 'target') {
        await updateTargetPrompt({
          projectId,
          taskType: selectedTask,
          user: currentUser,
          content: editingContent,
        });
      } else {
        await createPromptTemplate({
          projectId,
          taskType: selectedTask,
          promptType: selectedPromptType,
          content: editingContent,
          user: currentUser,
        });
      }

      showToast('提示词保存成功');
      setIsEditing(false);
      await loadSummary();
      await loadPromptContent();
    } catch (error) {
      console.error('保存提示词失败:', error);
      showToast('保存提示词失败', 'error');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefault = async () => {
    if (!projectId || !selectedTask || !selectedPromptType) return;

    if (!confirm('确定要重置为默认提示词吗？这将删除您的自定义内容。')) {
      return;
    }

    try {
      if (selectedPromptType === 'target') {
        await resetTargetPromptToDefault({
          projectId,
          taskType: selectedTask,
          user: currentUser,
        });
      } else {
        await resetPromptToDefault({
          projectId,
          taskType: selectedTask,
          promptType: selectedPromptType,
          user: currentUser,
        });
      }

      showToast('已重置为默认提示词');
      setIsEditing(false);
      await loadSummary();
      await loadPromptContent();
    } catch (error) {
      console.error('重置提示词失败:', error);
      showToast('重置提示词失败', 'error');
    }
  };

  const getStatusIcon = (taskType: string, hasCustomMain: boolean, hasCustomCheck: boolean, hasCustomTarget?: boolean) => {
    // 对于spec-to-prod-code任务，需要考虑target提示词
    if (taskType === 'spec-to-prod-code') {
      const totalCustom = [hasCustomMain, hasCustomCheck, hasCustomTarget].filter(Boolean).length;
      const totalTypes = 3; // main, check, target
      
      if (totalCustom === totalTypes) {
        return <IconCheck className="h-4 w-4 text-green-600" />;
      } else if (totalCustom > 0) {
        return <IconAlert className="h-4 w-4 text-amber-500" />;
      } else {
        return <IconClock className="h-4 w-4 text-gray-400" />;
      }
    } else {
      // 其他任务只考虑main和check
      if (hasCustomMain && hasCustomCheck) {
        return <IconCheck className="h-4 w-4 text-green-600" />;
      } else if (hasCustomMain || hasCustomCheck) {
        return <IconAlert className="h-4 w-4 text-amber-500" />;
      } else {
        return <IconClock className="h-4 w-4 text-gray-400" />;
      }
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';

    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 无权限页面
  if (!hasPermission) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-6">
        <div className="w-full max-w-md text-center">
          <div className="mb-8">
            <IconLock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">访问受限</h1>
            <p className="text-gray-600">
              您没有权限访问此页面
            </p>
          </div>

          <div className="space-y-3">
            <Button 
              className="w-full" 
              color="primary"
              variant="solid"
              onPress={() => navigate('/project-list')}
            >
              返回项目列表
            </Button>
            <Button 
              className="w-full"
              variant="bordered"
              onPress={() => navigate('/')}
            >
              返回首页
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  const renderPromptEditor = () => {
    if (!promptContent) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <IconEye className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>选择任务和提示词类型查看内容</p>
          </div>
        </div>
      );
    }

    const getPromptTypeLabel = () => {
      switch (selectedPromptType) {
        case 'main': return '主要任务提示词';
        case 'check': return '结果自检查提示词';
        case 'target': return '任务目标提示词';
        default: return '提示词';
      }
    };

    const getPromptTypePlaceholder = () => {
      switch (selectedPromptType) {
        case 'main': return '输入主要任务提示词...';
        case 'check': return '输入结果自检查提示词...';
        case 'target': return '输入任务目标提示词...';
        default: return '输入提示词...';
      }
    };

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <div className="text-sm text-gray-600">
            {getPromptTypeLabel()}
            {selectedPromptType === 'target' && (
              <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                仅 spec-to-prod-code 任务
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {!isEditing ? (
              <>
                <Button
                  size="sm"
                  startContent={<IconEdit className="h-4 w-4" />}
                  variant="bordered"
                  onPress={() => setIsEditing(true)}
                >
                  编辑
                </Button>
                {promptContent.isCustom && (
                  <Button
                    size="sm"
                    startContent={<IconReset className="h-4 w-4" />}
                    variant="bordered"
                    onPress={resetToDefault}
                  >
                    重置
                  </Button>
                )}
              </>
            ) : (
              <>
                <Button
                  size="sm"
                  variant="bordered"
                  onPress={() => {
                    setIsEditing(false);
                    setEditingContent(promptContent.content || '');
                  }}
                >
                  取消
                </Button>
                <Button
                  color="primary"
                  isLoading={saving}
                  size="sm"
                  startContent={<IconSave className="h-4 w-4" />}
                  onPress={savePromptContent}
                >
                  {saving ? '保存中...' : '保存'}
                </Button>
              </>
            )}
          </div>
        </div>

        <div className="flex-1 min-h-0">
          <Textarea
         className="font-mono text-sm"
            classNames={{
              base: "h-full",
              input: "h-full resize-none min-h-[calc(100vh-300px)]",
              inputWrapper: "h-full border border-gray-200 shadow-none"
            }}
            isReadOnly={!isEditing}
            placeholder={getPromptTypePlaceholder()}
            value={isEditing ? editingContent : (promptContent.content || '')}
            onValueChange={setEditingContent}
          />
        </div>

        {!promptContent.content && !isEditing && (
          <div className="absolute inset-0 flex items-center justify-center text-gray-500 pointer-events-none">
            <div className="text-center">
              <p>该任务类型暂无{getPromptTypeLabel()}</p>
              <p className="text-sm mt-1">点击编辑按钮创建自定义提示词</p>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <Button
              isIconOnly
              className="text-gray-600"
              variant="light"
              onPress={() => navigate('/project-list')}
            >
              <IconArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-3">
              <IconFileDocument className="h-6 w-6 text-gray-700" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">提示词管理</h1>
                <p className="text-gray-500 text-sm">管理项目的各种任务提示词模板</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto h-[calc(100vh-81px)]">
        <div className="grid grid-cols-12 h-full">
          {/* 左侧：任务列表 */}
          <div className="col-span-4 border-r border-gray-200">
            <div className="h-full flex flex-col">
              <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
                <div className="flex items-center gap-2">
                  <IconSettings className="h-4 w-4 text-gray-600" />
                  <h3 className="font-medium text-gray-900">任务类型</h3>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto">
                {summary.map((task, index) => (
                  <button
                    key={task.taskType}
                    className={clsx(
                      "w-full text-left p-4 border-b border-gray-100 transition-colors hover:bg-gray-50",
                      selectedTask === task.taskType && 'bg-blue-50'
                    )}
                    onClick={() => setSelectedTask(task.taskType)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-sm text-gray-900">{task.taskName}</div>
                        <div className="text-xs text-gray-500 mt-1 font-mono">
                          {task.taskType}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(task.taskType, task.hasCustomMain, task.hasCustomCheck, task.hasCustomTarget)}
                      </div>
                    </div>
                    
                    <div className="flex gap-1 mt-3">
                      <Chip 
                        className="text-xs"
                        color={task.hasCustomMain ? 'primary' : 'default'}
                        size="sm"
                        variant={task.hasCustomMain ? 'solid' : 'bordered'}
                      >
                        主提示词
                      </Chip>
                      <Chip 
                        className="text-xs"
                        color={task.hasCustomCheck ? 'primary' : 'default'}
                        size="sm"
                        variant={task.hasCustomCheck ? 'solid' : 'bordered'}
                      >
                        自检查
                      </Chip>
                      {task.taskType === 'spec-to-prod-code' && (
                        <Chip 
                          className="text-xs"
                          color={task.hasCustomTarget ? 'primary' : 'default'}
                          size="sm"
                          variant={task.hasCustomTarget ? 'solid' : 'bordered'}
                        >
                          任务目标
                        </Chip>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧：提示词编辑 */}
          <div className="col-span-8">
            <div className="h-full flex flex-col">
              <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
                <div className="flex items-center justify-between w-full">
                  <h3 className="font-medium text-gray-900">
                    {summary.find(t => t.taskType === selectedTask)?.taskName || '选择任务'}
                  </h3>
                  {promptContent && (
                    <div className="flex items-center gap-2">
                      <Chip 
                        className="text-xs"
                        color={promptContent.isCustom ? 'warning' : 'default'}
                        size="sm"
                        variant="bordered"
                      >
                        {promptContent.isCustom ? '自定义' : '默认'}
                      </Chip>
                      {promptContent.isCustom && promptContent.template && (
                        <span className="text-xs text-gray-500">
                          v{promptContent.template.version} · {formatDate(promptContent.template.updated)}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex-1 min-h-0 flex flex-col">
                {selectedTask && (
                  <Tabs 
                    className="w-full h-full flex flex-col" 
                    classNames={{
                      tabList: "w-full border-b border-gray-200 bg-white px-4",
                      tabContent: "flex-1 p-4 min-h-0"
                    }}
                    selectedKey={selectedPromptType}
                    onSelectionChange={(key) => {
                      setSelectedPromptType(key as 'main' | 'check' | 'target');
                    }}
                  >
                    <Tab 
                      key="main" 
                      title={
                        <div className="flex items-center gap-2">
                          <IconEdit className="h-4 w-4" />
                          主提示词
                        </div>
                      }
                    >
                      {renderPromptEditor()}
                    </Tab>
                    <Tab 
                      key="check" 
                      title={
                        <div className="flex items-center gap-2">
                          <IconCheck className="h-4 w-4" />
                          自检查提示词
                        </div>
                      }
                    >
                      {renderPromptEditor()}
                    </Tab>
                    {supportsTarget && (
                      <Tab 
                        key="target" 
                        title={
                          <div className="flex items-center gap-2">
                            <IconFileDocument className="h-4 w-4" />
                            任务目标
                          </div>
                        }
                      >
                        {renderPromptEditor()}
                      </Tab>
                    )}
                  </Tabs>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptManagement; 