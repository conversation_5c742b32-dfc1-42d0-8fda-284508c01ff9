import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { DesignPreviewPage } from "@/components/DesignToCode/DesignPreviewPage";
import { useProjectStore } from "@/stores/project";

export function Component() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { setCurrent } = useProjectStore();

  useEffect(() => {
    if (id) {
      setCurrent(id);
    } else {
      navigate('/project', { replace: true });
    }
  }, [id, setCurrent, navigate]);

  if (!id) {
    return null;
  }

  return (
    <div className="p-4">
      <DesignPreviewPage projectId={id} />
    </div>
  );
}
