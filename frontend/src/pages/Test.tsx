import { useEffect } from "react";
import { Spinner } from "@heroui/spinner";
import { useChat } from "@ai-sdk/react";

export function Component() {
  const { messages, append, status, data, error } = useChat({
    api: '/api/ai-coding/image2Code',
    experimental_prepareRequestBody({ messages, id, requestData }) {
      // Only send last message
      return { message: messages[messages.length - 1], id, requestData };
    },
  });

  useEffect(() => {
    if (error) {
      console.error('Received error from backend:', error);
    }
  }, [error]);

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      return;
    }
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === 'string') {
        append({
          content: '生成react代码，使用antd框架',
          role: 'user',
        }, {
          experimental_attachments: [
            {
              name: file.name,
              contentType: file.type,
              url: result,
            }
          ],
          data: {
            model: 'htsc::saas-deepseek-v3',
            user: 'admin',
            isPublic: true,
          },
        });
      }
    };
    reader.readAsDataURL(file);
  };

  useEffect(() => {
    if (status === 'ready' && data) {
      const item = data?.find(e => e.type == 'status' && e.status === 'start-stream');
      if (item) {
        // Now we can download it
        window.open(`/api/ai-coding/${item.workspaceId}/@download`, '_blank');
      }
    }
  }, [data, status]);

  return (
    <>
      <div className="flex items-center gap-2">
        <input
          type="file"
          accept="image/*"
          multiple={false}
          disabled={status === 'streaming' || status === 'submitted'}
          onChange={onFileChange}
        />
        { (status === 'streaming' || status === 'submitted') && <Spinner variant="wave" /> }
      </div>
      <section className="mt-4">
        <p>State</p>
        <p>{ JSON.stringify(data) }</p>
      </section>
      <section className="mt-4">
        <h1>Messages</h1>
        <div>
          {messages.map((message, index) => (
            <div key={index}>
              <h1>{message.role}</h1>
              <div key={index}>{message.content}</div>
            </div>
          ))}
        </div>
      </section>
    </>
  );
}