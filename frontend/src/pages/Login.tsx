import { <PERSON><PERSON>, <PERSON>, Input, Divider, <PERSON><PERSON><PERSON>, CardHeader } from "@heroui/react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
// @ts-ignore
import store from 'store';
import Logo from "@/assets/images/logo.svg?react";
import { useUserInfoStore } from '@/hooks/login';

export function Component() {
  const { fetchUserInfo } = useUserInfoStore();
  const navigate = useNavigate();

  // 如果已登录，跳到首页
  useEffect(() => {
    const isLogin = store.get('isLogin');

    if (isLogin) {
      navigate('/');
    }
  }, [])

  const [workNumber, setWorkNumber] = useState("");
  const [password, setPassword] = useState("");
  
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    fetchUserInfo(workNumber, password);
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-full max-w-md p-8 gap-2">
        <CardHeader>
          <div className="text-center inline-flex m-auto items-center gap-2">
            <Logo fontSize={32} />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-zinc-200">
            魔方智能生成
            </h2>
          </div>
        </CardHeader>
          <Divider className="" />
        <CardBody>
          <form className="space-y-6" onSubmit={handleLogin}>
            <div>
              <Input
                required
                className="block w-full rounded-md"
                id="workNumber"
                label="工号"
                type="text"
                value={workNumber}
                onChange={(e) => setWorkNumber(e.target.value)}
              />
            </div>
            
            <div>
              <Input
                required
                className="block w-full rounded-md"
                id="password"
                label="密码"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            
            <div className="justify-center">
              <Button
                className="w-full flex justify-center px-4 text-sm text-white bg-blue-500"
                radius="full"
                size="lg"
                type="submit"
              >
                登录
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}
