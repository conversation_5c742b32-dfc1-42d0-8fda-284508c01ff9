import { Button } from '@heroui/react';
import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';


// 导入MDI图标
import { useUserInfoStore } from '@/hooks/login';
import { restartWorkflow } from '@/utils/workflowUtils';
import IconAlertCircle from '~icons/mdi/alert-circle';
import IconCheckCircle from '~icons/mdi/check-circle';
import IconCircleOutline from '~icons/mdi/circle-outline';
import IconClockOutline from '~icons/mdi/clock-outline';
import IconFileMultiple from '~icons/mdi/file-multiple';
import IconLoading from '~icons/mdi/loading';
import IconRefresh from '~icons/mdi/refresh';
import IconRestart from '~icons/mdi/restart';
import IconViewGrid from '~icons/mdi/view-grid';

// 导入工作流工具函数
interface PageWorkflow {
  pageId: string;
  pageName: string;
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  startTime?: string;
  endTime?: string;
  error?: string;
}

interface MultiPageWorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  pageWorkflows: PageWorkflow[];
  startTime?: string;
  endTime?: string;
  error?: string;
  totalPages: number;
  completedPages: number;
  failedPages: number;
}

// 研发平台风格的色系配置 - 转换为 Tailwind 类名
const getStatusColorClasses = (status: string) => {
  switch (status) {
    case 'pending':
      return {
        bg: 'bg-gray-50',
        border: 'border-gray-500',
        text: 'text-gray-600',
        icon: 'text-gray-500',
        progressBg: 'bg-gray-500'
      };
    case 'processing':
      return {
        bg: 'bg-blue-50',
        border: 'border-blue-600',
        text: 'text-blue-700',
        icon: 'text-blue-600',
        progressBg: 'bg-blue-600'
      };
    case 'completed':
      return {
        bg: 'bg-green-50',
        border: 'border-green-600',
        text: 'text-green-700',
        icon: 'text-green-600',
        progressBg: 'bg-green-600'
      };
    case 'partial-success':
      return {
        bg: 'bg-orange-50',
        border: 'border-orange-500',
        text: 'text-orange-600',
        icon: 'text-orange-500',
        progressBg: 'bg-orange-500'
      };
    case 'failed':
      return {
        bg: 'bg-red-50',
        border: 'border-red-600',
        text: 'text-red-700',
        icon: 'text-red-600',
        progressBg: 'bg-red-600'
      };
    default:
      return {
        bg: 'bg-gray-50',
        border: 'border-gray-500',
        text: 'text-gray-600',
        icon: 'text-gray-500',
        progressBg: 'bg-gray-500'
      };
  }
};

const getStatusIcon = (status: string) => {
  const iconProps = { width: 16, height: 16 };

  switch (status) {
    case 'pending':
      return <IconClockOutline {...iconProps} />;
    case 'processing':
      return <IconLoading {...iconProps} className="animate-spin" />;
    case 'completed':
      return <IconCheckCircle {...iconProps} />;
    case 'partial-success':
      return <IconCheckCircle {...iconProps} />;
    case 'failed':
      return <IconAlertCircle {...iconProps} />;
    default:
      return <IconClockOutline {...iconProps} />;
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '等待中';
    case 'processing':
      return '执行中';
    case 'completed':
      return '已完成';
    case 'partial-success':
      return '部分成功';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};

const MultiPageWorkflowVisualization: React.FC = () => {
  const { projectId, workflowId: urlWorkflowId } = useParams<{ projectId: string; workflowId?: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();

  const workflowId = urlWorkflowId || searchParams.get('workflowId');

  const [workflowStatus, setWorkflowStatus] = useState<MultiPageWorkflowStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [retryingWorkflows, setRetryingWorkflows] = useState<Set<string>>(new Set());

  const fetchWorkflowStatus = useCallback(async () => {
    if (!projectId || !workflowId) return;

    try {
      const response = await fetch(`/api/design-project/${projectId}/workflows/${workflowId}/multi-page-status`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data: MultiPageWorkflowStatus = await response.json();

      setWorkflowStatus(data);
      setError(null);
    } catch (err) {
      console.error('获取多页面工作流状态失败:', err);
      setError(err instanceof Error ? err.message : '获取工作流状态失败');
    } finally {
      setLoading(false);
    }
  }, [projectId, workflowId]);

  // 获取当前用户信息的方法
  const getCurrentUser = useCallback(() => {
    return userInfo?.username || 'system-retry';
  }, [userInfo]);

  // 重试整个多页面工作流（真正的重试，不是创建新工作流）
  const restartMultiPageWorkflow = useCallback(async () => {
    if (!workflowStatus || !projectId || !workflowId) {
      return;
    }

    const workflowKey = `multi-${workflowStatus.workflowId}`;
    
    if (retryingWorkflows.has(workflowKey)) {
      return; // 防止重复重试
    }

    setRetryingWorkflows(prev => new Set([...prev, workflowKey]));

    try {
      const currentUser = getCurrentUser();

      console.log(`🔄 重试多页面工作流: ${workflowId}`);
      
      // 调用真正的重试API
      const result = await restartWorkflow(projectId, workflowId, currentUser);

      console.log(`✅ 多页面工作流重试成功: ${result.workflowId}`);
      
      // 重试成功后立即刷新状态
      await fetchWorkflowStatus();
      
      // 然后每2秒刷新一次，持续10次，确保能看到状态变化
      let refreshCount = 0;
      const quickRefresh = setInterval(async () => {
        refreshCount++;
        await fetchWorkflowStatus();
        
        if (refreshCount >= 10) {
          clearInterval(quickRefresh);
        }
      }, 2000);
      
    } catch (error) {
      console.error('重试多页面工作流失败:', error);
      // 这里可以添加用户提示，比如使用toast
    } finally {
      setRetryingWorkflows(prev => {
        const newSet = new Set(prev);

        newSet.delete(workflowKey);

        return newSet;
      });
    }
  }, [workflowStatus, projectId, workflowId, retryingWorkflows, getCurrentUser, fetchWorkflowStatus]);

  // 重试单个页面工作流（真正的重试，不是创建新工作流）
  const restartPageWorkflow = useCallback(async (pageWorkflow: PageWorkflow) => {
    if (!projectId || !pageWorkflow.workflowId) {
      return;
    }

    const workflowKey = `page-${pageWorkflow.pageId}`;
    
    if (retryingWorkflows.has(workflowKey)) {
      return; // 防止重复重试
    }

    setRetryingWorkflows(prev => new Set([...prev, workflowKey]));

    try {
      const currentUser = getCurrentUser();

      console.log(`🔄 重试页面工作流: ${pageWorkflow.workflowId} (页面: ${pageWorkflow.pageName})`);
      
      // 调用真正的重试API
      const result = await restartWorkflow(projectId, pageWorkflow.workflowId, currentUser);

      console.log(`✅ 页面工作流重试成功: ${result.workflowId}`);
      
      // 重试成功后立即刷新状态
      await fetchWorkflowStatus();
      
      // 然后每2秒刷新一次，持续5次，确保能看到状态变化
      let refreshCount = 0;
      const quickRefresh = setInterval(async () => {
        refreshCount++;
        await fetchWorkflowStatus();
        
        if (refreshCount >= 5) {
          clearInterval(quickRefresh);
        }
      }, 2000);
      
    } catch (error) {
      console.error('重试页面工作流失败:', error);
      // 这里可以添加用户提示
    } finally {
      setRetryingWorkflows(prev => {
        const newSet = new Set(prev);

        newSet.delete(workflowKey);

        return newSet;
      });
    }
  }, [projectId, retryingWorkflows, getCurrentUser, fetchWorkflowStatus]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (workflowStatus?.status === 'processing') {
        fetchWorkflowStatus();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [fetchWorkflowStatus, autoRefresh, workflowStatus?.status]);

  // 初始加载
  useEffect(() => {
    fetchWorkflowStatus();
  }, [fetchWorkflowStatus]);

  const handleViewPageWorkflow = (pageWorkflow: PageWorkflow) => {
    if (pageWorkflow.workflowId) {
      const url = `/workflow/${projectId}?workflowId=${pageWorkflow.workflowId}`;

      window.open(url, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="flex flex-col items-center gap-4 p-8">
          <div className="w-10 h-10 border-3 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
          <span className="text-gray-600 text-sm">加载多页面工作流数据...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full bg-white p-6 flex flex-col">
        {/* 头部信息 - 保持结构一致 */}
        <div className="border-b border-gray-200 pb-4 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-gray-700">
                  <IconFileMultiple height={20} width={20} />
                </div>
                <h1 className="text-base font-semibold text-gray-800 m-0">
                  多页面代码生成工作流
                </h1>
              </div>
            </div>

            {/* 操作按钮区域 */}
            <div className="flex gap-3 items-start flex-shrink-0">
              <Button
                className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700 px-2 py-1 text-xs"
                size="sm"
                startContent={<IconRefresh height={14} width={14} />}
                onClick={fetchWorkflowStatus}
              >
                刷新状态
              </Button>

              <Button
                className={`transition-all duration-200 rounded-md border-1 dark:border-zinc-700 px-2 py-1 text-xs ${autoRefresh ? 'bg-green-600 text-white' : 'bg-transparent'}`}
                size="sm"
                startContent={autoRefresh ? <IconCheckCircle height={14} width={14} /> : <IconCircleOutline height={14} width={14} />}
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                自动刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 错误状态展示区域 */}
        <div className="flex-1 flex items-center justify-center py-20">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="mb-6">
              <IconAlertCircle className="mx-auto text-red-500 mb-4" height={64} width={64} />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                加载失败
              </h3>
              <div className="text-gray-600 text-sm leading-relaxed mb-6">
                {error}
              </div>
            </div>
            
            <div className="space-y-3">
              <Button
                className="w-full transition-all duration-200"
                color="primary"
                size="md"
                startContent={<IconRefresh height={16} width={16} />}
                onClick={fetchWorkflowStatus}
              >
                重新加载
              </Button>
              <div className="text-xs text-gray-500">
                如果问题持续存在，请检查工作流ID或联系系统管理员
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!workflowStatus) {
    return (
      <div className="min-h-full bg-white p-6 flex flex-col">
        {/* 头部信息 - 保持结构一致 */}
        <div className="border-b border-gray-200 pb-4 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-gray-700">
                  <IconFileMultiple height={20} width={20} />
                </div>
                <h1 className="text-base font-semibold text-gray-800 m-0">
                  多页面代码生成工作流
                </h1>
              </div>
            </div>

            {/* 操作按钮区域 */}
            <div className="flex gap-3 items-start flex-shrink-0">
              <Button
                className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700 px-2 py-1 text-xs"
                size="sm"
                startContent={<IconRefresh height={14} width={14} />}
                onClick={fetchWorkflowStatus}
              >
                刷新状态
              </Button>

              <Button
                className={`transition-all duration-200 rounded-md border-1 dark:border-zinc-700 px-2 py-1 text-xs ${autoRefresh ? 'bg-green-600 text-white' : 'bg-transparent'}`}
                size="sm"
                startContent={autoRefresh ? <IconCheckCircle height={14} width={14} /> : <IconCircleOutline height={14} width={14} />}
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                自动刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 空状态展示区域 */}
        <div className="flex-1 flex items-center justify-center py-20">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="mb-6">
              <IconFileMultiple className="mx-auto text-gray-400 mb-4" height={64} width={64} />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                多页面工作流不存在
              </h3>
              <div className="text-gray-600 text-sm leading-relaxed mb-6">
                请检查工作流ID是否正确，或者该工作流可能已被删除
              </div>
            </div>
            
            <div className="space-y-3">
              <Button
                className="w-full transition-all duration-200"
                color="default"
                size="md"
                startContent={<IconRefresh height={16} width={16} />}
                onClick={fetchWorkflowStatus}
              >
                重新检查
              </Button>
              <div className="text-xs text-gray-500">
                您可以返回上一页或检查URL中的工作流ID
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusColor = getStatusColorClasses(workflowStatus.status);

  return (
    <div className="min-h-full bg-white p-6 flex flex-col">
      {/* 头部信息 */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="text-gray-700">
                <IconFileMultiple height={20} width={20}  />
              </div>
              <h1 className="text-base font-semibold text-gray-800 m-0">
                多页面代码生成工作流
              </h1>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-4">
              <div>
                <div className="text-xs text-gray-500 mb-1 uppercase tracking-wider font-medium">
                  工作流ID
                </div>
                <div className="text-sm font-mono text-gray-800 bg-gray-100 px-2 py-1.5 rounded border border-gray-200">
                  {workflowStatus.workflowId}
                </div>
              </div>

              <div>
                <div className="text-xs text-gray-500 mb-2 uppercase tracking-wider font-medium">
                  执行状态
                </div>
                <div className="flex items-center gap-2">
                  <div className={statusColor.icon}>
                    {getStatusIcon(workflowStatus.status)}
                  </div>
                  <span className={`text-sm font-medium px-2 py-1 rounded border ${statusColor.bg} ${statusColor.border} ${statusColor.text}`}>
                    {getStatusText(workflowStatus.status)}
                  </span>
                </div>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="text-xl font-bold text-gray-800 mb-1">
                  {workflowStatus.totalPages}
                </div>
                <div className="text-xs text-gray-500 font-medium">
                  总页面数
                </div>
              </div>

              <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="text-xl font-bold text-green-700 mb-1">
                  {workflowStatus.completedPages}
                </div>
                <div className="text-xs text-green-700 font-medium">
                  已完成
                </div>
              </div>

              <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                <div className="text-xl font-bold text-red-700 mb-1">
                  {workflowStatus.failedPages}
                </div>
                <div className="text-xs text-red-700 font-medium">
                  失败
                </div>
              </div>

              {/* 隐藏整体进度统计卡片，除非是processing状态 */}
              {workflowStatus.status === 'processing' && (
                <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className={`text-xl font-bold mb-1 ${statusColor.text}`}>
                    {workflowStatus.progress}%
                  </div>
                  <div className="text-xs text-gray-500 font-medium">
                    整体进度
                  </div>
                </div>
              )}
            </div>

            {/* 时间信息 */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <div className="text-xs text-gray-500 mb-1 uppercase tracking-wider font-medium">
                  开始时间
                </div>
                <div className="text-sm text-gray-800">
                  {workflowStatus.startTime ? new Date(workflowStatus.startTime).toLocaleString('zh-CN') : '-'}
                </div>
              </div>

              <div>
                <div className="text-xs text-gray-500 mb-1 uppercase tracking-wider font-medium">
                  结束时间
                </div>
                <div className="text-sm text-gray-800">
                  {workflowStatus.endTime ? new Date(workflowStatus.endTime).toLocaleString('zh-CN') : '-'}
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮区域 */}
          <div className="flex gap-3 items-start flex-shrink-0">
            {/* 重试工作流按钮 - 仅在失败或部分成功时显示 */}
            {(workflowStatus.status === 'failed' || workflowStatus.status === 'partial-success') && (
              <Button
                className="transition-all duration-200 rounded-md bg-transparent border-1 border-orange-500 text-orange-600 dark:border-orange-400 dark:text-orange-400 px-2 py-1 text-xs hover:bg-orange-50 dark:hover:bg-orange-900/20"
                disabled={retryingWorkflows.has(`multi-${workflowStatus.workflowId}`)}
                size="sm"
                startContent={
                  retryingWorkflows.has(`multi-${workflowStatus.workflowId}`) ? (
                    <IconLoading className="animate-spin" height={14} width={14} />
                  ) : (
                    <IconRestart height={14} width={14} />
                  )
                }
                onClick={restartMultiPageWorkflow}
              >
                {retryingWorkflows.has(`multi-${workflowStatus.workflowId}`) ? '重试中...' : '重试工作流'}
              </Button>
            )}

            {/* 刷新状态按钮 */}
            <Button
              className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700 px-2 py-1 text-xs"
              size="sm"
              startContent={<IconRefresh height={14} width={14} />}
              onClick={fetchWorkflowStatus}
            >
              刷新状态
            </Button>

            {/* 自动刷新切换按钮 */}
            <Button
              className={`transition-all duration-200 rounded-md border-1 dark:border-zinc-700 px-2 py-1 text-xs ${autoRefresh ? 'bg-green-600 text-white' : 'bg-transparent'}`}
              size="sm"
              startContent={autoRefresh ? <IconCheckCircle height={14} width={14} /> : <IconCircleOutline height={14} width={14} />}
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              自动刷新
            </Button>
          </div>
        </div>

        {/* 进度条 */}
        {/* 隐藏整体进度条，除非是processing状态 */}
        {workflowStatus.status === 'processing' && (
          <div className="mt-5">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-800">
                整体进度
              </span>
              <span className={`text-sm font-semibold ${statusColor.text}`}>
                {workflowStatus.progress}%
              </span>
            </div>
            <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full rounded-full transition-all duration-300 ${statusColor.progressBg}`}
                style={{ width: `${workflowStatus.progress}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 页面工作流列表 */}
      <div className="mt-4">
        <div className="py-2">
          <div className="flex items-center gap-2">
            <IconViewGrid className="text-gray-700" height={20} width={20} />
            <h2 className="text-base font-semibold text-gray-800 m-0">
              页面工作流详情
            </h2>
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full font-medium">
              {workflowStatus.pageWorkflows.length} 个页面
            </span>
          </div>
        </div>

        {/* 移除这里的滚动容器，让内容直接展示 */}
        <div>
          {workflowStatus.pageWorkflows.map((pageWorkflow, index) => {
            const pageStatusColor = getStatusColorClasses(pageWorkflow.status);

            return (
              <div
                key={pageWorkflow.pageId}
                className="py-4 bg-white border-b border-gray-200 last:border-b-0"
              >
                {/* 标题和按钮行 */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-3">
                    <div className={pageStatusColor.icon}>
                      {getStatusIcon(pageWorkflow.status)}
                    </div>
                    <h3 className="text-base font-semibold text-gray-800 m-0">
                      {pageWorkflow.pageName}
                    </h3>
                    <span className={`text-xs font-medium px-2 py-0.5 rounded-full border ${pageStatusColor.bg} ${pageStatusColor.border} ${pageStatusColor.text}`}>
                      {getStatusText(pageWorkflow.status)}
                    </span>
                  </div>

                  {/* 操作按钮区域 */}
                  <div className="flex gap-2 flex-shrink-0">
                    {/* 查看详情按钮 */}
                    {pageWorkflow.workflowId && (
                      <Button
                        className="transition-all duration-200"
                        size="sm"
                        variant="light"
                        onClick={(e: React.MouseEvent) => {
                          e.stopPropagation();
                          handleViewPageWorkflow(pageWorkflow);
                        }}
                      >
                        查看详情
                      </Button>
                    )}
                    
                    {/* 重试按钮 - 仅在页面失败时显示 */}
                    {pageWorkflow.status === 'failed' && pageWorkflow.workflowId && (
                      <Button
                        className="transition-all duration-200 rounded-md bg-transparent border-1 border-orange-500 text-orange-600 dark:border-orange-400 dark:text-orange-400 px-2 py-1 text-xs hover:bg-orange-50 dark:hover:bg-orange-900/20"
                        disabled={retryingWorkflows.has(`page-${pageWorkflow.pageId}`)}
                        size="sm"
                        startContent={
                          retryingWorkflows.has(`page-${pageWorkflow.pageId}`) ? (
                            <IconLoading className="animate-spin" height={12} width={12} />
                          ) : (
                            <IconRestart height={12} width={12} />
                          )
                        }
                        onClick={(e: React.MouseEvent) => {
                          e.stopPropagation();
                          restartPageWorkflow(pageWorkflow);
                        }}
                      >
                        {retryingWorkflows.has(`page-${pageWorkflow.pageId}`) ? '重试中...' : '重试'}
                      </Button>
                    )}
                  </div>
                </div>

                {/* 执行状态信息 - 从左到右占位 */}
                <div className="w-full">
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <div className="text-xs text-gray-500 mb-1 font-medium">
                        页面ID
                      </div>
                      <div className="text-sm font-mono text-gray-800 bg-gray-100 px-1.5 py-1 rounded border border-gray-200">
                        {pageWorkflow.pageId}
                      </div>
                    </div>

                    <div>
                      <div className="text-xs text-gray-500 mb-1 font-medium">
                        工作流ID
                      </div>
                      {pageWorkflow.workflowId ? (
                        <div 
                          className="text-sm font-mono text-blue-600 hover:text-blue-800 cursor-pointer underline bg-gray-100 px-1.5 py-1 rounded border border-gray-200"
                          onClick={() => {
                            // 点击工作流ID跳转到详情页面
                            const url = `/workflow/${projectId}/${pageWorkflow.workflowId}`;

                            window.open(url, '_blank');
                          }}
                        >
                          {pageWorkflow.workflowId}
                        </div>
                      ) : (
                        <div className="text-sm font-mono text-gray-800 bg-gray-100 px-1.5 py-1 rounded border border-gray-200">
                          -
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 进度条 */}
                  {/* 隐藏页面进度条，除非是processing状态 */}
                  {pageWorkflow.status === 'processing' && (
                    <div className="mb-2">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs text-gray-500 font-medium">
                          执行进度
                        </span>
                        <span className={`text-xs font-semibold ${pageStatusColor.text}`}>
                          {pageWorkflow.progress}%
                        </span>
                      </div>
                      <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className={`h-full rounded-full transition-all duration-300 ${pageStatusColor.progressBg}`}
                          style={{ width: `${pageWorkflow.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* 时间信息 */}
                  {pageWorkflow.startTime && (
                    <div>
                      <span className="text-xs text-gray-500 font-medium">
                        开始时间: 
                      </span>
                      <span className="text-xs text-gray-800 ml-2">
                        {new Date(pageWorkflow.startTime).toLocaleString('zh-CN')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export const Component = MultiPageWorkflowVisualization; 