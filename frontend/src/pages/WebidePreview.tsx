import React, { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';

const WebidePreview: React.FC = () => {
  const [searchParams] = useSearchParams();
  const initialUrl = searchParams.get('url') || '';

  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [inputUrl, setInputUrl] = useState(initialUrl);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 模拟多个预览地址
  const [previewSites] = useState<string[]>([
    initialUrl,
    initialUrl.replace(':8080', ':3000'),
    initialUrl.replace(':8080', ':5173'),
  ]);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 处理URL变化
  const handleUrlChange = (url: string) => {
    setCurrentUrl(url);
    setInputUrl(url);
    setIsLoading(true);
  };

  // 处理输入框提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputUrl.trim()) {
      handleUrlChange(inputUrl.trim());
    }
  };

  // 选择预设地址
  const handleSiteSelect = (url: string) => {
    handleUrlChange(url);
    setIsDropdownOpen(false);
  };

  // 刷新页面
  const handleRefresh = () => {
    if (iframeRef.current) {
      setIsLoading(true);
      iframeRef.current.src = currentUrl;
    }
  };

  // 切换全屏
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // 处理iframe加载完成
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // 处理iframe加载错误
  const handleIframeError = () => {
    setIsLoading(false);
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 监听点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`h-full w-full flex flex-col overflow-hidden ${isFullscreen ? 'fixed inset-0 z-50 h-screen' : ''}`}
    >
      {/* 浏览器顶部工具栏 - 固定高度 */}
      <div className="flex-none bg-gray-50 border-b border-gray-200">
        {/* 地址栏区域 */}
        <div className="flex items-center px-4 py-3 space-x-3">
          {/* 现代化地址栏组合 */}
          <form className="flex-1" onSubmit={handleSubmit}>
            <div ref={dropdownRef} className="relative">
              {/* 主地址栏容器 */}
              <div className="relative flex items-center bg-white border border-gray-300 rounded-lg shadow-sm hover:border-gray-400 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500/20 transition-all duration-200">
                {/* 地址输入框 */}
                <input
                  className={`flex-1 ${isLoading ? 'pl-12' : 'pl-5'} pr-2 py-2.5 bg-transparent border-0 rounded-lg text-sm text-gray-900 placeholder-gray-500 focus:outline-none`}
                  placeholder="输入预览地址..."
                  style={{
                    paddingLeft: '15px'
                  }}
                  type="url"
                  value={inputUrl}
                  onChange={(e) => setInputUrl(e.target.value)}
                />

                {/* 下拉箭头按钮 */}
                {previewSites.length > 1 && (
                  <button
                    className="flex-shrink-0 px-3 py-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition-colors"
                    type="button"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <svg
                      className={`w-4 h-4 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M19 9l-7 7-7-7" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                    </svg>
                  </button>
                )}
              </div>

              {/* 下拉菜单 */}
              {isDropdownOpen && previewSites.length > 1 && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-30 overflow-hidden">
                  {previewSites.map((url, index) => (
                    <button
                      key={url}
                      className="w-full px-4 py-3 text-left text-sm text-gray-700 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                      type="button"
                      onClick={() => handleSiteSelect(url)}
                    >
                      <div className="flex items-center space-x-3">
                        {/* 环境标识 */}
                        <div className={`w-2 h-2 rounded-full flex-shrink-0 ${index === 0 ? 'bg-green-500' :
                            index === 1 ? 'bg-blue-500' : 'bg-orange-500'
                          }`} />
                        {/* URL显示 */}
                        <span className="truncate font-mono text-xs">{url}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </form>

          {/* 右侧操作按钮组 */}
          <div className="flex items-center space-x-2">
            {/* 刷新按钮 */}
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 flex-shrink-0"
              title="刷新页面"
              onClick={handleRefresh}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
              </svg>
            </button>

            {/* 全屏按钮 */}
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 flex-shrink-0"
              title={isFullscreen ? "退出全屏" : "全屏显示"}
              onClick={toggleFullscreen}
            >
              {isFullscreen ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path d="M9 9V4.5M9 9H4.5M9 9l-5-5m11 11v4.5m0-4.5h4.5m-4.5 0l5 5M15 3h6v6M9 21H3v-6" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 预览内容区域 - 占满剩余空间 */}
      <div className="flex-1 bg-white relative overflow-hidden min-h-0">
        {currentUrl ? (
          <>
            {isLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                <div className="flex flex-col items-center space-y-4 text-gray-600">
                  <div className="relative">
                    <div className="w-10 h-10 border-3 border-gray-200 border-t-blue-500 rounded-full animate-spin" />
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">正在加载预览</div>
                    <div className="text-xs text-gray-500 mt-1">请稍候...</div>
                  </div>
                </div>
              </div>
            )}

            <iframe
              ref={iframeRef}
              className="w-full h-full border-0"
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              src={currentUrl}
              style={{ overflow: 'hidden' }}
              title="WebIDE Preview"
              onError={handleIframeError}
              onLoad={handleIframeLoad}
            />
          </>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400 bg-gray-50">
            <div className="text-center">
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-sm">
                <svg className="w-10 h-10 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">连接已断开</h3>
              <p className="text-sm text-gray-500 max-w-sm">
                请在地址栏中输入要预览的地址，或从下拉列表中选择预设地址
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WebidePreview; 