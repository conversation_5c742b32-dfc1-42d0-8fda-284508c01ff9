import { useNavigate, useParams } from "react-router-dom";
import { AiCoding } from "@/components/LowCode/AiCoding";
import { title } from "@/components/primitives";

export function Component() {
  const navigate = useNavigate();
  const param = useParams();

  return (
    <div>
      <h1 className={title()}>魔方组件展示</h1>
      <section className="flex flex-col items-center justify-center gap-4 py-10">
        <AiCoding
          id={param.id}
          onChange={() => {
            console.log('New content generated!');
          }}
          onCreate={({ id }) => navigate(`/low-code/${id}`, {
            replace: true,
          })}
        />
      </section>
    </div>
  );
}
