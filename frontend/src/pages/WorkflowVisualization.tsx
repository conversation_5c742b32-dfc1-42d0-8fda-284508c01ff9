import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Background,
  Controls,
  MiniMap,
  MarkerType,
  NodeMouseHandler,
  useReactFlow,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';

// 导入通用页面样式
import {
  LOADING_CONTAINER_STYLES,
  ERROR_CONTAINER_STYLES,
} from '@/utils/pageStyles';

// 导入MDI图标
import IconAlertCircle from '~icons/mdi/alert-circle';
import IconCheckCircle from '~icons/mdi/check-circle';
import IconChevronLeft from '~icons/mdi/chevron-left';
import IconChevronRight from '~icons/mdi/chevron-right';
import IconClockOutline from '~icons/mdi/clock-outline';
import IconCode from '~icons/mdi/code-braces';
import IconFileDocument from '~icons/mdi/file-document';
import IconInformation from '~icons/mdi/information';
import IconLoading from '~icons/mdi/loading';
import IconMerge from '~icons/mdi/merge';
import IconOpenInNew from '~icons/mdi/open-in-new';
import IconRefresh from '~icons/mdi/refresh';
import IconSkip from '~icons/mdi/skip-next';

interface WorkflowNode {
  id: string;
  name: string;
  type: 'lanhu-transcode' | 'merge' | 'spec-to-prod-code';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: string;
  endTime?: string;
  error?: string;
  taskId?: string;
  result?: any;
  dependencies: string[];
  taskGroupType?: 'lanhu-transcode-group' | 'merge-group' | 'spec-to-prod-code-group';
  taskGroupId?: string;
  taskGroupName?: string;
  taskGroupStatus?: string;
  taskGroupProgress?: number;
  playgroundId?: string; // 添加 playgroundId 字段
}

interface WorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  nodes: WorkflowNode[];
  startTime?: string;
  endTime?: string;
  error?: string;
}

// 研发平台风格的色系配置
const getNodeColor = (status: string) => {
  switch (status) {
    case 'pending':
      return '#64748b';
    case 'processing':
      return '#2563eb';
    case 'completed':
      return '#16a34a';
    case 'failed':
      return '#dc2626';
    case 'skipped':
      return '#d97706';
    default:
      return '#64748b';
  }
};

const getTaskGroupColor = (taskGroupType?: string, groupStatus?: string) => {
  // 如果任务组状态为失败，返回红色主题
  if (groupStatus === 'failed') {
    return {
      background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
      border: '#dc2626',
      shadow: '0 8px 24px rgba(220, 38, 38, 0.2)',
      light: '#fee2e2'
    };
  }

  switch (taskGroupType) {
    case 'lanhu-transcode-group':
      return {
        background: 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)',
        border: '#3b82f6',
        shadow: '0 8px 24px rgba(59, 130, 246, 0.2)',
        light: '#dbeafe'
      };
    case 'merge-group':
      return {
        background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
        border: '#22c55e',
        shadow: '0 8px 24px rgba(34, 197, 94, 0.2)',
        light: '#dcfce7'
      };
    case 'spec-to-prod-code-group':
      return {
        background: 'linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 100%)',
        border: '#a855f7',
        shadow: '0 8px 24px rgba(168, 85, 247, 0.2)',
        light: '#f3e8ff'
      };
    default:
      return {
        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
        border: '#64748b',
        shadow: '0 8px 24px rgba(100, 116, 139, 0.2)',
        light: '#f1f5f9'
      };
  }
};

const getNodeIcon = (type: string) => {
  const iconProps = { width: 16, height: 16 };
  
  switch (type) {
    case 'lanhu-transcode':
      return <IconFileDocument {...iconProps} />;
    case 'merge':
      return <IconMerge {...iconProps} />;
    case 'spec-to-prod-code':
      return <IconCode {...iconProps} />;
    default:
      return <IconFileDocument {...iconProps} />;
  }
};

const getStatusIcon = (status: string) => {
  const iconProps = { width: 16, height: 16 };
  
  switch (status) {
    case 'pending':
      return <IconClockOutline {...iconProps} />;
    case 'processing':
      return <IconLoading {...iconProps} className="animate-spin" />;
    case 'completed':
      return <IconCheckCircle {...iconProps} />;
    case 'failed':
      return <IconAlertCircle {...iconProps} />;
    case 'skipped':
      return <IconSkip {...iconProps} />;
    default:
      return <IconClockOutline {...iconProps} />;
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return '等待中';
    case 'processing':
      return '执行中';
    case 'completed':
      return '已完成';
    case 'failed':
      return '失败';
    case 'skipped':
      return '已跳过';
    default:
      return '未知';
  }
};

// 添加动画样式的CSS
const animatedEdgeStyle = `
  @keyframes flowAnimation {
    0% {
      stroke-dashoffset: 20;
    }
    100% {
      stroke-dashoffset: 0;
    }
  }
  
  .animated-edge {
    stroke-dasharray: 15 5;
    animation: flowAnimation 2s linear infinite;
  }
  
  .processing-edge {
    stroke-dasharray: 12 4;
    animation: flowAnimation 1.5s linear infinite;
    filter: drop-shadow(0 0 6px currentColor);
  }
  
  .pulse-node {
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes slideAnimation {
    0% { left: -100%; }
    100% { left: 100%; }
  }
  
  .clickable-node {
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .clickable-node:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  }
`;

// 内部工作流组件，使用useReactFlow hook
const WorkflowVisualizationInner: React.FC = () => {
  const { projectId, workflowId: urlWorkflowId } = useParams<{ projectId: string; workflowId?: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { fitView } = useReactFlow(); // 现在可以安全使用这个hook
  
  const workflowId = urlWorkflowId || searchParams.get('workflowId');

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [workflowStatus, setWorkflowStatus] = useState<WorkflowStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(() => {
    // 根据屏幕宽度决定默认状态
    return window.innerWidth < 1200;
  });

  // 注入动画样式
  useEffect(() => {
    const style = document.createElement('style');

    style.textContent = animatedEdgeStyle;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 获取工作流状态
  const fetchWorkflowStatus = useCallback(async () => {
    if (!projectId || !workflowId) return;

    try {
      const response = await fetch(`/api/design-project/${projectId}/workflows/${workflowId}/status`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data: WorkflowStatus = await response.json();

      setWorkflowStatus(data);
      setError(null);
    } catch (err) {
      console.error('获取工作流状态失败:', err);
      setError(err instanceof Error ? err.message : '获取工作流状态失败');
    } finally {
      setLoading(false);
    }
  }, [projectId, workflowId]);

  // 处理任务节点点击事件
  const handleNodeClick: NodeMouseHandler = useCallback((event, node) => {
    console.log('Node clicked:', node.id, 'Data:', node.data);
    
    // 只处理任务节点，不处理分组标题节点
    if (node.id.startsWith('group-header-')) {
      console.log('Ignoring group header click');

      return;
    }
    
    // 从node.data中获取playgroundId
    const playgroundId = node.data?.result?.playgroundId || node.data?.workflowNode?.result?.playgroundId;

    console.log('Found playgroundId:', playgroundId);
    
    if (playgroundId) {
      console.log('Navigating to chat:', playgroundId);
      // 改成新建tab页打开
      window.open(`/chat/${playgroundId}`, '_blank');
    } else {
      console.log('No playgroundId found for node:', node.id);
    }
  }, [navigate]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (workflowStatus?.status === 'processing') {
        fetchWorkflowStatus();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [fetchWorkflowStatus, autoRefresh, workflowStatus?.status]);

  // 初始加载
  useEffect(() => {
    fetchWorkflowStatus();
  }, [fetchWorkflowStatus]);

  // 保持原有的流程图生成方法，只更新颜色主题
  const generateMindMapFlowElements = useCallback((status: WorkflowStatus) => {
    if (!status.nodes || status.nodes.length === 0) {
      return { nodes: [], edges: [] };
    }

    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];

    // 按任务组分组节点
    const taskGroups = new Map<string, WorkflowNode[]>();

    status.nodes.forEach(node => {
      const groupKey = node.taskGroupType || 'unknown';

      if (!taskGroups.has(groupKey)) {
        taskGroups.set(groupKey, []);
      }
      taskGroups.get(groupKey)!.push(node);
    });

    // 定义任务组的显示顺序和配置
    const taskGroupConfig = {
      'lanhu-transcode-group': { 
        label: '设计稿转换', 
        icon: <IconFileDocument height={20} width={20} />, 
        color: '#3b82f6',
        order: 1,
        x: 200,
      },
      'merge-group': { 
        label: '代码合并', 
        icon: <IconMerge height={20} width={20} />, 
        color: '#22c55e',
        order: 2,
        x: 600,
      },
      'spec-to-prod-code-group': { 
        label: '按照应用规范生成代码', 
        icon: <IconCode height={20} width={20} />, 
        color: '#a855f7',
        order: 3,
        x: 1000,
      },
    };

    // 按顺序处理任务组
    const orderedGroups = Array.from(taskGroups.entries()).sort(([a], [b]) => {
      const orderA = taskGroupConfig[a as keyof typeof taskGroupConfig]?.order || 999;
      const orderB = taskGroupConfig[b as keyof typeof taskGroupConfig]?.order || 999;

      return orderA - orderB;
    });

    const config = {
      groupSpacing: 400, // 任务组之间的间距
      nodeSpacing: 120,  // 节点之间的间距
      groupHeaderHeight: 80, // 任务组标题高度
      nodeHeight: 100,   // 单个节点高度
      startY: 150,       // 起始Y坐标
    };

    // 为每个任务组创建节点和组标题
    orderedGroups.forEach(([groupType, groupNodes], groupIndex) => {
      const groupConfig = taskGroupConfig[groupType as keyof typeof taskGroupConfig];

      if (!groupConfig) return;

      const groupX = groupConfig.x;
      const groupStartY = config.startY;

      // 创建任务组标题（背景框）
      const groupHeaderY = groupStartY - config.groupHeaderHeight;
      // 获取任务组状态（从第一个节点获取）
      const firstNode = groupNodes[0];
      const groupStatus = firstNode?.taskGroupStatus || 'pending';
      
      const groupColors = getTaskGroupColor(groupType, groupStatus);
      
      newNodes.push({
        id: `group-header-${groupType}`,
        type: 'default',
        position: { x: groupX - 50, y: groupHeaderY },
        data: {
          label: (
            <div style={{
              padding: '20px 24px',
              textAlign: 'center',
              background: groupColors.background,
              border: `3px solid ${groupColors.border}`,
              borderRadius: '16px',
              boxShadow: groupColors.shadow,
              minWidth: '200px',
              position: 'relative',
              overflow: 'hidden',
            }}>
              {/* 添加处理中的动态背景效果 */}
              {groupStatus === 'processing' && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: `linear-gradient(90deg, transparent, ${groupColors.border}20, transparent)`,
                  animation: 'slideAnimation 2s infinite',
                }} />
              )}
              <div style={{ 
                fontSize: '16px', 
                fontWeight: '700', 
                color: groupColors.border,
                marginBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                position: 'relative',
                zIndex: 1,
              }}>
                <div style={{ color: groupColors.border }}>
                  {groupConfig.icon}
                </div>
                <span>{groupConfig.label}</span>
              </div>
              
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
                marginBottom: '12px',
                position: 'relative',
                zIndex: 1,
              }}>
                <div style={{ color: groupColors.border }}>
                  {getStatusIcon(groupStatus)}
                </div>
                <span style={{
                  fontSize: '14px',
                  color: groupColors.border,
                  fontWeight: '600',
                }}>
                  {getStatusText(groupStatus)}
                </span>
              </div>
              
              <div style={{ 
                fontSize: '14px', 
                color: groupColors.border,
                fontWeight: '600',
                marginBottom: '8px',
                position: 'relative',
                zIndex: 1,
              }}>
                {groupNodes.filter(n => n.status === 'completed').length}/{groupNodes.length} 已完成
              </div>
              
              {/* 进度条 */}
              <div style={{
                width: '100%',
                height: '6px',
                backgroundColor: 'rgba(255,255,255,0.3)',
                borderRadius: '3px',
                overflow: 'hidden',
                position: 'relative',
                zIndex: 1,
              }}>
                <div style={{
                  width: `${(groupNodes.filter(n => n.status === 'completed').length / groupNodes.length) * 100}%`,
                  height: '100%',
                  background: `linear-gradient(90deg, ${getNodeColor('completed')}, ${groupColors.border})`,
                  borderRadius: '3px',
                  transition: 'width 0.5s ease',
                }} />
              </div>
            </div>
          ),
        },
        draggable: false,
        selectable: false,
        style: {
          background: 'transparent',
          border: 'none',
        },
      });

      // 为任务组中的每个节点创建可视化节点
      groupNodes.forEach((node, nodeIndex) => {
        const nodeY = groupStartY + nodeIndex * config.nodeSpacing;
        const nodeColors = getTaskGroupColor(groupType, node.taskGroupStatus);
        const hasPlaygroundId = !!node.playgroundId;
        
        // 创建任务节点
        const taskNode: Node = {
          id: node.id,
          type: 'default',
          position: { 
            x: groupX, 
            y: nodeY 
          },
          data: {
            // 将完整的工作流节点信息保存到data中
            workflowNode: node,
            playgroundId: node.playgroundId,
            label: (
              <div 
                className={`${node.status === 'processing' ? 'pulse-node' : ''} ${hasPlaygroundId ? 'clickable-node' : ''}`}
                style={{
                  textAlign: 'left',
                  padding: '12px 16px',
                  fontSize: '13px',
                  lineHeight: '1.4',
                  background: nodeColors.background,
                  border: `2px solid ${nodeColors.border}`,
                  borderRadius: '8px',
                  boxShadow: nodeColors.shadow,
                  minWidth: '180px',
                  position: 'relative',
                  overflow: 'hidden',
                  transition: 'all 0.3s ease',
                  cursor: hasPlaygroundId ? 'pointer' : 'default',
                  // 为可点击节点添加更明显的样式提示
                  ...(hasPlaygroundId && {
                    border: `3px solid ${nodeColors.border}`,
                    transform: 'scale(1.01)',
                  }),
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = hasPlaygroundId ? 'scale(1.03)' : 'scale(1.02)';
                  e.currentTarget.style.boxShadow = `0 6px 20px ${nodeColors.border}40`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = hasPlaygroundId ? 'scale(1.01)' : 'scale(1)';
                  e.currentTarget.style.boxShadow = nodeColors.shadow;
                }}
              >
                {/* 左侧状态指示条 */}
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    bottom: 0,
                    width: '4px',
                    background: getNodeColor(node.status),
                  }}
                />
                
                {/* 处理中状态的动态效果 */}
                {node.status === 'processing' && (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent)',
                    animation: 'slideAnimation 1.5s infinite',
                  }} />
                )}
                
                <div style={{ position: 'relative', zIndex: 1, paddingLeft: '8px' }}>
                  <div style={{ 
                    fontWeight: '600', 
                    marginBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    fontSize: '14px',
                    color: '#1f2937',
                    lineHeight: '1.3',
                  }}>
                    <div style={{ color: nodeColors.border }}>
                      {getNodeIcon(node.type)}
                    </div>
                    <span style={{ flex: 1 }}>
                      {node.name.replace(/^[^-]+-\s*/, '')}
                    </span>
                    {/* 显示跳转图标 */}
                    {hasPlaygroundId && (
                      <div style={{ 
                        color: nodeColors.border,
                        opacity: 0.7,
                        display: 'flex',
                        alignItems: 'center',
                      }}>
                        <IconOpenInNew height={12} width={12} />
                      </div>
                    )}
                  </div>
                  
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    gap: '8px',
                    marginBottom: '8px',
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                    }}>
                      <div style={{ color: getNodeColor(node.status) }}>
                        {getStatusIcon(node.status)}
                      </div>
                      <span style={{
                        fontSize: '12px',
                        color: getNodeColor(node.status),
                        fontWeight: '500',
                      }}>
                        {getStatusText(node.status)}
                      </span>
                    </div>
                    
                    <span style={{
                      fontSize: '12px',
                      fontWeight: '600',
                      color: getNodeColor(node.status),
                    }}>
                      {node.progress}%
                    </span>
                  </div>

                  {/* 进度条 */}
                  {node.status !== 'pending' && (
                    <div style={{
                      width: '100%',
                      height: '4px',
                      backgroundColor: 'rgba(0,0,0,0.1)',
                      borderRadius: '2px',
                      overflow: 'hidden',
                    }}>
                      <div style={{
                        width: `${node.progress}%`,
                        height: '100%',
                        backgroundColor: getNodeColor(node.status),
                        borderRadius: '2px',
                        transition: 'width 0.3s ease',
                      }} />
                    </div>
                  )}

                  {/* 时间信息和跳转提示 */}
                  <div style={{ 
                    fontSize: '10px', 
                    color: '#6b7280', 
                    marginTop: '6px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                    {node.startTime && (
                      <span>
                        {node.endTime ? '✅' : '⏳'} {new Date(node.startTime).toLocaleTimeString()}
                      </span>
                    )}
                    {hasPlaygroundId && (
                      <span style={{ 
                        color: nodeColors.border,
                        fontWeight: '500',
                      }}>
                        点击查看对话
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ),
          },
          style: {
            background: 'transparent',
            border: 'none',
          },
        };

        newNodes.push(taskNode);
      });
    });

    // 创建依赖关系的连接线
    status.nodes.forEach((node) => {
      node.dependencies.forEach((depId) => {
        const depNode = status.nodes.find(n => n.id === depId);

        if (depNode) {
          // 确定连接线的样式
          const isProcessing = node.status === 'processing' || depNode.status === 'processing';
          const isCompleted = depNode.status === 'completed';
          
          const edgeStyle: any = {
            stroke: isCompleted ? '#16a34a' : isProcessing ? '#2563eb' : '#64748b',
            strokeWidth: isCompleted ? 4 : isProcessing ? 4 : 3,
          };

          let edgeClassName = '';
          
          if (isProcessing) {
            edgeStyle.strokeDasharray = '12,4';
            edgeClassName = 'processing-edge';
          } else if (isCompleted) {
            edgeStyle.strokeDasharray = '0';
          } else {
            edgeStyle.strokeDasharray = '15,5';
            edgeClassName = 'animated-edge';
          }

          const edge: Edge = {
            id: `dep-${depId}-${node.id}`,
            source: depId,
            target: node.id,
            type: 'smoothstep',
            style: edgeStyle,
            className: edgeClassName,
            markerEnd: {
              type: MarkerType.ArrowClosed,
              color: edgeStyle.stroke,
              width: 20,
              height: 20,
            },
            label: isCompleted ? '✅' : isProcessing ? '⚡' : '⏳',
            labelStyle: { 
              fontSize: '12px',
              fontWeight: 'bold',
              fill: edgeStyle.stroke,
            },
            labelBgStyle: { 
              fill: 'white',
              fillOpacity: 0.9,
            },
            animated: isProcessing,
          };

          newEdges.push(edge);
        }
      });
    });

    return { nodes: newNodes, edges: newEdges };
  }, [handleNodeClick]);

  // 注入滑动动画样式
  useEffect(() => {
    const slideAnimationStyle = `
      @keyframes slideAnimation {
        0% { left: -100%; }
        100% { left: 100%; }
      }
    `;
    
    const existingStyle = document.getElementById('slide-animation-style');

    if (!existingStyle) {
      const style = document.createElement('style');

      style.id = 'slide-animation-style';
      style.textContent = slideAnimationStyle;
      document.head.appendChild(style);
    }
  }, []);

  // 更新React Flow节点和边
  useEffect(() => {
    if (workflowStatus) {
      const { nodes: newNodes, edges: newEdges } = generateMindMapFlowElements(workflowStatus);

      setNodes(newNodes);
      setEdges(newEdges);
    }
  }, [workflowStatus, generateMindMapFlowElements]);

  // 监听面板折叠状态变化，重新调整视图
  useEffect(() => {
    // 移除复杂的viewport调整逻辑，让ReactFlow自然处理fitView
  }, [isPanelCollapsed]);

  // 监听窗口大小变化，自适应调整流程图
  useEffect(() => {
    const handleResize = () => {
      // 延迟执行 fitView，确保布局完成后再调整
      setTimeout(() => {
        fitView({
          padding: 0.15,
          includeHiddenNodes: false,
          maxZoom: 1.0,
          minZoom: 0.2,
        });
      }, 300);
    };

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
    
    // 监听浏览器缩放变化
    window.addEventListener('orientationchange', handleResize);
    
    // 使用 ResizeObserver 监听更精确的容器大小变化
    let resizeObserver: ResizeObserver | null = null;
    const container = document.querySelector('[data-testid="rf__wrapper"]');

    if (container && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(container);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [fitView]);

  // 面板展开/收起时自适应调整流程图
  useEffect(() => {
    // 延迟执行，等待动画完成
    const timer = setTimeout(() => {
      fitView({
        padding: 0.15,
        includeHiddenNodes: false,
        maxZoom: 1.0,
        minZoom: 0.2,
      });
    }, 450); // 略大于动画时间

    return () => clearTimeout(timer);
  }, [isPanelCollapsed, fitView]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds: Edge[]) => addEdge(params, eds)),
    [setEdges]
  );

  // 根据工作流状态渲染内容
  if (loading) {
    return (
      <div style={LOADING_CONTAINER_STYLES}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '3px solid #f3f3f3',
            borderTop: '3px solid #2563eb',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
          }} />
          <span style={{ color: '#6b7280', fontSize: '14px' }}>加载工作流数据...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={ERROR_CONTAINER_STYLES}>
        <div style={{
          background: 'white',
          padding: '32px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          textAlign: 'center',
          maxWidth: '400px',
        }}>
          <div style={{
            color: '#dc2626',
            marginBottom: '16px',
            fontSize: '16px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
          }}>
            <IconAlertCircle height={20} width={20} />
            加载失败
          </div>
          <div style={{
            color: '#6b7280',
            marginBottom: '24px',
            fontSize: '14px',
            lineHeight: '1.5',
          }}>
            {error}
          </div>
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              margin: '0 auto',
            }}
            onClick={fetchWorkflowStatus}
          >
            <IconRefresh height={16} width={16} />
            重试
          </button>
        </div>
      </div>
    );
  }

  if (!workflowStatus) {
    return (
      <div style={ERROR_CONTAINER_STYLES}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '16px',
          color: '#6b7280',
        }}>
          <IconInformation height={48} width={48} />
          <div style={{ fontSize: '16px', fontWeight: '500' }}>工作流不存在</div>
          <div style={{ fontSize: '14px' }}>请检查工作流ID是否正确</div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%', position: 'relative' }}>
      {/* React Flow 思维导图 - 占满整个容器 */}
      <ReactFlow
        fitView
        attributionPosition="bottom-right"
        defaultViewport={{ x: 0, y: 0, zoom: 0.6 }}
        edges={edges}
        fitViewOptions={{ 
          padding: 0.15, 
          includeHiddenNodes: false,
          maxZoom: 1.0,
          minZoom: 0.2,
        }}
        maxZoom={2}
        minZoom={0.1}
        nodes={nodes}
        style={{
          width: '100%',
          height: '100%',
        }}
        onConnect={onConnect}
        onEdgesChange={onEdgesChange}
        onNodeClick={handleNodeClick}
        onNodesChange={onNodesChange}
      >
        <Background 
          color="#f1f5f9" 
          gap={40} 
          size={1.5}
        />
        <Controls 
          position="bottom-left"
          showInteractive={false}
          style={{
            background: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          }}
        />
        <MiniMap 
          pannable
          zoomable
          nodeColor={(node) => {
            // 为思维导图节点使用不同颜色
            if (node.id.startsWith('group-header-')) {
              if (node.id.includes('lanhu-transcode')) return '#3b82f6';
              if (node.id.includes('merge')) return '#22c55e';
              if (node.id.includes('spec-to-prod-code')) return '#a855f7';
            }
            // 叶子节点使用状态颜色
            const workflowNode = workflowStatus?.nodes.find(n => n.id === node.id);

            return workflowNode ? getNodeColor(workflowNode.status) : '#64748b';
          }}
          nodeStrokeWidth={2}
          position="bottom-right"
          style={{
            background: 'white',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          }}
        />
      </ReactFlow>

      {/* 悬浮的工作流概览面板 */}
      <div
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          zIndex: 1000,
          width: '300px',
          maxHeight: 'calc(100vh - 60px)',
          background: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: '1px solid #e2e8f0',
          transform: isPanelCollapsed ? 'translateX(-280px)' : 'translateX(0)',
          transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          overflow: 'hidden',
        }}
      >
        {/* 面板内容 */}
        <div
          style={{
            padding: '16px',
            overflowY: 'auto',
            maxHeight: 'calc(100vh - 120px)',
            opacity: isPanelCollapsed ? 0 : 1,
            transition: 'opacity 0.3s ease-in-out 0.1s',
          }}
        >
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            marginBottom: '16px',
          }}>
            <div style={{ color: '#374151' }}>
              <IconFileDocument height={20} width={20} />
            </div>
            <h2 style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: '600',
              color: '#1f2937',
            }}>
              工作流概览
            </h2>
          </div>

          <div style={{ marginBottom: '12px' }}>
            <div style={{
              fontSize: '11px',
              color: '#6b7280',
              marginBottom: '4px',
              fontWeight: '500',
            }}>
              工作流ID
            </div>
            <div style={{
              fontSize: '12px',
              fontFamily: 'Monaco, Consolas, monospace',
              color: '#374151',
              backgroundColor: '#f8fafc',
              padding: '6px 8px',
              borderRadius: '4px',
              border: '1px solid #e2e8f0',
              wordBreak: 'break-all',
            }}>
              {workflowStatus.workflowId}
            </div>
          </div>

          <div style={{ marginBottom: '12px' }}>
            <div style={{
              fontSize: '11px',
              color: '#6b7280',
              marginBottom: '6px',
              fontWeight: '500',
            }}>
              执行状态
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}>
              <div style={{ color: getNodeColor(workflowStatus.status) }}>
                {getStatusIcon(workflowStatus.status)}
              </div>
              <span style={{
                fontSize: '13px',
                fontWeight: '600',
                color: getNodeColor(workflowStatus.status),
              }}>
                {getStatusText(workflowStatus.status)}
              </span>
            </div>
          </div>

          {/* <div style={{ marginBottom: '12px' }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '6px',
            }}>
              <span style={{
                fontSize: '11px',
                color: '#6b7280',
                fontWeight: '500',
              }}>
                整体进度
              </span>
              <span style={{
                fontSize: '14px',
                fontWeight: '700',
                color: '#374151',
              }}>
                {workflowStatus.progress}%
              </span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#f1f5f9',
              borderRadius: '4px',
              overflow: 'hidden',
            }}>
              <div style={{
                width: `${workflowStatus.progress}%`,
                height: '100%',
                background: `linear-gradient(90deg, ${getNodeColor(workflowStatus.status)}, ${getNodeColor(workflowStatus.status)}dd)`,
                borderRadius: '4px',
                transition: 'width 0.5s ease',
              }} />
            </div>
          </div> */}

          <div style={{ marginBottom: '16px' }}>
            <div style={{
              fontSize: '11px',
              color: '#6b7280',
              marginBottom: '4px',
              fontWeight: '500',
            }}>
              节点数量
            </div>
            <div style={{ fontSize: '14px', fontWeight: '600', color: '#374151' }}>
              {workflowStatus.nodes.length}
            </div>
          </div>

          {/* 交互提示 */}
          <div style={{
            backgroundColor: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '6px',
            padding: '10px',
            marginBottom: '12px',
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              marginBottom: '4px',
            }}>
              <IconOpenInNew height={14} style={{ color: '#0ea5e9' }} width={14} />
              <span style={{
                fontSize: '11px',
                fontWeight: '600',
                color: '#0ea5e9',
              }}>
                交互提示
              </span>
            </div>
            <div style={{
              fontSize: '11px',
              color: '#0369a1',
              lineHeight: '1.4',
            }}>
              点击带有跳转图标的任务节点可以查看对应的Chat对话详情
            </div>
          </div>

          {workflowStatus.startTime && (
            <div style={{ marginBottom: '10px' }}>
              <div style={{
                fontSize: '10px',
                color: '#6b7280',
                marginBottom: '2px',
                fontWeight: '500',
              }}>
                开始时间
              </div>
              <div style={{ fontSize: '11px', color: '#374151' }}>
                {new Date(workflowStatus.startTime).toLocaleString()}
              </div>
            </div>
          )}

          {workflowStatus.endTime && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{
                fontSize: '10px',
                color: '#6b7280',
                marginBottom: '2px',
                fontWeight: '500',
              }}>
                结束时间
              </div>
              <div style={{ fontSize: '11px', color: '#374151' }}>
                {new Date(workflowStatus.endTime).toLocaleString()}
              </div>
            </div>
          )}

          <div style={{ display: 'flex', gap: '8px', marginTop: '12px' }}>
            <button
              style={{
                flex: 1,
                padding: '8px 12px',
                backgroundColor: '#2563eb',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: '600',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '4px',
                transition: 'all 0.2s ease',
              }}
              onClick={fetchWorkflowStatus}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#1d4ed8';
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = '#2563eb';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <IconRefresh height={14} width={14} />
              刷新
            </button>

            <button
              style={{
                flex: 1,
                padding: '8px 12px',
                backgroundColor: autoRefresh ? '#16a34a' : '#f8fafc',
                color: autoRefresh ? 'white' : '#6b7280',
                border: autoRefresh ? 'none' : '1px solid #e2e8f0',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: '600',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '4px',
                transition: 'all 0.2s ease',
              }}
              onClick={() => setAutoRefresh(!autoRefresh)}
              onMouseEnter={(e) => {
                if (autoRefresh) {
                  e.currentTarget.style.backgroundColor = '#15803d';
                } else {
                  e.currentTarget.style.backgroundColor = '#f1f5f9';
                }
                e.currentTarget.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = autoRefresh ? '#16a34a' : '#f8fafc';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              {autoRefresh ? (
                <IconCheckCircle height={14} width={14} />
              ) : (
                <IconClockOutline height={14} width={14} />
              )}
              自动刷新
            </button>
          </div>
        </div>
      </div>

      {/* 完美贴合的折叠/展开按钮 */}
      <button
        style={{
          position: 'absolute',
          top: '32px',
          left: isPanelCollapsed ? '32px' : '312px', // 调整位置以匹配新的面板宽度(20+300-8=312px)
          width: '36px',
          height: '36px',
          background: 'white',
          border: '1px solid #e2e8f0',
          borderRadius: '18px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
          color: '#6b7280',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          zIndex: 1001,
          backdropFilter: 'blur(8px)',
          WebkitBackdropFilter: 'blur(8px)',
        }}
        title={isPanelCollapsed ? '展开工作流概览' : '收起工作流概览'}
        onClick={() => setIsPanelCollapsed(!isPanelCollapsed)}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#f8fafc';
          e.currentTarget.style.color = '#374151';
          e.currentTarget.style.transform = 'scale(1.08)';
          e.currentTarget.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.16)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = 'white';
          e.currentTarget.style.color = '#6b7280';
          e.currentTarget.style.transform = 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.12)';
        }}
      >
        {isPanelCollapsed ? <IconChevronRight height={18} width={18} /> : <IconChevronLeft height={18} width={18} />}
      </button>
    </div>
  );
};

// 主组件，提供ReactFlow Provider
const WorkflowVisualization: React.FC = () => {
  return (
    <ReactFlowProvider>
      <WorkflowVisualizationInner />
    </ReactFlowProvider>
  );
};

export default WorkflowVisualization;
export const Component = WorkflowVisualization; 