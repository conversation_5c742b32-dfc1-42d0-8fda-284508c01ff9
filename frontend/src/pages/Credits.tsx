import { <PERSON>, CardBody, CardHeader, <PERSON>, <PERSON><PERSON>, Spinner } from '@heroui/react';
import { addToast } from '@heroui/toast';
import { useState, useEffect } from 'react';
import { getCredits } from '@/apis';
import IconAlert from '~icons/mdi/alert-circle-outline';
import IconCreditCard from '~icons/mdi/credit-card';
import IconRefresh from '~icons/mdi/refresh';

interface CreditInfo {
  provider: string;
  label: string;
  credit: number | string;
  totalCredits?: number | string;
  totalUsage?: number | string;
  currency: string;
  status: 'active' | 'error';
  error?: string;
}

// 格式化货币显示
const formatCurrency = (amount: number | string, currency: string) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numAmount)) return 'N/A';
  
  if (currency === 'USD') {
    return `$${numAmount.toFixed(2)}`;
  } else if (currency === 'CNY') {
    return `¥${numAmount.toFixed(2)}`;
  }

  return `${numAmount.toFixed(2)} ${currency}`;
};

// 获取货币符号
const getCurrencySymbol = (currency: string) => {
  if (currency === 'USD') return '$';
  if (currency === 'CNY') return '¥';

  return currency;
};

// 将余额转换为美元等值进行统一比较
const convertToUSD = (amount: number | string, currency: string) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numAmount)) return 0;
  
  // 简单汇率转换 (实际项目中可以使用实时汇率API)
  if (currency === 'CNY') {
    return numAmount / 7; // 1 USD ≈ 7 CNY
  }

  return numAmount; // USD
};

export function Component() {
  const [credits, setCredits] = useState<CreditInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchCredits = async () => {
    setLoading(true);
    try {
      const data = await getCredits();

      // 确保返回的数据是数组
      if (Array.isArray(data)) {
        setCredits(data);
      } else {
        console.error('API返回的数据不是数组:', data);
        setCredits([]);
        addToast({
          color: 'warning',
          title: '数据格式错误',
          description: 'API返回的数据格式不正确',
        });
      }
    } catch (error: any) {
      
      setCredits([]);
      addToast({
        color: 'danger',
        title: '获取余额失败',
        description: error?.message || '网络错误',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCredits();
  }, []);

  const formatCredit = (credit: number, currency: string) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency === 'USD' ? 'USD' : 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4,
    }).format(credit);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'error':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <IconCreditCard className="text-2xl text-primary" />
            <h1 className="text-2xl font-bold">模型余额</h1>
          </div>
          <Button
            color="primary"
            isLoading={loading}
            startContent={<IconRefresh />}
            variant="flat"
            onPress={fetchCredits}
          >
            刷新余额
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {credits.map((creditInfo) => (
            <Card key={creditInfo.provider} className="w-full">
              <CardHeader className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="flex flex-col">
                    <h4 className="text-lg font-semibold">{creditInfo.label}</h4>
                    <p className="text-small text-default-500">{creditInfo.provider}</p>
                  </div>
                </div>
                <Chip
                  color={getStatusColor(creditInfo.status)}
                  size="sm"
                  variant="flat"
                >
                  {creditInfo.status === 'active' ? '正常' : '异常'}
                </Chip>
              </CardHeader>
              <CardBody>
                {creditInfo.status === 'active' ? (
                  <div className="flex flex-col gap-2">
                    <div className="text-center">
                      <p className="text-small text-default-500">剩余余额</p>
                      {/* 大字显示剩余额度 */}
                      <p className="text-2xl font-bold text-primary">
                        {formatCurrency(creditInfo.credit, creditInfo.currency)}
                      </p>
                      {/* 小字显示总额和已使用 */}
                      {creditInfo.totalCredits !== undefined && creditInfo.totalUsage !== undefined && (
                        <p className="text-xs text-default-500 mt-1">
                          总额: {formatCurrency(creditInfo.totalCredits, creditInfo.currency)} | 
                          已用: {formatCurrency(creditInfo.totalUsage, creditInfo.currency)}
                        </p>
                      )}
                    </div>
                    {convertToUSD(creditInfo.credit, creditInfo.currency) < 5 && (
                                              <div className="flex items-center gap-2 p-2 bg-warning-50 rounded-lg">
                          <IconAlert className="text-warning" />
                          <span className="text-small text-warning-700">
                            余额不足（低于5美元等值），请及时充值
                          </span>
                        </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2 p-2 bg-danger-50 rounded-lg">
                      <IconAlert className="text-danger" />
                      <span className="text-small text-danger-700">
                        {creditInfo.error || '获取余额失败'}
                      </span>
                    </div>
                    <p className="text-small text-default-500">
                      请检查API密钥配置或网络连接
                    </p>
                  </div>
                )}
              </CardBody>
            </Card>
          ))}
        </div>

        {credits.length === 0 && !loading && (
          <Card className="w-full">
            <CardBody className="text-center py-8">
              <IconCreditCard className="mx-auto h-16 w-16 text-default-400 mb-4" />
              <h3 className="text-xl font-medium text-default-600 mb-3">暂无配置的模型提供商</h3>
              <p className="text-default-500 mb-6">
                请配置以下环境变量来启用外网模型余额查询：
              </p>
              <div className="max-w-md mx-auto space-y-3 text-left bg-default-100 rounded-lg p-4">
                <div className="font-mono text-sm space-y-2">
                  <div className="text-default-600">
                    <span className="text-primary font-medium">OPENROUTER_API_KEY</span>=your_openrouter_key
                  </div>
                  <div className="text-default-600">
                    <span className="text-primary font-medium">SILICONFLOW_API_KEY</span>=your_siliconflow_key
                  </div>
                </div>
              </div>
              <p className="text-sm text-default-400 mt-6">
                配置环境变量后重启服务即可查看余额信息
              </p>
            </CardBody>
          </Card>
        )}

        {loading && credits.length === 0 && (
          <div className="flex justify-center py-8">
            <Spinner size="lg" />
          </div>
        )}

        <Card className="w-full">
          <CardHeader>
            <h3 className="text-lg font-semibold">使用说明</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4 text-small">
              <div>
                <h4 className="font-medium mb-2">支持的模型提供商：</h4>
                <ul className="list-disc list-inside space-y-1 text-default-600">
                  <li><strong>OpenRouter</strong>：支持多种大型语言模型的API服务<br/>
                      <span className="text-xs text-default-500">API地址：</span>
                      <a 
                        className="text-xs text-primary hover:text-primary-600 underline" 
                        href="https://openrouter.ai" 
                        rel="noopener noreferrer"
                        target="_blank"
                      >
                        https://openrouter.ai
                      </a>
                  </li>
                  <li><strong>SiliconFlow</strong>：国内AI模型服务平台<br/>
                      <span className="text-xs text-default-500">API地址：</span>
                      <a 
                        className="text-xs text-primary hover:text-primary-600 underline" 
                        href="https://api.siliconflow.cn" 
                        rel="noopener noreferrer"
                        target="_blank"
                      >
                        https://api.siliconflow.cn
                      </a>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">余额：</h4>
                <ul className="list-disc list-inside space-y-1 text-default-600">
                  <li>余额信息每次访问时自动刷新</li>
                  <li>余额不足时会显示警告提醒</li>
                  <li>请及时关注余额变化，避免影响模型使用</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
  );
} 