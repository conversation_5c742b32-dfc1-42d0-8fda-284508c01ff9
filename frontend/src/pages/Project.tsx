import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { title } from "@/components/primitives";
import { ProjectForm } from "@/components/Project/Form";
import { ProjectList } from "@/components/Project/List";
import { useProjectStore } from "@/stores/project";

export function Component() {
  const id = useParams().id;
  const { setCurrent } = useProjectStore();

  useEffect(() => {
    setCurrent(id);
  }, [id]);

  return (
    <div>
      <h1 className={title()}>项目</h1>
      <section className="flex flex-row gap-2 py-10">
        <ProjectList />
        <ProjectForm className="flex-1" />
      </section>
    </div>
  );
}
