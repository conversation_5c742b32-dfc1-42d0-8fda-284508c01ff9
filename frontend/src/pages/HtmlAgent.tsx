import { Card, CardBody, CardHeader, Button, Textarea, Chip, Divider, Progress, Switch } from '@heroui/react';
import React, { useState, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { createPlayground } from '@/apis';
import { ModelSelector } from '@/components/ModelSelector';
import { useUserInfoStore } from '@/hooks/login';
import IconBroom from '~icons/mdi/broom';
import IconCheck from '~icons/mdi/check';
import IconFileDocument from '~icons/mdi/file-document';
import IconLanguageCss3 from '~icons/mdi/language-css3';
import IconLanguageHtml5 from '~icons/mdi/language-html5';
import IconNumeric1Box from '~icons/mdi/numeric-1-box';
import IconNumeric2Box from '~icons/mdi/numeric-2-box';
import IconNumeric3Box from '~icons/mdi/numeric-3-box';
import IconNumeric4Box from '~icons/mdi/numeric-4-box';
import IconRocket from '~icons/mdi/rocket-launch';
import IconUpload from '~icons/mdi/upload';

// 扩展Window类型，添加分步执行相关属性
declare global {
  interface Window {
    __remainingSteps?: Array<{title: string; content: string; stepNumber?: number}>;
    __currentStepIndex?: number;
    __totalSteps?: number;
  }
}

// 解析步骤文档的函数 - 改进版，支持复杂的markdown结构
function parseStepsFromMarkdown(content: string): Array<{
  title: string;
  content: string;
  stepNumber?: number;
}> {
  if (!content.trim()) return [];
  
  const lines = content.split('\n');
  const steps: Array<{title: string; content: string; stepNumber?: number}> = [];
  let currentStep: {title: string; content: string; stepNumber?: number} | null = null;
  let inStep = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();
    
    // 检测步骤标题的多种格式
    const stepMatch = trimmedLine.match(/^#{2,4}\s*步骤\s*(\d+)[：:]\s*(.+)/i) ||
                     trimmedLine.match(/^#{2,4}\s*第(\d+)步[：:]\s*(.+)/i) ||
                     trimmedLine.match(/^#{2,4}\s*(\d+)[\.、]\s*(.+)/i);
    
    if (stepMatch) {
      // 保存上一个步骤
      if (currentStep) {
        steps.push(currentStep);
      }
      
      // 开始新步骤
      const stepNumber = parseInt(stepMatch[1]);
      const stepTitle = stepMatch[2] || stepMatch[1];
      
      currentStep = {
        title: stepTitle.trim(),
        content: line + '\n',
        stepNumber: stepNumber
      };
      inStep = true;
    } else if (inStep && currentStep) {
      // 检查是否遇到新的主要章节（一级或二级标题，但不是步骤）
      const isNewSection = trimmedLine.match(/^#{1,2}\s+[^步第\d]/) && 
                          !trimmedLine.match(/步骤|第\d+步|\d+[\.、]/);
      
      if (isNewSection) {
        // 如果遇到新的主要章节，结束当前步骤
        if (currentStep.content.trim()) {
          steps.push(currentStep);
        }
        currentStep = null;
        inStep = false;
      } else {
        // 继续添加到当前步骤
        currentStep.content += line + '\n';
      }
    }
  }
  
  // 添加最后一个步骤
  if (currentStep && currentStep.content.trim()) {
    steps.push(currentStep);
  }
  
  // 如果没有找到明确的步骤标题，尝试按照章节分割
  if (steps.length === 0) {
    return parseByHeadings(content);
  }
  
  return steps.filter(step => step.content.trim().length > 0);
}

// 备用解析方法：按照二级和三级标题分割
function parseByHeadings(content: string): Array<{title: string; content: string; stepNumber?: number}> {
  const lines = content.split('\n');
  const sections: Array<{title: string; content: string; stepNumber?: number}> = [];
  let currentSection: {title: string; content: string; stepNumber?: number} | null = null;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 检测二级或三级标题
    const headingMatch = trimmedLine.match(/^#{2,3}\s+(.+)/);
    
    if (headingMatch) {
      // 保存上一个章节
      if (currentSection && currentSection.content.trim()) {
        sections.push(currentSection);
      }
      
      // 开始新章节
      const title = headingMatch[1].trim();

      currentSection = {
        title: title,
        content: line + '\n'
      };
    } else if (currentSection) {
      // 如果遇到一级标题，结束当前章节
      if (trimmedLine.match(/^#\s+(.+)/)) {
        if (currentSection.content.trim()) {
          sections.push(currentSection);
        }
        currentSection = null;
      } else {
        // 继续添加到当前章节
        currentSection.content += line + '\n';
      }
    }
  }
  
  // 添加最后一个章节
  if (currentSection && currentSection.content.trim()) {
    sections.push(currentSection);
  }
  
  return sections;
}

export function Component() {
  const navigate = useNavigate();
  const { userInfo } = useUserInfoStore();
  const [loading, setLoading] = useState(false);
  
  const [form, setForm] = useState({
    htmlContent: '',
    cssContent: '',
    stepsContent: '',
    customPrompt: '',
    enableAutoIteration: false,
    enableStepByStep: false, // 新增：是否启用分步执行
    selectedModel: 'htsc::saas-deepseek-v3', // 修正：使用正确的模型名称
  });

  const stepsFileInputRef = useRef<HTMLInputElement>(null);
  const htmlFileInputRef = useRef<HTMLInputElement>(null);
  const cssFileInputRef = useRef<HTMLInputElement>(null);

  // 计算完成进度
  const getProgress = () => {
    let completed = 0;

    if (form.htmlContent.trim()) completed += 1; // HTML是必需的
    if (form.cssContent.trim()) completed += 0.5;
    if (form.stepsContent.trim()) completed += 0.5;

    // customPrompt不参与进度计算，因为它有默认值且是额外要求
    return Math.min((completed / 1) * 100, 100); // 最大1分，HTML占1分，其他各占0.5分但作为加分项
  };

  // 检查是否可以启用分步执行
  const canEnableStepByStep = () => {
    return form.stepsContent.trim().length > 0;
  };

  // 执行设计稿优化
  const handleOptimize = async () => {
    if (!form.htmlContent.trim()) {
      alert('请先上传或输入HTML代码');

      return;
    }

    setLoading(true);
    try {
      // 构建文件列表，只传递HTML和CSS作为需要优化的源文件
      const files = [];

      if (form.htmlContent.trim()) {
        // 将HTML内容转换为data URL格式
        const htmlDataUrl = `data:text/html;base64,${btoa(unescape(encodeURIComponent(form.htmlContent)))}`;

        files.push({
          name: 'index.html',
          contentType: 'text/html',
          url: htmlDataUrl
        });
      }
      if (form.cssContent.trim()) {
        // 将CSS内容转换为data URL格式
        const cssDataUrl = `data:text/css;base64,${btoa(unescape(encodeURIComponent(form.cssContent)))}`;

        files.push({
          name: 'styles.css', 
          contentType: 'text/css',
          url: cssDataUrl
        });
      }

      let initialMessage = '';
      
      // 检查是否启用分步执行
      if (form.enableStepByStep && form.stepsContent.trim()) {
        // 解析步骤
        const steps = parseStepsFromMarkdown(form.stepsContent);
        
        if (steps.length > 0) {
          // 分步执行模式 - 只发送第一步的具体任务描述
          const firstStep = steps[0];
          const stepNum = firstStep.stepNumber || 1;
          const stepTitle = firstStep.title || `步骤${stepNum}`;
          
          // 第一次只发送第一个步骤的具体内容
          initialMessage = `# 🚨 设计稿代码优化 - 分步执行 - 步骤 ${stepNum}: ${stepTitle}

## 📋 当前任务要求
${firstStep.content}

---

## ⚠️ 🚨 ⛔ 强制性代码输出要求 ⛔ 🚨 ⚠️

**【重要警告】本步骤必须输出完整的HTML和CSS代码文件！**

### 🔴 必须遵守的硬性规则：

1. **必须使用 \`<files>\` 标签** - 这是绝对要求，不可违反
2. **必须输出 index.html 文件** - 包含完整的HTML代码
3. **必须输出 styles.css 文件** - 包含完整的CSS代码  
4. **代码必须完整可运行** - 不允许任何省略或占位符
5. **不能只输出分析或说明** - 必须有实际的代码文件

### 📝 强制输出格式（严格按此格式）：

\`\`\`
<files>
# 文件列表
- index.html
- styles.css

# 文件：index.html
\`\`\`html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 这里必须是完整的HTML内容，不能省略 -->
    <!-- 所有的div、section、header、footer等都要完整写出 -->
    <!-- 不允许使用"...其他内容..."这样的省略表示 -->
</body>
</html>
\`\`\`

# 文件：styles.css
\`\`\`css
/* 这里必须是完整的CSS样式，不能省略 */
/* 所有的选择器、属性、值都要完整写出 */
/* 不允许使用"/* 其他样式 */"这样的省略表示 */
body {
    margin: 0;
    padding: 0;
    /* 完整的样式属性... */
}
/* 继续写出所有其他样式... */
\`\`\`
</files>
\`\`\`

### ❌ 绝对禁止的错误做法：

1. ❌ 只输出分析报告而没有代码文件
2. ❌ 使用省略符号如"...其他代码..."
3. ❌ 不使用 \`<files>\` 标签
4. ❌ 只输出代码片段而不是完整文件
5. ❌ 文件名不是 index.html 和 styles.css

### ✅ 正确的执行流程：

1. **理解步骤要求** - 分析本步骤的具体任务
2. **执行代码修改** - 根据步骤要求修改或优化代码
3. **输出完整文件** - 使用 \`<files>\` 标签输出完整的 index.html 和 styles.css
4. **确保代码完整** - 检查输出的代码是否完整可运行

${form.customPrompt ? `\n## 💡 额外要求\n${form.customPrompt}\n` : ''}

---

## 🎯 立即开始执行

我需要你对提供的HTML和CSS文件进行专业的设计稿代码重构。

请现在立即执行上述步骤，并严格按照要求输出完整的代码文件。

**记住：这不是可选的，是必须的！每个步骤都必须输出完整的代码文件！**`;

          // 将剩余步骤保存到window对象，供分步执行器使用
          window.__remainingSteps = steps.slice(1);
          window.__currentStepIndex = 0;
          window.__totalSteps = steps.length;
          
          console.log('🚀 分步执行模式：', {
            currentStep: firstStep.title,
            remainingSteps: steps.slice(1).map(s => s.title),
            totalSteps: steps.length
          });
        } else {
          // 如果解析步骤失败，回退到普通模式
          initialMessage = buildDefaultMessage();
        }
      } else {
        // 普通执行模式
        initialMessage = buildDefaultMessage();
      }

      // 构建默认优化消息的函数
      function buildDefaultMessage() {
        const defaultOptimizationGuide = `
## 🎯 设计稿代码重构四阶段流程

### 核心原则
1. **保持视觉一致性** - 重构后的页面与重构前的页面视觉效果完全一致
2. **代码精简化** - 消除重复代码，建立规范的代码结构
3. **现代布局优先** - 必须使用 Flexbox 和 Grid 布局
4. **禁用过时布局** - 严禁使用 z-index 和 position: absolute

### 第一阶段：组件识别
- 识别页面的主要功能区域，为有明确业务功能的区域添加具有业务含义的 ID
- 使用 kebab-case 命名法，必须体现具体业务功能
- 禁止使用通用词汇（header、nav、content、main等）

### 第二阶段：CSS优化
- **严禁使用 ID 选择器进行样式设置**（仅限JavaScript和锚点使用）
- **统一使用类选择器**，严禁使用内联样式（style属性）
- 只抽取确实重复的完整样式块，避免过度抽象
- **所有视觉效果数值必须与原始CSS完全一致**

### 第三阶段：现代布局重构
- **完全消除 z-index 的使用**
- **完全消除 position: absolute 的使用**
- 使用 Flexbox 实现行级和列级布局
- 使用 Grid 实现复杂的网格布局
- 保持页面布局与设计稿完全一致

### 第四阶段：代码整合
- 根据代码复杂度智能选择样式方式：
  * **简单页面**：将CSS整合为内联样式，放在HTML的<head>标签内的<style>中
  * **复杂页面**：保持CSS为外部文件 \`styles.css\`，HTML通过 \`<link rel="stylesheet" href="./styles.css">\` 引用
- 确保最终输出能正常显示页面，视觉效果与原始代码完全一致`;

        const optimizationGuide = form.stepsContent.trim() || defaultOptimizationGuide;

        return `# 设计稿代码重构任务

我需要你对提供的HTML和CSS文件进行专业的设计稿代码重构，按照以下步骤进行：

${optimizationGuide}

${form.customPrompt ? `\n## 💡 重点优化要求\n${form.customPrompt}` : ''}

## 🚨 重要约束

- **文件名严格要求：输出的html文件必须为index.html，输出的css文件必须为styles.css**
- **页面视觉效果必须与原始代码完全一致**
- **输出内容一定要完整，不要省略任何内容**

请按照以上要求开始重构。`;
      }

      // 设置初始消息
      window.__initialMessage = {
        desc: initialMessage,
        files: files,
        enableAutoIteration: form.enableAutoIteration,
        enableStepByStep: form.enableStepByStep,
      };
      
      // 创建新的HTML类型playground - 简化描述以避免在系统提示词中重复
      const result = await createPlayground({
        model: form.selectedModel,
        desc: form.enableStepByStep ? `设计稿代码优化 - 分步执行` : `设计稿代码优化`, 
        projectId: undefined,
        user: userInfo?.username || 'anonymous',
        isPublic: true,
        type: 'html', // 指定为HTML类型
        enableAutoIteration: form.enableAutoIteration,
        enableStepByStep: form.enableStepByStep,
      }) as { id: string };

      const { id } = result;

      // 跳转到chat页面
      navigate(`/chat/${id}`);
    } catch (error) {
      console.error('Failed to create playground:', error);
      alert('创建优化任务失败，请重试');
      setLoading(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = useCallback((fileType: 'steps' | 'html' | 'css') => (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      const reader = new FileReader();

      reader.onload = (e) => {
        const content = e.target?.result as string;

        if (fileType === 'steps' && file.name.endsWith('.md')) {
          setForm(prev => ({ ...prev, stepsContent: content }));
        } else if (fileType === 'html' && file.name.endsWith('.html')) {
          setForm(prev => ({ ...prev, htmlContent: content }));
        } else if (fileType === 'css' && file.name.endsWith('.css')) {
          setForm(prev => ({ ...prev, cssContent: content }));
        } else {
          alert(`请选择正确的${fileType === 'steps' ? '.md' : fileType === 'html' ? '.html' : '.css'}文件`);
        }
      };
      reader.readAsText(file);
    }
  }, []);

  // 清空表单
  const handleClear = () => {
    setForm({
      htmlContent: '',
      cssContent: '',
      stepsContent: '',
      customPrompt: '📌 重点优化要求：\n• 输出的HTML内容必须结构完整且和优化前视觉效果一致\n• 支持内联样式（简单页面）或外部CSS文件（复杂页面）\n• 确保输出完整可运行的代码\n• 使用现代CSS技术（flexbox/grid）提升布局效率',
      enableAutoIteration: false,
      enableStepByStep: false,
      selectedModel: 'htsc::saas-deepseek-v3', // 重置为默认模型
    });
  };

  return (
    <div className="min-h-screen">
      <div className="container mx-auto p-6 max-w-4xl">
        {/* 页面头部 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
              <IconRocket className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              AI驱动的设计稿代码优化
            </h1>
          </div>
          
          {/* 进度条 */}
          <div className="max-w-md mx-auto">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">完成进度</span>
              <span className="text-sm font-bold text-blue-600">{Math.round(getProgress())}%</span>
            </div>
            <Progress 
              className="w-full" 
              color="primary"
              size="sm"
              value={getProgress()}
            />
          </div>
          
          {/* 模型选择 */}
          <div className="max-w-md mx-auto mt-6">
            <div className="flex items-center justify-center gap-4">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">选择AI模型：</span>
              <ModelSelector
                model={form.selectedModel}
                onChange={(model: string) => setForm(prev => ({ ...prev, selectedModel: model }))}
              />
            </div>
          </div>
        </div>

        {/* 步骤指示器 */}
        <div className="mb-8">
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <IconNumeric1Box className={`w-6 h-6 ${form.htmlContent.trim() ? 'text-green-600' : 'text-blue-600'}`} />
              <span className={`text-sm font-medium ${form.htmlContent.trim() ? 'text-green-600' : 'text-blue-600'}`}>
                上传HTML
              </span>
              {form.htmlContent.trim() && <IconCheck className="w-4 h-4 text-green-600" />}
            </div>
            <div className="w-8 h-px bg-gray-300 dark:bg-gray-600" />
            <div className="flex items-center gap-2">
              <IconNumeric2Box className={`w-6 h-6 ${form.cssContent.trim() ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm font-medium ${form.cssContent.trim() ? 'text-green-600' : 'text-gray-400'}`}>
                上传CSS
              </span>
              {form.cssContent.trim() && <IconCheck className="w-4 h-4 text-green-600" />}
            </div>
            <div className="w-8 h-px bg-gray-300 dark:bg-gray-600" />
            <div className="flex items-center gap-2">
              <IconNumeric3Box className={`w-6 h-6 ${form.stepsContent.trim() ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm font-medium ${form.stepsContent.trim() ? 'text-green-600' : 'text-gray-400'}`}>
                  任务规划
              </span>
              {form.stepsContent.trim() && <IconCheck className="w-4 h-4 text-green-600" />}
            </div>
            <div className="w-8 h-px bg-gray-300 dark:bg-gray-600" />
            <div className="flex items-center gap-2">
              <IconNumeric4Box className={`w-6 h-6 ${form.htmlContent.trim() ? 'text-blue-600' : 'text-gray-400'}`} />
              <span className={`text-sm font-medium ${form.htmlContent.trim() ? 'text-blue-600' : 'text-gray-400'}`}>
                开始优化
              </span>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="space-y-6">
          {/* 步骤1：HTML代码 - 必需 */}
          <Card className="shadow-lg border-2 border-blue-200 dark:border-blue-700 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-800/50">
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-600 text-white rounded-lg">
                    <IconNumeric1Box className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-blue-900 dark:text-blue-100">上传HTML代码</h3>
                      <p className="text-sm text-blue-700 dark:text-blue-300">这是必需的第一步，请提供需要优化的设计稿HTML代码</p>
                  </div>
                </div>
                <div className="ml-auto">
                  <Chip color="danger" size="sm" variant="flat">必需</Chip>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-4">
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Button
                    className="flex-1"
                    startContent={<IconUpload className="w-4 h-4" />}
                    variant="bordered"
                    onPress={() => htmlFileInputRef.current?.click()}
                  >
                    <IconLanguageHtml5 className="w-4 h-4 text-orange-600" />
                    上传 HTML 文件
                  </Button>
                  {form.htmlContent.trim() && (
                    <div className="flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                      <IconCheck className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-700 dark:text-green-300 font-medium">已上传</span>
                    </div>
                  )}
                </div>
                <Textarea
                    classNames={{
                    input: "font-mono text-sm",
                  }}
                  maxRows={12}
                  minRows={6}
                  placeholder="请粘贴设计稿HTML代码，或使用上方按钮上传HTML文件..."
                  value={form.htmlContent}
                  variant="bordered"
                  onValueChange={(value: string) => setForm(prev => ({ ...prev, htmlContent: value }))}
                />
              </div>
            </CardBody>
          </Card>

          {/* 步骤2：CSS样式 - 可选 */}
          <Card className="shadow-lg border border-gray-200 dark:border-gray-700 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50">
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500 text-white rounded-lg">
                    <IconNumeric2Box className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">上传CSS样式</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">提供设计稿相关的CSS样式文件，AI将智能选择内联样式或外部文件方式</p>
                  </div>
                </div>
                <div className="ml-auto">
                  <Chip color="default" size="sm" variant="flat">可选</Chip>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-4">
              <div className="space-y-4">
                <div className="flex gap-3">
                  <Button
                    className="flex-1"
                    startContent={<IconUpload className="w-4 h-4" />}
                    variant="bordered"
                    onPress={() => cssFileInputRef.current?.click()}
                  >
                    <IconLanguageCss3 className="w-4 h-4 text-blue-600" />
                    上传 CSS 文件
                  </Button>
                  {form.cssContent.trim() && (
                    <div className="flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                      <IconCheck className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-700 dark:text-green-300 font-medium">已上传</span>
                    </div>
                  )}
                </div>
                <Textarea
                    classNames={{
                    input: "font-mono text-sm",
                  }}
                  maxRows={8}
                  minRows={4}
                  placeholder="请粘贴设计稿CSS代码，或使用上方按钮上传CSS文件..."
                  value={form.cssContent}
                  variant="bordered"
                  onValueChange={(value: string) => setForm(prev => ({ ...prev, cssContent: value }))}
                />
              </div>
            </CardBody>
          </Card>

          {/* 步骤3：优化规则 - 可选 */}
          <Card className="shadow-lg border border-gray-200 dark:border-gray-700 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50">
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500 text-white rounded-lg">
                    <IconNumeric3Box className="w-5 h-5" />
                  </div>
                  <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">自定义任务规划</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">制定详细的设计稿优化步骤和特殊要求，指导AI进行精准优化</p>
                  </div>
                </div>
                <div className="ml-auto">
                  <Chip color="default" size="sm" variant="flat">可选</Chip>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-4">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="flex gap-3 mb-3">
                      <Button
                        className="flex-1"
                        startContent={<IconUpload className="w-4 h-4" />}
                        variant="bordered"
                        onPress={() => stepsFileInputRef.current?.click()}
                      >
                        <IconFileDocument className="w-4 h-4 text-blue-600" />
                          上传任务规划文档
                      </Button>
                      {form.stepsContent.trim() && (
                        <div className="flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                          <IconCheck className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-700 dark:text-green-300 font-medium">已设置</span>
                        </div>
                      )}
                    </div>
                    <Textarea
                        classNames={{
                        input: "font-mono text-sm",
                      }}
                      maxRows={6}
                      minRows={4}
                      placeholder="自定义设计稿优化规划步骤..."
                      value={form.stepsContent}
                      variant="bordered"
                      onValueChange={(value: string) => setForm(prev => ({ ...prev, stepsContent: value }))}
                    />
                      
                      {/* 分步执行开关 */}
                      {canEnableStepByStep() && (
                        <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                          <Switch
                            color="secondary"
                            isSelected={form.enableStepByStep}
                            size="sm"
                            onValueChange={(checked: boolean) => setForm(prev => ({ ...prev, enableStepByStep: checked }))}
                          >
                            <div className="ml-2">
                              <span className="text-sm font-medium text-purple-900 dark:text-purple-100">启用分步执行模式</span>
                              <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">
                                AI将按照上传的步骤文档逐步执行优化，每完成一步都会输出当前阶段的代码
                              </p>
                            </div>
                          </Switch>
                          
                          {/* 步骤预览 */}
                          {form.stepsContent.trim() && (
                            <div className="mt-3 pt-3 border-t border-purple-200 dark:border-purple-700">
                              <h5 className="text-xs font-medium text-purple-900 dark:text-purple-100 mb-2">📋 解析的执行步骤预览：</h5>
                              <div className="space-y-1 max-h-32 overflow-y-auto">
                                {parseStepsFromMarkdown(form.stepsContent).map((step, index) => (
                                  <div key={index} className="flex items-start gap-2 text-xs">
                                    <span className="inline-flex items-center justify-center w-4 h-4 bg-purple-200 dark:bg-purple-800 text-purple-800 dark:text-purple-200 rounded-full text-[10px] font-medium flex-shrink-0 mt-0.5">
                                      {step.stepNumber || (index + 1)}
                                    </span>
                                    <span className="text-purple-700 dark:text-purple-300 line-clamp-2">
                                      {step.title}
                                    </span>
                                  </div>
                                ))}
                              </div>
                              <p className="text-[10px] text-purple-600 dark:text-purple-400 mt-2">
                                共解析出 {parseStepsFromMarkdown(form.stepsContent).length} 个执行步骤
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                  </div>
                  <div>
                    <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">重点优化要求</h4>
                    </div>
                    <Textarea
                      maxRows={6}
                      minRows={4}
                      placeholder="例如：专注移动端优化、提升加载速度、改善颜色搭配、增强可访问性等..."
                      value={form.customPrompt}
                      variant="bordered"
                      onValueChange={(value: string) => setForm(prev => ({ ...prev, customPrompt: value }))}
                    />
                      
                      {/* 自迭代优化开关 */}
                      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                        <Switch
                          color="primary"
                          isSelected={form.enableAutoIteration}
                          size="sm"
                          onValueChange={(checked: boolean) => setForm(prev => ({ ...prev, enableAutoIteration: checked }))}
                        >
                          <div className="ml-2">
                            <span className="text-sm font-medium text-blue-900 dark:text-blue-100">启用智能自迭代优化</span>
                            <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                              AI将自动检测优化结果并进行2-3轮迭代改进，提升代码质量（可能增加处理时间）
                            </p>
                  </div>
                        </Switch>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        <Divider className="my-8" />

        {/* 步骤4：执行优化 */}
            <div className="flex gap-4 justify-center">
              <Button
                className="px-8 py-3 font-semibold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                color="success"
                isDisabled={!form.htmlContent.trim()}
                isLoading={loading}
                size="lg"
                startContent={<IconRocket className="w-5 h-5" />}
                onPress={handleOptimize}
              >
              {loading ? '创建优化任务中...' : form.enableStepByStep ? '开始分步优化' : '开始智能优化'}
              </Button>
              <Button
                className="px-6 py-3 font-semibold border-green-300 text-green-700 hover:bg-green-50"
                size="lg"
                startContent={<IconBroom className="w-5 h-5" />}
                variant="bordered"
                onPress={handleClear}
              >
                清空重置
              </Button>
            </div>
            
            {!form.htmlContent.trim() && (
            <p className="text-red-600 dark:text-red-400 text-sm mt-3 text-center">
              ⚠️ 请先上传设计稿HTML代码才能开始优化
              </p>
            )}
        
          {form.enableStepByStep && (
            <div className="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
              <div className="flex items-start gap-3">
                <div className="p-1.5 bg-purple-600 text-white rounded-lg flex-shrink-0">
                  <IconFileDocument className="w-4 h-4" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">🚀 分步执行模式已启用</h4>
                  <div className="space-y-2 text-xs text-purple-700 dark:text-purple-300">
                    <p>
                      <strong>执行方式：</strong>AI将严格按照您上传的步骤文档逐步执行优化，每完成一个步骤都会输出当前阶段的完整代码。
                    </p>
                    <div>
                      <strong>支持的markdown格式：</strong>
                      <ul className="list-disc list-inside ml-2 mt-1 space-y-0.5">
                        <li><code>## 步骤 1: 标题</code> 或 <code>### 第1步: 标题</code></li>
                        <li><code>## 1. 标题</code> 或 <code>### 1、标题</code></li>
                        <li>包含执行指令、约束条件、验证要求的复杂结构</li>
                      </ul>
                    </div>
                    <p>
                      <strong>执行特点：</strong>每步都会进行验证检查，确保代码质量和视觉效果的一致性，失败时会重新执行直到通过验证。
                    </p>
                    <p className="text-purple-600 dark:text-purple-400">
                      💡 已解析 <strong>{parseStepsFromMarkdown(form.stepsContent).length}</strong> 个执行步骤，预计处理时间会相应增加。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

        {/* 隐藏的文件输入 */}
        <input
          ref={stepsFileInputRef}
          accept=".md"
          className="hidden"
          type="file"
          onChange={handleFileUpload('steps')}
        />
        <input
          ref={htmlFileInputRef}
          accept=".html,.htm"
          className="hidden"
          type="file"
          onChange={handleFileUpload('html')}
        />
        <input
          ref={cssFileInputRef}
          accept=".css"
          className="hidden"
          type="file"
          onChange={handleFileUpload('css')}
        />
      </div>
    </div>
  );
} 