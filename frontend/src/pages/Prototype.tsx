import { Button } from '@heroui/react';
import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ChatBox } from "@/components/ChatBox";
import { PlaygroundListWithLimit } from "@/components/PlaygroundListWithLimit";
import { title } from "@/components/primitives";
import { examples } from "@/config/examples";
import { useProjectStore } from '@/stores/project';

export function Component() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { setCurrent } = useProjectStore();

  useEffect(() => {
    if (id) {
      // 使用store管理项目选择，store会自动处理localStorage和事件通知
      setCurrent(id);
    }
    // 移除自动重定向逻辑，允许在没有ID时也显示页面
  }, [id, setCurrent]);

  // 如果没有ID，显示项目选择界面
  if (!id) {
    return (
      <div className="relative">
        <div className="flex flex-col items-center justify-center px-6 py-24">
          <h1 className={title({ size: 'lg', className: 'mb-8'})}>
            选择项目开始原型代码生成
          </h1>
          <div className="w-full max-w-4xl">
            <div className="text-center">
              <p className="text-default-500 mb-4">请选择一个项目来开始生成原型代码</p>
              <Button 
                color="primary" 
                onPress={() => navigate('/project-list')}
              >
                选择项目
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* 主要内容区域 */}
      <div className="flex flex-col items-center justify-center px-6 py-24">
        <h1 className={title({ size: 'lg', className: 'mb-8'})}>
           AI 驱动开发，释放创造潜力
        </h1>
        
        <div className="w-full max-w-4xl">
          <ChatBox
            examples={examples}
            showProjectSelector={false}
            onCreate={(id) => {
              navigate('/chat/' + id);
            }}
          />
        </div>
      </div>
      
      {/* 项目列表区域 */}
      <div className="border-t border-gray-200/60 dark:border-zinc-800/60 bg-gradient-to-b from-white/40 to-gray-50/60 dark:from-zinc-900/40 dark:to-zinc-950/60 backdrop-blur-sm">
        <div className="p-6 pb-20">
          <PlaygroundListWithLimit projectId={id} />
        </div>
      </div>
    </div>
  );
} 