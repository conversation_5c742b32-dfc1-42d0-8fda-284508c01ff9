import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  Input,
  Select,
  SelectItem,
  Chip,
  Spinner
} from "@heroui/react";
import moment from 'moment';
import { useEffect, useState, useCallback, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { getChat, listPlaygrounds } from "@/apis";
import { title } from "@/components/primitives";
import { useUserInfoStore } from '@/hooks/login';
import { type Playground } from "@/stores/playground";
import { useProjectStore } from "@/stores/project";
import { splitModel } from "@/utils/model";
import IconArrowRight from '~icons/mdi/arrow-right';
import IconFilter from '~icons/mdi/filter';
import IconSearch from '~icons/mdi/magnify';

// 过滤器选项
const TYPE_OPTIONS = [
  { key: 'all', label: '全部类型' },
  { key: 'react', label: 'React' },
  { key: 'vue', label: 'Vue' },
  { key: 'lit', label: 'Lit' },
];

const SORT_OPTIONS = [
  { key: 'desc', label: '最新优先' },
  { key: 'asc', label: '最旧优先' },
];

const SCOPE_OPTIONS = [
  { key: 'all', label: '全部对话' },
  { key: 'my', label: '我的对话' },
];

function useFirstMessage(id: string) {
  const [firstMessage, setFirstMessage] = useState('');

  useEffect(() => {
    const load = async () => {
      if (!id) {
        setFirstMessage('');
      } else {
        const ret = await getChat({ id, take: 1, sort: 'asc' });

        if (ret && ret.length > 0) {
          setFirstMessage(ret[0].content);
        }
      }
    };

    load();
  }, [id]);

  return { firstMessage };
}

export function Component() {
  const { userInfo } = useUserInfoStore();
  const { projects } = useProjectStore();
  const navigate = useNavigate();
  const [playgrounds, setPlaygrounds] = useState<Playground[]>([]);
  const [infoOpen, setInfoOpen] = useState('');
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [initialized, setInitialized] = useState(false);
  
  // 过滤器状态
  const [filters, setFilters] = useState({
    type: 'all',
    sort: 'desc' as 'asc' | 'desc',
    scope: 'all',
    search: '',
    projectId: '',
  });
  
  const observerRef = useRef<IntersectionObserver>();
  const lastElementRef = useCallback((node: HTMLDivElement) => {
    if (loading) return;
    if (observerRef.current) observerRef.current.disconnect();
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMore();
      }
    });
    if (node) observerRef.current.observe(node);
  }, [loading, hasMore]);

  // 加载数据
  const loadPlaygrounds = useCallback(async (skip = 0, reset = false) => {
    setLoading(true);
    try {
      const params: any = {
        sort: filters.sort,
        take: 20,
        skip,
      };

      // 处理类型过滤
      if (filters.type !== 'all') {
        params.tag = filters.type;
      }

      // 处理范围过滤 - 我的项目通过用户字段比对
      if (filters.scope === 'my') {
        params.user = userInfo?.username;
      }

      // 处理项目过滤
      if (filters.projectId && filters.projectId !== 'all') {
        params.projectId = filters.projectId;
      }

      const ret = await listPlaygrounds(params);
      
      // 前端过滤处理
      let filteredRet = ret;
      
      // 搜索过滤
      if (filters.search) {
        filteredRet = filteredRet.filter((p: Playground) => 
          p.name.toLowerCase().includes(filters.search.toLowerCase()) ||
          p.desc.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      if (reset) {
        setPlaygrounds(filteredRet);
      } else {
        setPlaygrounds(prev => [...prev, ...filteredRet]);
      }
      
      setHasMore(ret.length === 20);
    } catch (error) {
      console.error('加载失败:', error);
    } finally {
      setLoading(false);
    }
  }, [filters, userInfo]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadPlaygrounds(playgrounds.length);
    }
  }, [loading, hasMore, playgrounds.length, loadPlaygrounds]);

  const [searchParams, setSearchParams] = useSearchParams();

  const handleFilterChange = useCallback((key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    
    // 当项目筛选变化时，同步更新URL参数
    if (key === 'projectId') {
      if (value && value !== 'all') {
        setSearchParams({ project: value }, { replace: true });
      } else {
        setSearchParams({}, { replace: true });
      }
    }
  }, [setSearchParams]);

  // 从 URL projectId 初始化项目筛选
  useEffect(() => {
    if (projects.length === 0 || initialized) return;

    const projectId = searchParams.get('projectId');
    const projectParam = searchParams.get('project');

    if (projectId) {
      const project = projects.find(p => p.id === projectId);

      if (project) {
        setFilters(prev => ({ ...prev, projectId }));
        // 保留URL参数，只是将参数名改为更简洁的 project
        setSearchParams({ project: projectId }, { replace: true });
      }
      setInitialized(true);
    } else if (projectParam) {
      // 处理简洁的 project 参数
      const project = projects.find(p => p.id === projectParam);

      if (project) {
        setFilters(prev => ({ ...prev, projectId: projectParam }));
      }
      setInitialized(true);
    } else {
      setInitialized(true);
    }
  }, [searchParams, projects, setSearchParams, initialized]);

  // 初始加载和过滤器变化时重新加载
  useEffect(() => {
    if (initialized) {
      loadPlaygrounds(0, true);
    }
  }, [filters, userInfo, initialized, loadPlaygrounds]);

  const onShowInfo = (id: string) => {
    setInfoOpen(id);
  };
  const onCloseInfo = () => {
    setInfoOpen('');
  };

  const getProjectName = (projectId: string | undefined) => {
    if (!projectId) return null;
    const project = projects.find(p => p.id === projectId);

    return project?.name || '未知项目';
  };

  const { firstMessage } = useFirstMessage(infoOpen);

  return (
    <div className="container mx-auto px-4 py-8">
        <h1 className={title()}>广场</h1>
        <p className="text-zinc-600 dark:text-zinc-400 mt-2 mb-8">
          在这里，浏览和学习由AI驱动的真实编程对话，激发你的下一个绝妙灵感。
        </p>

        {/* 过滤器区域 */}
        <div className="bg-white dark:bg-zinc-900 my-6 border-b">
          <div className="flex items-center gap-2 mb-4">
            <IconFilter className="text-zinc-500" />
            <span className="font-medium">筛选条件</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            {/* 搜索框 */}
            <Input
              classNames={{
                input: "text-sm",
                inputWrapper: "rounded-md",
              }}
              label="对话名称或描述"
              placeholder=""
              startContent={<IconSearch className="text-zinc-400" />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
            
            {/* 项目筛选 */}
            <Select
              classNames={{
                trigger: "rounded-md",
              }}
              label="指定项目"
              selectedKeys={[filters.projectId || 'all']}
              onSelectionChange={(keys) => {
                const key = Array.from(keys)[0] as string;

                handleFilterChange('projectId', key);
              }}
            >
              {[
                <SelectItem key="all">全部项目</SelectItem>,
                ...projects.map((project) => (
                  <SelectItem key={project.id}>{project.name}</SelectItem>
                ))
              ]}
            </Select>

            {/* 项目类型 */}
            <Select
              classNames={{
                trigger: "rounded-md",
              }}
              label="技术框架"
              selectedKeys={[filters.type]}
              onSelectionChange={(keys) => {
                const key = Array.from(keys)[0] as string;

                handleFilterChange('type', key);
              }}
            >
              {TYPE_OPTIONS.map((option) => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            {/* 排序方式 */}
            <Select
              classNames={{
                trigger: "rounded-md",
              }}
              label="排序方式"
              selectedKeys={[filters.sort]}
              onSelectionChange={(keys) => {
                const key = Array.from(keys)[0] as string;

                handleFilterChange('sort', key);
              }}
            >
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            {/* 项目范围 */}
            <Select
              classNames={{
                trigger: "rounded-md",
              }}
              label="对话范围"
              selectedKeys={[filters.scope]}
              onSelectionChange={(keys) => {
                const key = Array.from(keys)[0] as string;

                handleFilterChange('scope', key);
              }}
            >
              {SCOPE_OPTIONS.map((option) => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            
          </div>

          {/* 活跃的过滤器标签 */}
          <div className="flex flex-wrap items-center gap-2 pt-2 pb-4 min-h-14">
            {filters.type !== 'all' && (
              <Chip 
                size="sm" 
                variant="flat" 
                onClose={() => handleFilterChange('type', 'all')}
              >
                类型: {TYPE_OPTIONS.find(o => o.key === filters.type)?.label}
              </Chip>
            )}
            {filters.scope !== 'all' && (
              <Chip 
                size="sm" 
                variant="flat" 
                onClose={() => handleFilterChange('scope', 'all')}
              >
                范围: {SCOPE_OPTIONS.find(o => o.key === filters.scope)?.label}
              </Chip>
            )}
            {filters.search && (
              <Chip 
                size="sm" 
                variant="flat" 
                onClose={() => handleFilterChange('search', '')}
              >
                搜索: {filters.search}
              </Chip>
            )}
            {filters.projectId && filters.projectId !== 'all' && (
              <Chip 
                size="sm" 
                variant="flat" 
                onClose={() => handleFilterChange('projectId', 'all')}
              >
                项目: {getProjectName(filters.projectId)}
              </Chip>
            )}
          </div>
        </div>

        {/* 对话列表 */}
        <div className="grid grid-cols-[repeat(auto-fill,minmax(280px,1fr))] gap-x-8 gap-y-12">
          {playgrounds.map((playground, index) => {
            const { id, name, desc, created, type, model, user, projectId } = playground;
            const isLast = index === playgrounds.length - 1;
            
            return (
              <div
                key={id}
                ref={isLast ? lastElementRef : null}
                className="cursor-pointer group"
                onClick={() => {
                  window.open(`/chat/${id}`, '_blank');
                }}
              >
                {/* Preview Area */}
                <div className="aspect-[1.618] bg-neutral-100 dark:bg-neutral-800 rounded-lg flex items-center justify-center relative overflow-hidden">
                  <div className="absolute top-2 right-2 flex gap-2 z-10">
                    <Chip size="sm" variant="flat">
                      <div className="flex items-center gap-1.5">
                        <div className={`w-2 h-2 rounded-full ${
                          type === 'react' ? 'bg-blue-500' : 
                          type === 'vue' ? 'bg-green-500' : 
                          'bg-amber-500'
                        }`} />
                        <span className="text-xs">{type || '通用'}</span>
                      </div>
                    </Chip>
                    {projectId && (
                      <Chip color="secondary" size="sm" variant="flat">
                        {getProjectName(projectId)}
                      </Chip>
                    )}
                  </div>
                  <span className="text-sm text-neutral-500 dark:text-neutral-400">No screenshot available</span>
                  <div className="absolute bottom-2 left-3 z-10 flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-300">
                    <span className="font-medium truncate">{user}</span>
                    {!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::') && (
                      <span className="font-medium truncate">· {model ? splitModel(model).modelName : 'Unknown Model'}</span>
                    )}
                    <span className="text-nowrap">· {moment(created).fromNow()}</span>
                  </div>
                </div>

                {/* Content Area */}
                <div className="mt-4 flex items-start justify-between gap-2">
                  <div className="min-w-0">
                    <h3 className="font-semibold text-default-900 truncate" title={name}>{name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate" title={desc || ''}>
                      {desc || ''}
                    </p>
                  </div>
                  <div className="flex items-center pl-2">
                    <IconArrowRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 group-hover:translate-x-1 transition-all duration-200" />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        )}

        {/* 无更多数据提示 */}
        {!hasMore && playgrounds.length > 0 && (
          <div className="text-center py-8 text-zinc-500">
            已显示全部对话
          </div>
        )}

        {/* 空状态 */}
        {!loading && playgrounds.length === 0 && (
          <div className="text-center py-16">
            <p className="text-zinc-500 text-lg mb-4">未找到相关对话，不妨调整筛选条件或换个关键词试试？</p>
          </div>
        )}

        {/* 详情模态框 */}
        {infoOpen && (
          <Modal isOpen={!!infoOpen} size="lg" onClose={onCloseInfo}>
            <ModalContent>
              {(onClose) => {
                const playground = playgrounds.find(e => e.id === infoOpen);

                if (!playground) return null;
                
                const { name, desc, created, type, model, user } = playground;
                const fields: Record<string, string> = {
                  '名称': name,
                  '描述': desc,
                  '作者': user,
                  '创建时间': moment(created).format('YYYY-MM-DD HH:mm:ss'),
                  '类型': type,
                };
                
                // 只有当模型不以openrouter::或siliconflow::开头时才显示模型字段
                if (!model?.startsWith('openrouter::') && !model?.startsWith('siliconflow::')) {
                  fields['模型'] = model;
                }

                return (
                  <>
                    <ModalHeader className="flex flex-col gap-1">对话信息</ModalHeader>
                    <ModalBody>
                      <h2 className="font-semibold mb-2">基本信息</h2>
                      <dl className="grid gap-3 grid-cols-[80px_1fr] border rounded-lg p-4 mb-4">
                        {Object.entries(fields).map(([key, value]) => (
                          <div key={key} className="contents">
                            <dt className="text-sm font-medium text-zinc-600 dark:text-zinc-400">{key}</dt>
                            <dd className="text-sm">{value}</dd>
                          </div>
                        ))}
                      </dl>
                      
                      <h2 className="font-semibold mb-2">开发需求</h2>
                      <div className="border rounded-lg p-4 bg-zinc-50 dark:bg-zinc-800">
                        <p className="text-sm">{firstMessage || '加载中...'}</p>
                      </div>
                    </ModalBody>
                    <ModalFooter>
                      <Button color="primary" variant="flat" onPress={() => {
                        window.open(`/chat/${infoOpen}`, '_blank');
                        onClose();
                      }}>
                        查看对话
                      </Button>
                      <Button color="danger" variant="light" onPress={onClose}>
                        关闭
                      </Button>
                    </ModalFooter>
                  </>
                );
              }}
            </ModalContent>
          </Modal>
        )}
      </div>
  );
}
