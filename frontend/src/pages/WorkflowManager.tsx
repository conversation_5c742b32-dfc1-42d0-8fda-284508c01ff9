import {
  Button,
  Table,
  Chip,
  Card,
  CardBody,
  TableHeader,
  TableColumn,
  TableBody,
  TableCell,
  TableRow,
  Spinner,
} from '@heroui/react';
import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import WorkflowMRModal from '@/components/WorkflowMRModal';

// 导入MDI图标
import IconAlert from '~icons/mdi/alert-circle';
import IconCheckCircle from '~icons/mdi/check-circle';
import IconCircle from '~icons/mdi/circle';
import IconCircleOutline from '~icons/mdi/circle-outline';
import IconClockOutline from '~icons/mdi/clock-outline';
// import IconFileDocument from '~icons/mdi/file-document';
import IconLoading from '~icons/mdi/loading';
import IconMonitor from '~icons/mdi/monitor';
import IconRefresh from '~icons/mdi/refresh';
import IconRestart from '~icons/mdi/restart';
import IconMerge from '~icons/mdi/source-merge';

// 移除Ant Design相关的CSS注入和样式
// 移除通用页面样式导入，使用HeroUI的样式

interface WorkflowNode {
  id: string;
  name: string;
  type: 'lanhu-transcode' | 'merge' | 'spec-to-prod-code';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: string;
  endTime?: string;
  error?: string;
  taskId?: string;
  result?: any;
  dependencies: string[];
}

interface WorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  nodes: WorkflowNode[];
  startTime?: string;
  endTime?: string;
  error?: string;
  // 单页面工作流的页面信息
  pageId?: string;
  pageName?: string;
  projectName?: string;
}

interface PageWorkflow {
  pageId: string;
  pageName: string;
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  startTime?: string;
  endTime?: string;
  error?: string;
  // 单页面工作流的附加信息
  projectName?: string;
}

interface MultiPageWorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  pageWorkflows: PageWorkflow[];
  startTime?: string;
  endTime?: string;
  error?: string;
  totalPages: number;
  completedPages: number;
  failedPages: number;
}

interface ProjectWorkflows {
  singlePageWorkflows: WorkflowStatus[];
  multiPageWorkflows: MultiPageWorkflowStatus[];
}

// 状态图标组件
const StatusIcon: React.FC<{ status: string }> = ({ status }) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'pending':
        return <IconClockOutline />;
      case 'processing':
        return <IconLoading className="animate-spin" />;
      case 'completed':
        return <IconCheckCircle />;
      case 'partial-success':
        return <IconCheckCircle />; // 使用相同的图标但不同颜色
      case 'failed':
        return <IconAlert />;
      default:
        return <IconCircle />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'primary';
      case 'completed':
        return 'success';
      case 'partial-success':
        return 'warning'; // 橙色，介于成功和警告之间
      case 'failed':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <div className={`text-${getStatusColor()}`}>
      {getStatusIcon()}
    </div>
  );
};

// 状态标签组件
const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const getStatusText = () => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'processing':
        return '进行中';
      case 'completed':
        return '完成';
      case 'partial-success':
        return '部分成功';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'primary';
      case 'completed':
        return 'success';
      case 'partial-success':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <Chip
      color={getStatusColor()}
      size="sm"
      variant="flat"
    >
      {getStatusText()}
    </Chip>
  );
};

// 进度条组件
// const ProgressBar: React.FC<{ progress: number; status: string }> = ({ progress, status }) => {
//   const getProgressColor = () => {
//     switch (status) {
//       case 'processing':
//         return 'primary';
//       case 'completed':
//         return 'success';
//       case 'failed':
//         return 'danger';
//       default:
//         return 'default';
//     }
//   };

//   return (
//     <div className="flex items-center gap-2">
//       <div className="w-20 h-1.5 bg-gray-200 rounded-full overflow-hidden">
//         <div
//           className={`h-full bg-${getProgressColor()} rounded-full transition-all duration-300`}
//           style={{ width: `${progress}%` }}
//         />
//       </div>
//       <span className="text-xs text-gray-500">{progress}%</span>
//     </div>
//   );
// };

// Add interface for component props
interface WorkflowManagerProps {
  projectId?: string;
}

const WorkflowManager: React.FC<WorkflowManagerProps> = ({ projectId: propProjectId }) => {
  const { projectId: urlProjectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();

  // Use prop projectId if provided, otherwise use URL param
  const projectId = propProjectId || urlProjectId;

  const [workflows, setWorkflows] = useState<ProjectWorkflows | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [activeTab, setActiveTab] = useState<'application' | 'page'>('application');

  // 添加重试相关状态
  const [retryingWorkflows, setRetryingWorkflows] = useState<Set<string>>(new Set());

  // MR弹窗相关状态
  const [isMRModalVisible, setMRModalVisible] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null);

  // 打开MR弹窗
  const handleViewMRs = useCallback((workflow: any, isPageWorkflow = false) => {
    setSelectedWorkflow({ ...workflow, isPageWorkflow });
    setMRModalVisible(true);
  }, []);

  // 关闭MR弹窗
  const handleCloseMRModal = useCallback(() => {
    setMRModalVisible(false);
    setSelectedWorkflow(null);
  }, []);

  // 获取项目的所有工作流
  const fetchWorkflows = useCallback(async () => {
    if (!projectId) return;

    try {
      const response = await fetch(`/api/design-project/${projectId}/workflows`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data: ProjectWorkflows = await response.json();

      // 清理数据，过滤掉 null 值
      const cleanedData: ProjectWorkflows = {
        singlePageWorkflows: (data.singlePageWorkflows || []).filter(w => w != null),
        multiPageWorkflows: (data.multiPageWorkflows || []).filter(w => w != null),
      };

      setWorkflows(cleanedData);
      setError(null);
    } catch (err) {
      console.error('获取工作流列表失败:', err);
      setError(err instanceof Error ? err.message : '获取工作流列表失败');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // 获取项目Git配置的方法（复用逻辑）
  const getProjectGitConfig = useCallback(async () => {
    try {
      const response = await fetch(`/api/design-project/${projectId}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const project = await response.json();

      // 从项目设置中获取Git配置，如果没有设置则使用默认值
      const gitUrl = project?.gitUrl || 'git@*************:codebox-test/zhangyu-test/eam-web-app.git';
      const branch = project?.gitBranch || 'dev';

      return { gitUrl, branch };
    } catch (error) {
      console.error('获取项目Git配置失败:', error);

      // 返回默认值
      return {
        gitUrl: 'git@*************:codebox-test/zhangyu-test/eam-web-app.git',
        branch: 'dev'
      };
    }
  }, [projectId]);

  // 获取当前用户信息的方法
  const getCurrentUser = useCallback(() => {
    // 这里可以从用户状态或其他地方获取用户信息
    // 暂时返回固定值，实际应用中应该从用户管理系统获取
    return 'system-retry';
  }, []);

  // 重试项目级工作流
  const retryApplicationWorkflow = useCallback(async (workflow: any) => {
    const workflowId = workflow.workflowId;

    if (retryingWorkflows.has(workflowId)) {
      return; // 防止重复重试
    }

    setRetryingWorkflows(prev => new Set([...prev, workflowId]));

    try {
      const { gitUrl, branch } = await getProjectGitConfig();
      const currentUser = getCurrentUser();

      if (workflow.type === 'single-page') {
        // 重试单页面工作流
        const response = await fetch(`/api/design-project/${projectId}/tasks/page-code-generation-workflow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pageId: workflow.pageId,
            user: currentUser,
            gitUrl,
            branch,
            enableAutoIteration: true,
            enableStepByStep: false,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        console.log(`单页面工作流重试成功: ${result.taskId}`);

        // 可以选择跳转到新的工作流页面
        window.open(`/workflow/${projectId}/${result.taskId}`, '_blank');

      } else if (workflow.type === 'multi-page') {
        // 重试多页面工作流 - 需要获取原始的页面IDs
        // 从workflow.metadata或其他地方获取页面列表
        const pageIds = workflow.pageWorkflows?.map((pw: any) => pw.pageId) || [];

        if (pageIds.length === 0) {
          throw new Error('无法获取多页面工作流的页面列表');
        }

        const response = await fetch(`/api/design-project/${projectId}/tasks/multi-page-code-generation-workflow`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pageIds,
            user: currentUser,
            gitUrl,
            branch,
            enableAutoIteration: true,
            enableStepByStep: false,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        console.log(`多页面工作流重试成功: ${result.taskId}`);

        // 可以选择跳转到新的工作流页面
        window.open(`/multi-workflow/${projectId}/${result.taskId}`, '_blank');
      }

      // 重试成功后刷新列表
      await fetchWorkflows();

    } catch (error) {
      console.error('重试项目级工作流失败:', error);
      // 这里可以添加用户提示
    } finally {
      setRetryingWorkflows(prev => {
        const newSet = new Set(prev);

        newSet.delete(workflowId);

        return newSet;
      });
    }
  }, [projectId, retryingWorkflows, getProjectGitConfig, getCurrentUser, fetchWorkflows]);

  // 重试页面级工作流
  const retryPageWorkflow = useCallback(async (pageWorkflow: any) => {
    const workflowKey = pageWorkflow.key || (pageWorkflow.isSinglePage ?
      `single-${pageWorkflow.workflowId}` :
      `multi-${pageWorkflow.multiWorkflowId || pageWorkflow.workflowId}-${pageWorkflow.pageId}`);

    if (retryingWorkflows.has(workflowKey)) {
      return; // 防止重复重试
    }

    setRetryingWorkflows(prev => new Set([...prev, workflowKey]));

    try {
      const { gitUrl, branch } = await getProjectGitConfig();
      const currentUser = getCurrentUser();

      // 页面级重试总是调用单页面工作流接口
      const response = await fetch(`/api/design-project/${projectId}/tasks/page-code-generation-workflow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageId: pageWorkflow.pageId,
          user: currentUser,
          gitUrl,
          branch,
          enableAutoIteration: true,
          enableStepByStep: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      console.log(`页面工作流重试成功: ${result.taskId}`);

      // 跳转到新的工作流页面
      window.open(`/workflow/${projectId}/${result.taskId}`, '_blank');

      // 重试成功后刷新列表
      await fetchWorkflows();

    } catch (error) {
      console.error('重试页面级工作流失败:', error);
      // 这里可以添加用户提示
    } finally {
      setRetryingWorkflows(prev => {
        const newSet = new Set(prev);

        newSet.delete(workflowKey);

        return newSet;
      });
    }
  }, [projectId, retryingWorkflows, getProjectGitConfig, getCurrentUser, fetchWorkflows]);

  // 自动刷新 - 优化版本：有进行中任务时5秒轮询，无进行中任务时15秒轮询
  useEffect(() => {
    if (!autoRefresh) return;

    let interval;

    // 检查是否有处理中的工作流
    const hasProcessing = workflows ? [
      ...workflows.singlePageWorkflows.filter(w => w != null),
      ...workflows.multiPageWorkflows.filter(w => w != null),
    ].some(workflow => workflow.status === 'processing') : false;

    if (hasProcessing) {
      // 有进行中的任务，使用高频轮询（5秒）
      interval = setInterval(() => {
        fetchWorkflows();
      }, 5000);
    } else {
      // 没有进行中的任务，使用低频轮询（15秒）来检测新任务
      interval = setInterval(() => {
        fetchWorkflows();
      }, 15000);
    }

    return () => clearInterval(interval);
  }, [fetchWorkflows, autoRefresh, workflows]);

  // 初始加载
  useEffect(() => {
    fetchWorkflows();
  }, [fetchWorkflows]);

  // 查看单页面工作流详情
  const handleViewSinglePageWorkflow = (workflow: WorkflowStatus) => {
    const url = `/workflow/${projectId}/${workflow.workflowId}`;

    window.open(url, '_blank');
  };

  // 查看多页面工作流详情
  const handleViewMultiPageWorkflow = (workflow: MultiPageWorkflowStatus) => {
    const url = `/multi-workflow/${projectId}/${workflow.workflowId}`;

    window.open(url, '_blank');
  };

  // 格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '-';
    const date = new Date(timeStr);

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 计算耗时
  const getDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '-';
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffSec = Math.round(diffMs / 1000);

    if (diffSec < 60) return `${diffSec}秒`;
    if (diffSec < 3600) return `${Math.round(diffSec / 60)}分钟`;

    return `${Math.round(diffSec / 3600)}小时`;
  };

  // 合并项目级工作流数据
  const getApplicationWorkflows = () => {
    if (!workflows) return [];

    // 创建一个集合来存储所有多页面工作流中包含的单页面工作流ID
    const singlePageWorkflowsInMultiPage = new Set<string>();

    // 收集所有多页面工作流中的单页面工作流ID
    workflows.multiPageWorkflows.filter(w => w != null).forEach(multiWorkflow => {
      multiWorkflow.pageWorkflows.forEach(pageWorkflow => {
        if (pageWorkflow.workflowId) {
          singlePageWorkflowsInMultiPage.add(pageWorkflow.workflowId);
        }
      });
    });

    // 过滤掉那些属于多页面工作流的单页面工作流
    const filteredSinglePageWorkflows = workflows.singlePageWorkflows
      .filter(w => w != null && !singlePageWorkflowsInMultiPage.has(w.workflowId))
      .map(w => ({
        ...w,
        type: 'single-page' as const,
        pageCount: 1,
      }));

    const allWorkflows = [
      ...filteredSinglePageWorkflows,
      ...workflows.multiPageWorkflows.filter(w => w != null).map(w => ({
        ...w,
        type: 'multi-page' as const,
        pageCount: w.totalPages,
        nodes: [] as WorkflowNode[], // 多页面工作流没有节点概念
      })),
    ];

    return allWorkflows.sort((a, b) => {
      const timeA = new Date(a.startTime || 0).getTime();
      const timeB = new Date(b.startTime || 0).getTime();

      return timeB - timeA; // 降序排列，最新的在前
    });
  };

  // 获取页面级工作流数据 - 包含所有页面生成的执行历史
  const getPageWorkflows = () => {
    if (!workflows) return [];

    const pageWorkflows: (PageWorkflow & {
      isSinglePage: boolean;
      multiWorkflowId?: string;
      workflowType: 'single-page' | 'multi-page';
    })[] = [];

    // 1. 添加所有单页面工作流（每个单页面工作流就是一个页面的生成记录）
    workflows.singlePageWorkflows.filter(w => w != null).forEach(singleWorkflow => {
      pageWorkflows.push({
        pageId: singleWorkflow.pageId || `single-${singleWorkflow.workflowId}`,
        pageName: singleWorkflow.pageName || '未知页面',
        workflowId: singleWorkflow.workflowId,
        status: singleWorkflow.status,
        progress: singleWorkflow.progress,
        startTime: singleWorkflow.startTime,
        endTime: singleWorkflow.endTime,
        error: singleWorkflow.error,
        projectName: singleWorkflow.projectName,
        isSinglePage: true,
        workflowType: 'single-page',
      });
    });

    // 2. 添加多页面工作流中的每个页面执行记录
    workflows.multiPageWorkflows.filter(w => w != null).forEach(multiWorkflow => {
      multiWorkflow.pageWorkflows.forEach(pageWorkflow => {
        pageWorkflows.push({
          ...pageWorkflow,
          multiWorkflowId: multiWorkflow.workflowId,
          isSinglePage: false,
          workflowType: 'multi-page',
        });
      });
    });

    return pageWorkflows.sort((a, b) => {
      const timeA = new Date(a.startTime || 0).getTime();
      const timeB = new Date(b.startTime || 0).getTime();

      return timeB - timeA; // 降序排列，最新的在前
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="flex flex-col items-center gap-4 p-8">
          <Spinner size="lg" />
          <span className="text-gray-600 text-lg">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full flex flex-col h-full">
        {/* 头部 - 保持和正常状态一致 */}
        <div className="bg-white shadow-sm border-b border-gray-200">
          <div className="p-2 pr-0 flex justify-between items-center">
            <div>
              <h1 className="text-base font-bold text-gray-800 mb-1">
                工作流执行记录
              </h1>
            </div>
            <div className="flex gap-3">
              <Button
                className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700 px-2 py-1 text-xs"
                size="sm"
                startContent={<IconRefresh />}
                onClick={fetchWorkflows}
              >
                刷新
              </Button>
              <Button
                className={`transition-all duration-200 rounded-md border-1 dark:border-zinc-700 px-2 py-1 text-xs ${autoRefresh ? 'bg-green-600 text-white' : 'bg-transparent'}`}
                size="sm"
                startContent={autoRefresh ? <IconCheckCircle /> : <IconCircleOutline />}
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                自动刷新
              </Button>
            </div>
          </div>
        </div>

        {/* 错误状态内容 */}
        <div className="flex-1 py-4 flex flex-col">
          {/* 标签切换 - 保持和正常状态一致 */}
          <div className="flex border-b border-gray-200 bg-white flex-shrink-0">
            <Button
              className="p-5 text-sm font-medium transition-all duration-200 flex items-center gap-2 rounded-tl-md bg-blue-600 text-white hover:!bg-blue-600"
              radius="none"
              size="sm"
              variant="light"
              onClick={() => setActiveTab('application')}
            >
              <IconMonitor height={16} width={16} />
              <span>项目级生码工作流 (0)</span>
            </Button>
          </div>

          {/* 错误状态展示区域 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-auto bg-gray-50 rounded-b-lg border border-gray-200 min-h-0">
              <div className="flex items-center justify-center h-full py-20">
                <div className="text-center max-w-md mx-auto px-6">
                  <div className="mb-6">
                    <IconAlert className="mx-auto text-red-500 mb-4" height={64} width={64} />
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      加载失败
                    </h3>
                    <div className="text-gray-600 text-sm leading-relaxed mb-6">
                      {error}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <Button
                      className="w-full transition-all duration-200"
                      color="primary"
                      size="md"
                      startContent={<IconRefresh height={16} width={16} />}
                      onClick={fetchWorkflows}
                    >
                      重新加载
                    </Button>
                    <div className="text-xs text-gray-500">
                      如果问题持续存在，请联系系统管理员
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const applicationWorkflows = getApplicationWorkflows();
  const pageWorkflows = getPageWorkflows();

  return (
    <div className="w-full flex flex-col h-full">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="p-2 pr-0 flex justify-between items-center">
          <div>
            <h1 className="text-base font-bold text-gray-800 mb-1">
              工作流执行记录
            </h1>
          </div>
          <div className="flex gap-3">
            <Button
              className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700 px-2 py-1 text-xs"
              size="sm"
              startContent={<IconRefresh />}
              onClick={fetchWorkflows}
            >
              刷新
            </Button>
            <Button
              className={`transition-all duration-200 rounded-md border-1 dark:border-zinc-700 px-2 py-1 text-xs ${autoRefresh ? 'bg-green-600 text-white' : 'bg-transparent'}`}
              size="sm"
              startContent={autoRefresh ? <IconCheckCircle /> : <IconCircleOutline />}
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              自动刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 py-4 flex flex-col">
        {/* 标签切换 */}
        <div className="flex border-b border-gray-200 bg-white flex-shrink-0">
          <Button
            className={`p-5 text-sm font-medium transition-all duration-200 flex items-center gap-2 rounded-tl-md ${activeTab === 'application'
                ? 'bg-blue-600 text-white hover:!bg-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            radius="none"
            size="sm"
            variant="light"
            onClick={() => setActiveTab('application')}
          >
            <IconMonitor height={16} width={16} />
            <span>项目级生码工作流 ({applicationWorkflows.length})</span>
          </Button>
          {/* 隐藏页面级工作流tab */}
          {/* <Button
            className={`p-5 text-sm font-medium transition-all duration-200 flex items-center gap-2 rounded-tr-md ${activeTab === 'page'
                ? 'bg-blue-600 text-white hover:!bg-blue-600'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
            radius="none"
            size="sm"
            variant="light"
            onClick={() => setActiveTab('page')}
          >
            <IconFileDocument height={16} width={16} />
            <span>页面级生码工作流 ({pageWorkflows.length})</span>
          </Button> */}
        </div>

        {/* 表格内容区域 */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* 项目级工作流表格 */}
          {activeTab === 'application' && (
            <div className="flex-1 overflow-auto bg-gray-50 rounded-b-lg border border-gray-200 min-h-0">
              <Card className="shadow-none border-0 h-full">
                <CardBody className="p-0 h-full">
                  <Table
                    aria-label="项目级工作流表格"
                    className="h-full"
                  >
                    <TableHeader>
                      <TableColumn>工作流ID</TableColumn>
                      <TableColumn>类型</TableColumn>
                      <TableColumn>状态</TableColumn>
                      {/* 隐藏进度列 */}
                      {/* <TableColumn>进度</TableColumn> */}
                      <TableColumn>页面数</TableColumn>
                      <TableColumn>页面名称</TableColumn>
                      <TableColumn>开始时间</TableColumn>
                      <TableColumn>耗时</TableColumn>
                      <TableColumn>操作</TableColumn>
                    </TableHeader>
                    <TableBody
                      emptyContent={
                        <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                          <IconMonitor className="mb-4" height={48} width={48} />
                          <div className="text-base font-semibold">暂无项目级工作流执行记录</div>
                        </div>
                      }
                      items={applicationWorkflows.map((workflow, index) => ({
                        ...workflow,
                        key: workflow.workflowId || `workflow-${index}`
                      }))}
                    >
                      {(workflow) => (
                        <TableRow key={workflow.key}>
                          <TableCell>
                            <div 
                              className="font-mono text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer underline"
                              onClick={() => {
                                if (workflow.type === 'single-page') {
                                  handleViewSinglePageWorkflow(workflow as WorkflowStatus);
                                } else {
                                  handleViewMultiPageWorkflow(workflow as MultiPageWorkflowStatus);
                                }
                              }}
                            >
                              {workflow.workflowId}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Chip color={workflow.type === 'single-page' ? 'primary' : 'success'} size="sm" variant="flat">
                              {workflow.type === 'single-page' ? '单页面' : '多页面'}
                            </Chip>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <StatusIcon status={workflow.status} />
                              <StatusBadge status={workflow.status} />
                            </div>
                          </TableCell>
                          {/* 隐藏进度列 */}
                          {/* <TableCell>
                            <ProgressBar progress={workflow.progress} status={workflow.status} />
                          </TableCell> */}
                          <TableCell>
                            <span className="text-sm text-gray-800">
                              {workflow.pageCount}
                            </span>
                          </TableCell>
                          <TableCell>
                            {workflow.type === 'single-page' ? (
                              <div className="flex flex-wrap items-center gap-2 max-w-md min-h-8">
                                <Chip
                                  color={workflow.status === 'failed' ? 'danger' : 'primary'}
                                  size="sm"
                                  title={workflow.status === 'failed' ? '任务失败' : (workflow.pageName || '未知页面')}
                                  variant="flat"
                                >
                                  {workflow.pageName || '未知页面'}
                                </Chip>
                              </div>
                            ) : (
                              <div className="flex flex-wrap items-center gap-2 max-w-md min-h-8">
                                {workflow.pageWorkflows?.slice(0, 6).map((pageWorkflow: any, index: number) => {
                                  // 获取页面工作流状态来决定颜色
                                  const pageColor = pageWorkflow.status === 'failed' ? 'danger' : 'success';
                                  const pageTooltip = pageWorkflow.status === 'failed' ? '任务失败' : (pageWorkflow.pageName || '未知页面');
                                  
                                  return (
                                    <Chip
                                      key={index}
                                      color={pageColor}
                                      size="sm"
                                      title={pageTooltip}
                                      variant="flat"
                                    >
                                      {pageWorkflow.pageName || '未知页面'}
                                    </Chip>
                                  );
                                })}
                                {workflow.pageWorkflows?.length > 6 && (
                                  <Chip
                                    color="default"
                                    size="sm"
                                    title={`共${workflow.pageWorkflows.length}个页面`}
                                    variant="flat"
                                  >
                                    +{workflow.pageWorkflows.length - 6}
                                  </Chip>
                                )}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-gray-600">
                              {formatTime(workflow.startTime)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-gray-600">
                              {getDuration(workflow.startTime, workflow.endTime)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                className="transition-all duration-200"
                                size="sm"
                                variant="light"
                                onClick={() => {
                                  if (workflow.type === 'single-page') {
                                    handleViewSinglePageWorkflow(workflow as WorkflowStatus);
                                  } else {
                                    handleViewMultiPageWorkflow(workflow as MultiPageWorkflowStatus);
                                  }
                                }}
                              >
                                查看详情
                              </Button>
                              <Button
                                className="transition-all duration-200"
                                color="primary"
                                size="sm"
                                startContent={<IconMerge />}
                                variant="light"
                                onClick={() => handleViewMRs(workflow)}
                              >
                                查看MR
                              </Button>
                              {workflow.status === 'failed' && (
                                <Button
                                  className="transition-all duration-200"
                                  color="warning"
                                  disabled={retryingWorkflows.has(workflow.workflowId)}
                                  isLoading={retryingWorkflows.has(workflow.workflowId)}
                                  size="sm"
                                  startContent={<IconRestart />}
                                  variant="light"
                                  onClick={() => retryApplicationWorkflow(workflow)}
                                >
                                  {retryingWorkflows.has(workflow.workflowId) ? '重试中...' : '重试'}
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardBody>
              </Card>
            </div>
          )}

          {/* 页面级工作流表格 */}
          {/* 隐藏页面级工作流表格 */}
          {/* {activeTab === 'page' && (
            <div className="flex-1 overflow-auto bg-gray-50 rounded-b-lg border border-gray-200 min-h-0">
              <Card className="shadow-none border-0 h-full">
                <CardBody className="p-0 h-full">
                  <Table aria-label="页面级工作流表格" className="h-full">
                    <TableHeader>
                      <TableColumn>页面ID</TableColumn>
                      <TableColumn>工作流类型</TableColumn>
                      <TableColumn>页面名称</TableColumn>
                      <TableColumn>状态</TableColumn>
                      <TableColumn>进度</TableColumn>
                      <TableColumn>所属工作流</TableColumn>
                      <TableColumn>开始时间</TableColumn>
                      <TableColumn>耗时</TableColumn>
                      <TableColumn>操作</TableColumn>
                    </TableHeader>
                    <TableBody
                      emptyContent={
                        <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                          <IconFileDocument className="mb-4" height={48} width={48} />
                          <div className="text-base font-semibold">暂无页面级工作流执行记录</div>
                        </div>
                      }
                      items={pageWorkflows.map((pageWorkflow, index) => {
                        const workflowKey = pageWorkflow.isSinglePage ?
                          `single-${pageWorkflow.workflowId}` :
                          `multi-${pageWorkflow.multiWorkflowId || pageWorkflow.workflowId}-${pageWorkflow.pageId}`;

                        return {
                          ...pageWorkflow,
                          key: workflowKey || `page-workflow-${index}`
                        };
                      })}
                    >
                      {(pageWorkflow) => {
                        return (
                          <TableRow key={pageWorkflow.key}>
                            <TableCell>
                              <div className="font-mono text-sm text-gray-600">
                                {pageWorkflow.pageId}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Chip color={pageWorkflow.workflowType === 'single-page' ? 'primary' : 'success'} size="sm" variant="flat">
                                {pageWorkflow.workflowType === 'single-page' ? '单页面' : '多页面'}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm font-medium text-gray-800">
                                {pageWorkflow.pageName || `页面-${pageWorkflow.pageId}`}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <StatusIcon status={pageWorkflow.status} />
                                <StatusBadge status={pageWorkflow.status} />
                              </div>
                            </TableCell>
                            <TableCell>
                              <ProgressBar progress={pageWorkflow.progress} status={pageWorkflow.status} />
                            </TableCell>
                            <TableCell>
                              <div className="font-mono text-sm text-gray-600">
                                {pageWorkflow.isSinglePage ? pageWorkflow.workflowId : (pageWorkflow.multiWorkflowId || pageWorkflow.workflowId)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-600">
                                {formatTime(pageWorkflow.startTime)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-600">
                                {getDuration(pageWorkflow.startTime, pageWorkflow.endTime)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button
                                  className="transition-all duration-200"
                                  size="sm"
                                  variant="light"
                                  onClick={() => {
                                    if (pageWorkflow.isSinglePage) {
                                      navigate(`/workflow/${projectId}/${pageWorkflow.workflowId}`);
                                    } else {
                                      navigate(`/multi-workflow/${projectId}/${pageWorkflow.multiWorkflowId}`);
                                    }
                                  }}
                                >
                                  查看详情
                                </Button>
                                <Button
                                  className="transition-all duration-200"
                                  color="primary"
                                  size="sm"
                                  startContent={<IconMerge />}
                                  variant="light"
                                  onClick={() => handleViewMRs(pageWorkflow, true)}
                                >
                                  查看MR
                                </Button>
                                {pageWorkflow.status === 'failed' && (
                                  <Button
                                    className="transition-all duration-200"
                                    color="warning"
                                    disabled={retryingWorkflows.has(pageWorkflow.key)}
                                    isLoading={retryingWorkflows.has(pageWorkflow.key)}
                                    size="sm"
                                    startContent={<IconRestart />}
                                    variant="light"
                                    onClick={() => retryPageWorkflow(pageWorkflow)}
                                  >
                                    {retryingWorkflows.has(pageWorkflow.key) ? '重试中...' : '重试'}
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      }}
                    </TableBody>
                  </Table>
                </CardBody>
              </Card>
            </div>
          )} */}
        </div>
      </div>

      {/* MR列表弹窗 */}
      <WorkflowMRModal
        pageId={undefined}
        projectId={projectId!}
        title={selectedWorkflow?.isPageWorkflow ?
          `页面 "${selectedWorkflow?.pageName}" 的生码结果MR` :
          '项目级工作流生码结果MR'
        }
        visible={isMRModalVisible}
        workflowId={selectedWorkflow?.workflowId}
        onCancel={handleCloseMRModal}
      />
    </div>
  );
};

export const Component = WorkflowManager; 