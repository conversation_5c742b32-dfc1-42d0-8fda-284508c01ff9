import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { DesignProjectDetail } from "@/components/DesignToCode/DesignProjectDetail";
import { ProjectGrid } from "@/components/Project/ProjectGrid";
import { useProjectStore } from "@/stores/project";

export function Component() {
  const { id } = useParams();
  const { setCurrent } = useProjectStore();

  useEffect(() => {
    if (id) {
      setCurrent(id);
    } else {
      setCurrent(undefined);
    }
  }, [id, setCurrent]);

  return (
    <div className="p-6">
      {id ? (
        <DesignProjectDetail projectId={id} />
      ) : (
        <ProjectGrid />
      )}
    </div>
  );
}
