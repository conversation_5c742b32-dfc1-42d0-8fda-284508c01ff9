import { useState, useEffect } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { useParams } from "react-router-dom";
import { useStore } from "zustand";
import { ArtifactEditor } from "@/components/ArtifactEditor";
import { Chat } from "@/components/Chat";
import { createPlaygroundStore, PlaygroundContext, StoreType } from "@/stores/playground";

export function Component() {
  const id = useParams().id!;
  const [store] = useState<StoreType>(() => {
    return createPlaygroundStore(id);
  });

  const pgStore = useStore(store);
  const [isProjectPreview, setIsProjectPreview] = useState(false);

  // 检测是否为项目预览模式
  useEffect(() => {
    const checkProjectPreviewMode = () => {
      const playground = pgStore.playground;

      if (playground?.tags) {
        const hasProjectPreviewTag = playground.tags.includes('project-preview');

        setIsProjectPreview(hasProjectPreviewTag);

        if (hasProjectPreviewTag) {
          console.log('🎯 [Chat] 检测到项目预览模式，将显示特殊布局');
        }
      }
    };

    // 初始检查
    checkProjectPreviewMode();

    // 监听playground变化
    const unsubscribe = store.subscribe(
      (state) => state.playground,
      checkProjectPreviewMode
    );

    return unsubscribe;
  }, [pgStore.playground, store]);

  // 项目预览模式 - 仅显示ArtifactEditor布局
  if (isProjectPreview) {
    return (
      <PlaygroundContext.Provider value={store}>
        <PanelGroup className="flex-1 flex flex-row h-full" direction="horizontal">
          <Panel>
            <ArtifactEditor />
          </Panel>
        </PanelGroup>
      </PlaygroundContext.Provider>
    );
  }

  // 普通模式：显示标准的Chat + ArtifactEditor布局
  return (
    <PlaygroundContext.Provider value={store}>
      <PanelGroup className="flex-1 flex flex-row h-full" direction="horizontal">
        <Panel className="flex overflow-hidden" defaultSize={50}>
          <Chat id={id} />
        </Panel>
        <PanelResizeHandle />
        <Panel>
          <ArtifactEditor />
        </Panel>
      </PanelGroup>
    </PlaygroundContext.Provider>
  );
}
