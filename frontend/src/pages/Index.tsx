import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ChatBox } from "@/components/ChatBox";
import { PlaygroundListWithLimit } from "@/components/PlaygroundListWithLimit";
import { title } from "@/components/primitives";
import { examples } from "@/config/examples";

export default function IndexPage() {
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<string | null>(null);


  return (
    <div className="relative">
      {/* 主要内容区域 */}
      <div className="flex flex-col items-center justify-center px-6 pb-24 pt-36">
        <h1 className={title({ size: 'lg', className: 'mb-8'})}>
          AI 驱动开发，释放创造潜力
        </h1>
        
        <div className="w-full max-w-4xl">
          <ChatBox
            examples={examples}
            projectId={selectedProject}
            onCreate={(id) => {
              navigate('/chat/' + id);
            }}
            onProjectChange={(id) => setSelectedProject(id || null)}
          />
        </div>
      </div>
      
      {/* 项目列表区域 */}
      <div className="border-t border-gray-200/60 dark:border-zinc-800/60 bg-gradient-to-b from-white/40 to-gray-50/60 dark:from-zinc-900/40 dark:to-zinc-950/60 backdrop-blur-sm">
        <div className="p-6 pb-20">
          <PlaygroundListWithLimit projectId={selectedProject} />
        </div>
      </div>
    </div>
  );
}
