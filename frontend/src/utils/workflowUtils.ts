/**
 * 工作流相关的工具函数
 */

// 获取项目Git配置
export async function getProjectGitConfig(projectId: string): Promise<{ gitUrl: string; branch: string }> {
  try {
    const response = await fetch(`/api/design-project/${projectId}`);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const project = await response.json();
    
    // 从项目设置中获取Git配置，如果没有设置则使用默认值
    const gitUrl = project?.gitUrl || 'git@168.61.114.20:codebox-test/zhangyu-test/eam-web-app.git';
    const branch = project?.gitBranch || 'dev';
    
    return { gitUrl, branch };
  } catch (error) {
    console.error('获取项目Git配置失败:', error);

    // 返回默认值
    return {
      gitUrl: 'git@168.61.114.20:codebox-test/zhangyu-test/eam-web-app.git',
      branch: 'dev'
    };
  }
}

// 重试单页面工作流
export async function retrySinglePageWorkflow(
  projectId: string,
  pageId: string,
  user: string,
  options: {
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
  } = {}
): Promise<{ taskId: string }> {
  const { gitUrl, branch } = await getProjectGitConfig(projectId);

  const response = await fetch(`/api/design-project/${projectId}/tasks/page-code-generation-workflow`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      pageId,
      user,
      gitUrl,
      branch,
      enableAutoIteration: options.enableAutoIteration ?? true,
      enableStepByStep: options.enableStepByStep ?? false,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

// 重试多页面工作流
export async function retryMultiPageWorkflow(
  projectId: string,
  pageIds: string[],
  user: string,
  options: {
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
  } = {}
): Promise<{ taskId: string }> {
  const { gitUrl, branch } = await getProjectGitConfig(projectId);

  const response = await fetch(`/api/design-project/${projectId}/tasks/multi-page-code-generation-workflow`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      pageIds,
      user,
      gitUrl,
      branch,
      enableAutoIteration: options.enableAutoIteration ?? true,
      enableStepByStep: options.enableStepByStep ?? false,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

// 启动项目级代码生成工作流
export async function startProjectCodeGenWorkflow(
  projectId: string,
  pageIds: string[],
  user: string,
  options: {
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
  } = {}
): Promise<{ taskId: string; workflowType: 'single-page' | 'multi-page' }> {
  if (pageIds.length === 1) {
    // 单页面工作流
    const result = await retrySinglePageWorkflow(projectId, pageIds[0], user, options);

    return { ...result, workflowType: 'single-page' };
  } else {
    // 多页面工作流
    const result = await retryMultiPageWorkflow(projectId, pageIds, user, options);

    return { ...result, workflowType: 'multi-page' };
  }
}

// 重试现有工作流（重试，不是创建新工作流）
export async function restartWorkflow(
  projectId: string,
  workflowId: string,
  user: string
): Promise<{ workflowId: string; status: any }> {
  const response = await fetch(`/api/design-project/${projectId}/workflows/${workflowId}/restart`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
} 