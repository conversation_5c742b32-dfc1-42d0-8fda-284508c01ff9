import { addToast } from '@heroui/toast';
import copy from 'copy-to-clipboard';

export interface ICopyOptions {
  debug?: boolean;
  message?: string;
  format?: string; // MIME type
  onCopy?: (clipboardData: object) => void;
}
const copyContent = (text: string, options?: ICopyOptions) => {
  const isSuccess = copy(text, options);

  if (isSuccess) {
    addToast({
      title: '复制成功',
      color: 'success',
    });
  } else {
    addToast({
      title: '复制失败',
      description: '请手动复制内容',
      color: 'danger',
    });
  }
};

export default copyContent;
