import { CSSProperties } from 'react';

/**
 * 通用页面样式工具
 * 解决页面高度与布局容器的适配问题
 * 适用于所有需要全屏展示的页面组件
 */

// 主页面容器样式 - 适应布局的main容器
export const PAGE_CONTAINER_STYLES: CSSProperties = {
  height: '100%', // 使用100%而不是100vh，适应布局容器
  backgroundColor: '#f5f6fa',
  padding: '24px',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden', // 防止不必要的滚动条
};

// 页面头部区域样式
export const PAGE_HEADER_STYLES: CSSProperties = {
  background: 'white',
  padding: '24px 32px',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
  marginBottom: '24px',
  flexShrink: 0, // 防止头部被压缩
};

// 页面主内容区域样式
export const PAGE_CONTENT_STYLES: CSSProperties = {
  background: 'white',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
  flex: 1, // 占据剩余空间
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden', // 内容超出时由子元素处理滚动
};

// 加载状态容器样式
export const LOADING_CONTAINER_STYLES: CSSProperties = {
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f5f6fa',
};

// 错误状态容器样式
export const ERROR_CONTAINER_STYLES: CSSProperties = {
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexDirection: 'column',
  backgroundColor: '#f5f6fa',
};

// 空状态容器样式
export const EMPTY_STATE_STYLES: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  flex: 1,
  color: '#9ca3af',
  padding: '32px',
};

// 表格容器样式 - 处理表格滚动
export const TABLE_CONTAINER_STYLES: CSSProperties = {
  flex: 1,
  overflow: 'auto', // 只有表格内容需要滚动
};

// 固定表头样式
export const STICKY_TABLE_HEADER_STYLES: CSSProperties = {
  position: 'sticky',
  top: 0,
  backgroundColor: '#f9fafb',
  zIndex: 1,
};

// 标签切换区域样式
export const TAB_CONTAINER_STYLES: CSSProperties = {
  display: 'flex',
  borderBottom: '1px solid #e5e7eb',
  flexShrink: 0, // 防止标签区域被压缩
};

// 表格内容区域样式
export const TABLE_CONTENT_AREA_STYLES: CSSProperties = {
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden', // 让表格容器处理滚动
};

/**
 * 通用按钮样式生成器
 */
export const createButtonStyles = (
  variant: 'primary' | 'secondary' | 'ghost' = 'primary',
  size: 'sm' | 'md' | 'lg' = 'md'
): CSSProperties => {
  const baseStyles: CSSProperties = {
    border: 'none',
    borderRadius: '6px',
    cursor: 'pointer',
    fontWeight: '500',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    transition: 'all 0.2s ease',
  };

  const sizeStyles = {
    sm: { padding: '4px 8px', fontSize: '12px' },
    md: { padding: '8px 16px', fontSize: '14px' },
    lg: { padding: '12px 24px', fontSize: '16px' },
  };

  const variantStyles = {
    primary: {
      backgroundColor: '#1890ff',
      color: 'white',
    },
    secondary: {
      backgroundColor: '#f0f0f0',
      color: '#666',
    },
    ghost: {
      backgroundColor: 'transparent',
      color: '#374151',
    },
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
};

/**
 * 通用表格单元格样式
 */
export const TABLE_CELL_STYLES: CSSProperties = {
  padding: '16px',
  verticalAlign: 'middle',
};

export const TABLE_HEADER_CELL_STYLES: CSSProperties = {
  padding: '12px 16px',
  textAlign: 'left',
  fontSize: '12px',
  fontWeight: '600',
  color: '#374151',
  borderBottom: '1px solid #e5e7eb',
  whiteSpace: 'nowrap',
};

/**
 * 页面布局Hook - 未来可以扩展为React Hook
 * 目前提供样式常量，后续可以添加响应式逻辑
 */
export const usePageLayout = () => {
  return {
    PAGE_CONTAINER_STYLES,
    PAGE_HEADER_STYLES,
    PAGE_CONTENT_STYLES,
    LOADING_CONTAINER_STYLES,
    ERROR_CONTAINER_STYLES,
    EMPTY_STATE_STYLES,
    TABLE_CONTAINER_STYLES,
    STICKY_TABLE_HEADER_STYLES,
    TAB_CONTAINER_STYLES,
    TABLE_CONTENT_AREA_STYLES,
    createButtonStyles,
    TABLE_CELL_STYLES,
    TABLE_HEADER_CELL_STYLES,
  };
};

/**
 * 使用说明：
 * 
 * 1. 页面根容器使用 PAGE_CONTAINER_STYLES
 * 2. 页面头部使用 PAGE_HEADER_STYLES  
 * 3. 主内容区域使用 PAGE_CONTENT_STYLES
 * 4. 表格相关使用对应的表格样式
 * 
 * 示例：
 * ```tsx
 * import { PAGE_CONTAINER_STYLES, PAGE_HEADER_STYLES } from '@/utils/pageStyles';
 * 
 * export const MyPage = () => {
 *   return (
 *     <div style={PAGE_CONTAINER_STYLES}>
 *       <div style={PAGE_HEADER_STYLES}>
 *         页面头部内容
 *       </div>
 *       <div style={PAGE_CONTENT_STYLES}>
 *         主要内容
 *       </div>
 *     </div>
 *   );
 * };
 * ```
 */ 