export function isBinaryFile(path: string) {
    return /\.(png|jpg|jpeg|gif|ttf|eot|woff|woff2|pdf|mp3|mp4|webm|ogg|flac|wav|zip|tar|gz|bz2|7z|rar|exe|dll|so|bin|DS_Store)$/.test(path);
}

export function isImage(path: string) {
    return /\.(png|jpg|jpeg|gif)$/.test(path);
}

export const processHtmlContent = (htmlContent: string, cssContent?: string) => {
  let processedHtml = htmlContent;
  
  if (!cssContent) return processedHtml;

  // 1. 移除外联样式链接（避免 404 错误）
  processedHtml = processedHtml.replace(
    /<link[^>]*(?:rel=["']stylesheet["']|type=["']text\/css["']|href=["'][^"']*\.css["'])[^>]*>/gi,
    ''
  );

  // 2. 创建内联样式标签并插入到 </head> 前
  const styleTag = `<style type="text/css">\n${cssContent}\n</style>`;

  processedHtml = processedHtml.replace('</head>', `${styleTag}\n</head>`);

  return processedHtml;
};