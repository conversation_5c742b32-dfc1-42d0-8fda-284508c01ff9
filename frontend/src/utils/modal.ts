import { eventBus } from "./eventBus";
import { type ReactNode } from "react";

export async function confirm(args: {
  title?: ReactNode;
  message: ReactNode;
  buttons?: ReactNode[];
}) {
  return new Promise<boolean>((resolve) => {
    eventBus.emit('show-confirm', {
      ...args,
      onConfirm: () => {
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      },
    });
  });
}
