import { DesignProject } from "@/apis";

export const isProjectConfigured = (project: Partial<DesignProject>): boolean => {
  return !!(project.gitUrl && project.gitBranch && project.gitCodeDir && project.lanhuProjectId);
}

/**
 * 校验GitLab仓库地址是否为SSH协议格式
 * @param gitUrl GitLab仓库地址
 * @returns 是否为有效的SSH格式
 */
export const isValidGitLabSshUrl = (gitUrl: string): boolean => {
  // 允许的GitLab域名列表
  const allowedHosts = [
    'gitlab2.htsc',
    'gitlab.htzq.htsc.com.cn',
    '*************',
    '************'
  ];
  
  // SSH协议格式: git@允许的host:path/to/repo.git
  const hostPattern = allowedHosts.map(host => host.replace(/\./g, '\\.')).join('|');
  const sshGitPattern = new RegExp(`^git@(${hostPattern}):.+\\/[^/]+\\.git$`);
  
  return sshGitPattern.test(gitUrl);
} 