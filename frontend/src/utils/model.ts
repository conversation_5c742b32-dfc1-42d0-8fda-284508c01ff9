export function splitModel(modelConfig: string): { type: string, modelName: string, model: string } {
  // 防护：如果modelConfig为空或undefined，返回默认值
  if (!modelConfig || typeof modelConfig !== 'string') {
    return { type: 'unknown', modelName: 'unknown', model: 'unknown' };
  }

  const parts = modelConfig.split('::');
  const type = parts[0] || 'unknown';
  const model = parts[1] || modelConfig; // 如果没有::分隔符，使用原始配置作为model
  
  // 防护：确保model是字符串
  if (typeof model !== 'string') {
    return { type, modelName: 'unknown', model: 'unknown' };
  }

  const index = model.indexOf('/');
  return { type, modelName: index !== -1 ? model.slice(index + 1) : model, model };
}
