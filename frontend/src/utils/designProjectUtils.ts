import { DesignProject, updatePagePrototype, SlicedAssets } from '@/apis';

export async function updatePrototypeSlicedAssets(
  project: DesignProject,
  prototypeId: string,
  slicedAssets: SlicedAssets,
  onProjectUpdate: (project: DesignProject) => void
): Promise<DesignProject> {
  if (!project) {
    throw new Error('项目数据不能为空');
  }

  // 验证数据格式
  if (!validateSlicedAssets(slicedAssets)) {
    throw new Error('SlicedAssets 数据格式不正确');
  }

  // 第一步：更新本地状态
  const updatedProject = {
    ...project,
    pages: project.pages?.map(page => ({
      ...page,
      prototypes: page.prototypes?.map(prototype =>
        prototype.id === prototypeId
          ? { ...prototype, slicedAssets }
          : prototype
      ),
    })),
  };

  try {
    // 第二步：更新数据库
    // 找到原型所属的页面信息
    const prototype = project.pages
      ?.flatMap(page => page.prototypes?.map(p => ({ ...p, pageId: page.id })))
      ?.find(p => p.id === prototypeId);

    if (!prototype) {
      throw new Error(`未找到原型 ID: ${prototypeId}`);
    }

    await updatePagePrototype({
      projectId: project.id,
      pageId: prototype.pageId,
      prototypeId: prototypeId,
      slicedAssets: slicedAssets,
    });

    onProjectUpdate(updatedProject);

    return updatedProject;
  } catch (error) {
    // 如果数据库更新失败，回滚本地状态
    onProjectUpdate(project);
    throw new Error(`更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}


/**
 * 验证 SlicedAssets 数据格式
 * @param slicedAssets 要验证的数据
 * @returns boolean 是否有效
 */
export function validateSlicedAssets(slicedAssets: any): slicedAssets is SlicedAssets {
  if (!slicedAssets || typeof slicedAssets !== 'object') {
    return false;
  }

  // 验证 html 字段
  if (slicedAssets.html) {
    if (!slicedAssets.html.name || !slicedAssets.html.content) {
      return false;
    }
  }

  // 验证 segment 字段
  if (slicedAssets.segment) {
    if (!Array.isArray(slicedAssets.segment)) {
      return false;
    }
    
    for (const segment of slicedAssets.segment) {
      if (!segment.id || !segment.name || !segment.imgUrl || !segment.position) {
        return false;
      }
    }
  }

  return true;
}

/**
 * 更新原型的 HTML 和 CSS 内容
 * @param project 项目数据
 * @param prototypeId 原型ID
 * @param htmlContent HTML内容
 * @param cssContent CSS内容
 * @param setProject 更新项目状态的函数
 * @returns 更新后的项目数据
 */
export async function updatePrototypeContent(
  project: DesignProject,
  prototypeId: string,
  htmlContent: string,
  cssContent: string,
  onProjectUpdate: (project: DesignProject) => void
): Promise<DesignProject> {
  if (!project) {
    throw new Error('项目数据不能为空');
  }

  // 第一步：更新本地状态
  const updatedProject = {
    ...project,
    pages: project.pages?.map(page => ({
      ...page,
      prototypes: page.prototypes?.map(prototype =>
        prototype.id === prototypeId
          ? { ...prototype, htmlContent, cssContent }
          : prototype
      ),
    })),
  };

  try {
    // 第二步：更新数据库
    // 找到原型所属的页面信息
    const prototype = project.pages
      ?.flatMap(page => page.prototypes?.map(p => ({ ...p, pageId: page.id })))
      ?.find(p => p.id === prototypeId);

    if (!prototype) {
      throw new Error(`未找到原型 ID: ${prototypeId}`);
    }

    await updatePagePrototype({
      projectId: project.id,
      pageId: prototype.pageId,
      prototypeId: prototypeId,
      htmlContent,
      cssContent,
    });

    onProjectUpdate(updatedProject);


    return updatedProject;
  } catch (error) {
    // 如果数据库更新失败，回滚本地状态
    onProjectUpdate(project);
    throw new Error(`更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}