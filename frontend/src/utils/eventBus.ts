import { JSONValue } from "@ai-sdk/ui-utils";
import mitt from "mitt";
import { type ReactNode } from "react";

export interface ConfirmType {
  title?: ReactNode;
  message: ReactNode;
  buttons?: ReactNode[];
  onConfirm: () => void;
  onCancel: () => void;
}

export type AppendChatMessage = string | {
  text: string;
  annotations?: any[];
  cbk?: (ret: string) => void;
}

export interface Events extends Record<string | symbol, any> {
  'show-confirm': ConfirmType;                           // 显示确认弹窗
  'show-lightbox': { src: string }[];                    // 显示图片灯箱
  'artifact::set-tab': 'preview' | 'editor' | 'log';     // 聊天界面tab跳转
  'append-chat': AppendChatMessage;                      // 用户输入
  'artifact::new': number;                               // 新版本
  'artifact::update': void;                              // 更新版本
  'message::add-annotation': {                           // 添加消息注解
    messageId: string;
    annotation: any;
  };
}

export const eventBus = mitt<Events>();
