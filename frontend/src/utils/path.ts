import { dirname as patheDirname, basename, extname, join, resolve, normalize } from 'pathe';

export function splitPath(path: string) {
  // 使用pathe库来处理跨平台路径
  const normalizedPath = normalize(path);
  const dir = patheDirname(normalizedPath);
  const name = basename(normalizedPath);
  
  return {
    parent: dir === '.' ? '' : dir,
    name: name,
  };
}

export function dirname(path: string) {
  const { parent } = splitPath(path);
  return parent;
}

// 导出其他常用的路径工具函数
export { basename, extname, join, resolve, normalize };
