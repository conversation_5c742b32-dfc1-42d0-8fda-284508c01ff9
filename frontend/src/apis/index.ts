import { baseUrl } from '../apis';

export const getRunningTasks = async (playgroundId: string) => {
  const response = await fetch(`${baseUrl}/api/playground/${playgroundId}/running-tasks`);

  if (!response.ok) {
    throw new Error('Failed to get running tasks');
  }

  return response.json();
};

export const getChat = async ({ id, sort }: { id: string; sort?: 'asc' | 'desc' }) => {
  const response = await fetch(`${baseUrl}${id}/@chat?${new URLSearchParams({
    ...(sort && { sort })
  }).toString()}`);

  if (!response.ok) {
    throw new Error('Failed to get chat messages');
  }

  return response.json();
};
