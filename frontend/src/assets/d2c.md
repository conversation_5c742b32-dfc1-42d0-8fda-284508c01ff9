## 介绍

[D2C](http://gitlab2.htsc/htsc-web/d2c/codegen.git) 项目旨在帮助公司内部将设计稿转换为代码。

## AiCoding 平台中使用 D2C

通过 Figma 插件，D2C 可以与 AiCoding 平台无缝集成，实现设计稿与 AiCoding 系统的自动对接，支持后续的二次开发。


### 获取 Figma 插件

有两种方式可以获取 Figma 插件：

#### 构建 Fgima 插件

克隆项目代码

```
git clone http://gitlab2.htsc/htsc-web/d2c/codegen.git
```

构建 Figma 插件

```
cd plugins/figma

yarn install

yarn build
```
运行后在当前目录生成 code.js 文件。之后，通过 figma 导入插件


#### 直接下载 Figma 插件

下载 [D2C 插件](https://cft-3g-test.s3.cn-north-1.amazonaws.com.cn/pic/cft/picture/mofang/rc-upload-1743476440894-5-plugin.zip)，下载后解压文件

### 安装 Figma 插件

1、打开 Figma，选择“导入插件”。

<img src="https://cft-3g-test.s3.cn-north-1.amazonaws.com.cn/pic/cft/picture/mofang/rc-upload-1743476440894-6-clipbord_1743477388545.png" width="500"/>

2、导入插件时，选择 manifest.json 文件：

<img src="https://cft-3g-test.s3.cn-north-1.amazonaws.com.cn/pic/cft/picture/mofang/<EMAIL>" width="500"/>

### 使用插件

使用 Figma 插件，导出到 Aicoding 平台

<img src="https://cft-3g-test.s3.cn-north-1.amazonaws.com.cn/pic/cft/picture/mofang/<EMAIL>" width="300"/>

### 在 Aicoding 中

在 Aicoding 中生成对应的 Aicoding 项目 (figma 插件项目)

<img src="https://cft-3g-test.s3.cn-north-1.amazonaws.com.cn/pic/cft/picture/mofang/<EMAIL>" width="800"/>

选择合适的项目并执行代码生成。
