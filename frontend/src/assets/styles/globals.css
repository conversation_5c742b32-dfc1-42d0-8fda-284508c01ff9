html.dark {
  --border-color: #27272a;
  --text-color: #eee;
  --background-color: black;
}
html.light {
  --border-color: #f4f4f5;
  --text-color: #222;
  --background-color: white;
}

/* Mac风格的滚动条样式 */
/* Webkit浏览器 (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 深色模式下的滚动条 */
html.dark ::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

/* Firefox浏览器 */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

html.dark * {
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

/* 针对特定元素的滚动条样式 */
.cm-scroller::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.cm-scroller::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 3px;
}

.cm-scroller::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

html.dark .cm-scroller::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.2);
}

html.dark .cm-scroller::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

div[data-resize-handle] {
  &[data-panel-group-direction='vertical'] {
    height: 1px;
    background: var(--border-color);
  }
  &[data-panel-group-direction='horizontal'] {
    width: 1px;
    background: var(--border-color);
  }
  &[data-resize-handle-state='hover'], &[data-resize-handle-state='drag'] {
    @apply bg-indigo-500;
    transition: all 0.2s ease-in-out;
  }
}

/* Loading dots animation */
@keyframes loadingDots {
  0%, 20% { opacity: 1; }
  50%, 100% { opacity: 0.3; }
}

.loading-dots span {
  animation: loadingDots 1.5s infinite;
}

.loading-dots .dot-1 {
  animation-delay: 0s;
}

.loading-dots .dot-2 {
  animation-delay: 0.3s;
}

.loading-dots .dot-3 {
  animation-delay: 0.6s;
}

/* Tour 引导组件自定义样式 - 仿照参考图片设计 */
.tour-custom .ant-tour-content {
  min-width: 380px;
  max-width: 420px;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  background: #ffffff !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 强制覆盖 antd primary theme 的蓝色背景 */
.tour-custom.ant-tour.ant-tour-primary .ant-tour-inner {
  background-color: #ffffff !important;
}

.tour-custom.ant-tour.ant-tour-primary .ant-tour-inner .ant-tour-close {
  color: var(--ant-color-icon);
}

.tour-custom.ant-tour.ant-tour-primary .ant-tour-content {
  background-color: #ffffff !important;
}

.tour-custom .ant-tour-header {
  padding: 24px 24px 0 24px;
  border-bottom: none;
}

.tour-custom .ant-tour-header .ant-tour-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
}

.tour-custom .ant-tour-inner {
  padding: 16px 24px 0 24px;
  background-color: #ffffff !important;
}

.tour-custom .ant-tour-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

.tour-custom .ant-tour-footer {
  padding: 20px 24px 24px 24px;
  border-top: 1px solid #f3f4f6;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tour-custom .ant-tour-arrow {
  border-color: #ffffff;
}

.tour-custom .ant-tour-arrow::before {
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.tour-custom .ant-tour-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.tour-custom .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  height: 36px;
  padding: 0 16px;
  transition: all 0.2s ease;
  border: 1px solid #d1d5db;
}

.tour-custom .ant-btn-primary {
  background: #1f2937;
  border-color: #1f2937;
  color: #ffffff;
}

.tour-custom .ant-btn-primary:hover {
  background: #374151;
  border-color: #374151;
}

.tour-custom .ant-btn-default {
  background: #ffffff;
  border-color: #d1d5db;
  color: #6b7280;
}

.tour-custom .ant-btn-default:hover {
  border-color: #9ca3af;
  color: #374151;
}

/* 自定义按钮样式 */
.tour-custom .tour-btn-skip {
  background: transparent;
  border: none;
  color: #6b7280;
  font-size: 14px;
  padding: 0;
  height: auto;
}

.tour-custom .tour-btn-skip:hover {
  color: #374151;
  background: transparent;
  border: none;
}



