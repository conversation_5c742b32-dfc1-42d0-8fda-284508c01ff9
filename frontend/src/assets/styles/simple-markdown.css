.simple-markdown-body {
  /* 确保文本正确换行和显示，但保持原有行间距 */
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    word-wrap: break-word;
    margin: 1em 0;
  }
  ol {
    list-style: decimal;
  }
  ul {
    list-style: disc;
  }
  ol, ul {
    margin-left: 2em;
  }
  
  /* 确保代码块和预格式化文本正确显示 */
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
    max-width: 100%;
  }
  
  code {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  /* 确保表格和其他元素不会溢出 */
  table {
    max-width: 100%;
    table-layout: auto;
    word-wrap: break-word;
  }
  
  /* 确保长URL和链接能够换行 */
  a {
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  /* 确保段落文本正确换行 */
  p {
    word-wrap: break-word;
    overflow-wrap: break-word;
    margin: 1em 0;
  }
}
