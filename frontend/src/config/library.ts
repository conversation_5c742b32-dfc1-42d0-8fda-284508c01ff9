export const typeMap = [
  { label: 'React', value: 'react' },
  { label: 'Vue', value: 'vue' },
  { label: 'Vue2', value: 'vue2' },
  { label: 'Lit', value: 'lit' },
];

export const libraryMap: Record<string, { label: string; value: string }[]> = {
  react: [
    {
      label: 'Ant Design',
      value: 'antd'
    },
    {
      label: 'SpriteUI',
      value: '@htsc/sprite-ui'
    },
    {
      label: 'Ant Design Mobile',
      value: 'antd-mobile'
    },
  ],
  vue: [
    {
      label: 'Element Plus',
      value: 'element-plus'
    },
    // {
    //   label: 'Ant Design Vue',
    //   value: 'ant-design-vue'
    // },
  ],
  vue2: [
    {
      label: 'Element UI',
      value: 'element-ui'
    },
  ],
};
