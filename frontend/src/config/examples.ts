const opsForm = `写一个页面包含三个tab。
第一个tab，叫数据修改，里面是一个表单，下面有提交按钮。

表单包含如下字段：

客户信息：客户号、客户类型、客户名称、证件类型、证件号码。
证件信息：证件有效期、证件地址、签发机关、辅助证件、国籍或地区。
基本信息：出生日期、民族、性别、年收入、学历、职业、行业类型、机构类别、私募基金管理人编码、注册资本、注册币种、经营范围。
联系信息：联系地址、手机号码、经办人、联系人。
适当性信息：风险等级、税务登记证、专业投资者信息、税收居民国-税号信息、机主信息。
客户联系人信息：法定代表人、控股股东、受益所有人、管理人、托管人、合伙人、实际控制人。

注意这些字段是分组的，可以使用antd的Collapse组件，进行分组。表单内的输入框分成两列。

第二个tab叫流程审批
里面是一个表格，展示客户号、客户名称，审批状态，操作列。操作列包含按钮：查看，批准，拒绝

第三个tab叫运维操作
里面是一个表格，展示客户号、客户名称，审批人，审批时间，运维状态，操作列。操作列包含按钮：查看，执行`;

export const examples = [
  {
    label: '弹框组件',
    value: `写一个弹框组件展示。页面中央是一个按钮，点击后弹出酷炫的弹框。弹框可以自定义内容和底部按钮。使用framer motion做动画。`,
  },
  {
    label: '交易页面',
    value: `写个交易页面，右边是交易面板，需要包含交易要素有：标的，价格，数量，市价或限价，买卖按钮。左边是当日订单列表。使用antd做UI。`,
  },
  {
    label: '表格CRUD',
    value: `使用antd写一个表格，包含如下列：姓名，邮件，生日，地址，电话，公司名称，使用axios从https://jsonplaceholder.typicode.com/users加载数据，是GET请求。\n1. 验证下返回的字段。\n2. 确保loading状态的展示Spin组件。`,
  },
  {
    label: '运维审批表单',
    value: opsForm,
  },
];
