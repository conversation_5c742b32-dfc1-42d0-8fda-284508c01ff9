export const defaultModel = 'htsc::saas-deepseek-v3';
export const defaultVisionModel = 'openrouter::anthropic/claude-3.7-sonnet';

// 仅用于判断所选模型是否为视觉模型
const visionModelList: string[] = [
  'openrouter::anthropic/claude-sonnet-4',
  'openrouter::anthropic/claude-3.7-sonnet',
  'openrouter::anthropic/claude-3.5-sonnet',
  'openrouter::google/gemini-2.5-pro-preview',
  'openrouter::google/gemini-2.5-flash-preview-05-20',
  'siliconflow::Qwen/Qwen2.5-VL-72B-Instruct',
  'siliconflow::deepseek-ai/deepseek-vl2',
];

// 仅用于判断所选模型是否为视觉模型
export function isVisionEnabled(model: string) {
  return visionModelList.includes(model);
}

/**
 * deepseek-r1模型，思考过程较长又没有反馈，不建议使用
 */

export const modelList = {
  htsc: {
    label: 'HTSC',
    models: [
      'saas-deepseek-v3',
      'saas-doubao-15-pro-32k',
      ///////////// 魔方的deepseek代理 //////////////
      // 'deepseek-r1',
      // 'deepseek-v3',
    ],
  },
  // siliconflow: {
  //   label: 'SiliconFlow',
  //   models: [
  //     'deepseek-ai/DeepSeek-V3',
  //     'Qwen/Qwen3-32B',
  //     'Qwen/Qwen2.5-72B-Instruct',
  //     // 'deepseek-ai/deepseek-vl2',
  //     // 'Qwen/Qwen2.5-VL-72B-Instruct',
  //   ],
  // },
  // openrouter: {
  //   label: 'OpenRouter',
  //   models: [
  //     'anthropic/claude-sonnet-4',
  //     'anthropic/claude-3.7-sonnet',
  //     'anthropic/claude-3.5-sonnet',
  //     'google/gemini-2.5-pro-preview',
  //     'google/gemini-2.5-flash-preview-05-20',
  //     // 'google/gemini-2.5-pro-exp-03-25:free',
  //     // 'deepseek/deepseek-chat-v3-0324:free',
  //     // 'openrouter/quasar-alpha',
  //   ],
  // },
};
