import IconCode from '~icons/mdi/code-tags';
import IconFolder from '~icons/mdi/folder-outline';
import IconList from '~icons/mdi/format-list-bulleted';
import IconMonitor from '~icons/mdi/monitor';
import IconApps from '~icons/mdi/view-grid-outline';
export interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  path?: string;
  hasSubmenu?: boolean;
  submenu?: MenuItem[];
  breadcrumbPath?: string;
}

// 菜单配置
export const menuItems: MenuItem[] = [
  {
    id: 'project',
    label: '项目',
    icon: IconFolder,
    hasSubmenu: true,
    breadcrumbPath: '/project-list',
    submenu: [
      {
        id: 'project-list',
        label: '项目列表',
        path: '/project-list',
        icon: IconList,
      },
      {
        id: 'prototype',
        label: '原型代码生成',
        path: '/prototype',
        icon: IconCode,
      },
      {
        id: 'project',
        label: '项目代码生成',
        path: '/project',
        icon: IconMonitor,
      },
    ],
  },
  {
    id: 'square',
    label: '广场',
    icon: IconApps,
    path: '/square',
  },
];

export function findMenuItemBySegment(segment: string): MenuItem | undefined {
  for (const item of menuItems) {
    const itemPath = item.path?.substring(1); // remove leading '/'
    const breadcrumbPath = item.breadcrumbPath?.substring(1);

    if (item.id === segment || itemPath === segment || breadcrumbPath === segment) {
      return item;
    }

    if (item.submenu) {
      const subItemFound = item.submenu.some(sub => sub.path?.substring(1) === segment || sub.id === segment);

      if (subItemFound) {
        return item; // Return the parent item
      }
    }
  }

  return undefined;
} 