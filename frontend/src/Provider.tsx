import { Hero<PERSON><PERSON><PERSON>ider, ToastProvider } from "@heroui/react";
// @ts-ignore
import { ConfigProvider, theme as antTheme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { useEffect, useState } from "react";
import { useHref, useNavigate, Outlet, type NavigateOptions, useLocation } from "react-router-dom";
import Lightbox from "yet-another-react-lightbox";
import { ModalConfirm } from "@/components/ModalConfirm";
import { useUserInfoStore } from '@/hooks/login';
import AppLayout from "@/layouts/app";
import { useProjectStore } from '@/stores/project';
import { useThemeStore } from "@/stores/theme";
import { type ConfirmType, eventBus } from "@/utils/eventBus";
import "yet-another-react-lightbox/styles.css";

declare module "@react-types/shared" {
  interface RouterConfig {
    routerOptions: NavigateOptions;
  }
}

export function Provider() {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { getUserStatus } = useUserInfoStore();
  const { isDark } = useThemeStore();
  const [confirm, setConfirm] = useState<ConfirmType>();
  const [lightboxImages, setLightboxImages] = useState<{ src: string }[]>([]);
  const { initializeFromStorage } = useProjectStore();

  useEffect(() => {
    // 如果登录失效，跳到登录页
    const getUserLoginStatus = async () => {
      await getUserStatus();
    };

    if (pathname !== '/login') {
      getUserLoginStatus();
    }
    

    const onConfirm = (args: ConfirmType) => {
      setConfirm({
        ...args,
      });
    };
    const onLightbox = (images: { src: string }[]) => {
      setLightboxImages(images);
    };

    eventBus.on('show-confirm', onConfirm);
    eventBus.on('show-lightbox', onLightbox);

    return () => {
      eventBus.off('show-confirm', onConfirm);
      eventBus.off('show-lightbox', onLightbox);
    };
  }, []);

  useEffect(() => {
    // 初始化项目store，从localStorage读取当前项目
    initializeFromStorage();
  }, [initializeFromStorage]);

  const singleImage = lightboxImages.length === 1;
  const lightbox = (
    <Lightbox
      carousel={{
        finite: singleImage,
      }}
      close={() => setLightboxImages([])}
      open={lightboxImages.length > 0}
      render={{
        buttonPrev: singleImage? () => null : undefined,
        buttonNext: singleImage? () => null : undefined,
      }}
      slides={lightboxImages}
    />
  );

  // 不需要侧边栏布局的页面（登录页面、Chat页面和WebIDE启动页面有自己的布局）
  const noLayoutPages = ['/login', '/webide-start'];
  const chatPagePattern = /^\/chat\/[^/]+$/;
  const shouldUseAppLayout = !noLayoutPages.includes(pathname);

  const content = (
    <HeroUIProvider locale="zh-CN" navigate={navigate} useHref={useHref}>
      <ConfigProvider
        locale={zhCN}
        theme={{
        cssVar: true,
        token: {
          colorPrimary: '#3b82f6',
        },
        components: {
          Tree: {
            nodeSelectedBg: '#3b82f6',
            nodeSelectedColor: '#fff',
          },
          Splitter: {
            controlItemBgActive: '#615fff',
            controlItemBgActiveHover: '#615fff',
          },
        },
        algorithm: isDark ? antTheme.darkAlgorithm : antTheme.defaultAlgorithm,
      }}>
        <ToastProvider placement="top-center" />
        <ModalConfirm
          buttons={confirm?.buttons}
          isOpen={!!confirm}
          onOpenChange={(open) => {
            if (!open) {
              setConfirm(undefined);
            }
          }}
          {...confirm}
        />
        { lightbox }
        {shouldUseAppLayout ? (
          <AppLayout>
            <Outlet />
          </AppLayout>
        ) : (
          <Outlet />
        )}
      </ConfigProvider>
    </HeroUIProvider>
  );

  return content;
}
