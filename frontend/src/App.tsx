import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { Provider } from './Provider';
import IndexPage from '@/pages/Index';

function App() {
  const router = createBrowserRouter([
    {
      path: '/',
      element: <Provider />,
      children: [
        {
          index: true,
          element: <IndexPage />,
        },
        {
          path: '/chat/:id',
          lazy: () => import('@/pages/Chat'),
        },
        {
          path: '/prototype/:id?',
          lazy: () => import('@/pages/Prototype'),
        },
        {
          path: '/project-list',
          lazy: () => import('@/pages/ProjectList'),
        },
        {
          path: '/project/:id',
          lazy: () => import('@/pages/DesignToCodePreview'),
        },
        {
          path: '/square',
          lazy: () => import('@/pages/Square'),
        },
        {
          path: '/low-code/:id?',
          lazy: () => import('@/pages/LowCode'),
        },
        {
          path: '/d2c',
          lazy: () => import('@/pages/D2C'),
        },
        {
          path: '/credits',
          lazy: () => import('@/pages/Credits'),
        },
        {
          path: '/login',
          lazy: () => import('@/pages/Login'),
        },
        {
          path: '/test',
          lazy: () => import('@/pages/Test'),
        },
        {
          path: '/html-agent',
          lazy: () => import('@/pages/HtmlAgent'),
        },
        {
          path: '/split-image',
          lazy: () => import('@/components/SplitImage'),
        },
        {
          path: '/workflow/:projectId/:workflowId',
          lazy: () => import('@/pages/WorkflowVisualization'),
        },
        {
          path: '/workflow/:projectId',
          lazy: () => import('@/pages/WorkflowVisualization'),
        },
        {
          path: '/multi-workflow/:projectId/:workflowId',
          lazy: () => import('@/pages/MultiPageWorkflowVisualization'),
        },
        {
          path: '/multi-workflow/:projectId',
          lazy: () => import('@/pages/MultiPageWorkflowVisualization'),
        },
        {
          path: '/workflow-manager/:projectId',
          lazy: () => import('@/pages/WorkflowManager'),
        },
        {
          path: '/webide-start',
          lazy: async () => {
            const { default: Component } = await import('@/pages/WebideStart');

            return { Component };
          },
        },
        {
          path: '/webide-preview',
          lazy: async () => {
            const { default: Component } = await import('@/pages/WebidePreview');

            return { Component };
          },
        },
      ],
    },
    {
      path: '/prompt-management/:projectId',
      lazy: async () => {
        const { default: Component } = await import('@/pages/PromptManagement');

        return { Component };
      },
    },
  ]);

  return <RouterProvider router={router} />;
}

export default App;
