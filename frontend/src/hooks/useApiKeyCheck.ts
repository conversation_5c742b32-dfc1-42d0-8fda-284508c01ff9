import { addToast } from '@heroui/toast';
import { useEffect, useState } from 'react';
import { getCredits } from '@/apis';

interface ApiKeyStatus {
  hasKey: boolean;
  balance: number;
  currency: string;
  provider: string;
}

// 检测模型提供商
export const getProviderFromModel = (model: string): 'openrouter' | 'siliconflow' | 'other' => {
  if (model.startsWith('openrouter::')) {
    return 'openrouter';
  }
  if (model.startsWith('siliconflow::')) {
    return 'siliconflow';
  }

  return 'other';
};

// 将余额转换为美元等值
const convertToUSD = (amount: number | string, currency: string) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  if (isNaN(numAmount)) return 0;
  
  if (currency === 'CNY') {
    return numAmount / 7; // 1 USD ≈ 7 CNY
  }

  return numAmount; // USD
};

export const useApiKeyCheck = (model: string) => {
  const [checking, setChecking] = useState(false);
  const [apiKeyStatus, setApiKeyStatus] = useState<ApiKeyStatus | null>(null);

  const checkApiKeyAndBalance = async () => {
    const provider = getProviderFromModel(model);
    
    // 只检查 openrouter 和 siliconflow
    if (provider === 'other') {
      setApiKeyStatus(null);

      return;
    }

    // v0取消余额检查
    return;

    setChecking(true);
    try {
      const credits = await getCredits();
      
      if (!Array.isArray(credits)) {
        // API key 可能未配置
        addToast({
          color: 'warning',
          title: 'API Key 未配置',
          description: `请配置 ${provider.toUpperCase()} 的 API Key 以使用此模型`,
        });
        setApiKeyStatus({ hasKey: false, balance: 0, currency: '', provider });

        return;
      }

      // 查找对应提供商的余额信息
      const providerCredit = credits.find(credit => 
        credit.provider.toLowerCase() === provider
      );

      if (!providerCredit) {
        addToast({
          color: 'warning',
          title: 'API Key 未配置',
          description: `请配置 ${provider.toUpperCase()} 的 API Key 以使用此模型`,
        });
        setApiKeyStatus({ hasKey: false, balance: 0, currency: '', provider });

        return;
      }

      if (providerCredit.status !== 'active') {
        addToast({
          color: 'danger',
          title: 'API Key 异常',
          description: `${provider.toUpperCase()} API Key 配置有误，请检查配置`,
        });
        setApiKeyStatus({ hasKey: false, balance: 0, currency: '', provider });

        return;
      }

      // 检查余额
      const balance = typeof providerCredit.credit === 'string' 
        ? parseFloat(providerCredit.credit) 
        : providerCredit.credit;
      
      const currency = providerCredit.currency;
      const balanceUSD = convertToUSD(balance, currency);

      setApiKeyStatus({ 
        hasKey: true, 
        balance: balanceUSD, 
        currency, 
        provider 
      });

      // 余额不足提示
      if (balance === 0) {
        addToast({
          color: 'danger',
          title: '余额不足',
          description: `${provider.toUpperCase()} 余额为 0，请及时充值`,
        });
      } else if (balanceUSD < 5) {
        addToast({
          color: 'warning',
          title: '余额不足',
          description: `${provider.toUpperCase()} 余额不足 5 美元，请及时充值`,
        });
      }

    } catch (error) {
      console.error('检查API Key和余额失败:', error);
      addToast({
        color: 'danger',
        title: '检查失败',
        description: '无法获取余额信息，请检查网络连接',
      });
      setApiKeyStatus(null);
    } finally {
      setChecking(false);
    }
  };

  useEffect(() => {
    if (model) {
      checkApiKeyAndBalance();
    }
  }, [model]);

  return {
    checking,
    apiKeyStatus,
    checkApiKeyAndBalance,
  };
}; 