import { useState, useCallback, useRef } from 'react';
import { UIMessage } from '@ai-sdk/ui-utils';
import React from 'react';
import { updatePlaygroundStepByStepData } from '@/apis';

export interface ExecutionStep {
  stepNumber: number;
  title: string;
  content: string;
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'waiting_user';
  result?: string;
  error?: string;
}

export interface StepExecutionState {
  steps: ExecutionStep[];
  currentStepIndex: number;
  isExecuting: boolean;
  isCompleted: boolean;
  isCancelled: boolean;
  needsUserInput: boolean;
  userInputPrompt?: string;
}

export function useHtmlStepByStepExecutor({
  isEnabled,
  onAppendMessage,
  playgroundId,
}: {
  isEnabled: boolean;
  onAppendMessage: (message: string) => void;
  playgroundId?: string;
}) {
  const [state, setState] = useState<StepExecutionState>({
    steps: [],
    currentStepIndex: -1,
    isExecuting: false,
    isCompleted: false,
    isCancelled: false,
    needsUserInput: false,
  });

  const executionTimeoutRef = useRef<number>();
  const stepsInitializedRef = useRef(false);
  // 使用ref来存储最新状态，避免callback依赖问题
  const stateRef = useRef(state);
  stateRef.current = state;
  
  // 使用ref存储稳定的函数引用
  const onAppendMessageRef = useRef(onAppendMessage);
  onAppendMessageRef.current = onAppendMessage;
  
  // 添加防抖机制，防止频繁调用
  const lastCheckRef = useRef<string>('');
  const checkDebounceRef = useRef<number>();
  
  // 添加已尝试解析的消息ID记录
  const attemptedParseMessageIdsRef = useRef<Set<string>>(new Set());

  // 添加状态变化的调试日志
  React.useEffect(() => {
    console.log('🔍 StepByStepExecutor状态变化:', {
      isEnabled,
      stepsCount: state.steps.length,
      currentStepIndex: state.currentStepIndex,
      isExecuting: state.isExecuting,
      isCompleted: state.isCompleted,
      isCancelled: state.isCancelled,
      needsUserInput: state.needsUserInput,
      stepsInitialized: stepsInitializedRef.current,
      attemptedParseCount: attemptedParseMessageIdsRef.current.size,
    });
  }, [isEnabled, state]);

  // 添加状态保存逻辑
  React.useEffect(() => {
    // 只有在启用分步执行且有playground ID时才保存状态
    if (isEnabled && playgroundId && state.steps.length > 0) {
      const saveState = async () => {
        try {
          await updatePlaygroundStepByStepData({
            id: playgroundId,
            stepByStepData: state,
          });
          console.log('💾 分步执行状态已保存到playground');
        } catch (error) {
          console.error('❌ 保存分步执行状态失败:', error);
        }
      };
      
      // 防抖保存，避免频繁调用API
      const timeoutId = setTimeout(saveState, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [isEnabled, playgroundId, state]);

  // 解析步骤文档 - 移除依赖项，使用稳定的函数
  const parseStepsFromContent = useCallback((content: string): ExecutionStep[] => {
    console.log('🔍 开始解析步骤内容，内容长度:', content.length);
    console.log('📝 内容预览:', content.substring(0, 500) + (content.length > 500 ? '...' : ''));
    
    const lines = content.split('\n');
    const steps: ExecutionStep[] = [];
    let currentStep: Partial<ExecutionStep> | null = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // 检测步骤标题 - 支持更多格式
      const stepMatch = trimmedLine.match(/^#{1,4}\s*步骤\s*(\d+)[：:]\s*(.+)/i) ||
                       trimmedLine.match(/^#{1,4}\s*第(\d+)步[：:]\s*(.+)/i) ||
                       trimmedLine.match(/^#{1,4}\s*(\d+)[\.、]\s*(.+)/i) ||
                       trimmedLine.match(/^#{1,4}\s*Step\s*(\d+)[：:]\s*(.+)/i) ||
                       // 支持强制执行流程格式
                       trimmedLine.match(/^#{1,4}\s*步骤\s*(\d+)[：:]?\s*(.+)/i) ||
                       // 支持简单的编号格式（不带#）
                       trimmedLine.match(/^(\d+)[\.、]\s*(.+)/i) ||
                       // 支持"第X步"格式
                       trimmedLine.match(/^第(\d+)步[：:]\s*(.+)/i) ||
                       // 支持"步骤X："格式（宽松匹配）
                       trimmedLine.match(/步骤\s*(\d+)[：:]\s*(.+)/i);
      
      if (stepMatch) {
        console.log('🎯 检测到步骤标题:', trimmedLine);
        
        // 保存上一个步骤
        if (currentStep && currentStep.stepNumber && currentStep.title) {
          const stepContent = (currentStep.content || '').trim();
          if (stepContent) {
            steps.push({
              stepNumber: currentStep.stepNumber,
              title: currentStep.title,
              content: stepContent,
              status: 'pending',
            });
            console.log(`✅ 保存步骤 ${currentStep.stepNumber}: ${currentStep.title}`);
          }
        }
        
        // 开始新步骤
        const stepNumber = parseInt(stepMatch[1]);
        const stepTitle = stepMatch[2]?.trim() || `步骤${stepNumber}`;
        
        currentStep = {
          stepNumber,
          title: stepTitle,
          content: '', // 初始化为空，后续添加内容
        };
        
        console.log(`🚀 开始新步骤 ${stepNumber}: ${stepTitle}`);
      } else if (currentStep) {
        // 检查是否遇到新的步骤（避免将后续步骤的内容合并到当前步骤）
        const isNewStep = trimmedLine.match(/^#{1,4}\s*(?:步骤|第\d+步|\d+[\.、]|Step\s*\d+)/i) ||
                         trimmedLine.match(/^(\d+)[\.、]\s*.+/i) ||
                         trimmedLine.match(/^第(\d+)步[：:]/i) ||
                         trimmedLine.match(/步骤\s*\d+[：:]/i);
        
        if (isNewStep) {
          // 如果遇到新步骤，先保存当前步骤，然后重新处理这一行
          if (currentStep.stepNumber && currentStep.title) {
            const stepContent = (currentStep.content || '').trim();
            if (stepContent) {
              steps.push({
                stepNumber: currentStep.stepNumber,
                title: currentStep.title,
                content: stepContent,
                status: 'pending',
              });
              console.log(`✅ 保存步骤 ${currentStep.stepNumber}: ${currentStep.title}`);
            }
          }
          currentStep = null;
          // 重新处理这一行
          i--; // 回退一行，重新处理
          continue;
        } else {
          // 继续添加到当前步骤
          if (trimmedLine) { // 只添加非空行
            currentStep.content = (currentStep.content || '') + line + '\n';
          }
        }
      } else if (trimmedLine) {
        // 如果还没有当前步骤，但是遇到了有内容的行，检查是否是步骤的一部分
        const possibleStepLine = trimmedLine.match(/步骤\s*(\d+)/i) || 
                                trimmedLine.match(/第(\d+)步/i) ||
                                trimmedLine.match(/^(\d+)[\.、]/i);
        if (possibleStepLine) {
          console.log('🔍 发现可能的步骤行:', trimmedLine);
          // 重新处理这一行
          i--;
          continue;
        }
      }
    }
    
    // 添加最后一个步骤
    if (currentStep && currentStep.stepNumber && currentStep.title) {
      const stepContent = (currentStep.content || '').trim();
      if (stepContent) {
        steps.push({
          stepNumber: currentStep.stepNumber,
          title: currentStep.title,
          content: stepContent,
          status: 'pending',
        });
        console.log(`✅ 保存最后一个步骤 ${currentStep.stepNumber}: ${currentStep.title}`);
      }
    }
    
    console.log('📋 解析步骤结果:', {
      totalSteps: steps.length,
      stepTitles: steps.map(s => `${s.stepNumber}: ${s.title}`),
      stepContents: steps.map(s => ({ 
        step: s.stepNumber, 
        contentLength: s.content.length,
        contentPreview: s.content.substring(0, 100) + (s.content.length > 100 ? '...' : '')
      }))
    });
    
    return steps;
  }, []); // 移除所有依赖项

  // 重置执行状态 - 当用户发送新消息或禁用分步执行时调用
  const resetExecution = useCallback(() => {
    console.log('🔄 重置分步执行状态');
    
    // 清除所有定时器
    if (executionTimeoutRef.current) {
      window.clearTimeout(executionTimeoutRef.current);
      executionTimeoutRef.current = undefined;
    }
    
    if (checkDebounceRef.current) {
      window.clearTimeout(checkDebounceRef.current);
      checkDebounceRef.current = undefined;
    }
    
    // 重置状态
    setState({
      steps: [],
      currentStepIndex: -1,
      isExecuting: false,
      isCompleted: false,
      isCancelled: false,
      needsUserInput: false,
    });
    
    // 重置初始化标记
    stepsInitializedRef.current = false;
    lastCheckRef.current = '';
    
    // 清除已解析消息记录
    attemptedParseMessageIdsRef.current.clear();
    
    // 清理window对象中的步骤数据
    if (window.__remainingSteps) {
      delete window.__remainingSteps;
    }
    if (window.__currentStepIndex !== undefined) {
      delete window.__currentStepIndex;
    }
    if (window.__totalSteps !== undefined) {
      delete window.__totalSteps;
    }
  }, []);

  // 清除解析记录 - 允许重新解析消息
  const clearParseAttempts = useCallback(() => {
    console.log('🔄 清除解析记录');
    attemptedParseMessageIdsRef.current.clear();
  }, []);

  // 初始化步骤 - 简化依赖项
  const initializeSteps = useCallback((userMessage: string, messageId?: string, attachments?: any[]) => {
    console.log('🚀 initializeSteps 被调用:', {
      isEnabled,
      messageLength: userMessage.length,
      messageId,
      attachmentsCount: attachments?.length || 0,
      stepsInitialized: stepsInitializedRef.current,
      alreadyAttempted: messageId ? attemptedParseMessageIdsRef.current.has(messageId) : false,
      hasWindowSteps: !!window.__remainingSteps?.length,
      windowStepsDetails: window.__remainingSteps ? {
        length: window.__remainingSteps.length,
        currentStepIndex: window.__currentStepIndex,
        totalSteps: window.__totalSteps,
        stepTitles: window.__remainingSteps.map(s => s.title)
      } : null,
    });

    if (!isEnabled) {
      console.log('⏭️ 分步执行未启用，跳过初始化');
      // 如果分步执行被禁用，确保重置状态
      if (stepsInitializedRef.current) {
        resetExecution();
      }
      return;
    }

    if (stepsInitializedRef.current) {
      console.log('⏭️ 步骤已初始化，跳过重复初始化');
      return;
    }

    // 如果已经尝试过解析这条消息，跳过
    if (messageId && attemptedParseMessageIdsRef.current.has(messageId)) {
      console.log('⏭️ 已经尝试过解析此消息，跳过重复解析');
      return;
    }

    // 记录解析尝试
    if (messageId) {
      attemptedParseMessageIdsRef.current.add(messageId);
      console.log('📝 记录消息解析尝试:', messageId);
    }

    try {
      // 优先检查window对象中是否有预设的步骤（来自HtmlAgent页面）
      if (window.__remainingSteps && window.__remainingSteps.length > 0) {
        console.log('🔍 发现window对象中的预设步骤，使用预设步骤');
        
        // 构建完整的步骤列表（当前步骤 + 剩余步骤）
        const currentStepIndex = window.__currentStepIndex || 0;
        const totalSteps = window.__totalSteps || (window.__remainingSteps.length + 1);
        
        // 构建当前步骤（从用户消息中提取）
        const currentStepTitle = userMessage.match(/步骤\s*(\d+)[：:]\s*(.+)/)?.[2] || `步骤${currentStepIndex + 1}`;
        const currentStep: ExecutionStep = {
          stepNumber: currentStepIndex + 1,
          title: currentStepTitle.trim(),
          content: userMessage,
          status: 'pending',
        };
        
        // 构建剩余步骤
        const remainingSteps: ExecutionStep[] = window.__remainingSteps.map((step, index) => ({
          stepNumber: step.stepNumber || (currentStepIndex + 2 + index),
          title: step.title,
          content: step.content,
          status: 'pending',
        }));
        
        const allSteps = [currentStep, ...remainingSteps];
        
        console.log('✅ 从window对象构建步骤列表:', {
          currentStep: currentStep.title,
          remainingSteps: remainingSteps.map(s => s.title),
          totalSteps: allSteps.length
        });
        
        setState(prev => ({
          ...prev,
          steps: allSteps,
          currentStepIndex: -1, // 从-1开始，第一次执行时会变成0
          isExecuting: false,
          isCompleted: false,
          isCancelled: false,
        }));
        
        stepsInitializedRef.current = true;
        
        // 清理window对象
        delete window.__remainingSteps;
        delete window.__currentStepIndex;
        delete window.__totalSteps;
        
        console.log('🎉 步骤初始化完成 - 来源: window对象预设步骤');
        return;
      } else {
        console.log('🔍 window对象中没有预设步骤，检查消息内容和附件');
      }
      
      // 如果没有预设步骤，尝试从消息内容或附件中解析
      let contentToAnalyze = userMessage;
      let isFromAttachment = false;
      
      // 检查附件中是否有步骤规划文件
      if (attachments && attachments.length > 0) {
        console.log('🔍 检查附件中的步骤规划文件，附件数量:', attachments.length);
        console.log('📎 附件列表:', attachments.map(att => ({ 
          name: att.name, 
          hasContent: !!att.content,
          contentLength: att.content?.length || 0,
          contentType: att.contentType || 'unknown'
        })));
        
        for (const attachment of attachments) {
          console.log('🔍 检查附件:', attachment.name);
          
          // 检查是否是markdown或文本文件，且包含步骤相关内容
          if (attachment.name && 
              (attachment.name.includes('step') || 
               attachment.name.includes('步骤') || 
               attachment.name.includes('plan') || 
               attachment.name.includes('规划') ||
               attachment.name.endsWith('.md') || 
               attachment.name.endsWith('.txt'))) {
            
            console.log('📄 发现可能的步骤规划文件:', attachment.name);
            console.log('📄 附件详细信息:', {
              name: attachment.name,
              hasContent: !!attachment.content,
              contentLength: attachment.content?.length || 0,
              contentPreview: attachment.content?.substring(0, 200) + (attachment.content?.length > 200 ? '...' : ''),
              allKeys: Object.keys(attachment)
            });
            
            // 如果附件有内容，使用附件内容
            if (attachment.content && attachment.content.trim()) {
              contentToAnalyze = attachment.content;
              isFromAttachment = true;
              console.log('✅ 使用附件内容进行步骤解析，内容长度:', attachment.content.length);
              break;
            } else {
              console.warn('⚠️ 附件没有内容或内容为空:', attachment.name);
            }
          } else {
            console.log('⏭️ 跳过附件（不是步骤规划文件）:', attachment.name);
          }
        }
        
        if (!isFromAttachment) {
          console.log('⚠️ 没有找到有效的步骤规划附件');
        }
      } else {
        console.log('⚠️ 没有附件或附件列表为空');
      }

      // 对于附件内容，放宽检测条件，因为规划文件通常包含完整的步骤
      let shouldParseSteps = false;
      
      if (isFromAttachment) {
        // 附件内容：只要包含步骤格式就解析
        const hasFormattedSteps = /#{1,4}\s*(?:步骤|第\d+步|\d+[\.、]|Step\s*\d+)/i.test(contentToAnalyze);
        const hasNumberedItems = /^\s*\d+[\.、]\s*.+/m.test(contentToAnalyze);
        const hasStepKeywords = /(?:步骤|第\d+步|阶段|phase|step)/i.test(contentToAnalyze);
        
        shouldParseSteps = hasFormattedSteps || (hasNumberedItems && hasStepKeywords);
        
        console.log('🔍 附件内容分析结果:', {
          hasFormattedSteps,
          hasNumberedItems,
          hasStepKeywords,
          shouldParseSteps
        });
        
        if (shouldParseSteps) {
          console.log('✅ 附件内容包含步骤格式，开始解析...');
        }
      } else {
        // 消息内容：需要明确的分步执行意图
        const explicitStepByStepIntent = /(?:分步执行|分步实现|分步完成|逐步执行|逐步实现|逐步完成|按步骤执行|按步骤实现)/i.test(contentToAnalyze);
        const hasFormattedSteps = /#{1,4}\s*(?:步骤|第\d+步|\d+[\.、]|Step\s*\d+)/i.test(contentToAnalyze);
        
        shouldParseSteps = explicitStepByStepIntent && hasFormattedSteps;
        
        console.log('🔍 消息内容分析结果:', {
          explicitStepByStepIntent,
          hasFormattedSteps,
          shouldParseSteps
        });
        
        if (shouldParseSteps) {
          console.log('✅ 消息内容包含明确的分步执行意图和格式化步骤，开始解析...');
        }
      }
      
      if (!shouldParseSteps) {
        console.log('⏭️ 未检测到可解析的步骤内容，跳过初始化');
        console.log('📝 内容预览:', contentToAnalyze.substring(0, 300) + (contentToAnalyze.length > 300 ? '...' : ''));
        return;
      }

      // 尝试从内容中解析步骤 - 支持更多格式
      let parsedSteps: ExecutionStep[] = [];
      
      // 首先尝试解析markdown格式的步骤
      const stepsMatch = contentToAnalyze.match(/((?:#{1,4}\s*(?:步骤|第\d+步|\d+[\.、]|Step\s*\d+)[\s\S]*?)+)/i);
      
      if (stepsMatch) {
        const stepsContent = stepsMatch[1];
        console.log('🔍 发现匹配的步骤内容，长度:', stepsContent.length);
        parsedSteps = parseStepsFromContent(stepsContent);
      }
      
      // 如果没有解析到步骤，尝试解析整个内容
      if (parsedSteps.length === 0) {
        console.log('🔍 markdown格式解析失败，尝试解析整个内容');
        parsedSteps = parseStepsFromContent(contentToAnalyze);
      }
      
      if (parsedSteps.length > 0) {
        console.log('✅ 成功解析步骤:', {
          stepCount: parsedSteps.length,
          stepTitles: parsedSteps.map(s => `${s.stepNumber}: ${s.title}`),
          source: isFromAttachment ? '附件' : '消息'
        });
        
        setState(prev => ({
          ...prev,
          steps: parsedSteps,
          currentStepIndex: -1,
          isExecuting: false,
          isCompleted: false,
          isCancelled: false,
        }));
        
        stepsInitializedRef.current = true;
        console.log(`🎉 分步执行器初始化完成，共解析 ${parsedSteps.length} 个步骤（从${isFromAttachment ? '附件' : '消息'}解析）`);
      } else {
        console.warn('⚠️ 未能解析到有效步骤');
        console.log('📝 解析失败的内容预览:', contentToAnalyze.substring(0, 500));
      }
    } catch (error) {
      console.error('❌ initializeSteps 执行出错:', error);
      // 发生错误时重置状态
      resetExecution();
    }
  }, [isEnabled, parseStepsFromContent, resetExecution]); // 添加resetExecution依赖

  // 执行下一步 - 使用ref避免依赖问题
  const executeNextStep = useCallback(() => {
    try {
      const currentState = stateRef.current;
      if (currentState.isCancelled || currentState.needsUserInput || currentState.isCompleted) return;

      const nextStepIndex = currentState.currentStepIndex + 1;
      
      if (nextStepIndex >= currentState.steps.length) {
        // 所有步骤执行完成
        setState(prev => ({
          ...prev,
          isExecuting: false,
          isCompleted: true,
        }));
        console.log('🎉 所有步骤执行完成！');
        return;
      }

      const nextStep = currentState.steps[nextStepIndex];
      
      // 防止重复执行同一步骤
      if (nextStep.status === 'executing') {
        console.warn(`⚠️ 步骤 ${nextStep.stepNumber} 已在执行中，跳过重复执行`);
        return;
      }
      
      setState(prev => {
        const newSteps = [...prev.steps];
        newSteps[nextStepIndex].status = 'executing';
        return {
          ...prev,
          steps: newSteps,
          currentStepIndex: nextStepIndex,
          isExecuting: true,
        };
      });

      // 检查是否需要用户输入
      const needsUserInput = checkIfStepNeedsUserInput(nextStep.content);
      
      if (needsUserInput.needed) {
        setState(prev => ({
          ...prev,
          needsUserInput: true,
          userInputPrompt: needsUserInput.prompt,
          isExecuting: false,
        }));
        return;
      }

      // 分析步骤类型，决定输出规范
      const outputType = analyzeStepOutputType(nextStep.content);
      const outputGuidelines = getOutputGuidelines(outputType, nextStepIndex, currentState.steps.length);

      // 构建超强制性的步骤执行消息
      const stepMessage = `# 🚨 分步执行 - 步骤 ${nextStep.stepNumber}: ${nextStep.title}

## 📋 步骤要求
${nextStep.content}

---

## ⚠️ 🚨 ⛔ 强制性代码输出要求 ⛔ 🚨 ⚠️

**【重要警告】本步骤必须按照正确的格式输出代码！**

### 🎯 输出格式分析
- **检测到的步骤类型**: ${outputType === 'full' ? '全新代码生成' : outputType === 'incremental' ? '增量修改' : '智能选择'}
- **要求的输出格式**: ${outputType === 'full' ? '<files> 格式' : outputType === 'incremental' ? '<diff> 格式' : '根据具体情况选择'}

${outputGuidelines}

---

## 🎯 立即开始执行

请现在立即执行上述步骤，并严格按照${outputType === 'full' ? '<files>' : outputType === 'incremental' ? '<diff>' : '合适的'}格式要求输出代码。

**记住：格式选择很重要！**
- 🆕 **全新创建代码** → 使用 \`<files>\` 格式
- 🔧 **修改现有代码** → 使用 \`<diff>\` 格式
- 📋 **不确定时** → 使用 \`<files>\` 格式（更安全）

**这不是可选的，是必须的！每个步骤都必须输出正确格式的代码！**`;

      console.log(`🚀 自动执行步骤 ${nextStep.stepNumber}: ${nextStep.title} (输出类型: ${outputType})`);
      onAppendMessageRef.current(stepMessage);
    } catch (error) {
      console.error('❌ executeNextStep 执行出错:', error);
      // 发生错误时停止执行
      setState(prev => ({
        ...prev,
        isExecuting: false,
        isCancelled: true,
      }));
    }
  }, []); // 移除所有依赖项，使用ref

  // 检查是否应该开始执行下一步 - 使用ref避免依赖问题
  const checkAndExecuteNextStep = useCallback((messages: UIMessage[], chatStatus: string) => {
    try {
      const currentState = stateRef.current;
      if (!isEnabled || currentState.isCancelled || currentState.needsUserInput || currentState.isCompleted) return;

      const lastMessage = messages[messages.length - 1];
      
      // 创建唯一标识符来防止重复执行
      const checkId = `${chatStatus}-${lastMessage?.id}-${currentState.currentStepIndex}-${currentState.steps.length}`;
      if (lastCheckRef.current === checkId) {
        return; // 避免重复执行
      }
      lastCheckRef.current = checkId;
      
      // 新增：当AI开始回复时，自动开始执行第一步
      if (chatStatus === 'streaming' && lastMessage?.role === 'assistant' && 
          currentState.steps.length > 0 && currentState.currentStepIndex === -1 && !currentState.isExecuting) {
        console.log('🚀 AI开始回复，自动开始执行第一步');
        
        // 更新状态：开始执行，设置当前步骤为第一步，并标记为执行中
        setState(prev => {
          const newSteps = [...prev.steps];
          newSteps[0].status = 'executing';
          return {
            ...prev,
            steps: newSteps,
            currentStepIndex: 0,
            isExecuting: true,
            isCancelled: false,
            isCompleted: false,
          };
        });
        
        return; // 执行状态更新后直接返回
      }
      
      // 如果AI刚完成回复，检查是否需要执行下一步
      if (chatStatus === 'ready' && lastMessage?.role === 'assistant') {
        // 检查是否有文件保存的注解
        const hasFileSaved = lastMessage.annotations?.some((annotation: any) => 
          annotation?.type === 'all-files-saved'
        );

        if (hasFileSaved && currentState.currentStepIndex >= 0) {
          // 标记当前步骤为完成
          setState(prev => {
            // 防止重复更新
            if (prev.steps[prev.currentStepIndex]?.status === 'completed') {
              return prev;
            }
            
            const newSteps = [...prev.steps];
            newSteps[prev.currentStepIndex].status = 'completed';
            newSteps[prev.currentStepIndex].result = '步骤执行完成，代码已生成';
            return { ...prev, steps: newSteps };
          });

          // 检查是否还有下一步需要执行
          if (currentState.currentStepIndex < currentState.steps.length - 1) {
            // 清除之前的防抖定时器
            if (checkDebounceRef.current) {
              window.clearTimeout(checkDebounceRef.current);
            }
            
            // 延迟执行下一步，给用户时间查看结果
            if (executionTimeoutRef.current) {
              window.clearTimeout(executionTimeoutRef.current);
            }
            executionTimeoutRef.current = window.setTimeout(() => {
              executeNextStep();
            }, 2000);
          } else {
            // 所有步骤都完成了
            setState(prev => ({
              ...prev,
              isExecuting: false,
              isCompleted: true,
            }));
            console.log('🎉 所有分步执行任务已完成！');
          }
        }
      }
    } catch (error) {
      console.error('❌ checkAndExecuteNextStep 执行出错:', error);
      // 发生错误时停止执行，防止无限循环
      setState(prev => ({
        ...prev,
        isExecuting: false,
        isCancelled: true,
      }));
    }
  }, [isEnabled, executeNextStep]); // 保持必要的依赖项

  // 检查步骤是否需要用户输入
  const checkIfStepNeedsUserInput = useCallback((stepContent: string): { needed: boolean; prompt?: string } => {
    const userInputKeywords = [
      '用户确认', '用户选择', '用户输入', '用户决定',
      '请确认', '请选择', '请输入', '请决定',
      '需要确认', '需要选择', '需要输入', '需要决定',
    ];

    for (const keyword of userInputKeywords) {
      if (stepContent.includes(keyword)) {
        return {
          needed: true,
          prompt: `步骤执行需要用户${keyword.replace('用户', '').replace('请', '').replace('需要', '')}，请在下方输入框中提供相关信息。`
        };
      }
    }

    return { needed: false };
  }, []);

  // 分析步骤输出类型
  const analyzeStepOutputType = useCallback((stepContent: string): 'incremental' | 'full' | 'mixed' => {
    const content = stepContent.toLowerCase();
    
    // 检测增量输出关键词
    const incrementalKeywords = [
      '添加', '新增', '插入', '修改', '更新', '调整', '优化',
      '增加', '补充', '完善', '改进', '扩展', '加入', '变更',
      '替换', '移除', '删除', '重构', '微调', '细化'
    ];
    
    // 检测全量输出关键词  
    const fullKeywords = [
      '创建', '生成', '构建', '建立', '制作', '编写',
      '完整', '全部', '整个', '所有', '从头', '重新创建',
      '新建', '初始化', '搭建', '构造'
    ];
    
    const hasIncremental = incrementalKeywords.some(keyword => content.includes(keyword));
    const hasFull = fullKeywords.some(keyword => content.includes(keyword));
    
    // 如果既有增量又有全量关键词，优先判断为增量（因为通常是在已有基础上的全面修改）
    if (hasIncremental) return 'incremental';
    if (hasFull) return 'full';
    
    // 根据步骤位置和内容长度进行判断
    // 如果内容很长且包含详细描述，可能是全量
    if (stepContent.length > 500) return 'full';
    
    // 默认为增量模式（更安全）
    return 'incremental';
  }, []);

  // 获取输出指导原则
  const getOutputGuidelines = useCallback((outputType: 'incremental' | 'full' | 'mixed', stepIndex: number, totalSteps: number): string => {
    const baseInstructions = `
#### 🔴 绝对必须遵守的核心规则：
- **代码必须完整且可直接运行** - 不允许任何形式的省略或占位符
- **文件名必须严格遵守**：HTML文件必须命名为 \`index.html\`，CSS文件必须命名为 \`styles.css\`
- **不能只输出分析说明而没有代码** - 必须有实际的代码文件`;

    switch (outputType) {
      case 'full':
        return `${baseInstructions}

### 🔴 全新代码生成模式 - 使用 <files> 格式

#### 📝 强制输出格式（严格按此格式）：
\`\`\`
<files>
# 文件列表
- index.html  
- styles.css

# 文件：index.html
\`\`\`html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 完整的HTML内容 - 每一行都要写出 -->
    <!-- 不允许使用"...其他内容..."这样的省略 -->
</body>
</html>
\`\`\`

# 文件：styles.css
\`\`\`css
/* 完整的CSS样式 - 每个样式规则都要写出 */
/* 不允许使用"/* 其他样式 */"这样的省略 */
body {
    margin: 0;
    padding: 0;
    /* 所有样式属性都要完整写出 */
}
/* 继续写出所有其他完整样式... */
\`\`\`
</files>
\`\`\`

#### ⚠️ 全新代码生成特别要求：
- **必须使用 \`<files>\` 标签** - 这是全新代码的标准格式
- **输出完整的文件内容** - 从头到尾每一行代码都要写出
- **绝对禁止省略** - 不能使用任何省略符号或占位符
- **代码必须能够直接运行** - 不依赖任何外部补充`;

      case 'incremental':
        return `${baseInstructions}

### 🟡 增量修改模式 - 使用 <diff> 格式

#### 📝 强制输出格式（严格按此格式）：
\`\`\`
<diff>
# 文件修改：index.html
\`\`\`diff
--- a/index.html
+++ b/index.html
@@ -行号,行数 +行号,行数 @@
 保持不变的代码行
-要删除的代码行
+要添加的新代码行
 保持不变的代码行
\`\`\`

# 文件修改：styles.css
\`\`\`diff
--- a/styles.css
+++ b/styles.css
@@ -行号,行数 +行号,行数 @@
 保持不变的样式
-要删除的样式规则
+要添加的新样式规则
 保持不变的样式
\`\`\`
</diff>
\`\`\`

#### ⚠️ 增量修改特别要求：
- **必须使用 \`<diff>\` 标签** - 这是增量修改的标准格式
- **提供精确的diff格式** - 包含正确的行号和上下文
- **明确标识变更内容** - 用 \`-\` 表示删除，\`+\` 表示添加
- **保留足够上下文** - 让修改位置清晰可见
- **确保修改的逻辑完整性** - 所有相关的修改都要包含

#### 🔧 diff格式说明：
- \`--- a/文件名\`：修改前的文件
- \`+++ b/文件名\`：修改后的文件  
- \`@@\` 行：指定修改的行号范围
- 空格开头：保持不变的行
- \`-\` 开头：要删除的行
- \`+\` 开头：要添加的行`;

      case 'mixed':
      default:
        return `${baseInstructions}

### 🟢 智能格式选择模式

#### 🎯 格式选择规则：
- **如果是全新创建** → 使用 \`<files>\` 格式输出完整文件
- **如果是修改现有代码** → 使用 \`<diff>\` 格式输出变更内容
- **不确定时优先使用** → \`<files>\` 格式（更安全）

#### 📊 当前步骤分析：第 ${stepIndex + 1} 步 / 共 ${totalSteps} 步
${stepIndex === 0 ? '- **首步骤**：通常是创建基础结构，建议使用 `<files>` 格式' : ''}
${stepIndex === totalSteps - 1 ? '- **最终步骤**：可能是完善和修改，根据具体需求选择格式' : ''}
${stepIndex > 0 && stepIndex < totalSteps - 1 ? '- **中间步骤**：通常是增量修改，建议使用 `<diff>` 格式' : ''}

#### 🔄 格式决策建议：
1. 分析步骤要求：是创建新内容还是修改现有内容？
2. 选择合适格式：创建用 \`<files>\`，修改用 \`<diff>\`
3. 严格按照选定格式输出代码

#### 🚨 无论选择哪种格式，都必须：
- 输出可直接使用的代码
- 不能有任何省略或占位符
- 确保代码的完整性和可运行性`;
    }
  }, []);

  // 开始执行
  const startExecution = useCallback(() => {
    const currentState = stateRef.current;
    if (currentState.steps.length === 0) {
      console.warn('⚠️ 没有可执行的步骤');
      return;
    }

    setState(prev => ({
      ...prev,
      isExecuting: true,
      isCancelled: false,
      isCompleted: false,
      needsUserInput: false,
    }));

    // 开始执行第一步
    executeNextStep();
  }, [executeNextStep]);

  // 取消执行
  const cancelExecution = useCallback(() => {
    if (executionTimeoutRef.current) {
      window.clearTimeout(executionTimeoutRef.current);
    }
    
    if (checkDebounceRef.current) {
      window.clearTimeout(checkDebounceRef.current);
    }

    setState(prev => ({
      ...prev,
      isExecuting: false,
      isCancelled: true,
      needsUserInput: false,
    }));

    console.log('🛑 分步执行已取消');
  }, []);

  // 继续执行（用户提供输入后）
  const continueExecution = useCallback((userInput: string) => {
    const currentState = stateRef.current;
    if (!currentState.needsUserInput) return;

    // 将用户输入添加到当前步骤
    const currentStep = currentState.steps[currentState.currentStepIndex];
    
    // 分析步骤类型，决定输出规范
    const outputType = analyzeStepOutputType(currentStep.content);
    const outputGuidelines = getOutputGuidelines(outputType, currentState.currentStepIndex, currentState.steps.length);
    
    const enhancedStepMessage = `# 🚨 分步执行 - 步骤 ${currentStep.stepNumber}: ${currentStep.title}

## 📋 步骤要求
${currentStep.content}

## 👤 用户提供的信息
${userInput}

---

## ⚠️ 🚨 ⛔ 强制性代码输出要求 ⛔ 🚨 ⚠️

**【重要警告】本步骤必须按照正确的格式输出代码！**

### 🎯 输出格式分析
- **检测到的步骤类型**: ${outputType === 'full' ? '全新代码生成' : outputType === 'incremental' ? '增量修改' : '智能选择'}
- **要求的输出格式**: ${outputType === 'full' ? '<files> 格式' : outputType === 'incremental' ? '<diff> 格式' : '根据具体情况选择'}
- **用户输入已提供**: 请将用户信息正确整合到代码中

${outputGuidelines}

---

## 🎯 立即开始执行

请现在立即执行上述步骤，结合用户提供的信息，并严格按照${outputType === 'full' ? '<files>' : outputType === 'incremental' ? '<diff>' : '合适的'}格式要求输出代码。

**记住：格式选择很重要！**
- 🆕 **全新创建代码** → 使用 \`<files>\` 格式
- 🔧 **修改现有代码** → 使用 \`<diff>\` 格式
- 📋 **不确定时** → 使用 \`<files>\` 格式（更安全）
- 👤 **必须整合用户提供的信息**

**这不是可选的，是必须的！每个步骤都必须输出正确格式的代码！**`;

    setState(prev => ({
      ...prev,
      needsUserInput: false,
      userInputPrompt: undefined,
      isExecuting: true,
    }));

    console.log(`🚀 用户提供输入后继续执行步骤 ${currentStep.stepNumber} (输出类型: ${outputType})`);
    onAppendMessageRef.current(enhancedStepMessage);
  }, [analyzeStepOutputType, getOutputGuidelines]); // 移除onAppendMessage依赖，使用ref

  // 恢复状态（从playground数据恢复）
  const restoreState = useCallback((savedState: any) => {
    console.log('🔄 恢复分步执行状态:', savedState);
    
    try {
      setState(prev => ({
        ...prev,
        steps: savedState.steps || [],
        currentStepIndex: savedState.currentStepIndex || -1,
        isExecuting: savedState.isExecuting || false,
        isCompleted: savedState.isCompleted || false,
        isCancelled: savedState.isCancelled || false,
        needsUserInput: savedState.needsUserInput || false,
        userInputPrompt: savedState.userInputPrompt,
      }));
      
      stepsInitializedRef.current = savedState.steps && savedState.steps.length > 0;
      
      console.log('✅ 分步执行状态恢复完成');
    } catch (error) {
      console.error('❌ 恢复分步执行状态失败:', error);
    }
  }, []);

  return {
    ...state,
    initializeSteps,
    checkAndExecuteNextStep,
    startExecution,
    cancelExecution,
    continueExecution,
    resetExecution,
    clearParseAttempts,
    restoreState,
  };
}
