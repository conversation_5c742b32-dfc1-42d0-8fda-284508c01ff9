import { addToast } from "@heroui/toast";
import { useCallback, useState } from 'react';
import { useNavigate } from "react-router-dom";
import store from 'store';
import { logIn, logOut, getUserInfo, lanhuLogin, getLanhuAuthorizationStatus } from "@/apis";
// @ts-ignore

/**
 * 全局用户信息
 */
export const useUserInfoStore = () => {
  const navigate = useNavigate();
  const userInfoCache = store.get('userInfo');
  const [userInfo, setUserInfo] = useState(userInfoCache);

  const fetchUserInfo = useCallback((username: string, password: string) => {
    const fetchUser = async () => {
      const result = await logIn({ username, password });

      try {
        await lanhuLogin();
      } catch (lanhuError) {
        console.log(lanhuError);
      }  
      if (result?.data?.code !== 0) {
        addToast({
          color: 'danger',
          title: '登录失败',
          description: result?.data?.message,
        });
      } else {
        const user = result?.data?.data;
        const token = result?.headers?.get('web-heim-token');

        if (user) {
          store.set('isLogin', true);
          store.set('userInfo', user);
          store.set('x-web-heim-token', token);
          setUserInfo(user);
          navigate('/');
        }
      }
    };

    return fetchUser();
  }, []);

  const clearToken = () => {
    store.remove('isLogin');
    store.remove('userInfo');
    store.remove('x-web-heim-token');
  }

  const logOutUser = useCallback(async () => {
    await logOut();
    // if (result?.data?.code !== 0) {
    //   addToast({
    //     color: 'danger',
    //     title: '登出失败',
    //     description: result?.data?.message,
    //   });
    // } else {
    clearToken();
    setUserInfo(null);
    navigate('/login');
    // }
  }, []);

  const getUserStatus = useCallback(async () => {
    try {
      await getUserInfo();
    } catch (error: any) {
      if (error?.status === 401) {
        clearToken();
        setUserInfo(null);
        navigate('/login');
      } else {
        // 蓝湖授权验证失败或其他错误
        console.log('授权验证失败，跳转登录页:', error.message);
        navigate('/login');
      }
    }
  }, [navigate]);

  // 检查蓝湖授权状态
  const checkLanhuAuthStatus = useCallback(async () => {
    try {
      const result = await getLanhuAuthorizationStatus();
      
      if (result?.success) {
        // 如果没有设置自定义令牌（即使用默认令牌），需要跳转到登录页面重新获取
        if (!result.data?.hasCustomToken) {
          console.log('未检测到蓝湖授权令牌，需要重新登录');
          navigate('/login');

          return false;
        }

        return true;
      } else {
        console.log('检查蓝湖授权状态失败:', result?.error);
        setTimeout(() => {
          navigate('/login');
        }, 0);

        return false;
      }
    } catch (error: any) {
      console.log('检查蓝湖授权状态异常:', error);
      setTimeout(() => {
        navigate('/login');
      }, 0);

      return false;
    }
  }, [navigate]);

  return {
    userInfo,
    fetchUserInfo,
    logOutUser,
    getUserStatus,
    checkLanhuAuthStatus,
  };
};
