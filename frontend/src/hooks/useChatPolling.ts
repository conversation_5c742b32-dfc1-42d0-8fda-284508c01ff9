import { UIMessage } from '@ai-sdk/ui-utils';
import { useState, useRef, useCallback, useEffect } from 'react';
import { getChat } from '@/apis';

// 扩展UIMessage类型以包含status字段
interface ExtendedUIMessage extends UIMessage {
  status?: 'pending' | 'processing' | 'completed' | 'failed';
}

interface UseChatPollingOptions {
  playgroundId: string;
  messages: UIMessage[];
  setMessages: (messages: UIMessage[]) => void;
  enabled: boolean;
  pollInterval?: number;
}

interface ChatPollingState {
  isPolling: boolean;
  hasRunningTasks: boolean;
  lastPollTime: number | null;
}

export function useChatPolling({
  playgroundId,
  messages,
  setMessages,
  enabled,
  pollInterval = 10000
}: UseChatPollingOptions): ChatPollingState {
  
  const [isPolling, setIsPolling] = useState(false);
  const [hasRunningTasks, setHasRunningTasks] = useState(false);
  const [lastPollTime, setLastPollTime] = useState<number | null>(null);
  
  const pollIntervalRef = useRef<number | null>(null);
  const lastMessageCountRef = useRef(messages.length);
  const consecutiveNoChangeCount = useRef(0);
  const pollIntervalValueRef = useRef(pollInterval);
  const messagesRef = useRef(messages);
  const setMessagesRef = useRef(setMessages);
  const lastTaskCheckRef = useRef<boolean>(false);
  
  // 更新refs
  pollIntervalValueRef.current = pollInterval;
  messagesRef.current = messages;
  setMessagesRef.current = setMessages;
  
  // 检测是否有未完成的后台任务消息
  const hasIncompleteBackgroundTasks = useCallback((messages: UIMessage[]): boolean => {


    // 优先检查是否有状态为processing或pending的消息
    const processingMessages = messages.filter((message: ExtendedUIMessage) => 
      message.role === 'assistant' && 
      (message.status === 'processing' || message.status === 'pending')
    );
    
    if (processingMessages.length > 0) {


      return true;
    }
    
    // 检查是否有后台任务相关的消息内容（用于兼容旧消息）
    const hasTaskMessage = messages.some((message: ExtendedUIMessage) => {
      if (message.role !== 'assistant') return false;
      
      const content = message.content?.trim() || '';
      
      // 检查是否包含未完成的后台任务内容标识
      const hasIncompleteTaskContent = content.includes('任务已启动') ||
                                       content.includes('开始执行') ||
                                       content.includes('正在处理') ||
                                       content.includes('后台任务') ||
                                       (content.includes('任务') && !content.includes('完成'));
      
      return hasIncompleteTaskContent;
    });
    
    if (hasTaskMessage) {
      return true;
    }
    
    // 最后检查是否所有AI消息都已完成
    const hasAnyAIMessage = messages.some((message: ExtendedUIMessage) => message.role === 'assistant');
    const allAIMessagesCompleted = hasAnyAIMessage && messages.every((message: ExtendedUIMessage) => 
      message.role !== 'assistant' || 
      (message.status === 'completed' || message.status === 'failed')
    );
    
    if (allAIMessagesCompleted) {
      return false;
    }
    
    return false;
  }, []);
  
  // 检测是否有后台任务相关的消息
  const hasBackgroundTaskMessages = useCallback((messages: UIMessage[]): boolean => {
    return messages.some(message => {
      // 检查消息注解中是否有任务相关标识
      const hasTaskAnnotation = message.annotations?.some((annotation: any) => 
        annotation?.type === 'task-status' || 
        annotation?.type === 'background-task' ||
        annotation?.taskId ||
        annotation?.playgroundId
      );
      
      // 检查消息内容是否包含任务相关关键词
      const hasTaskContent = message.role === 'assistant' && message.content && (
        message.content.includes('后台任务') ||
        message.content.includes('正在处理') ||
        message.content.includes('任务执行中') ||
        message.content.includes('background task') ||
        message.content.includes('processing') ||
        message.content.includes('任务已启动') ||
        message.content.includes('开始执行') ||
        message.content.includes('转码任务') ||
        message.content.includes('开始转码')
      );
      
      return hasTaskAnnotation || hasTaskContent;
    });
  }, []);
  
  // 轮询函数
  const pollMessages = useCallback(async () => {
    try {
      
      const latestMessages = await getChat({ id: playgroundId, sort: 'asc' });
      
      // 检查消息是否有变化
      const hasChanged = latestMessages.length !== messages.length ||
                        JSON.stringify(latestMessages) !== JSON.stringify(messages);
      
      if (hasChanged) {
        setMessages(latestMessages);
        consecutiveNoChangeCount.current = 0;
        lastMessageCountRef.current = latestMessages.length;
        setLastPollTime(Date.now());
        
        // 检查是否还有未完成的后台任务
        const stillHasIncomplete = hasIncompleteBackgroundTasks(latestMessages);
        
        // 如果没有未完成的任务，停止轮询
        if (!stillHasIncomplete) {
          console.log('✅ [ChatPolling] 任务完成，停止轮询');
          setIsPolling(false);
          setHasRunningTasks(false);

          return;
        }
      } else {
        consecutiveNoChangeCount.current++;
        
        // 如果连续5次轮询都没有变化，重新检查任务状态
        if (consecutiveNoChangeCount.current >= 5) {
          const stillHasIncomplete = hasIncompleteBackgroundTasks(messages);

          if (!stillHasIncomplete) {
            console.log('🔍 [ChatPolling] 任务已完成，停止轮询');
            setIsPolling(false);
            setHasRunningTasks(false);

            return;
          }
        }
        
        // 如果连续10次轮询都没有变化，停止轮询
        if (consecutiveNoChangeCount.current >= 10) {
          console.log('⏹️ [ChatPolling] 超时停止轮询');
          setIsPolling(false);
          setHasRunningTasks(false);

          return;
        }
      }
      
    } catch (error) {
      console.error('❌ [ChatPolling] 轮询失败:', error);
      consecutiveNoChangeCount.current++;
      
      // 如果连续失败3次，停止轮询
      if (consecutiveNoChangeCount.current >= 3) {
        console.log('❌ [ChatPolling] 轮询失败，停止轮询');
        setIsPolling(false);
        setHasRunningTasks(false);
      }
    }
  }, [playgroundId, messages, setMessages, hasIncompleteBackgroundTasks, hasBackgroundTaskMessages]);
  
  // 开始轮询
  const startPolling = useCallback(() => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
    }
    

    setIsPolling(true);
    setHasRunningTasks(true);
    consecutiveNoChangeCount.current = 0;
    
    // 立即执行一次
    pollMessages();
    
    // 设置定时轮询
    pollIntervalRef.current = setInterval(pollMessages, pollInterval);
  }, [pollMessages, pollInterval]);
  
  // 停止轮询
  const stopPolling = useCallback(() => {
    
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }
    
    setIsPolling(false);
    setHasRunningTasks(false);
    consecutiveNoChangeCount.current = 0;
  }, []);
  
  // 监听消息变化，决定是否开始/停止轮询
  useEffect(() => {
    if (!enabled) {
      // 直接调用停止轮询逻辑，避免函数依赖
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
      setIsPolling(false);
      setHasRunningTasks(false);

      return;
    }
    
    // 直接在这里检查是否有未完成的后台任务，避免函数依赖
    const processingMessages = messages.filter((message: ExtendedUIMessage) => 
      message.role === 'assistant' && 
      (message.status === 'processing' || message.status === 'pending')
    );
    
    const hasTaskMessage = messages.some((message: ExtendedUIMessage) => {
      if (message.role !== 'assistant') return false;
      const content = message.content?.trim() || '';

      return content.includes('任务已启动') ||
             content.includes('开始执行') ||
             content.includes('正在处理') ||
             content.includes('后台任务') ||
             (content.includes('任务') && !content.includes('完成'));
    });
    
        const hasIncomplete = processingMessages.length > 0 || hasTaskMessage;
    
    // 只有当任务状态真正发生变化时才执行轮询操作
    if (hasIncomplete !== lastTaskCheckRef.current) {
      lastTaskCheckRef.current = hasIncomplete;
      
      if (hasIncomplete && !isPolling) {
        console.log('🎯 [ChatPolling] 开始轮询');
        // 直接调用开始轮询逻辑，避免函数依赖
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
        }
        setIsPolling(true);
        setHasRunningTasks(true);
        consecutiveNoChangeCount.current = 0;
        
        // 立即执行一次轮询
        pollMessages();
        // 设置定时轮询
        pollIntervalRef.current = setInterval(pollMessages, pollIntervalValueRef.current);
        
      } else if (!hasIncomplete && isPolling) {
        console.log('✅ [ChatPolling] 停止轮询');
        // 直接调用停止轮询逻辑，避免函数依赖
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current);
          pollIntervalRef.current = null;
        }
        setIsPolling(false);
        setHasRunningTasks(false);
      }
    }
    
  }, [messages, enabled]); // 只保留必要的依赖，避免函数依赖导致的循环
  
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);
  
  return {
    isPolling,
    hasRunningTasks,
    lastPollTime
  };
}