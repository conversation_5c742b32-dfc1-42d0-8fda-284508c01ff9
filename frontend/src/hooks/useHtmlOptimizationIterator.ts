import { useEffect, useRef, useState, useCallback } from 'react';
import { UIMessage } from '@ai-sdk/ui-utils';

interface HtmlOptimizationIteratorConfig {
  maxIterations: number;
  playgroundType: string;
  isEnabled: boolean;
  onAppendMessage: (message: string) => void;
}

export function useHtmlOptimizationIterator({
  maxIterations = 3,
  playgroundType,
  isEnabled,
  onAppendMessage,
}: HtmlOptimizationIteratorConfig) {
  const [iterationCount, setIterationCount] = useState(0);
  const [isIterating, setIsIterating] = useState(false);
  const [maxIterationsReached, setMaxIterationsReached] = useState(false);
  const [hasStartedIterating, setHasStartedIterating] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);
  const lastMessageRef = useRef<string>('');
  const timeoutRef = useRef<number>();

  // 检查是否应该开始自迭代
  const shouldStartIteration = useCallback((messages: UIMessage[], status: string) => {
    // 只对HTML类型的playground生效
    if (!isEnabled || playgroundType !== 'html' || status !== 'ready') {
      return false;
    }

    // 如果被用户取消，不再迭代
    if (isCancelled) {
      return false;
    }

    // 如果已经达到最大迭代次数，不再迭代
    if (iterationCount >= maxIterations) {
      if (!maxIterationsReached) {
        setMaxIterationsReached(true);
      }
      return false;
    }

    // 检查是否有AI助手的回复包含文件生成
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'assistant') {
      return false;
    }

    // 检查消息是否包含文件生成的注解
    const hasFileGeneration = lastMessage.annotations?.some(
      (annotation: any) => annotation?.type === 'all-files-saved'
    );

    if (!hasFileGeneration) {
      return false;
    }

    // 避免重复触发相同消息的迭代
    const messageKey = `${lastMessage.id}-${iterationCount}`;
    if (lastMessageRef.current === messageKey) {
      return false;
    }

    // 检查是否是用户发起的自动迭代消息
    const isAutoIterationMessage = messages.some(msg => 
      msg.role === 'user' && 
      msg.content === '实现的还是有问题，请继续优化' &&
      msg.id !== messages[0].id // 不是第一条消息
    );

    // 如果已经有自动迭代消息，说明已经开始了迭代流程
    if (isAutoIterationMessage) {
      setHasStartedIterating(true);
    }

    // 只有在第一次生成文件后，或者已经开始迭代流程时才进行自动迭代
    if (!hasStartedIterating && !isAutoIterationMessage) {
      // 这是第一次生成文件，开始迭代流程
      setHasStartedIterating(true);
    }

    lastMessageRef.current = messageKey;
    return true;
  }, [isEnabled, playgroundType, iterationCount, maxIterations, maxIterationsReached, hasStartedIterating, isCancelled]);

  // 开始自迭代检查
  const startIteration = useCallback(() => {
    if (isIterating || iterationCount >= maxIterations || isCancelled) {
      return;
    }

    setIsIterating(true);
    
    // 延迟发送消息，给用户一些时间看到结果
    timeoutRef.current = window.setTimeout(() => {
      // 再次检查是否被取消
      if (isCancelled) {
        setIsIterating(false);
        return;
      }
      
      const iterationMessage = '实现的还是有问题，请继续优化';
      onAppendMessage(iterationMessage);
      
      setIterationCount(prev => prev + 1);
      setIsIterating(false);
    }, 3000); // 延迟3秒发送，给用户更多时间查看结果
  }, [isIterating, iterationCount, maxIterations, onAppendMessage, isCancelled]);

  // 取消当前迭代
  const cancelIteration = useCallback(() => {
    setIsCancelled(true);
    setIsIterating(false);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  // 重新启用迭代
  const resumeIteration = useCallback(() => {
    setIsCancelled(false);
  }, []);

  // 重置迭代状态
  const resetIteration = useCallback(() => {
    setIterationCount(0);
    setIsIterating(false);
    setMaxIterationsReached(false);
    setHasStartedIterating(false);
    setIsCancelled(false);
    lastMessageRef.current = '';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    iterationCount,
    isIterating,
    maxIterationsReached,
    hasStartedIterating,
    isCancelled,
    shouldStartIteration,
    startIteration,
    cancelIteration,
    resumeIteration,
    resetIteration,
  };
} 