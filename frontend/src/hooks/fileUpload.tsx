import { useRef, useState } from "react";

interface IFile {
 name: string; 
 contentType: string; 
 url: string;
 imgWidth?: string; 
 imgHeight?: string;
}

export function useFileUpload(multiple=true) {
  const iptRef = useRef<HTMLInputElement>(null);
  const [files, setFiles] = useState<IFile[]>([]);

  const handleSetFiles = (fileItem: IFile) => {
    setFiles(multiple ? [
      ...files, fileItem
    ] : [fileItem]);
    iptRef.current!.value = '';
  }

  function onFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.item(0);

    if (file) {
      const reader = new FileReader();

      reader.readAsDataURL(file);
      reader.onload = (e) => {
        const result = e.target?.result as string;

        if(!result){
          return 
        }

        let fileItem: IFile = {
          name: file.name,
          contentType: file.type,
          url: result
        }

        if(file.type.startsWith('image/')){
          const img = new Image();

          img.onload = function () {
            // 图片加载完成后获取宽高
            fileItem = {
              ...fileItem,
              imgWidth: img.width.toString(),
              imgHeight: img.height.toString(),
            }
            handleSetFiles(fileItem)
          };
          img.src = result;

          return 
        }
        handleSetFiles(fileItem)
      };
    }
  }

  function removeFile(file: { name: string; contentType: string; url: string }) {
    setFiles(files.filter(f => f !== file));
  }

  function clearFiles() {
    setFiles([]);
  }

  return { iptRef, files, onFileChange, removeFile, clearFiles };
}
