import { Button } from "@heroui/react";
import { useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import { getDesignProject, DesignProject } from "@/apis";
import { Breadcrumb } from "@/components/Breadcrumb";
import { CreateProject } from "@/components/Project/CreateProject";
import { ProjectSettings } from "@/components/Project/ProjectSettings";
import { Sidebar } from "@/components/Sidebar";
import { TopBar } from "@/components/TopBar";
import { ProjectProvider } from "@/contexts/ProjectContext";
import IconCog from "~icons/mdi/cog";

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const location = useLocation();
  const params = useParams<{ projectId?: string; id?: string }>();
  const [projectId, setProjectId] = useState<string | undefined>();
  const [project, setProject] = useState<DesignProject | undefined>(undefined);

  useEffect(() => {
    const currentProjectId = params.projectId || params.id;
    const path = location.pathname;

    if (path.startsWith('/design-projects/') || 
        path.startsWith('/project/') || 
        path.startsWith('/prototype/') ||
        path.startsWith('/workflow/') ||
        path.startsWith('/multi-workflow/') ||
        path.startsWith('/workflow-manager/')) {
      if (currentProjectId) {
        setProjectId(currentProjectId);
      }
    } else {
      setProjectId(undefined);
    }
  }, [location.pathname, params]);

  const loadProject = async () => {
    if (!projectId) {
      setProject(undefined);

      return;
    }
    try {
      const data = await getDesignProject({ id: projectId });

      if (data && typeof data === "object" && "id" in data) {
        setProject(data as DesignProject);
      } else {
        setProject(undefined);
      }
    } catch (error) {
      console.error("Failed to load project in layout:", error);
      setProject(undefined);
    }
  };

  useEffect(() => {
    loadProject();
  }, [projectId]);

  const showCreateProjectButton = location.pathname === '/project-list';
  const showBreadcrumb = !location.pathname.startsWith('/chat/') && location.pathname !== '/';

  const handleProjectCreated = () => {
    window.location.reload();
  };

  return (
    <div className="flex h-screen bg-zinc-50 dark:bg-zinc-950 p-2 gap-2">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden gap-2">
        <TopBar />
        <main className="flex-1 flex flex-col border-[rgb(242,242,242)] overflow-hidden rounded-lg bg-white shadow-lg dark:bg-zinc-900/50 backdrop-blur-sm">
          {showBreadcrumb && (
            <div className="flex-shrink-0 border-b border-[rgb(242,242,242)] dark:border-zinc-700">
              <div className="h-12 px-6 py-4 flex items-center justify-between">
                <Breadcrumb />
                <div className="flex items-center gap-3">
                  {showCreateProjectButton && (
                    <CreateProject onProjectCreated={handleProjectCreated}>
                      {(onOpen) => (
                        <Button
                          className="transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
                          size="sm"
                          onPress={onOpen}
                        >
                          创建项目
                        </Button>
                      )}
                    </CreateProject>
                  )}
                  <ProjectSettings project={project} onProjectUpdated={loadProject}>
                    {(onOpen) => (
                      <Button
                        isIconOnly
                        className="ml-1 transition-all duration-200 rounded-md bg-transparent border-1 dark:border-zinc-700"
                        size="sm"
                        onPress={onOpen}
                      >
                        <IconCog className="w-4 h-4" />
                      </Button>
                    )}
                  </ProjectSettings>
                </div>
              </div>
            </div>
          )}
          <div className={`flex-1 overflow-auto pb-3 ${!showBreadcrumb ? 'h-full' : ''}`}>
            <ProjectProvider 
              project={project} 
              setProject={setProject}
              onProjectUpdate={loadProject}
            >
              {children}
            </ProjectProvider>
          </div>
        </main>
      </div>
    </div>
  );
}
