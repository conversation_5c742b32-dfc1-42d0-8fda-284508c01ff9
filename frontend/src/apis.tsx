import axios from 'axios';
// @ts-ignore
import store from 'store';

export const baseUrl = '/api/ai-coding/';

function buildApi<Req extends Record<string, any> | void, Res>(
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
  url: string,
  options?: {
    transform?: (res: any) => Res;
    baseUrl?: string;
    isNeedFullResponse?: boolean;
  },
) {
  return async (params?: Req) => {
    let sendParams,
      reqUrl = url;

    if (typeof params === 'object') {
      sendParams = { ...params };
      reqUrl = url.replace(/:(\w+)/g, (_match, p1) => {
        delete sendParams![p1];

        return params[p1];
      });
    }

    // 用户登录token
    const token = store.get('x-web-heim-token');
    const ret = await axios({
      method,
      url: (options?.baseUrl ?? baseUrl) + reqUrl,
      params: method === 'GET' ? sendParams : null,
      data: method !== 'GET' ? sendParams : null,
      headers: {
        'x-web-system-id': 'ai-coding',
        'x-web-heim-token': token,
      },
    });

    if (options?.isNeedFullResponse) {
      return ret;
    }

    if (ret.data.error) {
      throw ret.data.error;
    }

    return options?.transform ? options.transform(ret.data) : (ret.data as Res);
  };
}

export const createPlayground = buildApi<
  {
    model: string;
    desc: string;
    projectId?: string;
    user: string;
    isPublic: boolean;
    type?: string;

    // HTML Agent 执行配置
    enableAutoIteration?: boolean; // 是否启用自动迭代
    enableStepByStep?: boolean; // 是否启用分步执行
    enablePreview?: boolean; // 是否启用预览功能
  },
  {
    id: string;
  }
>('POST', '');

export const updatePlaygroundStepByStepData = buildApi<
  {
    id: string;
    stepByStepData: any;
  },
  any
>('PUT', ':id/step-by-step-data');

export const updatePlaygroundCustomPreviewUrl = buildApi<
  {
    id: string;
    customPreviewUrl: string;
  },
  any
>('PATCH', ':id/custom-preview-url');

export const listPlaygrounds = buildApi<
  { tag?: string; sort?: 'asc' | 'desc'; user?: string; take?: number; skip?: number; projectId?: string },
  any
>('GET', '');
export const getPlayground = buildApi<{ id: string }, any>('GET', ':id');
export const deletePlayground = buildApi<{ id: string }, any>('DELETE', ':id');
export const startPlayground = buildApi<{ id: string }, any>('POST', ':id/@start');
export const stopPlayground = buildApi<{ id: string }, any>('POST', ':id/@stop');
export const restartPlayground = buildApi<{ id: string }, { url: string }>('POST', ':id/@restart');
export const listFiles = buildApi<{ id: string }, any>('GET', ':id/@files');
export const getFile = buildApi<{ id: string; path: string }, string>('GET', ':id/@files/:path');
export const createDirectory = buildApi<{ id: string; path: string }, any>('POST', ':id/@mkdir');
export const moveFile = buildApi<{ id: string; oldPath: string; newPath: string }, any>('POST', ':id/@move');
export const saveFile = buildApi<{ id: string; path: string; content: string }, any>('POST', ':id/@save');
export const deleteFile = buildApi<{ id: string; path: string }, any>('DELETE', ':id/@delete');
export const versionStatus = buildApi<{ id: string }, { list: string[]; current: string }>('GET', ':id/@versionStatus');
export const changeVersion = buildApi<{ id: string; version: string }, any>('POST', ':id/@changeVersion');
export const commitFiles = buildApi<{ id: string }, any>('POST', ':id/@commitFiles');
export const playgroundInfo = buildApi<{ id: string; type: string }, any>('GET', ':id/@info');
export const installDeps = buildApi<{ id: string; deps: string }, any>('POST', ':id/@install');
export const uninstallDeps = buildApi<{ id: string; deps: string }, any>('POST', ':id/@uninstall');
export const getChat = buildApi<{ id: string; sort?: 'asc' | 'desc'; take?: number; skip?: number }, any>(
  'GET',
  ':id/@chat',
);
export const deleteMessage = buildApi<{ id: string; messageId: string }, any>('DELETE', ':id/@deleteMessage');
export const editMessage = buildApi<{ id: string; messageId: string; content: string }, any>('PUT', ':id/@editMessage');
export const deploy = buildApi<{ id: string; mode?: 'preview' }, any>('POST', ':id/@deploy');
export const unarchive = buildApi<{ id: string }, any>('POST', ':id/@unarchive');
export const uploadImage = buildApi<FormData, any>('POST', '@uploadImage');

export interface Project {
  id: string;
  name: string;
  description: string;
  framework: string;
  componentLibrary: string;
  llmstxt: string;
  created: string;
  user: string;
}

export const getProject = buildApi<{ id: string }, Project>('GET', ':id', {
  baseUrl: '/api/project/',
});
export const listProjects = buildApi<{ sort?: 'asc' | 'desc' }, Project[]>('GET', '', {
  baseUrl: '/api/project/',
});
export const createProject = buildApi<Omit<Project, 'id' | 'created' | 'user'>, Project>('POST', '', {
  baseUrl: '/api/project/',
});
export const deleteProject = buildApi<{ id: string }, any>('DELETE', ':id', {
  baseUrl: '/api/project/',
});
export const updateProject = buildApi<Partial<Project>, Project>('PATCH', ':id', {
  baseUrl: '/api/project/',
});
export const enhancePrompt = buildApi<{ desc: string }, string>('POST', '@enhance');

export const logIn = buildApi<{ username: string; password: string }, any>('POST', 'login', {
  baseUrl: '/api/user/',
  isNeedFullResponse: true,
});

export const logOut = buildApi<{}, any>('POST', 'logout', {
  baseUrl: '/api/user/',
  isNeedFullResponse: true,
});

export const lanhuLogin = buildApi<{}, any>('POST', 'lanhu/login');

// 系统配置相关接口
export const getSystemConfig = buildApi<{}, any>('GET', 'system/config');
export const updateLanhuAuthorization = buildApi<{ lanhuAuthorization: string }, any>('PUT', 'system/lanhu-authorization');
export const getLanhuAuthorizationStatus = buildApi<{}, any>('GET', 'system/lanhu-authorization/status');

export const getUserInfo = buildApi<{}, any>('POST', 'getUserInfo', {
  baseUrl: '/api/web-auth/',
  isNeedFullResponse: true,
});

export const getCredits = buildApi<{}, any>('GET', 'credits', {
  baseUrl: '/api/ai-coding/',
});

// 蓝湖相关接口和类型定义
export interface LanhuImageVersionCodeResponse {
  code?: number;
  message?: string;
  data?: any; // 根据实际返回结构调整
}

export const getLanhuImageVersionCode = buildApi<
  {
    version_id: string;
    lanhu_token?: string;
  },
  LanhuImageVersionCodeResponse
>('GET', 'lanhu/image/version/code', {
  baseUrl: '/api/ai-coding/',
});

// 蓝湖项目图片列表接口
export interface LanhuProjectImagesResponse {
  code?: number;
  message?: string;
  data?: {
    images?: Array<{
      id?: string;
      name?: string;
      url?: string;
      width?: number;
      height?: number;
      batch?: string;
      create_time?: string;
      update_time?: string;
      group?: any[];
      has_comment?: boolean;
      home?: boolean;
      is_replaced?: boolean;
      last_version_num?: number;
      latest_version?: string; // 最新版本ID，用于对比是否已分配
      layout_data?: string;
      order?: any;
      pinyinname?: string;
      position_x?: number;
      position_y?: number;
      share_id?: string;
      sketch_id?: string;
      source?: boolean;
      text_scale?: any;
      type?: string;
      user_id?: string;
      [key: string]: any;
    }>;
    total?: number;
    [key: string]: any;
  };
}

export const getLanhuProjectImages = buildApi<
  {
    project_id: string;
    lanhu_token?: string;
  },
  LanhuProjectImagesResponse
>('GET', 'lanhu/project/images', {
  baseUrl: '/api/ai-coding/',
});

// 蓝湖项目分区列表接口
export interface LanhuProjectSectorsResponse {
  code?: string;
  msg?: string;
  data?: {
    sectors?: Array<{
      id?: string;
      name?: string;
      order?: number;
      parent_id?: string | null;
      images?: string[]; // 图片ID数组
      [key: string]: any;
    }>;
    [key: string]: any;
  };
}

export const getLanhuProjectSectors = buildApi<
  {
    project_id: string;
    lanhu_token?: string;
  },
  LanhuProjectSectorsResponse
>('GET', 'lanhu/project/sectors', {
  baseUrl: '/api/ai-coding/',
});

// 批量绑定原型接口
export interface BatchBindPrototypesRequest {
  projectId: string;
  pageId: string;
  selectedImages: Array<{
    id: string;
    name: string;
    pinyinname?: string;
    latest_version?: string;
    width?: number;
    height?: number;
    url?: string;
    [key: string]: any;
  }>;
}

export interface BatchBindPrototypesResponse {
  success: boolean;
  message: string;
  results: {
    successCount: number;
    failedCount: number;
    createdPrototypes: any[];
    errors?: string[];
  };
}

export const batchBindPrototypes = buildApi<
  BatchBindPrototypesRequest,
  BatchBindPrototypesResponse
>('POST', 'batch-bind-prototypes', {
  baseUrl: '/api/ai-coding/',
});

// 设计稿转码相关接口和类型
export interface DesignProject {
  id: string;
  name: string;
  description: string;
  projectId?: string; // 关联的项目代码模板ID
  user: string;
  created: string;
  updated: string;
  config?: any; // 转码配置
  pages?: DesignPage[];

  // 技术栈配置
  framework?: string; // 框架
  componentLibrary?: string; // UI组件库
  llmstxt?: string; // 自定义组件说明

  // Git配置
  gitUrl?: string; // Git仓库地址
  gitBranch?: string; // Git分支（生码分支）
  gitCodeDir?: string; // 代码生成目录

  // 预览服务配置
  previewStartCommand?: string; // 自定义预览启动命令

  // 蓝湖配置
  lanhuProjectId?: string; // 蓝湖项目ID
  lanhuToken?: string; // 蓝湖Token
}

// 智能切图相关接口
export interface ISegment {
  imgUrl: string; // 组件的图片地址
  name: string;
  id: string;
  position: {
    left: number;
    top: number;
    right: number;
    bottom: number;
  }; // 组件坐标
}

export interface SlicedAssets {
  // 生成的 html 内容
  html: {
    name: string;
    content: string;
  };
  fullImgUrl: string; // 用户上传的原始图片地址
  segment?: ISegment[]; // 存储组件信息
  [key: string]: any; // 添加索引签名以兼容 Prisma InputJsonValue
}

// 设计稿页面原型
export interface DesignPagePrototype {
  id: string;
  prototypeName: string; // 原型名称，如A、B、C
  englishName?: string;
  designPageId: string;
  htmlContent: string;
  cssContent: string;
  htmlFileName: string;
  cssFileName: string;
  imgFileName?: string; // 原始图片存储地址
  imgFileLink?: string; // 图片访问链接
  lanhuVersionId?: string; // 蓝湖版本ID
  imgWidth?: string; // 图片宽度
  imgHeight?: string; // 图片高度
  status: 'pending' | 'processing' | 'completed' | 'failed';
  resultHtml?: string;
  resultCss?: string;
  playgroundId?: string; // 关联的chat页面ID，用于查看转码结果
  slicedAssets?: SlicedAssets; // 智能切图资产信息
  created: string;
  updated: string;
}

// 设计稿页面
export interface DesignPage {
  id: string;
  name: string;
  description?: string;
  designProjectId: string;
  prototypes: DesignPagePrototype[]; // 关联的原型
  created: string;
  updated: string;
  status?: string;
  resultHtml?: string;
  resultCss?: string;
  playgroundId?: string;
  designProject?: DesignProject;
}

// 设计稿转码工程接口
export const createDesignProject = buildApi<Omit<DesignProject, 'id' | 'created' | 'updated' | 'pages'>, DesignProject>(
  'POST',
  '',
  {
    baseUrl: '/api/design-project/',
  },
);

export const listDesignProjects = buildApi<{ user?: string }, DesignProject[]>('GET', '', {
  baseUrl: '/api/design-project/',
});

export const getDesignProject = buildApi<{ id: string }, DesignProject>('GET', ':id', {
  baseUrl: '/api/design-project/',
});

export const updateDesignProject = buildApi<{ id: string } & Partial<DesignProject>, DesignProject>('PATCH', ':id', {
  baseUrl: '/api/design-project/',
});

export const deleteDesignProject = buildApi<{ id: string }, any>('DELETE', ':id', {
  baseUrl: '/api/design-project/',
});

// 设计稿页面相关API
export const createDesignPage = buildApi<
  {
    projectId: string;
    name: string;
    description?: string;
    prototypes: Partial<DesignPagePrototype>[];
  },
  DesignPage
>('POST', ':projectId/pages', {
  baseUrl: '/api/design-project/',
});

export const listDesignPages = buildApi<{ projectId: string }, DesignPage[]>('GET', ':projectId/pages', {
  baseUrl: '/api/design-project/',
});

export const getDesignPage = buildApi<{ projectId: string; pageId: string }, DesignPage>(
  'GET',
  ':projectId/pages/:pageId',
  {
    baseUrl: '/api/design-project/',
  },
);

export const updateDesignPage = buildApi<
  {
    projectId: string;
    pageId: string;
  } & Partial<DesignPage>,
  DesignPage
>('PATCH', ':projectId/pages/:pageId', {
  baseUrl: '/api/design-project/',
});

export const deleteDesignPage = buildApi<{ projectId: string; pageId: string }, any>(
  'DELETE',
  ':projectId/pages/:pageId',
  {
    baseUrl: '/api/design-project/',
  },
);

// 页面原型相关API
export const createPagePrototype = buildApi<
  {
    projectId: string;
    pageId: string;
  } & Partial<DesignPagePrototype>,
  DesignPagePrototype
>('POST', ':projectId/pages/:pageId/prototypes', {
  baseUrl: '/api/design-project/',
});

export const updatePagePrototype = buildApi<
  {
    projectId: string;
    pageId: string;
    prototypeId: string;
  } & Partial<DesignPagePrototype>,
  DesignPagePrototype
>('PATCH', ':projectId/pages/:pageId/prototypes/:prototypeId', {
  baseUrl: '/api/design-project/',
});

export const deletePagePrototype = buildApi<
  {
    projectId: string;
    pageId: string;
    prototypeId: string;
  },
  any
>('DELETE', ':projectId/pages/:pageId/prototypes/:prototypeId', {
  baseUrl: '/api/design-project/',
});

// 批量选择页面原型进行转码
export const selectPagesForTranscode = buildApi<
  {
    projectId: string;
    prototypeIds: string[];
  },
  any
>('POST', ':projectId/transcode', {
  baseUrl: '/api/design-project/',
});

// 创建转码任务
export const createTranscodeTask = buildApi<
  {
    projectId: string;
    prototypeIds: string[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
  },
  any
>('POST', ':projectId/transcode-task', {
  baseUrl: '/api/design-project/',
});

// 创建后台转码任务（新接口）
export const createBackgroundTranscodeTask = buildApi<
  {
    projectId: string;
    prototypeIds: string[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
  },
  any
>('POST', ':projectId/background-transcode-task', {
  baseUrl: '/api/design-project/',
});

// 创建蓝湖设计稿转码任务（使用原型IDs）
export const createLanhuTranscodeTask = buildApi<
  {
    projectId: string;
    prototypeIds: string[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    metadata?: {};
    waitForCompletion?: boolean;
  },
  any
>('POST', ':projectId/tasks/lanhu-transcode', {
  baseUrl: '/api/design-project/',
});

// 创建设计稿原型代码合并任务
export const createMergeTask = buildApi<
  {
    projectId: string;
    prototypeIds: string[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    metadata?: {};
    waitForCompletion?: boolean;
  },
  any
>('POST', ':projectId/tasks/merge', {
  baseUrl: '/api/design-project/',
});

// 创建生产级代码生成任务
export const createSpecToProdCodeTask = buildApi<
  {
    projectId: string; // 项目ID (必填)
    taskName: string; // 任务名称
    gitUrl: string; // 项目框架Git地址 (必填)
    branch?: string; // Git分支 (可选，默认master)
    pages: Array<{
      name: string;
      htmlContent: string;
      description?: string;
    }>;
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion?: boolean;
  },
  any
>('POST', ':projectId/tasks/spec-to-prod-code', {
  baseUrl: '/api/design-project/',
});

// 获取转码任务详情
export const getTranscodeTask = buildApi<
  {
    taskId: string;
  },
  any
>('GET', 'transcode-task/:taskId', {
  baseUrl: '/api/design-project/',
});

// 获取项目的所有转码任务
export const getProjectTranscodeTasks = buildApi<
  {
    projectId: string;
  },
  any
>('GET', ':projectId/transcode-tasks', {
  baseUrl: '/api/design-project/',
});

// 获取项目的所有任务列表
export const getProjectTasks = buildApi<
  {
    projectId: string;
    taskType?: string;
    taskIds?: string;
  },
  any
>('GET', ':projectId/tasks/list', {
  baseUrl: '/api/design-project/',
});

// 更新转码任务状态
export const updateTranscodeTaskStatus = buildApi<
  {
    taskId: string;
    status: string;
    progress?: number;
  },
  any
>('PATCH', 'transcode-task/:taskId/status', {
  baseUrl: '/api/design-project/',
});

// 更新转码任务条目状态
export const updateTranscodeTaskItem = buildApi<
  {
    itemId: string;
    status?: string;
    progress?: number;
    stage?: string;
    playgroundId?: string;
    error?: string;
  },
  any
>('PATCH', 'transcode-task-item/:itemId', {
  baseUrl: '/api/design-project/',
});

export interface IImgVisualSplitItem {
  prototypeId?: string; // 原型ID
  name?: string; // 项目名称
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
    imgHeight: string; // 图片高度
    imgWidth: string; // 图片宽度
  };
}

// 图片分割任务
export const imgVisualSplitApi = buildApi<
  {
    items: IImgVisualSplitItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', ':projectId/tasks/img-visual-split', {
  baseUrl: '/api/design-project/',
});

// 后台图片分割任务
export const backgroundImgVisualSplitApi = buildApi<
  {
    items: IImgVisualSplitItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', 'img-visual-split', {
  baseUrl: '/api/background-tasks/',
});

export interface IImgToCodeItem {
  prototypeId?: string; // 原型ID
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
  };
}
// 图片转代码任务
export const imgToCodeApi = buildApi<
  {
    items: IImgToCodeItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', ':projectId/tasks/img-to-code', {
  baseUrl: '/api/design-project/',
});

// 后台图片转代码任务
export const backgroundImgToCodeApi = buildApi<
  {
    projectId?: string; // 项目ID (可选)
    items: IImgToCodeItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', 'img-to-code', {
  baseUrl: '/api/background-tasks/',
});

export enum ETaskType {
  DIRECT_IMG_TO_CODE = 'direct-img-to-code', // 直接图转码
  SPLIT_IMG_TO_CODE = 'split-img-to-code', // 拆图(智能拆分/手动拆分)转码
}

export interface IAsyncImgToCodeItem {
  prototypeId?: string; // 原型ID
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
    imgHeight?: string; // 图片高度
    imgWidth?: string; // 图片宽度
    coordinates?: TCoordinate[]; // 图片坐标
  };
}

export interface IAsyncImgToCodeTaskCreateParam {
  user: string;
  items: IAsyncImgToCodeItem[];
  type: ETaskType;
  imgSplitItems?: ISplitImgToCodeItem[];
  model?: string;
  projectId?: string; // 项目ID (可选)
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
}

// 后台图片转代码任务
export const asyncImgToCodeTaskCreate = buildApi<IAsyncImgToCodeTaskCreateParam, any>(
  'POST',
  'async-img-to-code/task/create',
  {
    baseUrl: '/api/design-project/',
  },
);

export enum ETaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// 后台图片转代码任务
export const getAsyncImgToCodeStatus = buildApi<{ taskId: string }, any>('GET', 'async-img-to-code/:taskId/status', {
  baseUrl: '/api/design-project/',
  isNeedFullResponse: true,
});

// 根据chatId获取图转码任务
export const getAsyncImgToCodeTaskByChatId = buildApi<{ chatId: string }, any>(
  'GET',
  'async-img-to-code/task/:chatId',
  {
    baseUrl: '/api/design-project/',
  },
);

export interface ISplitImgToCodeItem {
  prototypeId?: string; // 原型ID
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
    imgHeight: string; // 图片高度
    imgWidth: string; // 图片宽度
  };
}

// 切图转码
export const splitImgToCodeApi = buildApi<
  {
    items: ISplitImgToCodeItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', ':projectId/tasks/img-split-to-code', {
  baseUrl: '/api/design-project/',
});

// 后台切图转码
export const backgroundSplitImgToCodeApi = buildApi<
  {
    items: ISplitImgToCodeItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', 'img-split-to-code', {
  baseUrl: '/api/background-tasks/',
});

export type TCoordinate = {
  name: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
};
export interface ICoordsToLayoutItem {
  prototypeId?: string; // 原型ID
  metadata: {
    imgHeight: string; // 图片高度
    imgWidth: string; // 图片宽度
    coordinates: TCoordinate[];
  };
}
// 切图坐标生成布局任务
export const coordsToLayoutApi = buildApi<
  {
    items: ICoordsToLayoutItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', ':projectId/tasks/coords-to-layout', {
  baseUrl: '/api/design-project/',
});

// 后台切图坐标生成布局任务
export const backgroundCoordsToLayoutApi = buildApi<
  {
    items: ICoordsToLayoutItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    waitForCompletion: boolean; // 是否等待任务完成 (可选)
  },
  any
>('POST', 'coords-to-layout', {
  baseUrl: '/api/background-tasks/',
});

// 进程管理相关接口和类型
export interface ProcessInfo {
  id: string;
  command: string;
  args: string[];
  status: 'running' | 'completed' | 'failed' | 'killed';
  pid?: number;
  user: string;
  startTime: string;
  endTime?: string;
  exitCode?: number;
  output?: string;
  error?: string;
  metadata?: {
    pageName?: string;
    [key: string]: any;
  };
}

export interface ProcessStats {
  running: number;
  completed: number;
  failed: number;
  killed: number;
  total: number;
}

// 进程管理相关API
export const getUserProcesses = buildApi<{ userId: string }, ProcessInfo[]>('GET', 'user-processes', {
  baseUrl: '/api/process-manager/',
});

export const getRunningProcesses = buildApi<void, ProcessInfo[]>('GET', 'running-processes', {
  baseUrl: '/api/process-manager/',
});

export const getPlaygroundProcesses = buildApi<{ playgroundId: string }, ProcessInfo[]>(
  'GET',
  'playground-processes/:playgroundId',
  {
    baseUrl: '/api/process-manager/',
  },
);

export const killProcess = buildApi<{ processId: string; reason: string }, any>('POST', 'kill', {
  baseUrl: '/api/process-manager/',
});

export const getProcessStats = buildApi<void, ProcessStats>('GET', 'stats', {
  baseUrl: '/api/process-manager/',
});

// 🔧 新增：重启spec-to-prod-code类型playground的自定义预览服务
export const restartCustomPreview = buildApi<
  { playgroundId: string; user: string },
  { success: boolean; url?: string; message: string; processId?: string }
>('POST', 'restart:playgroundId', {
  baseUrl: '/api/pm2-preview/',
});

// 🔧 新增：心跳和生命周期管理API
export const startCustomPreviewHeartbeat = buildApi<
  { playgroundId: string; user: string; sessionId: string },
  { success: boolean; url?: string; message: string; processId?: string }
>('POST', 'start/:playgroundId', {
  baseUrl: '/api/pm2-preview/',
});

export const sendCustomPreviewHeartbeat = buildApi<
  { playgroundId: string; user: string; sessionId: string },
  { success: boolean; message: string }
>('POST', 'heartbeat/:playgroundId', {
  baseUrl: '/api/pm2-preview/',
});

export const stopCustomPreviewHeartbeat = buildApi<
  { playgroundId: string; user: string; sessionId: string },
  { success: boolean; message: string; stoppedProcesses?: string[] }
>('POST', 'stop/:playgroundId', {
  baseUrl: '/api/pm2-preview/',
});

// 🔧 新增：获取PM2管理的自定义预览进程状态
export const getCustomPreviewStatus = buildApi<
  { playgroundId: string },
  {
    success: boolean;
    message?: string;
    processes: Array<{
      name: string;
      status: string;
      cpu: string;
      memory: string;
      uptime: string;
      pid?: number;
    }>;
  }
>('GET', 'status/:playgroundId', {
  baseUrl: '/api/pm2-preview/',
});

// ========================= 项目预览相关API =========================

// 项目预览接口类型定义
export interface ProjectPreviewRequest {
  user: string;
}

export interface ProjectPreviewResult {
  success: boolean;
  message: string;
  previewBranch?: string;
  previewBranchUrl?: string;
  forkUrl?: string;
  forkProjectUrl?: string;
  // webideUrl?: string;
  pm2PreviewUrl?: string;
  originalRepoUrl?: string;
  originalRepoBranchUrl?: string;
  pageGenerations?: PageGenerationInfo[];
  mergeResults?: Array<{
    pageId: string;
    pageName: string;
    sourceBranch: string;
    sourceBranchUrl?: string;
    success: boolean;
    error?: string;
  }>;
}

export interface PageGenerationInfo {
  pageId: string;
  pageName: string;
  latestWorkflow: any;
  playgroundId?: string;
  branchName?: string;
}

export interface ProjectCodeGenerationHistory {
  project: {
    id: string;
    name: string;
    gitUrl?: string;
    gitBranch?: string;
  };
  pageGenerations: PageGenerationInfo[];
}

export interface ProjectPreviewResult {
  success: boolean;
  message: string;
  previewBranch?: string;
  previewBranchUrl?: string;
  forkUrl?: string;
  forkProjectUrl?: string;
  // webideUrl?: string;
  pm2PreviewUrl?: string;
  pageGenerations?: PageGenerationInfo[];
  mergeResults?: Array<{
    pageId: string;
    pageName: string;
    sourceBranch: string;
    sourceBranchUrl?: string;
    success: boolean;
    error?: string;
  }>;
}

// 创建项目预览
export const createProjectPreview = buildApi<{ projectId: string } & ProjectPreviewRequest, ProjectPreviewResult>(
  'POST',
  ':projectId/preview',
  {
    baseUrl: '/api/design-project/',
  },
);

// 查询项目代码生成历史
export const getProjectCodeGenerationHistory = buildApi<{ projectId: string }, ProjectCodeGenerationHistory>(
  'GET',
  ':projectId/code-generation-history',
  {
    baseUrl: '/api/design-project/',
  },
);

// ========================= 提示词管理相关API =========================

// 提示词模板概览
export interface PromptTemplateSummary {
  taskType: string;
  taskName: string;
  hasCustomMain: boolean;
  hasCustomCheck: boolean;
  hasCustomTarget?: boolean; // 仅spec-to-prod-code任务有
  mainTemplateId?: string;
  checkTemplateId?: string;
  targetTemplateId?: string; // 仅spec-to-prod-code任务有
  mainUpdated?: string;
  checkUpdated?: string;
  targetUpdated?: string; // 仅spec-to-prod-code任务有
}

// 项目提示词模板
export interface ProjectPromptTemplate {
  id: string;
  projectId: string;
  taskType: string;
  promptType: 'main' | 'check' | 'target';
  content: string;
  version: number;
  status: string;
  created: string;
  updated: string;
  createdBy: string;
  updatedBy?: string;
}

// 提示词内容响应
export interface PromptContentResponse {
  content: string | null;
  isCustom: boolean;
  template?: ProjectPromptTemplate;
}

// 创建/更新提示词请求
export interface CreatePromptRequest {
  taskType: string;
  promptType: 'main' | 'check' | 'target';
  content: string;
}

// Target提示词请求
export interface TargetPromptRequest {
  content: string;
}

// 默认提示词响应
export interface DefaultPromptResponse {
  taskType: string;
  promptType: 'main' | 'check' | 'target';
  content: string | null;
}

// 获取项目提示词模板概览
export const getPromptTemplateSummary = buildApi<
  { projectId: string; user?: string },
  PromptTemplateSummary[]
>('GET', 'projects/:projectId/summary', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 获取项目的所有提示词模板
export const getProjectPromptTemplates = buildApi<
  { projectId: string; user?: string },
  ProjectPromptTemplate[]
>('GET', 'projects/:projectId/templates', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 获取指定任务和类型的提示词内容
export const getPromptContent = buildApi<
  { projectId: string; taskType: string; promptType: 'main' | 'check'; user?: string },
  PromptContentResponse
>('GET', 'projects/:projectId/templates/:taskType/:promptType', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 创建或更新提示词模板
export const createPromptTemplate = buildApi<
  { projectId: string; user?: string } & CreatePromptRequest,
  ProjectPromptTemplate
>('POST', 'projects/:projectId/templates', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 更新提示词模板
export const updatePromptTemplate = buildApi<
  { id: string; user?: string; content: string },
  ProjectPromptTemplate
>('PUT', 'templates/:id', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 删除提示词模板
export const deletePromptTemplate = buildApi<
  { id: string },
  void
>('DELETE', 'templates/:id', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 重置为默认提示词
export const resetPromptToDefault = buildApi<
  { projectId: string; taskType: string; promptType: 'main' | 'check'; user?: string },
  void
>('POST', 'projects/:projectId/templates/:taskType/:promptType/reset', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 获取默认提示词内容
export const getDefaultPromptContent = buildApi<
  { taskType: string; promptType: 'main' | 'check' | 'target' },
  DefaultPromptResponse
>('GET', 'defaults/:taskType/:promptType', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 获取生效的提示词内容
export const getEffectivePromptContent = buildApi<
  { projectId: string; taskType: string; promptType: 'main' | 'check'; user?: string },
  { content: string; isCustom: boolean }
>('GET', 'projects/:projectId/effective/:taskType/:promptType', {
  baseUrl: '/api/design-project/prompt-management/',
});

// ============= Target提示词专用API（仅spec-to-prod-code任务） =============

// 获取target提示词内容
export const getTargetPromptContent = buildApi<
  { projectId: string; taskType: string; user?: string },
  PromptContentResponse
>('GET', 'projects/:projectId/templates/:taskType/target', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 更新target提示词
export const updateTargetPrompt = buildApi<
  { projectId: string; taskType: string; user?: string } & TargetPromptRequest,
  { message: string }
>('POST', 'projects/:projectId/templates/:taskType/target', {
  baseUrl: '/api/design-project/prompt-management/',
});

// 重置target提示词为默认
export const resetTargetPromptToDefault = buildApi<
  { projectId: string; taskType: string; user?: string },
  void
>('POST', 'projects/:projectId/templates/:taskType/target/reset', {
  baseUrl: '/api/design-project/prompt-management/',
});
