{"$schema": "https://json.schemastore.org/eslintrc.json", "root": true, "env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:prettier/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended"], "plugins": ["react", "unused-imports", "import", "@typescript-eslint", "jsx-a11y", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "settings": {"react": {"version": "detect"}}, "rules": {"prettier/prettier": "off", "no-console": "warn", "react/prop-types": "off", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/exhaustive-deps": "off", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/interactive-supports-focus": "warn", "jsx-a11y/label-has-associated-control": "off", "linebreak-style": "off", "no-unused-vars": "off", "unused-imports/no-unused-vars": "off", "unused-imports/no-unused-imports": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"args": "after-used", "ignoreRestSiblings": false, "argsIgnorePattern": "^_.*?$", "varsIgnorePattern": "^_.*?$"}], "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-types": "off", "import/order": ["warn", {"groups": ["type", "builtin", "object", "external", "internal", "parent", "sibling", "index"], "pathGroups": [{"pattern": "~/**", "group": "external", "position": "after"}], "newlines-between": "never", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "jsx-a11y/iframe-has-title": "off", "jsx-a11y/no-autofocus": "off", "prefer-const": "warn", "react/self-closing-comp": "warn", "react/jsx-sort-props": ["warn", {"callbacksLast": true, "shorthandFirst": true, "noSortAlphabetically": false, "reservedFirst": true}], "padding-line-between-statements": ["warn", {"blankLine": "always", "prev": "*", "next": "return"}, {"blankLine": "always", "prev": ["const", "let", "var"], "next": "*"}, {"blankLine": "any", "prev": ["const", "let", "var"], "next": ["const", "let", "var"]}], "no-unsafe-optional-chaining": "off", "@typescript-eslint/ban-ts-comment": "off", "no-var": "off", "no-useless-escape": "off", "no-inner-declarations": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/alt-text": "off", "react/jsx-key": "off", "react-hooks/rules-of-hooks": "off", "react/no-unescaped-entities": "off", "react/display-name": "off"}}