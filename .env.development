DATABASE_URL=********************************************************************************************
WEBIDE_URL=http://webide2-sit.web.htsc/start/webide-api-server/static/start-page/index.html
DEPLOY_URL=http://*************/analyzer/file/upload/minio
HOME_DIR=~/.ai-coding/playground
TEMP_DIR=~/.ai-coding/tmp
WEBIDE_DIR=~/.ai-coding/webide
USE_AGENT=0
PORT=8080
PM2_PREVIEW_PORT=8081
####### prod ########
# DEPLOY_URL=http://codebox.htsc.com.cn/analyzer/file/upload/minio
# OpenRouter API配置
OPENROUTER_API_KEY=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

# SiliconFlow API配置
SILICONFLOW_API_KEY=xxx

# GitLab tokens - 支持多环境
GITLAB1_TOKEN=UzL18-LuUqxpm1X7HKK4
GITLAB2_TOKEN=your_gitlab2_token_here

# GitLab 目标group配置 - 用于fork仓库
# Gitlab1和gitlab2测试环境待配置
GITLAB1_TARGET_GROUP=frontend/platform/d2c-apps 
GITLAB2_TARGET_GROUP=htsc-web/platform/d2c-apps
GITLAB1_TEST_TARGET_GROUP=codebox-test/d2c-apps
GITLAB2_TEST_TARGET_GROUP=codebox-test/d2c-apps

GITLAB1_JIRA_ID=WEBDP-599
**********************599
GITLAB1_TEST_JIRA_ID=WEBPRO-3
GITLAB1_TEST_JIRA_ID=WEBPRO-3


BUNDLE_ANALYZER_URL=http://*************/analyzer/auth/api/v1
PM2_PREVIEW_SERVER_URL=http://localhost:8081