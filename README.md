<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>
<p align="center">华泰人的AI编码平台</p>

# 项目介绍

一个基于LLM的AI编码平台。

## 代码结构

- 后端：nestjs，在根目录
- 前端：vite + react + zustand + heroui + 少量antd，在`frontend`目录

## 项目启动

1. 启动后台
```bash
$ yarn && yarn start:dev
```

2. 启动前端
```bash
$ cd frontend && yarn && yarn dev
```

3. 访问 http://localhost:5173

## 项目配置

`.env.development`中是测试环境的配置。
本地开发的时候可以新建`.env.local`文件配置本地开发环境的配置，这个文件不会被git提交。

前端相关的配置放在`frontend/config`目录

# 如何贡献

到[飞书TODO文档](https://xcg1a1l6ku.feishu.cn/wiki/SuDHwfPnYi4nmAkRXqhcjNqjnBb)中，找到一些你感兴趣的任务。
