import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PosterService } from '../src/poster/poster.service';
import * as fs from 'fs';


const testPage = 'https://research.htsc.com/3g/web_inst/lowcode/index.html?appId=5bdbfe49380073e06b50&utm_source=71&lang=1&colorstyle=1&large_version=0&appedition=normal%E9%A1%B5%E9%9D%A2%E5%8F%AF%E8%A7%81%E6%80%A7--'

describe('PosterController (e2e)', () => {
  let app: INestApplication;
  let posterService: PosterService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(PosterService)
      .useValue({
        takeScreenshot: jest.fn().mockImplementation((url, selector, width, height) => {
          const filePath = '/tmp/test.png';
          // 确保测试文件存在
          if (!fs.existsSync('/tmp')) {
            fs.mkdirSync('/tmp');
          }
          fs.writeFileSync(filePath, 'test');
          return Promise.resolve({ filePath });
        })
      })
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();

    posterService = moduleFixture.get<PosterService>(PosterService);
  });

  afterEach(async () => {
    await app.close();
  });

  it('/poster/screenshot (POST) - 成功截图', () => {
    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: testPage
      })
      .expect(200)
      .expect('Content-Type', /image/);
  });

  it('/poster/screenshot (POST) - 使用选择器', () => {
    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: testPage,
        selector: '#root'
      })
      .expect(200);
  });

  it('/poster/screenshot (POST) - 使用固定尺寸', () => {
    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: testPage,
        width: 800,
        height: 600
      })
      .expect(200);
  });

  it('/poster/screenshot (POST) - URL验证失败', () => {
    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: 'invalid-url'
      })
      .expect(400);
  });

  it('/poster/screenshot (POST) - 宽度验证失败', () => {
    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: testPage,
        width: -1
      })
      .expect(400);
  });

  it('/poster/screenshot (POST) - 处理服务错误', () => {
    jest.spyOn(posterService, 'takeScreenshot').mockRejectedValueOnce(new Error('截图失败'));

    return request(app.getHttpServer())
      .post('/poster/screenshot')
      .send({
        url: testPage
      })
      .expect(500)
      .expect(res => {
        expect(res.body).toHaveProperty('code', '-1');
        expect(res.body).toHaveProperty('msg', 'fail');
        expect(res.body).toHaveProperty('detail', '截图失败');
      });
  });
}); 