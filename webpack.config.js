const webpack = require('webpack');

module.exports = function (options) {

  if (options.module && options.module.rules) {
    options.module.rules.push({
      test: /\.d\.ts$/,
      loader: 'ignore-loader',
    });
  }

  if (options.plugins) {
    options.plugins.push(
      new webpack.DefinePlugin({
        'process.env.CSS_TRANSFORMER_WASM': false,
      }),
    );
  }

  return {
    ...options,
    externals: [
      ...options.externals || [],
      'sharp'
    ],
  };
};
