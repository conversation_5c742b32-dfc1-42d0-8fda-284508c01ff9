import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { open } from 'open';

const server = new McpServer({
  name: "Demo",
  version: "1.0.0"
});

server.tool("create ",
  { initialMsg: z.string(), },
  async ({ initialMsg }) => {
    open(`http://ai-coding.fe.htsc/start?desc=${encodeURIComponent(initialMsg)}`);
    return 'nice';
  }
);

const transport = new StdioServerTransport();
await server.connect(transport);
