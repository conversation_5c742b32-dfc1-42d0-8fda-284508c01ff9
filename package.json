{"name": "ai-coding", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build --builder webpack", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "dev": "yarn start:dev", "start": "cross-env NODE_ENV=development nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prebuild": "prisma generate", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky install && node ./.husky/prepare.js"}, "dependencies": {"@ai-sdk/deepseek": "^0.1.15", "@ai-sdk/openai-compatible": "^0.1.15", "@ai-sdk/ui-utils": "^1.1.19", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.0.6", "@openrouter/ai-sdk-provider": "^0.4.3", "@prisma/client": "^6.4.1", "@types/mime": "^2.0.3", "ai": "^4.1.61", "async": "^3.2.6", "axios": "^1.8.4", "chokidar": "^4.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cross-env": "^7.0.3", "dedent": "^1.5.3", "dotenv": "^16.4.7", "flatted": "^3.3.3", "form-data": "^4.0.2", "formdata-node": "^6.0.3", "glob": "^11.0.2", "html-minifier-terser": "^7.2.0", "https-proxy-agent": "^7.0.6", "jsdom": "^26.0.0", "juice": "^11.0.1", "lightningcss": "^1.21.0", "lodash": "^4.17.21", "marked": "^15.0.7", "mime": "^2.6.0", "minimatch": "^10.0.3", "moment": "^2.30.1", "nanoid-cjs": "^0.0.7", "nest-winston": "^1.10.2", "nunjucks": "^3.2.4", "prisma": "^6.4.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sharp": "^0.34.3", "shelljs": "^0.10.0", "undici": "^6.21.2", "vite": "^6.2.0", "winston": "^3.17.0", "xmldom": "^0.6.0", "xpath": "^0.0.34", "yauzl": "^3.2.0", "yazl": "^3.3.1", "zod": "^3.24.2", "zx": "^8.4.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/chokidar": "^2.1.7", "@types/express": "^5.0.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/xmldom": "^0.1.34", "@types/yauzl": "^2.10.3", "@types/yazl": "^2.4.6", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^7.0.4", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/"]}, "packageManager": "yarn@1.22.22"}