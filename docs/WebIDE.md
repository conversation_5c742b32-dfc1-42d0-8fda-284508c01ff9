# Storage
ai-coding's persistent storage is mounted on /workspace/data A shared NAS with webide is mounted on /webide.

# Sync

1. ai-coding to webide
When user clicks "WebIDE打开", and initial checksum baseed rsync will be triggered to sync data from ai-coding to webide.

2. webide back to ai-coding
When app loads, /webide directory will be added to watch directory using chokidar. Changes will be synced back to ai-coding.
