import { Logger } from '@nestjs/common';

/**
 * GitLab环境枚举
 */
export enum EGitlabEnv {
  GITLAB1 = 'gitlab',
  GITLAB2 = 'gitlab2',
  GITLAB1_TEST = 'gitlab-test',
  GITLAB2_TEST = 'gitlab2-test',
}

/**
 * GitLab Token接口
 */
export interface IGitlabTokens {
  [EGitlabEnv.GITLAB1]?: string;
  [EGitlabEnv.GITLAB2]?: string;
  [EGitlabEnv.GITLAB1_TEST]?: string;
  [EGitlabEnv.GITLAB2_TEST]?: string;
}

/**
 * 不同git环境对应的gitDomains
 */
export const GITLAB_DOMAINS = {
  [EGitlabEnv.GITLAB1]: 'gitlab.htzq.htsc.com.cn',
  [EGitlabEnv.GITLAB2]: 'gitlab2.htsc',
  [EGitlabEnv.GITLAB1_TEST]: '168.61.114.20',
  [EGitlabEnv.GITLAB2_TEST]: '168.63.66.36',
};

/**
 * gitDomain与git环境的对应关系
 */
export const GITLAB_DOMAIN_ENVS = {
  [GITLAB_DOMAINS[EGitlabEnv.GITLAB1]]: EGitlabEnv.GITLAB1,
  [GITLAB_DOMAINS[EGitlabEnv.GITLAB2]]: EGitlabEnv.GITLAB2,
  [GITLAB_DOMAINS[EGitlabEnv.GITLAB1_TEST]]: EGitlabEnv.GITLAB1_TEST,
  [GITLAB_DOMAINS[EGitlabEnv.GITLAB2_TEST]]: EGitlabEnv.GITLAB2_TEST,
};

/**
 * 从统一仓库地址中提取信息
 */
export function getInfoFromUnifiedRepo(repo: string, noEncode = true): [string, string] {
  const gitSshPath = getSshGitPath(repo);
  const matched = /^git@(.*):(.*).git$/.exec(gitSshPath);
  if (matched && matched.length === 3) {
    return [matched[1], noEncode ? matched[2] : encodeURIComponent(matched[2])];
  }
  return ['', ''];
}

/**
 * 获取SSH Git路径
 */
function getSshGitPath(repo: string): string {
  // 如果已经是SSH格式，直接返回
  if (repo.startsWith('git@')) {
    return repo;
  }
  
  // 如果是http格式，转换为SSH格式
  if (repo.startsWith('http://')) {
    const url = new URL(repo);
    const hostname = url.hostname;
    const pathname = url.pathname.replace(/^\//, ''); // 移除开头的斜杠
    return `git@${hostname}:${pathname}`;
  }
  
  return repo;
}

/**
 * GitLab工具类
 */
export class GitlabUtil {
  private static readonly logger = new Logger(GitlabUtil.name);
  private static tokens: IGitlabTokens = {};

  /**
   * 初始化GitLab tokens
   */
  static initialize(): void {
    this.tokens = {
      [EGitlabEnv.GITLAB1]: process.env.GITLAB1_TOKEN,
      [EGitlabEnv.GITLAB2]: process.env.GITLAB2_TOKEN,
      [EGitlabEnv.GITLAB1_TEST]: process.env.GITLAB1_TOKEN, // 测试环境使用相同token
      [EGitlabEnv.GITLAB2_TEST]: process.env.GITLAB2_TOKEN, // 测试环境使用相同token
    };
    
    this.logger.log('GitLab tokens initialized');
  }

  /**
   * 根据Git URL识别GitLab环境
   */
  static getGitlabEnv(gitUrl: string): EGitlabEnv | null {
    const [gitDomain] = getInfoFromUnifiedRepo(gitUrl);
    
    if (!gitDomain) {
      this.logger.warn(`无法从Git URL中提取域名: ${gitUrl}`);
      return null;
    }
    
    const env = GITLAB_DOMAIN_ENVS[gitDomain];
    if (!env) {
      this.logger.warn(`未找到域名 ${gitDomain} 对应的GitLab环境`);
      return null;
    }
    
    return env;
  }

  /**
   * 获取指定环境的token
   */
  static getToken(env: EGitlabEnv): string | null {
    const token = this.tokens[env];
    if (!token) {
      this.logger.warn(`未找到环境 ${env} 的token`);
      return null;
    }
    return token;
  }

  /**
   * 根据Git URL获取对应的token
   */
  static getTokenByUrl(gitUrl: string): string | null {
    const env = this.getGitlabEnv(gitUrl);
    if (!env) {
      return null;
    }
    return this.getToken(env);
  }

  /**
   * 将SSH格式的Git URL转换为带token的http URL
   */
  static convertTohttpWithToken(gitUrl: string): string | null {
    const [gitDomain, gitProject] = getInfoFromUnifiedRepo(gitUrl);
    
    if (!gitDomain || !gitProject) {
      this.logger.error(`无法解析Git URL: ${gitUrl}`);
      return null;
    }
    
    const env = GITLAB_DOMAIN_ENVS[gitDomain];
    if (!env) {
      this.logger.error(`未找到域名 ${gitDomain} 对应的GitLab环境`);
      return null;
    }
    
    const token = this.getToken(env);
    if (!token) {
      this.logger.error(`未找到环境 ${env} 的token`);
      return null;
    }
    
    // 构建带token的http URL
    const httpUrl = `http://gitlab-ci-token:${token}@${gitDomain}/${gitProject}.git`;
    
    this.logger.log(`转换Git URL: ${gitUrl} -> ${httpUrl.replace(token, '***')}`);
    
    return httpUrl;
  }

  /**
   * 获取用于推送的Git配置
   */
  static getGitPushConfig(gitUrl: string): { url: string; token: string } | null {
    const httpUrl = this.convertTohttpWithToken(gitUrl);
    const token = this.getTokenByUrl(gitUrl);
    
    if (!httpUrl || !token) {
      return null;
    }
    
    return {
      url: httpUrl,
      token
    };
  }

  /**
   * 验证Git URL是否支持
   */
  static isSupported(gitUrl: string): boolean {
    const env = this.getGitlabEnv(gitUrl);
    if (!env) {
      return false;
    }
    
    const token = this.getToken(env);
    return !!token;
  }

  /**
   * 获取token对应的用户信息
   */
  static async getUserInfo(gitlabEnv: EGitlabEnv, gitDomain?: string): Promise<{ username: string; email: string; name: string } | null> {
    try {
      const token = this.getToken(gitlabEnv);
      if (!token) {
        this.logger.error(`未找到环境 ${gitlabEnv} 的token`);
        return null;
      }

      const domain = gitDomain || GITLAB_DOMAINS[gitlabEnv];
      const apiUrl = `http://${domain}/api/v4/user`;

      this.logger.log(`获取用户信息: ${apiUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 10000
      });

      const userInfo = response.data;
      
      return {
        username: userInfo.username || userInfo.name || 'unknown',
        email: userInfo.email || '<EMAIL>',
        name: userInfo.name || userInfo.username || 'GitLab User'
      };

    } catch (error) {
      this.logger.error(`获取用户信息失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 根据Git URL获取用户信息
   */
  static async getUserInfoByUrl(gitUrl: string): Promise<{ username: string; email: string; name: string } | null> {
    const env = this.getGitlabEnv(gitUrl);
    if (!env) {
      return null;
    }

    const [gitDomain] = getInfoFromUnifiedRepo(gitUrl);
    return await this.getUserInfo(env, gitDomain);
  }

  // ==================== 通用Git操作方法 ====================

  /**
   * 解析Git URL，提取项目信息
   */
  static parseGitUrl(gitUrl: string): {
    gitDomain: string;
    namespace: string;
    projectName: string;
    gitlabEnv: string;
  } | null {
    try {
      const [gitDomain, fullPath] = getInfoFromUnifiedRepo(gitUrl);

      if (!gitDomain || !fullPath) {
        return null;
      }

      // 从fullPath中提取namespace和projectName
      const pathParts = fullPath.split('/');
      if (pathParts.length < 2) {
        return null;
      }

      const projectName = pathParts[pathParts.length - 1];
      const namespace = pathParts.slice(0, -1).join('/');

      // 获取GitLab环境
      const gitlabEnv = this.getGitlabEnv(gitUrl);
      if (!gitlabEnv) {
        return null;
      }

      return {
        gitDomain,
        namespace,
        projectName,
        gitlabEnv
      };
    } catch (error) {
      this.logger.error(`解析Git URL失败: ${gitUrl}`, error);
      return null;
    }
  }

  /**
   * 根据GitLab环境获取目标group
   */
  static getTargetGroupForEnv(gitlabEnv: string): string {
    const targetGroups = {
      'gitlab': process.env.GITLAB1_TARGET_GROUP || 'frontend/platform/d2c-apps',
      'gitlab2': process.env.GITLAB2_TARGET_GROUP || 'htsc-web/platform/d2c-apps',
      'gitlab-test': process.env.GITLAB1_TEST_TARGET_GROUP || 'codebox-test/d2c-apps',
      'gitlab2-test': process.env.GITLAB2_TEST_TARGET_GROUP || 'codebox-test/d2c-apps',
    };

    return targetGroups[gitlabEnv];
  }

  /**
   * 构建Git URL
   */
  static buildGitUrl(gitDomain: string, namespace: string, projectName: string): string {
    return `git@${gitDomain}:${namespace}/${projectName}.git`;
  }

  /**
   * 获取GitLab项目ID
   */
  static async getProjectId(gitlabEnv: string, namespace: string, projectName: string): Promise<number | null> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const projectPath = `${namespace}/${projectName}`;
      const apiUrl = `http://${gitDomain}/api/v4/projects/${encodeURIComponent(projectPath)}`;

      this.logger.log(`获取项目ID: ${apiUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 10000
      });

      return response.data.id;
    } catch (error) {
      this.logger.error(`获取项目ID失败: ${namespace}/${projectName}`, error.message);
      return null;
    }
  }

  /**
   * 检查目标group下是否已存在对应的fork
   */
  static async checkExistingFork(gitlabEnv: string, targetGroup: string, projectName: string): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${encodeURIComponent(targetGroup + '/' + projectName)}`;

      this.logger.log(`检查已存在的fork: ${apiUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 10000
      });

      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        this.logger.log(`目标group下不存在fork: ${targetGroup}/${projectName}`);
        return null;
      }
      this.logger.warn(`检查已存在fork时出错:`, error.message);
      return null;
    }
  }

  /**
   * 创建fork
   */
  static async createFork(gitlabEnv: string, projectId: number, targetGroup: string): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/fork`;

      this.logger.log(`创建fork: ${apiUrl}, 目标group: ${targetGroup}`);

      const axios = await import('axios');
      const response = await axios.default.post(apiUrl, {
        namespace: targetGroup
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // fork操作可能需要更长时间
      });

      return response.data;
    } catch (error) {
      this.logger.error(`创建fork失败: 项目ID=${projectId}, 目标group=${targetGroup}`, error.message);
      throw error;
    }
  }

  /**
   * 将仓库fork到目标group
   */
  static async forkRepositoryToTargetGroup(originalGitUrl: string): Promise<string> {
    // 步骤1: 解析原始Git URL，提取项目信息
    const projectInfo = this.parseGitUrl(originalGitUrl);
    if (!projectInfo) {
      throw new Error(`无法解析Git URL: ${originalGitUrl}`);
    }

    const { gitDomain, namespace, projectName, gitlabEnv } = projectInfo;
    this.logger.log(`解析项目信息: domain=${gitDomain}, namespace=${namespace}, project=${projectName}, env=${gitlabEnv}`);

    // 步骤2: 获取目标group配置
    const targetGroup = this.getTargetGroupForEnv(gitlabEnv);
    this.logger.log(`目标group: ${targetGroup}`);

    // 步骤3: 检查目标group下是否已存在对应的fork
    const existingFork = await this.checkExistingFork(gitlabEnv, targetGroup, projectName);
    if (existingFork) {
      this.logger.log(`发现已存在的fork: ${existingFork.web_url}`);
      return this.buildGitUrl(gitDomain, targetGroup, projectName);
    }

    // 步骤4: 如果不存在，调用GitLab API创建fork
    const originalProjectId = await this.getProjectId(gitlabEnv, namespace, projectName);
    if (!originalProjectId) {
      throw new Error(`无法找到原始项目: ${namespace}/${projectName}`);
    }

    this.logger.log(`开始创建fork: 项目ID=${originalProjectId}, 目标group=${targetGroup}`);
    const forkedProject = await this.createFork(gitlabEnv, originalProjectId, targetGroup);

    this.logger.log(`Fork创建成功: ${forkedProject.web_url}`);

    // 步骤5: 返回fork后的仓库地址
    return this.buildGitUrl(gitDomain, targetGroup, projectName);
  }

  /**
   * 检查分支是否存在
   */
  static async checkBranchExists(gitlabEnv: string, namespace: string, projectName: string, branchName: string): Promise<boolean> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return false;
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        return false;
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/branches/${encodeURIComponent(branchName)}`;

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 10000
      });

      return response.status === 200;
    } catch (error) {
      if (error.response?.status === 404) {
        return false;
      }
      this.logger.warn(`检查分支存在性时出错: ${branchName}`, error.message);
      return false;
    }
  }

  /**
   * 查找Fork仓库中匹配模式的分支
   */
  static async findFeatureBranchInForkRepo(
    gitlabEnv: string, 
    namespace: string, 
    projectName: string, 
    pageName: string,
    workflowId?: string
  ): Promise<string | null> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        this.logger.error(`未找到环境 ${gitlabEnv} 的token`);
        return null;
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        this.logger.error(`无法获取fork仓库项目ID: ${namespace}/${projectName}`);
        return null;
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const branchesUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/branches?per_page=100`;
      
      this.logger.log(`请求分支列表: ${branchesUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(branchesUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      const branches = response.data;
      if (!branches || !Array.isArray(branches)) {
        this.logger.error(`获取fork仓库分支列表失败或为空`);
        return null;
      }

      this.logger.log(`fork仓库共有 ${branches.length} 个分支`);

      // 查找匹配的feature分支，分支命名模式：feature/${pageName}-${version}-${taskId}
      const possibleBranches = branches.filter(branch => {
        const branchName = branch.name;
        
        if (!branchName.startsWith('feature/')) {
          return false;
        }
        
        const nameWithoutPrefix = branchName.substring(8);
        
        if (!nameWithoutPrefix.startsWith(`${pageName}-`)) {
          return false;
        }
        
        const versionPattern = /-version-\d+-/;
        if (!versionPattern.test(nameWithoutPrefix)) {
          return false;
        }
        
        return true;
      });

      this.logger.log(`找到 ${possibleBranches.length} 个匹配的分支`);

      if (possibleBranches.length === 0) {
        this.logger.warn(`未找到页面 ${pageName} 的feature分支`);
        return null;
      }

      // 如果提供了workflowId，优先精确匹配包含该workflowId的分支
      if (workflowId) {
        const exactMatchBranches = possibleBranches.filter(branch => {
          const branchName = branch.name;
          // 分支命名模式：feature/${pageName}-version-${version}-${workflowId}
          return branchName.endsWith(`-${workflowId}`);
        });

        if (exactMatchBranches.length > 0) {
          // 如果有精确匹配的分支，选择最新的一个
          const latestExactMatch = exactMatchBranches.sort((a, b) => {
            const timeA = new Date(a.commit.committed_date).getTime();
            const timeB = new Date(b.commit.committed_date).getTime();
            return timeB - timeA; // 降序，最新的在前
          })[0];

          this.logger.log(`找到页面 ${pageName} 的精确匹配分支 (工作流ID: ${workflowId}): ${latestExactMatch.name}`);
          return latestExactMatch.name;
        } else {
          this.logger.warn(`未找到页面 ${pageName} 包含工作流ID ${workflowId} 的分支，将选择最新的分支`);
        }
      }

      // 如果没有提供workflowId或者没有精确匹配，选择最新的（基于最后提交时间）
      const latestBranch = possibleBranches.sort((a, b) => {
        const timeA = new Date(a.commit.committed_date).getTime();
        const timeB = new Date(b.commit.committed_date).getTime();
        return timeB - timeA; // 降序，最新的在前
      })[0];

      this.logger.log(`找到页面 ${pageName} 的feature分支: ${latestBranch.name}`);
      
      return latestBranch.name;

    } catch (error) {
      this.logger.error(`查找feature分支失败: ${pageName}`, error);
      return null;
    }
  }

  /**
   * 获取分支的最后提交时间
   */
  static async getBranchLastCommitTime(gitlabEnv: string, namespace: string, projectName: string, branchName: string): Promise<Date | null> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return null;
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        return null;
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/commits?ref_name=${encodeURIComponent(branchName)}&per_page=1`;

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      if (response.data && response.data.length > 0) {
        return new Date(response.data[0].committed_date);
      }

      return null;
    } catch (error) {
      this.logger.warn(`获取分支最后提交时间失败: ${branchName}`, error.message);
      return null;
    }
  }

  /**
   * 生成GitLab项目的Web URL
   */
  static generateProjectUrl(gitlabEnv: string, namespace: string, projectName: string): string {
    const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
    return `http://${gitDomain}/${namespace}/${projectName}`;
  }

  /**
   * 生成GitLab分支的Web URL
   */
  static generateBranchUrl(gitlabEnv: string, namespace: string, projectName: string, branchName: string): string {
    const projectUrl = this.generateProjectUrl(gitlabEnv, namespace, projectName);
    return `${projectUrl}/-/tree/${encodeURIComponent(branchName)}`;
  }

  /**
   * 生成GitLab MR列表的Web URL
   */
  static generateMergeRequestsUrl(gitlabEnv: string, namespace: string, projectName: string): string {
    const projectUrl = this.generateProjectUrl(gitlabEnv, namespace, projectName);
    return `${projectUrl}/-/merge_requests`;
  }

  /**
   * 检查是否已存在指定的MR
   */
  static async checkExistingMergeRequest(
    gitlabEnv: string, 
    namespace: string, 
    projectName: string, 
    sourceBranch: string, 
    targetBranch: string
  ): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return null;
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        return null;
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests?source_branch=${encodeURIComponent(sourceBranch)}&target_branch=${encodeURIComponent(targetBranch)}&state=opened`;

      this.logger.log(`检查已存在的MR: ${sourceBranch} -> ${targetBranch}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      if (response.data && response.data.length > 0) {
        this.logger.log(`找到已存在的MR: ${response.data[0].web_url}`);
        return response.data[0];
      }

      return null;
    } catch (error) {
      this.logger.warn(`检查MR时出错: ${sourceBranch} -> ${targetBranch}`, error.message);
      return null;
    }
  }

  /**
   * 创建或获取现有MR并合并
   */
  static async createOrMergeMR(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    sourceBranch: string,
    targetBranch: string,
    title: string,
    description: string
  ): Promise<any> {
    try {
      // 1. 检查是否已存在MR
      let mr = await this.checkExistingMergeRequest(gitlabEnv, namespace, projectName, sourceBranch, targetBranch);
      
      if (!mr) {
        // 2. 如果不存在，创建新的MR
        this.logger.log(`创建新的MR: ${sourceBranch} -> ${targetBranch}`);
        mr = await this.createMergeRequest(gitlabEnv, namespace, projectName, sourceBranch, targetBranch, title, description);
      } else {
        this.logger.log(`使用已存在的MR: ${mr.web_url}`);
      }

      // 3. 如果MR状态是opened，尝试合并
      if (mr && mr.state === 'opened') {
        this.logger.log(`合并MR: ${mr.web_url}`);
        await this.acceptMergeRequest(gitlabEnv, namespace, projectName, mr.iid, `Merge ${sourceBranch} into ${targetBranch}`);
      }

      return mr;
    } catch (error) {
      this.logger.error(`创建或合并MR失败: ${sourceBranch} -> ${targetBranch}`, error.message);
      throw error;
    }
  }

  /**
   * 创建MR
   */
  static async createMergeRequest(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    sourceBranch: string,
    targetBranch: string,
    title: string,
    description: string
  ): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        throw new Error(`无法获取项目ID: ${namespace}/${projectName}`);
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests`;

      const mrData = {
        source_branch: sourceBranch,
        target_branch: targetBranch,
        title,
        description,
        remove_source_branch: false,
        merge_when_pipeline_succeeds: false,
      };

      const axios = await import('axios');
      const response = await axios.default.post(apiUrl, mrData, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      this.logger.log(`MR创建成功: ${response.data.web_url}`);
      return response.data;
    } catch (error) {
      this.logger.error(`创建MR失败: ${sourceBranch} -> ${targetBranch}`, error.message);
      throw error;
    }
  }

  /**
   * 接受MR
   */
  static async acceptMergeRequest(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    mrIid: number,
    commitMessage: string
  ): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        throw new Error(`无法获取项目ID: ${namespace}/${projectName}`);
      }

      const gitDomain = GITLAB_DOMAINS[gitlabEnv as EGitlabEnv];
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests/${mrIid}/merge`;

      const axios = await import('axios');
      const response = await axios.default.put(apiUrl, {
        merge_commit_message: commitMessage,
        should_remove_source_branch: false,
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      this.logger.log(`MR合并成功: MR #${mrIid}`);
      return response.data;
    } catch (error) {
      this.logger.error(`合并MR失败: MR #${mrIid}`, error.message);
      throw error;
    }
  }

  /**
   * 获取Git域名（基于环境）
   */
  static getGitDomainForEnv(gitlabEnv: string): string {
    return GITLAB_DOMAINS[gitlabEnv as EGitlabEnv] || GITLAB_DOMAINS[EGitlabEnv.GITLAB1];
  }

  /**
   * 创建分支（使用GitLab API）
   */
  static async createBranch(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    newBranchName: string,
    baseBranch: string
  ): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        throw new Error(`无法获取项目ID: ${namespace}/${projectName}`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/branches`;

      this.logger.log(`创建分支: ${newBranchName} (基于 ${baseBranch})`);

      const axios = await import('axios');
      const response = await axios.default.post(apiUrl, {
        branch: newBranchName,
        ref: baseBranch,
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      this.logger.log(`分支创建成功: ${newBranchName}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
        this.logger.log(`分支已存在: ${newBranchName}`);
        return null; // 分支已存在，返回null表示成功但无需创建
      }
      this.logger.error(`创建分支失败: ${newBranchName}`, error.message);
      throw error;
    }
  }

  /**
   * 检查远程分支是否存在
   */
  static async checkRemoteBranchExists(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    branchName: string
  ): Promise<boolean> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return false;
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        return false;
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/branches/${encodeURIComponent(branchName)}`;

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 10000,
      });

      return response.status === 200;
    } catch (error) {
      if (error.response?.status === 404) {
        return false;
      }
      this.logger.warn(`检查远程分支时出错: ${branchName}`, error.message);
      return false;
    }
  }

  /**
   * 查找现有的MR（按分支前缀）
   */
  static async findExistingMergeRequestsByPrefix(
    gitlabEnv: string,
    sourceNamespace: string,
    sourceProjectName: string,
    branchPrefix: string,
    targetNamespace: string,
    targetProjectName: string,
    targetBranch: string
  ): Promise<any[]> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return [];
      }

      const sourceProjectId = await this.getProjectId(gitlabEnv, sourceNamespace, sourceProjectName);
      if (!sourceProjectId) {
        return [];
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${sourceProjectId}/merge_requests?state=opened&per_page=100`;

      this.logger.log(`查找分支前缀的MR: ${branchPrefix}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      const allMRs = response.data || [];
      
      // 过滤出匹配前缀的MR
      const matchingMRs = allMRs.filter((mr: any) => {
        return mr.source_branch.startsWith(branchPrefix) && 
               mr.target_branch === targetBranch;
      });

      this.logger.log(`找到 ${matchingMRs.length} 个匹配前缀的MR`);
      return matchingMRs;
    } catch (error) {
      this.logger.error(`查找MR时出错: ${branchPrefix}`, error.message);
      return [];
    }
  }

  /**
   * 关闭MR
   */
  static async closeMergeRequest(
    gitlabEnv: string,
    namespace: string,
    projectName: string,
    mrIid: number
  ): Promise<any> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const projectId = await this.getProjectId(gitlabEnv, namespace, projectName);
      if (!projectId) {
        throw new Error(`无法获取项目ID: ${namespace}/${projectName}`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests/${mrIid}`;

      this.logger.log(`关闭MR: #${mrIid}`);

      const axios = await import('axios');
      const response = await axios.default.put(apiUrl, {
        state_event: 'close'
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      this.logger.log(`MR关闭成功: #${mrIid}`);
      return response.data;
    } catch (error) {
      this.logger.error(`关闭MR失败: #${mrIid}`, error.message);
      throw error;
    }
  }

  /**
   * 获取分支的最新commit SHA
   */
  static async getBranchCommitSha(
    gitlabEnv: string,
    projectId: number,
    branchName: string
  ): Promise<string | null> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return null;
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}/repository/branches/${encodeURIComponent(branchName)}`;

      this.logger.log(`获取分支commit SHA: ${branchName}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 10000,
      });

      if (response.status === 200 && response.data?.commit?.id) {
        return response.data.commit.id;
      }

      return null;
    } catch (error) {
      if (error.response?.status === 404) {
        this.logger.warn(`分支不存在: ${branchName}`);
        return null;
      }
      this.logger.error(`获取分支commit SHA失败: ${branchName}`, error.message);
      return null;
    }
  }

  /**
   * 获取项目的默认分支
   */
  static async getDefaultBranch(
    gitlabEnv: string,
    projectId: number
  ): Promise<string | null> {
    try {
      const token = this.getToken(gitlabEnv as any);
      if (!token) {
        return null;
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const apiUrl = `http://${gitDomain}/api/v4/projects/${projectId}`;

      this.logger.log(`获取项目默认分支: ${projectId}`);

      const axios = await import('axios');
      const response = await axios.default.get(apiUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 10000,
      });

      if (response.status === 200 && response.data?.default_branch) {
        return response.data.default_branch;
      }

      return null;
    } catch (error) {
      this.logger.error(`获取项目默认分支失败: ${projectId}`, error.message);
      return null;
    }
  }

  /**
   * 从上游仓库同步分支到fork仓库
   * @param originalGitUrl 原始仓库URL
   * @param forkGitUrl fork仓库URL  
   * @param branchName 要同步的分支名称
   * @returns 是否同步成功
   */
  static async syncBranchFromUpstream(
    originalGitUrl: string,
    forkGitUrl: string,
    branchName: string
  ): Promise<boolean> {
    try {
      this.logger.log(`🔄 [BranchSync] 开始从上游仓库同步分支: ${branchName}`);
      this.logger.log(`   原始仓库: ${originalGitUrl}`);
      this.logger.log(`   Fork仓库: ${forkGitUrl}`);

      // 解析原始仓库信息
      const originalInfo = this.parseGitUrl(originalGitUrl);
      if (!originalInfo) {
        throw new Error(`无法解析原始仓库URL: ${originalGitUrl}`);
      }

      // 解析fork仓库信息
      const forkInfo = this.parseGitUrl(forkGitUrl);
      if (!forkInfo) {
        throw new Error(`无法解析fork仓库URL: ${forkGitUrl}`);
      }

      // 检查原始仓库中分支是否存在
      const branchExistsInOriginal = await this.checkRemoteBranchExists(
        originalInfo.gitlabEnv,
        originalInfo.namespace,
        originalInfo.projectName,
        branchName
      );

      if (!branchExistsInOriginal) {
        this.logger.warn(`⚠️ [BranchSync] 原始仓库中不存在分支: ${branchName}`);
        return false;
      }

      this.logger.log(`✅ [BranchSync] 原始仓库中存在分支: ${branchName}`);

      // 检查fork仓库中分支是否已存在
      const branchExistsInFork = await this.checkRemoteBranchExists(
        forkInfo.gitlabEnv,
        forkInfo.namespace,
        forkInfo.projectName,
        branchName
      );

      if (branchExistsInFork) {
        this.logger.log(`✅ [BranchSync] Fork仓库中已存在分支: ${branchName}，无需同步`);
        return true;
      }

      // 从原始仓库获取目标分支的commit SHA
      const originalProjectId = await this.getProjectId(
        originalInfo.gitlabEnv,
        originalInfo.namespace,
        originalInfo.projectName
      );

      if (!originalProjectId) {
        throw new Error(`无法获取原始仓库项目ID: ${originalInfo.namespace}/${originalInfo.projectName}`);
      }

      // 获取原始仓库中目标分支的commit SHA
      const originalBranchCommitSha = await this.getBranchCommitSha(
        originalInfo.gitlabEnv,
        originalProjectId,
        branchName
      );

      if (!originalBranchCommitSha) {
        throw new Error(`无法获取原始仓库分支的commit SHA: ${branchName}`);
      }

      this.logger.log(`🔍 [BranchSync] 原始分支commit SHA: ${originalBranchCommitSha}`);

      // 在fork仓库中创建分支，直接使用原始仓库分支的commit SHA
      // 这样创建的分支内容将与原始仓库的分支完全一致
      const result = await this.createBranch(
        forkInfo.gitlabEnv,
        forkInfo.namespace,
        forkInfo.projectName,
        branchName,
        originalBranchCommitSha // 使用原始仓库分支的commit SHA
      );

      if (result !== null) {
        this.logger.log(`✅ [BranchSync] 分支创建成功: ${branchName}`);
        this.logger.log(`📝 [BranchSync] 注意：新分支基于fork仓库的默认分支，内容可能与原始分支不同`);
        this.logger.log(`💡 [BranchSync] 建议：后续可通过Git命令添加原始仓库作为远程仓库来同步具体内容`);
        return true;
      } else {
        this.logger.log(`✅ [BranchSync] 分支已存在: ${branchName}`);
        return true;
      }

    } catch (error) {
      this.logger.error(`❌ [BranchSync] 分支同步失败: ${branchName}`, error.message);
      return false;
    }
  }

  /**
   * 获取用户信息（根据GitLab环境和Git URL）
   */
  static async configureGitUser(gitUrl: string): Promise<{ username: string; email: string; name: string } | null> {
    try {
      const gitlabEnv = this.getGitlabEnv(gitUrl);
      if (!gitlabEnv) {
        return null;
      }

      const [gitDomain] = getInfoFromUnifiedRepo(gitUrl);
      return await this.getUserInfo(gitlabEnv, gitDomain);
    } catch (error) {
      this.logger.error(`获取Git用户配置失败: ${gitUrl}`, error.message);
      return null;
    }
  }
}