/**
 * 坐标类型定义：[x1, y1, x2, y2]
 */
type Coordinate = [number, number, number, number];

/**
 * 将坐标列表转换为HTML布局结构
 * @param coordinates 坐标列表，每个坐标为 [x1, y1, x2, y2]
 * @param options 配置选项
 * @returns 生成的HTML字符串
 */
export function coordinatesToHtml(coordinates: Coordinate[]): string {
  const containerClass = 'img-code-container';

  // 计算画布尺寸
  const getCanvasSize = () => {
    if (coordinates.length === 0) return { width: 375, height: 800 };

    const maxX = Math.max(...coordinates.map(([, , x2]) => x2));
    const maxY = Math.max(...coordinates.map(([, , , y2]) => y2));

    return { width: maxX, height: maxY };
  };

  const canvasSize = getCanvasSize();

  // 创建网格系统
  const createGridSystem = () => {
    // 收集所有的x和y坐标点
    const xPoints = new Set<number>();
    const yPoints = new Set<number>();

    coordinates.forEach(([x1, y1, x2, y2]) => {
      xPoints.add(x1);
      xPoints.add(x2);
      yPoints.add(y1);
      yPoints.add(y2);
    });

    // 排序坐标点
    const sortedX = Array.from(xPoints).sort((a, b) => a - b);
    const sortedY = Array.from(yPoints).sort((a, b) => a - b);

    return { sortedX, sortedY };
  };

  const { sortedX, sortedY } = createGridSystem();

  // 生成CSS样式
  const generateStyles = () => {
    // 计算网格模板
    const gridTemplateColumns = sortedX
      .slice(1)
      .map((x, i) => `${x - sortedX[i]}px`)
      .join(' ');
    const gridTemplateRows = sortedY
      .slice(1)
      .map((y, i) => `${y - sortedY[i]}px`)
      .join(' ');

    let styles = `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      .${containerClass} {
        display: grid;
        grid-template-columns: ${gridTemplateColumns};
        grid-template-rows: ${gridTemplateRows};
        width: ${canvasSize.width}px;
        height: ${canvasSize.height}px;
      }`;

    // 为每个坐标生成对应的CSS样式
    coordinates.forEach(([x1, y1, x2, y2]) => {
      const className = `container_${x1}_${y1}_${x2}_${y2}`;

      // 计算网格位置
      const colStart = sortedX.indexOf(x1) + 1;
      const colEnd = sortedX.indexOf(x2) + 1;
      const rowStart = sortedY.indexOf(y1) + 1;
      const rowEnd = sortedY.indexOf(y2) + 1;

      const itemStyle = `
        .${className} {
          grid-column: ${colStart} / ${colEnd};
          grid-row: ${rowStart} / ${rowEnd};
        }`;

      styles += itemStyle;
    });

    return styles;
  };

  // 生成div容器
  const generateContainers = () => {
    return coordinates
      .map(([x1, y1, x2, y2], index) => {
        const className = `container_${x1}_${y1}_${x2}_${y2}`;
        return `${index === 0 ? '' : '        '}<div class="${className}"></div>`;
      })
      .join('\n');
  };

  // 生成完整的HTML结构
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
        ${generateStyles()}
        </style>
    </head>
    <body>
        <div class="img-code-container">
          ${generateContainers()}
        </div>
    </body>
    </html>`;

  return html;
}

/**
 * 使用示例

function example() {
  // 示例坐标数据
  const coordinates: Coordinate[] = [
    [0, 0, 2868, 90],
    [0, 92, 432, 1224],
    [1963, 92, 2866, 1222],
    [433, 92, 1958, 1227],
  ];

  console.log('=== 完整HTML示例 ===');
  const fullHtml = coordinatesToHtml(coordinates);
  console.log(fullHtml);
}
 */

// 取消注释下面这行来运行示例
// example();
