// JSON.parse安全转换
export const safeParse = (json, def = {}) => {
  if (!json) return def;
  try {
    return JSON.parse(json);
  } catch (error) {
    return def;
  }
};

// JSON.stringify安全转换
export const safeStringify = (json, def = '{}') => {
  if (!json || typeof json === 'string') return json ?? def;
  try {
    return JSON.stringify(json);
  } catch (error) {
    return def;
  }
};

// 导出GitLab工具类
export * from './gitlab.util';

export * from './layout';
