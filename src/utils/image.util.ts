import * as sharp from 'sharp';

/**
 * 图片无损压缩公共方法
 * @param imageContent base64格式的图片内容（不包含data:image/...;base64,前缀）
 * @returns Promise<string> 压缩后的base64图片内容
 */
export const compressImageLossless = async (imageContent: string): Promise<string> => {
    try {
        // 将base64转换为Buffer
        const inputBuffer = Buffer.from(imageContent, 'base64');

        // 获取图片metadata以检测格式
        const metadata = await sharp(inputBuffer).metadata();
        const format = metadata.format;

        let compressedBuffer: Buffer;

        // 根据图片格式选择合适的无损压缩方法
        switch (format) {
            case 'png':
                compressedBuffer = await sharp(inputBuffer)
                    .png({
                        compressionLevel: 9, // 最高压缩级别
                        progressive: true,   // 渐进式扫描
                        effort: 10          // 最大压缩努力程度
                    })
                    .toBuffer();
                break;

            case 'jpeg':
            case 'jpg':
                compressedBuffer = await sharp(inputBuffer)
                    .jpeg({
                        quality: 100,       // 无损质量
                        progressive: true,  // 渐进式扫描
                        mozjpeg: true      // 使用mozjpeg优化
                    })
                    .toBuffer();
                break;

            case 'webp':
                compressedBuffer = await sharp(inputBuffer)
                    .webp({
                        lossless: true,     // 无损压缩
                        effort: 6          // 压缩努力程度
                    })
                    .toBuffer();
                break;

            default:
                // 对于其他格式，保持原格式但进行优化，如果失败则转换为PNG
                try {
                    compressedBuffer = await sharp(inputBuffer)
                        .toBuffer();
                } catch {
                    // 如果保持原格式失败，则转换为PNG进行无损压缩
                    compressedBuffer = await sharp(inputBuffer)
                        .png({
                            compressionLevel: 9,
                            progressive: true,
                            effort: 10
                        })
                        .toBuffer();
                }
                break;
        }

        // 转换回base64
        const compressedBase64 = compressedBuffer.toString('base64');

        console.log(`🗜️ 图片无损压缩完成 (${format}): ${inputBuffer.length} bytes -> ${compressedBuffer.length} bytes (压缩率: ${((1 - compressedBuffer.length / inputBuffer.length) * 100).toFixed(1)}%)`);

        return compressedBase64;
    } catch (error) {
        console.error('❌ 图片无损压缩失败:', error);
        // 如果压缩失败，返回原始图片内容
        return imageContent;
    }
};