import { Body, Controller, Get, Logger, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import * as path from 'node:path';
import { mkdir } from 'node:fs/promises';
import { writeFile } from 'node:fs/promises';
import { AppService } from './app.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { tempPath } from './ai-coding/playground/ctx';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  logger = new Logger(AppController.name);

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Body('codeType') type: string) {
    this.logger.log(`File uploaded: ${file.originalname}, ${file.size} bytes, type: ${type}`);

    const filePath = path.join(tempPath(), file.originalname);
    await mkdir(tempPath(), { recursive: true });
    await writeFile(filePath, file.buffer);

    return { message: 'File uploaded' };
  }
}
