import { defineConfig, mergeConfig, loadEnv, UserConfig, PluginOption } from 'vite';
import { join } from 'path';
{% if type === 'react' %}
import react from '@vitejs/plugin-react';
{% endif %}
{% if type === 'vue2' %}
import vue2 from '@vitejs/plugin-vue2';
{% endif %}
{% if type === 'vue' %}
import vue from '@vitejs/plugin-vue';
{% endif %}

import ViteRestart from 'vite-plugin-restart';
import Inspector from '@ht/vite-plugin-aicoding-inspector';


export default defineConfig(async ({ mode, command }) => {
  const isPreview = mode === 'preview';
  const root = __dirname;
  const env = loadEnv(mode, root, '');
  const type = env.VITE_PLAYGROUND_TYPE;

  let entry: string;
  if (type === 'react') {
    entry = 'index.tsx';
  } else if (type === 'vue' || type === 'vue2') {
    entry = 'index.vue';
  } else if (type === 'lit') {
    entry = 'index.ts';
  } else if (type === 'html') {
    entry = 'index.html';
  } else {
    throw new Error('Invalid playground type');
  }

  const plugins: PluginOption[] = [
    ViteRestart({
      restart: 'restart',
      reload: 'reload',
    }),
  ];

  {% if type === 'react' %}
  plugins.push(react());
  {% endif %}
  {% if type === 'vue2' %}
  plugins.push(vue2());
  {% endif %}
  {% if type === 'vue' %}
  plugins.push(vue());
  {% endif %}

  plugins.push(Inspector());

  const opts: UserConfig = {
    base: './',
    css: {
      preprocessorOptions: {
        less: {
          // @ht/sprite-ui needs this
          javascriptEnabled: true,
        },
      },
    },
    plugins,
    root,
    envDir: root,
    build: {
      outDir: join(root, isPreview? 'dist-preview' : 'dist'),
      minify: false,
    },
  };

  if (type === 'html') {
    return opts;
  }

  if (command === 'build' && !isPreview ) {
    return mergeConfig(opts, {
      build: {
        lib: {
          entry: `src/${entry}`,
          name: 'ai-coding-component',
          fileName: 'index',
          formats: ['es'],
        },
        rollupOptions: {
          external: ['react', 'react/jsx-runtime', 'react-dom', 'vue', 'lit'],
          output: {
            exports: 'named',
          },
        },
      },
    } satisfies Partial<UserConfig>);
  };
  return opts;
});
