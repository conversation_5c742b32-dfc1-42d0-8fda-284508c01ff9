import React from 'react';
import { ErrorBoundary as ReactErrorBoundary, FallbackProps } from 'react-error-boundary';
import styles from './styles.module.css';

interface AicodingSendMessage {
  error: Error;
  prompt: string;
}

function ErrorFallback(props: FallbackProps) {
  const { error, resetErrorBoundary } = props;
  
  const handleClick = () => {
    const send: AicodingSendMessage = {
      error,
      prompt: error.message,
    };
    window.parent.postMessage(send, "*");
  };

  return (
    <div role="alert" className={styles.errorBoundaryContainer}>
      <p>出错了：</p>
      <pre>{error.message}</pre>
      <button 
        id="indicator-message-box-button" 
        className={styles.aicodingIndicatorMessageBoxSelectedFormButton}
        onClick={handleClick}
      >
        AI 诊断修复
      </button>
    </div>
  );
}

export function ErrorBoundary({ children }: { children: React.ReactNode }) {
  const onError = (error: Error, info: React.ErrorInfo) => {
    console.log('Error:', error);
    const send: AicodingSendMessage = {
      error,
      prompt: error.message,
    };
    window.parent.postMessage(send, "*");
  };

  return (
    <ReactErrorBoundary FallbackComponent={ErrorFallback} onError={onError}>
      {children}
    </ReactErrorBoundary>
  );
} 