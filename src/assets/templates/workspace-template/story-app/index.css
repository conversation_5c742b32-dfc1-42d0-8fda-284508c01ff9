:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
}

story-app {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 100vh;
  > header {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    height: 2rem;
    button {
      border: 1px solid silver;
      height: 30px;
      padding: 0 0.4rem;
      background-color: #eee;
      color: #333;
      > * {
        vertical-align: middle;
      }
      &[data-active] {
        border-color: #2563eb; /* blue-600 */
        background-color: #3b82f6; /* blue-500 */
        color: white;
      }
    }
  }
  
  > main {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }  
}

story-wrapper {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  flex-direction: column;
  gap: 0.5rem;
  > h1 {
    font-size: 1.2rem;
  }
}
