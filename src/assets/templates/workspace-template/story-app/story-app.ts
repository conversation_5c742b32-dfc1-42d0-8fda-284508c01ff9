import { LitElement, html } from 'lit';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { customElement, state } from 'lit/decorators.js';
import { repeat } from 'lit/directives/repeat.js';
import { keyed } from 'lit/directives/keyed.js';
import { Task } from '@lit/task';
import iconApps from './assets/IonApps.svg?raw';
import './story-wrapper';

@customElement('story-app')
export class StoryApp extends LitElement {
  private _loadTask = new Task(this, {
    async task() {
      const url = new URL(location.href);
      const type = import.meta.env.VITE_PLAYGROUND_TYPE;
      const componentName = url.searchParams.get('component');
      let entryFile, componentMap;
      if (type === 'react') {
        entryFile = 'index.tsx';
        componentMap = {
          ...import.meta.glob('/src/index.tsx', {
            eager: true,
          }),
          ...import.meta.glob('/src/components/*/index.tsx', {
            eager: true,
          })
        };
      } else if (type === 'vue' || type === 'vue2') {
        entryFile = 'index.vue';
        componentMap = {
          ...import.meta.glob('/src/index.vue', {
            eager: true,
          }),
          ...import.meta.glob('/src/components/*/index.vue', {
            eager: true,
          })
        };
      } else if (type === 'lit') {
        entryFile = 'index.ts';
        componentMap = {
          ...import.meta.glob('/src/index.ts', {
            eager: true,
          }),
          ...import.meta.glob('/src/components/*/index.ts', {
            eager: true,
          })
        };
      }
      const storyMap = {
        ...import.meta.glob('/src/index.stories.js', {
          eager: true,
        }),
        ...import.meta.glob('/src/**/index.stories.js', {
          eager: true,
        })
      };

      let component, stories;
      if (componentName) {
        component = componentMap[`/src/components/${componentName}/${entryFile}`];
        stories = storyMap[`/src/components/${componentName}/index.stories.js`];
      } else {
        component = componentMap[`/src/${entryFile}`];
        stories = storyMap[`/src/index.stories.js`];
      }

      if (stories) {
        const { default: meta, ...allStories } = stories;
        return {
          component: component.default,
          storyMeta: meta,
          stories: Object.entries(allStories).map(([name, story]) => {
            return {
              name: name,
              title: name,
              ...story,
            };
          }),
        };
      } else {
        return {
          component: component.default,
        };
      }
    },
  });

  connectedCallback() {
    super.connectedCallback();
    this._loadTask.run();
  }

  createRenderRoot() {
    return this;
  }

  @state()
  _active = 0;

  _setActive(index: number) {
    this._active = index;
  }

  render() {
    return this._loadTask.render({
      error: (err) => {
        return html`Oops! Something went wrong. ${err}`;
      },
      complete: (data) => {
        const { _active: active } = this;
        const type = import.meta.env.VITE_PLAYGROUND_TYPE;
        if (!data.storyMeta) {
          return html`
            <main>
              <story-wrapper
                type=${type}
                .component=${data.component}
              ></story-wrapper>
            </main>`;
        }

        let activeStory = data.stories[active];
        let content;
        if (!activeStory) {
          content = repeat(data.stories, story => story.name, (story) => html`
            <story-wrapper
              show-title
              type=${type}
              .component=${data.component}
              .story=${story}
            ></story-wrapper>
          `);
        } else {
          content = html`
            <story-wrapper
              type=${type}
              .component=${data.component}
              .story=${activeStory}
            ></story-wrapper>`;
        }
        return html`
          <header>
            ${repeat(
              data.stories,
              story => story.name,
              (story, i) => html`
                <button
                  ?data-active=${active === i}
                  @click=${() => this._setActive(data.stories.indexOf(story))}
                >${story.title}</button>`,
            )}
            <button
              ?data-active=${active === data.stories.length}
              @click=${() => this._setActive(data.stories.length)}
            >
              <i>${unsafeHTML(iconApps)}</i>
            </button>
          </header>
          <main>
            ${keyed(activeStory?.name, content)}
          </main>
        `;
      }
    });
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'story-app': StoryApp
  }
}
