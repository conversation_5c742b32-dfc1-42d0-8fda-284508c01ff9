import { LitElement, html } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { ref, createRef } from 'lit/directives/ref.js';

{% if type === 'react' %}
import React from 'react';
import ReactDOM from 'react-dom/client';
import { ErrorBoundary } from './components/ErrorBoundary';
{% endif %}

{% if type === 'vue' %}
import { createApp } from 'vue';
{% endif %}

{% if type === 'vue2' %}
import Vue from 'vue';
{% endif %}

@customElement('story-wrapper')
export class StoryWrapper extends LitElement {
  @property()
  type: 'react' | 'vue' | 'vue2' | 'lit' = 'react';

  @property({
    attribute: false,
  })
  component: any;

  @property({
    attribute: false,
  })
  story?: {
    title: string;
    args: Record<string, any>;
  };

  @state()
  app: any;

  @property({
    attribute: 'show-title',
    type: Boolean,
  })
  showTitle: boolean = false;

  createRenderRoot() {
    return this;
  }

  container = createRef();
  ob?: ResizeObserver;

  firstUpdated() {
    if (this.type === 'vue') {
      this.app = createApp(this.component, this.story?.args);
      this.app.mount(this.container.value);
    } else if(this.type === 'vue2') {
      const Component = Vue.extend(this.component);
      this.app = new Component({
        propsData: this.story?.args
      });
      this.app.$mount(this.container.value);
    }
    else if (this.type === 'react') {
      this.app = ReactDOM.createRoot(this.container.value);
      this.app.render(
        React.createElement(
          React.StrictMode,
          null,
          React.createElement(
            ErrorBoundary,
            null,
            React.createElement(this.component, this.story?.args)
          ),
        ),
      );
    } else if (this.type === 'lit') {
      this.container.value.innerHTML = '';
      const el = document.createElement(this.component);
      const args = this.story?.args;
      if (args) {
        for (const key in args) {
          el.setAttribute(key, args[key]);
        }
      }
      this.container.value.appendChild(el);
      this.app = el;
    }
  }

  connectedCallback() {
    super.connectedCallback();
    const ob = new ResizeObserver(() => {
      parent.postMessage(
        {
          sender: 'story-app',
          type: 'resize',
          value: {
            width: this.clientWidth,
            height: this.clientHeight,
          },
        },
        '*',
      );
    });
    ob.observe(this);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    this.ob?.disconnect();

    if (this.type === 'vue' || this.type === 'vue2') {
      this.app?.unmount();
    } else if (this.type === 'react') {
      this.app?.unmount();
    } else if (this.type === 'lit') {
      this.app?.remove();
    }
  }

  render() {
    let title;
    if (this.showTitle && this.story?.title) {
      title = html`<h1>${this.story.title}</h1>`;
    }
    return html`
      ${title}
      <div ref=${ref(this.container)}></div>
    `;
  }
}
