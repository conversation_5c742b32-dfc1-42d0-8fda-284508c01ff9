{
  "name": "vite-workspace-template",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "@lit/task": "^1.0.1",
    "lit": "^3.2.1"{% if type === 'react' %},
    "react": "^18.3.1",
    "react-dom": "^18.3.1"{% endif %}{% if type === 'vue2' %},
    "vue": "^2.7.16"{% endif %}{% if type === 'vue' %},
    "vue": "^3.5.12"{% endif %}
  },
  "devDependencies": {
    "@ht/vite-plugin-aicoding-inspector": "^1.0.0",
    "@types/react": "^18.3.11",
    "@types/react-dom": "^18.3.1",
    "autoprefixer": "^10.4.20",
    "less": "^4.2.1",
    "postcss": "^8.4.49",
    "sass": "1.82.0",
    "tailwindcss": "^3.4.17",
    "typescript": "~5.6.2",
    "vite": "^6.1.0",
    "vite-plugin-restart": "^0.4.2"{% if type === 'vue2' %},
    "@vitejs/plugin-vue2": "^2.3.3"{% endif %}{% if type === 'vue' %},
    "@vitejs/plugin-vue": "^5.2.1"{% endif %}{% if type === 'react' %},
    "react-error-boundary": "^6.0.0",
    "@vitejs/plugin-react": "^4.3.4"{% endif %}
  },
  "resolutions": {
    "rollup": "4.34.6",
    "esbuild": "0.24.2"
  },
  "packageManager": "yarn@1.22.22"
}
