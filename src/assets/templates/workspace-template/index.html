<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preview</title>
    <link rel="stylesheet" href="./src/tailwind.css" />
    <link rel="stylesheet" href="./story-app/index.css" />
  </head>
  <body>
    <script type="module">
      // 获取playground类型
      const urlParams = new URLSearchParams(window.location.search);
      const playgroundType = urlParams.get('type') || import.meta.env.VITE_PLAYGROUND_TYPE;
      
      if (playgroundType === 'html') {
        // 对于HTML类型，直接使用iframe显示src/index.html
        const iframe = document.createElement('iframe');
        iframe.src = './src/index.html';
        iframe.style.width = '100%';
        iframe.style.height = '100vh';
        iframe.style.border = 'none';
        iframe.style.margin = '0';
        iframe.style.padding = '0';
        document.body.appendChild(iframe);
      } else {
        // 对于其他类型，使用story-app
        import('./story-app/story-app.ts').then(() => {
          const storyApp = document.createElement('story-app');
          storyApp.setAttribute('type', playgroundType);
          document.body.appendChild(storyApp);
        });
      }
    </script>
  </body>
</html>
