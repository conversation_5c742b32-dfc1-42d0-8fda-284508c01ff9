import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { GitlabUtil } from '../utils/gitlab.util';
import { DesignProjectBackgroundTaskService } from './background-task.service';
import { DesignProjectService } from './design-project.service';

export interface WorkflowNode {
  id: string;
  name: string;
  type: 'task-group'; // 统一使用任务组类型
  taskGroupType: 'lanhu-transcode-group' | 'merge-group' | 'spec-to-prod-code-group'; // 具体的任务组类型
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  taskId?: string;
  result?: any;
  dependencies: string[]; // 依赖的节点ID
  subTasks: WorkflowSubTask[]; // 子任务列表
}

export interface WorkflowSubTask {
  id: string;
  name: string;
  type: 'lanhu-transcode' | 'merge' | 'spec-to-prod-code';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  taskId?: string;
  result?: any;
  metadata?: any; // 子任务的额外信息，如原型ID等
}

export interface WorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  nodes: WorkflowNode[];
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

export interface MultiPageWorkflowStatus {
  workflowId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'partial-success';
  progress: number;
  pageWorkflows: Array<{
    pageId: string;
    pageName: string;
    workflowId: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    startTime?: Date;
    endTime?: Date;
    error?: string;
  }>;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  totalPages: number;
  completedPages: number;
  failedPages: number;
}

@Injectable()
export class WorkflowService {
  private readonly logger = new Logger(WorkflowService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly backgroundTaskService: DesignProjectBackgroundTaskService,
    private readonly designProjectService: DesignProjectService,
  ) { }

  /**
   * 创建页面级代码生成工作流
   */
  async createPageCodeGenerationWorkflow(
    projectId: string,
    pageId: string,
    user: string,
    options: {
      model?: string;
      enableAutoIteration?: boolean;
      enableStepByStep?: boolean;
      gitUrl?: string;
      branch?: string;
      shouldExecuteImmediately?: boolean; // 新增参数：是否立即执行，默认为true保持向后兼容
    } = {}
  ): Promise<{ workflowId: string; status: WorkflowStatus }> {
    this.logger.log(`🚀 开始创建页面级代码生成工作流: projectId=${projectId}, pageId=${pageId}`);

    // 1. 验证页面存在
    const page = await this.prisma.designPage.findUnique({
      where: { id: pageId },
      include: {
        prototypes: true,
        designProject: true,
      },
    });

    if (!page) {
      throw new Error(`页面 ${pageId} 不存在`);
    }

    if (page.designProjectId !== projectId) {
      throw new Error(`页面 ${pageId} 不属于项目 ${projectId}`);
    }

    // 2. 分析数据准备情况并创建工作流节点
    const workflowNodes = await this.analyzeAndCreateWorkflowNodes(page);

    // 3. 创建工作流状态
    const workflowId = generateId();
    const workflowStatus: WorkflowStatus = {
      workflowId,
      status: 'pending',
      progress: 0,
      nodes: workflowNodes,
      startTime: new Date(),
    };

    // 4. 创建工作流主任务，使用 metadata 存储状态
    const mainTask = await this.prisma.backgroundTask.create({
      data: {
        id: workflowId,
        taskType: 'page-code-generation-workflow',
        taskName: `页面级代码生成工作流 - ${page.name}`,
        user,
        status: 'pending',
        progress: 0,
        designProjectId: projectId,
        model: options.model || 'openrouter::google/gemini-2.5-pro-preview',
        enableAutoIteration: options.enableAutoIteration,
        enableStepByStep: options.enableStepByStep,
        metadata: {
          workflowStatus: workflowStatus,
          pageId,
          pageName: page.name,
          projectName: page.designProject.name,
          gitUrl: options.gitUrl,
          branch: options.branch || 'master',
          ...options,
        } as any,
      },
    });

    this.logger.log(`✅ 工作流创建成功: ${workflowId}`);

    // 5. 根据参数决定是否立即执行工作流
    const shouldExecute = options.shouldExecuteImmediately !== false; // 默认为true，保持向后兼容
    if (shouldExecute) {
      this.executeWorkflow(workflowId).catch(error => {
        this.logger.error(`❌ 工作流执行失败: ${workflowId}`, error);
      });
    } else {
      this.logger.log(`⏸️ 工作流已创建但暂不执行: ${workflowId}`);
    }

    return { workflowId, status: workflowStatus };
  }

  /**
   * 创建多页面代码生成工作流
   */
  async createMultiPageCodeGenerationWorkflow(
    projectId: string,
    pageIds: string[],
    user: string,
    options: {
      model?: string;
      enableAutoIteration?: boolean;
      enableStepByStep?: boolean;
      gitUrl?: string;
      branch?: string;
    } = {}
  ): Promise<{ workflowId: string; status: MultiPageWorkflowStatus }> {
    this.logger.log(`🚀 开始创建多页面代码生成工作流: projectId=${projectId}, pageCount=${pageIds.length}`);

    // 1. 验证所有页面存在
    const pages = await this.prisma.designPage.findMany({
      where: {
        id: { in: pageIds },
        designProjectId: projectId,
      },
      include: {
        prototypes: true,
        designProject: true,
      },
    });

    if (pages.length !== pageIds.length) {
      const foundIds = pages.map(p => p.id);
      const missingIds = pageIds.filter(id => !foundIds.includes(id));
      throw new Error(`以下页面不存在或不属于该项目: ${missingIds.join(', ')}`);
    }

    // 2. 创建多页面工作流主任务
    const mainWorkflowId = generateId();
    const pageWorkflows: MultiPageWorkflowStatus['pageWorkflows'] = [];

    // 3. 为每个页面创建独立的工作流（但不立即执行）
    const subWorkflowPromises = pages.map(async (page) => {
      try {
        // 调用原方法，设置不立即执行
        const subWorkflowResult = await this.createPageCodeGenerationWorkflow(
          projectId,
          page.id,
          user,
          {
            ...options,
            shouldExecuteImmediately: false, // 关键：设置为false，不立即执行
          }
        );

        return {
          pageId: page.id,
          pageName: page.name,
          workflowId: subWorkflowResult.workflowId,
          status: subWorkflowResult.status.status as any,
          progress: subWorkflowResult.status.progress,
          startTime: subWorkflowResult.status.startTime,
        };
      } catch (error) {
        this.logger.error(`❌ 创建页面 ${page.id} 的工作流失败:`, error);
        return {
          pageId: page.id,
          pageName: page.name,
          workflowId: '',
          status: 'failed' as const,
          progress: 0,
          error: error.message,
        };
      }
    });

    // 4. 等待所有子工作流创建完成
    pageWorkflows.push(...await Promise.all(subWorkflowPromises));

    // 5. 创建多页面工作流状态
    const multiPageWorkflowStatus: MultiPageWorkflowStatus = {
      workflowId: mainWorkflowId,
      status: 'processing',
      progress: 0,
      pageWorkflows,
      startTime: new Date(),
      totalPages: pages.length,
      completedPages: 0,
      failedPages: pageWorkflows.filter(pw => pw.status === 'failed').length,
    };

    // 6. 创建多页面工作流主任务记录，使用 metadata 存储状态
    await this.prisma.backgroundTask.create({
      data: {
        id: mainWorkflowId,
        taskType: 'multi-page-code-generation-workflow',
        taskName: `多页面代码生成工作流 - ${pages.length}个页面`,
        user,
        status: 'processing',
        progress: 0,
        designProjectId: projectId,
        model: options.model,
        enableAutoIteration: options.enableAutoIteration,
        enableStepByStep: options.enableStepByStep,
        metadata: {
          workflowStatus: multiPageWorkflowStatus,
          pageIds,
          pageNames: pages.map(p => p.name),
          subWorkflowIds: pageWorkflows.map(pw => pw.workflowId).filter(Boolean),
          gitUrl: options.gitUrl,
          branch: options.branch || 'master',
          ...options,
        } as any,
      },
    });

    this.logger.log(`✅ 多页面工作流创建成功: ${mainWorkflowId}, 子工作流数量: ${pageWorkflows.length}`);

    // 7. 现在并发启动所有子工作流的执行
    const validSubWorkflowIds = pageWorkflows.map(pw => pw.workflowId).filter(Boolean);
    this.logger.log(`🚀 开始并发执行 ${validSubWorkflowIds.length} 个子工作流`);

    // 并发启动所有子工作流执行，不等待完成
    validSubWorkflowIds.forEach(subWorkflowId => {
      this.executeWorkflow(subWorkflowId).catch(error => {
        this.logger.error(`❌ 子工作流执行失败: ${subWorkflowId}`, error);
      });
    });

    // 8. 启动状态监控
    this.monitorMultiPageWorkflow(mainWorkflowId).catch(error => {
      this.logger.error(`❌ 多页面工作流监控失败: ${mainWorkflowId}`, error);
    });

    return { workflowId: mainWorkflowId, status: multiPageWorkflowStatus };
  }

  /**
   * 监控多页面工作流状态
   */
  private async monitorMultiPageWorkflow(workflowId: string): Promise<void> {
    this.logger.log(`🔄 开始监控多页面工作流: ${workflowId}`);

    const checkInterval = setInterval(async () => {
      try {
        const workflow = await this.prisma.backgroundTask.findUnique({
          where: { id: workflowId },
        });

        if (!workflow || !workflow.metadata) {
          clearInterval(checkInterval);
          return;
        }

        const currentStatus = (workflow.metadata as any).workflowStatus as MultiPageWorkflowStatus;

        // 查询所有子工作流的最新状态
        const subWorkflowIds = currentStatus.pageWorkflows.map(pw => pw.workflowId).filter(Boolean);
        const subWorkflows = await this.prisma.backgroundTask.findMany({
          where: { id: { in: subWorkflowIds } },
        });

        let needsUpdate = false;
        let completedCount = 0;
        let failedCount = 0;
        let processingCount = 0;

        // 更新每个页面工作流的状态
        currentStatus.pageWorkflows.forEach(pageWorkflow => {
          if (!pageWorkflow.workflowId) return;

          const subWorkflow = subWorkflows.find(sw => sw.id === pageWorkflow.workflowId);
          if (subWorkflow) {
            const oldStatus = pageWorkflow.status;
            const oldProgress = pageWorkflow.progress;
            const newStatus = subWorkflow.status as any;
            const newProgress = subWorkflow.progress;

            if (oldStatus !== newStatus || oldProgress !== newProgress) {
              pageWorkflow.status = newStatus;
              pageWorkflow.progress = newProgress;
              needsUpdate = true;
              this.logger.log(`📝 页面工作流状态更新: ${pageWorkflow.pageName} ${oldStatus} -> ${newStatus} (${newProgress}%)`);
            }
          }

          // 统计各种状态的数量
          if (pageWorkflow.status === 'completed') completedCount++;
          else if (pageWorkflow.status === 'failed') failedCount++;
          else if (pageWorkflow.status === 'processing') processingCount++;
        });

        // 计算整体进度和状态
        const totalPages = currentStatus.totalPages;
        const finishedPages = completedCount + failedCount;
        const newProgress = Math.round((finishedPages / totalPages) * 100);
        let newStatus: MultiPageWorkflowStatus['status'];

        if (finishedPages === totalPages) {
          // 只要有一个页面失败，整个多页面工作流就失败
          if (failedCount > 0) {
            // 有任何失败的页面，整个工作流就是失败
            newStatus = 'failed';
            this.logger.log(`❌ 多页面工作流失败: ${workflowId}, 成功: ${completedCount}, 失败: ${failedCount}`);
          } else {
            // 全部成功
            newStatus = 'completed';
            this.logger.log(`✅ 多页面工作流全部成功: ${workflowId}`);
          }
          currentStatus.endTime = new Date();
          clearInterval(checkInterval);
        } else if (processingCount > 0 || currentStatus.status === 'processing') {
          // 有任务正在处理中
          newStatus = 'processing';
        } else {
          // 保持当前状态
          newStatus = currentStatus.status;
        }

        // 强制更新状态信息（即使状态没变也要更新统计数据）
        const statusChanged = currentStatus.status !== newStatus;
        const progressChanged = currentStatus.progress !== newProgress;
        const countsChanged = currentStatus.completedPages !== completedCount || currentStatus.failedPages !== failedCount;

        if (needsUpdate || statusChanged || progressChanged || countsChanged) {
          currentStatus.progress = newProgress;
          currentStatus.status = newStatus;
          currentStatus.completedPages = completedCount;
          currentStatus.failedPages = failedCount;

          await this.prisma.backgroundTask.update({
            where: { id: workflowId },
            data: {
              metadata: {
                ...(workflow.metadata as any),
                workflowStatus: currentStatus,
              } as any,
              status: newStatus,
              progress: newProgress,
              updated: new Date(),
            },
          });

          this.logger.log(`📊 多页面工作流状态更新: ${workflowId}, 状态: ${newStatus}, 进度: ${newProgress}%, 完成: ${completedCount}, 失败: ${failedCount}, 处理中: ${processingCount}`);
        }

      } catch (error) {
        this.logger.error(`❌ 监控多页面工作流失败: ${workflowId}`, error);
      }
    }, 5000); // 改为每5秒检查一次，提高响应速度

    // 30分钟后强制停止监控
    setTimeout(() => {
      clearInterval(checkInterval);
      this.logger.log(`⏰ 多页面工作流监控超时停止: ${workflowId}`);
    }, 1800000);
  }

  /**
   * 获取工作流状态
   */
  async getWorkflowStatus(workflowId: string): Promise<WorkflowStatus> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow) {
      throw new Error(`工作流 ${workflowId} 不存在`);
    }

    if (!workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 状态信息不存在`);
    }

    const metadata = workflow.metadata as any;
    const internalWorkflowStatus = metadata.workflowStatus as WorkflowStatus;

    // 将内部的任务组结构转换为前端期望的扁平节点结构
    const flattenedNodes: any[] = [];

    internalWorkflowStatus.nodes.forEach(taskGroup => {
      // 为每个子任务创建一个前端节点
      taskGroup.subTasks.forEach(subTask => {
        flattenedNodes.push({
          id: subTask.id,
          name: subTask.name,
          type: subTask.type,
          status: subTask.status,
          progress: subTask.progress,
          startTime: subTask.startTime,
          endTime: subTask.endTime,
          error: subTask.error,
          taskId: subTask.taskId,
          result: subTask.result,
          // 关键：转换依赖关系，将任务组依赖转换为子任务依赖
          dependencies: this.convertTaskGroupDependenciesToSubTaskDependencies(
            taskGroup,
            internalWorkflowStatus.nodes,
            subTask
          ),
          // 添加任务组信息供前端使用
          taskGroupType: taskGroup.taskGroupType,
          taskGroupId: taskGroup.id,
          taskGroupName: taskGroup.name,
          taskGroupStatus: taskGroup.status,
          taskGroupProgress: taskGroup.progress,
        });
      });
    });

    // 返回前端期望的扁平结构
    return {
      workflowId: internalWorkflowStatus.workflowId,
      status: internalWorkflowStatus.status,
      progress: internalWorkflowStatus.progress,
      nodes: flattenedNodes,
      startTime: internalWorkflowStatus.startTime,
      endTime: internalWorkflowStatus.endTime,
      error: internalWorkflowStatus.error,
    };
  }

  /**
   * 获取多页面工作流状态
   */
  async getMultiPageWorkflowStatus(workflowId: string): Promise<MultiPageWorkflowStatus> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow) {
      throw new Error(`多页面工作流 ${workflowId} 不存在`);
    }

    if (workflow.taskType !== 'multi-page-code-generation-workflow') {
      throw new Error(`工作流 ${workflowId} 不是多页面工作流`);
    }

    if (!workflow.metadata) {
      throw new Error(`多页面工作流 ${workflowId} 状态信息不存在`);
    }

    // 在返回状态前先强制同步最新状态
    try {
      await this.syncMultiPageWorkflowStatus(workflowId);

      // 重新获取更新后的工作流数据
      const updatedWorkflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (updatedWorkflow?.metadata) {
        const metadata = updatedWorkflow.metadata as any;
        const status = metadata.workflowStatus as MultiPageWorkflowStatus;
        this.logger.log(`✅ 返回同步后的多页面工作流状态: ${workflowId}, 状态: ${status.status}, 完成: ${status.completedPages}, 失败: ${status.failedPages}`);
        return status;
      }
    } catch (error) {
      this.logger.warn(`⚠️ 同步多页面工作流状态失败，返回原始状态: ${workflowId}`, error);
    }

    // 如果同步失败，返回原始状态
    const metadata = workflow.metadata as any;
    return metadata.workflowStatus as MultiPageWorkflowStatus;
  }

  /**
   * 获取项目的所有工作流（包括单页面和多页面）
   */
  async getProjectWorkflows(projectId: string): Promise<{
    singlePageWorkflows: WorkflowStatus[];
    multiPageWorkflows: MultiPageWorkflowStatus[];
  }> {
    const workflows = await this.prisma.backgroundTask.findMany({
      where: {
        designProjectId: projectId,
        taskType: {
          in: ['page-code-generation-workflow', 'multi-page-code-generation-workflow']
        }
      },
      orderBy: { created: 'desc' },
    });

    // 🔧 新增：异步检查并处理超时的工作流
    setImmediate(() => {
      this.checkAndHandleTimeoutWorkflows(workflows).catch(error => {
        this.logger.error(`❌ 检查超时工作流失败:`, error);
      });
    });

    const singlePageWorkflows: WorkflowStatus[] = [];
    const multiPageWorkflows: MultiPageWorkflowStatus[] = [];

    workflows.forEach(workflow => {
      if (workflow.metadata) {
        const metadata = workflow.metadata as any;
        if (workflow.taskType === 'page-code-generation-workflow') {
          // 对于单页面工作流，在返回的工作流状态中添加页面信息
          const workflowStatus = metadata.workflowStatus as WorkflowStatus;
          const enhancedWorkflowStatus = {
            ...workflowStatus,
            // 添加页面信息到工作流状态中
            pageId: metadata.pageId,
            pageName: metadata.pageName,
            projectName: metadata.projectName,
          };
          // 必须要要有工作流id，避免返回脏数据
          if (enhancedWorkflowStatus?.workflowId) {
            singlePageWorkflows.push(enhancedWorkflowStatus);
          }
        } else if (workflow.taskType === 'multi-page-code-generation-workflow') {
          const multiWorkflowStatus = metadata.workflowStatus as MultiPageWorkflowStatus;
          // 必须要要有工作流id，避免返回脏数据
          if (multiWorkflowStatus?.workflowId) {
            multiPageWorkflows.push(multiWorkflowStatus);
          }
        }
      }
    });

    // debugger;
    return { singlePageWorkflows, multiPageWorkflows };
  }

  /**
   * 检查并处理超时的工作流
   * 如果工作流状态为"进行中"且耗时超过2小时，将第一个pending任务设为失败
   */
  private async checkAndHandleTimeoutWorkflows(workflows: any[]): Promise<void> {
    const now = new Date();
    const twoHourAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2小时前

    this.logger.log(`🕐 开始检查超时工作流，当前时间: ${now.toISOString()}，超时阈值: ${twoHourAgo.toISOString()}`);

    for (const workflow of workflows) {
      try {
        // 检查工作流是否处于进行中状态
        if (workflow.status !== 'processing') {
          continue;
        }

        // 检查工作流开始时间是否超过2小时
        if (!workflow.metadata) {
          continue;
        }

        const metadata = workflow.metadata as any;

        // 🔧 新增：检查是否已经处理过超时（避免重复处理）
        if (metadata.timeoutHandledAt) {
          this.logger.debug(`⏭️ 工作流已处理过超时，跳过: ${workflow.id}`);
          continue;
        }

        let workflowStartTime: Date | null = null;

        // 根据工作流类型获取开始时间
        if (workflow.taskType === 'page-code-generation-workflow') {
          const workflowStatus = metadata.workflowStatus as WorkflowStatus;
          workflowStartTime = workflowStatus.startTime ? new Date(workflowStatus.startTime) : null;
        } else if (workflow.taskType === 'multi-page-code-generation-workflow') {
          const multiWorkflowStatus = metadata.workflowStatus as MultiPageWorkflowStatus;
          workflowStartTime = multiWorkflowStatus.startTime ? new Date(multiWorkflowStatus.startTime) : null;
        }

        // 如果没有开始时间，跳过
        if (!workflowStartTime) {
          continue;
        }

        // 检查是否超时
        if (workflowStartTime > twoHourAgo) {
          continue; // 未超时，跳过
        }

        this.logger.warn(`⚠️ 发现超时工作流: ${workflow.id}, 类型: ${workflow.taskType}, 开始时间: ${workflowStartTime.toISOString()}, 运行时长: ${Math.round((now.getTime() - workflowStartTime.getTime()) / (1000 * 60))} 分钟`);

        // 处理超时工作流
        await this.handleTimeoutWorkflow(workflow.id, workflow.taskType, metadata);

      } catch (error) {
        this.logger.error(`❌ 处理超时工作流失败: ${workflow.id}`, error);
        // 继续处理其他工作流，不中断整个流程
      }
    }

    this.logger.log(`✅ 超时工作流检查完成`);
  }

  /**
   * 处理单个超时工作流
   * 将第一个pending任务设为失败，然后更新整个工作流为失败
   */
  private async handleTimeoutWorkflow(workflowId: string, taskType: string, metadata: any): Promise<void> {
    this.logger.log(`🔧 开始处理超时工作流: ${workflowId}, 类型: ${taskType}`);
    // debugger
    if (taskType === 'page-code-generation-workflow') {
      // 处理单页面工作流超时
      await this.handleSinglePageWorkflowTimeout(workflowId, metadata);
    } else if (taskType === 'multi-page-code-generation-workflow') {
      // 处理多页面工作流超时
      await this.handleMultiPageWorkflowTimeout(workflowId, metadata);
    }
  }

  /**
   * 处理单页面工作流超时
   */
  private async handleSinglePageWorkflowTimeout(workflowId: string, metadata: any): Promise<void> {
    const workflowStatus = metadata.workflowStatus as WorkflowStatus;

    // 查找第一个pending状态的任务组
    let foundPendingNode = false;
    const updatedNodes = workflowStatus.nodes.map(node => {
      if (!foundPendingNode && node?.status === 'pending') {
        foundPendingNode = true;
        this.logger.log(`📝 将超时工作流中的pending任务组设为失败: ${node?.name} (${node?.id})`);

        // 设置任务组为失败
        const failedNode = {
          ...node,
          status: 'failed' as const,
          progress: 100,
          startTime: new Date(),
          endTime: new Date(),
          error: '工作流执行超时（超过2小时），自动设置为失败',
          // 同时设置所有子任务为失败
          subTasks: (node?.subTasks || []).map(subTask => ({
            ...subTask,
            status: 'failed' as const,
            progress: 100,
            startTime: new Date(),
            endTime: new Date(),
            error: '工作流执行超时（超过2小时），自动设置为失败',
          }))
        };
        return failedNode;
      }
      return node;
    });

    // 即使没有找到pending任务组，也要将超时工作流设为失败
    if (!foundPendingNode) {
      this.logger.log(`📝 超时工作流中没有找到pending任务组，但仍将整个工作流设为失败: ${workflowId}`);
    }

    // 更新工作流状态为失败
    const updatedWorkflowStatus = {
      ...workflowStatus,
      status: 'failed' as const,
      progress: 100,
      endTime: new Date(),
      error: foundPendingNode
        ? '工作流执行超时（超过2小时），自动设置为失败'
        : '工作流执行超时（超过2小时），虽无pending任务但整体设为失败',
      nodes: updatedNodes,
    };

    // 更新数据库
    await this.prisma.backgroundTask.update({
      where: { id: workflowId },
      data: {
        status: 'failed',
        progress: 100,
        metadata: {
          ...metadata,
          workflowStatus: updatedWorkflowStatus,
          timeoutHandledAt: new Date(),
        } as any,
        updated: new Date(),
      },
    });

    this.logger.log(`✅ 单页面工作流超时处理完成: ${workflowId}${foundPendingNode ? '（处理了pending任务组）' : '（无pending任务组但已设为失败）'}`);
  }

  /**
   * 处理多页面工作流超时
   */
  private async handleMultiPageWorkflowTimeout(workflowId: string, metadata: any): Promise<void> {
    const multiWorkflowStatus = metadata.workflowStatus as MultiPageWorkflowStatus;

    // 查找第一个processing状态的页面工作流，将其设为失败
    let foundProcessingPage = false;
    const updatedPageWorkflows = multiWorkflowStatus.pageWorkflows.map(pageWorkflow => {
      if (!foundProcessingPage && pageWorkflow.status === 'processing') {
        foundProcessingPage = true;
        this.logger.log(`📝 将超时多页面工作流中的processing页面设为失败: ${pageWorkflow.pageName} (${pageWorkflow.workflowId})`);

        return {
          ...pageWorkflow,
          status: 'failed' as const,
          progress: 100,
          error: '多页面工作流执行超时（超过2小时），自动设置为失败',
        };
      }
      return pageWorkflow;
    });

    // 即使没有找到processing页面，也要将超时工作流设为失败
    if (!foundProcessingPage) {
      this.logger.log(`📝 超时多页面工作流中没有找到processing页面，但仍将整个工作流设为失败: ${workflowId}`);
    }

    // 重新计算统计信息
    const completedCount = updatedPageWorkflows.filter(pw => pw.status === 'completed').length;
    const failedCount = updatedPageWorkflows.filter(pw => pw.status === 'failed').length;

    // 更新多页面工作流状态
    const updatedMultiWorkflowStatus = {
      ...multiWorkflowStatus,
      status: (completedCount > 0 && failedCount > 0) ? 'partial-success' as const : 'failed' as const,
      progress: 100,
      endTime: new Date(),
      completedPages: completedCount,
      failedPages: failedCount,
      pageWorkflows: updatedPageWorkflows,
    };

    // 更新数据库
    await this.prisma.backgroundTask.update({
      where: { id: workflowId },
      data: {
        status: updatedMultiWorkflowStatus.status,
        progress: 100,
        metadata: {
          ...metadata,
          workflowStatus: updatedMultiWorkflowStatus,
          timeoutHandledAt: new Date(),
        } as any,
        updated: new Date(),
      },
    });

    this.logger.log(`✅ 多页面工作流超时处理完成: ${workflowId}, 最终状态: ${updatedMultiWorkflowStatus.status}${foundProcessingPage ? '（处理了processing页面）' : '（无processing页面但已设为失败）'}`);
  }

  /**
   * 分析数据准备情况并创建工作流节点
   */
  private async analyzeAndCreateWorkflowNodes(page: any): Promise<WorkflowNode[]> {
    const nodes: WorkflowNode[] = [];

    // 始终创建所有节点，并且都从pending状态开始

    // 修复性能问题：创建阶段不验证原型存在性，只在执行阶段验证
    // 工作流创建时，直接使用页面的原型数据，不进行额外的数据库查询
    let validPrototypes = page.prototypes || [];

    this.logger.log(`📋 页面 ${page.name} 包含 ${validPrototypes.length} 个原型`);

    // 在工作流创建时就重置相关原型的状态，避免继承之前工作流的失败状态
    if (validPrototypes.length > 0) {
      this.logger.log(`🔄 重置工作流相关原型状态，确保新工作流从干净状态开始`);

      for (const prototype of validPrototypes) {
        try {
          await this.prisma.designPagePrototype.update({
            where: { id: prototype.id },
            data: {
              resultHtml: null,
              status: 'pending',
              playgroundId: null,
            },
          });
          this.logger.log(`✅ 已重置原型状态: ${prototype.id} (${prototype.prototypeName})`);
        } catch (error) {
          this.logger.warn(`⚠️ 无法重置原型 ${prototype.id} 的状态: ${error.message}`);
        }
      }

      // 同时重置页面的合并结果
      try {
        await this.prisma.designPage.update({
          where: { id: page.id },
          data: {
            resultHtml: null,
            playgroundId: null,
          },
        });
        this.logger.log(`✅ 已重置页面合并状态: ${page.id} (${page.name})`);
      } catch (error) {
        this.logger.warn(`⚠️ 无法重置页面 ${page.id} 的合并状态: ${error.message}`);
      }
    }

    // 1. 创建设计稿转码任务组（包含所有原型的转码子任务）
    if (validPrototypes.length > 0) {
      const transcodeSubTasks: WorkflowSubTask[] = validPrototypes.map((prototype: any) => {
        // 始终将转码子任务状态设置为pending，不再检查已有结果
        return {
          id: `transcode_${prototype.id}`,
          name: `蓝湖设计稿转码 - ${prototype.prototypeName}`,
          type: 'lanhu-transcode',
          status: 'pending',
          progress: 0,
          metadata: {
            prototypeId: prototype.id,
            prototypeName: prototype.prototypeName,
          },
        };
      });

      // 转码任务组状态始终设置为pending
      nodes.push({
        id: `transcode_group_${page.id}`,
        name: `设计稿转码任务`,
        type: 'task-group',
        taskGroupType: 'lanhu-transcode-group',
        status: 'pending',
        progress: 0,
        dependencies: [],
        subTasks: transcodeSubTasks,
      });

      // 2. 创建代码合并任务组（依赖转码任务组）
      const mergeSubTasks: WorkflowSubTask[] = [{
        id: `merge_${page.id}`,
        name: `设计稿原型代码合并 - ${page.name}`,
        type: 'merge',
        status: 'pending', // 始终设置为pending
        progress: 0,
        metadata: {
          pageId: page.id,
          pageName: page.name,
          prototypeIds: validPrototypes.map((p: any) => p.id), // 使用原始原型ID列表
        },
      }];

      nodes.push({
        id: `merge_group_${page.id}`,
        name: `代码合并任务`,
        type: 'task-group',
        taskGroupType: 'merge-group',
        status: 'pending', // 始终设置为pending
        progress: 0,
        dependencies: [`transcode_group_${page.id}`],
        subTasks: mergeSubTasks,
      });
    } else {
      this.logger.warn(`⚠️ 页面 ${page.id} 没有原型数据，将跳过转码和合并节点`);
    }

    // 3. 创建生产级代码生成任务组（依赖合并任务组或无依赖）
    const prodCodeSubTasks: WorkflowSubTask[] = [{
      id: `spec_to_prod_${page.id}`,
      name: `页面级代码生成 - ${page.name}`,
      type: 'spec-to-prod-code',
      status: 'pending', // 这个任务始终从pending开始，即使前置条件已满足
      progress: 0,
      metadata: {
        pageId: page.id,
        pageName: page.name,
      },
    }];

    const prodCodeDependencies = (validPrototypes.length > 0) ? [`merge_group_${page.id}`] : [];

    nodes.push({
      id: `prod_code_group_${page.id}`,
      name: `生产级代码生成任务`,
      type: 'task-group',
      taskGroupType: 'spec-to-prod-code-group',
      status: 'pending',
      progress: 0,
      dependencies: prodCodeDependencies,
      subTasks: prodCodeSubTasks,
    });

    this.logger.log(`📋 工作流节点分析完成: 总计${nodes.length}个任务组，基于${validPrototypes.length}个原型`);

    // 打印节点详细信息
    nodes.forEach(node => {
      const dependencyInfo = node.dependencies.length > 0 ? `依赖: ${node.dependencies.join(', ')}` : '依赖: 无';
      this.logger.log(`  - ${node.name} (状态: ${node.status}, 子任务数: ${node.subTasks.length}, ${dependencyInfo})`);

      node.subTasks.forEach(subTask => {
        this.logger.log(`    └─ ${subTask.name} (状态: ${subTask.status})`);
      });
    });

    return nodes;
  }

  /**
   * 执行工作流
   */
  private async executeWorkflow(workflowId: string): Promise<void> {
    this.logger.log(`🔄 开始执行工作流: ${workflowId}`);

    try {
      // 更新工作流状态为处理中
      await this.updateWorkflowStatus(workflowId, { status: 'processing' });

      let hasProgress = true;
      let iterationCount = 0;
      const maxIterations = 50; // 防止无限循环

      while (hasProgress && iterationCount < maxIterations) {
        hasProgress = false;
        iterationCount++;

        this.logger.log(`🔄 工作流执行第${iterationCount}轮循环`);

        // 获取内部工作流状态，而不是转换后的状态
        const currentStatus = await this.getInternalWorkflowStatus(workflowId);

        // 找出可以执行的任务组（依赖已完成且自身未执行）
        const readyTaskGroups = currentStatus.nodes.filter(taskGroup => {
          // 跳过已完成、失败和跳过的任务组
          if (taskGroup.status === 'completed' || taskGroup.status === 'failed' || taskGroup.status === 'skipped') {
            return false;
          }

          const isReady = taskGroup.status === 'pending' &&
            taskGroup.dependencies.every(depId => {
              const depTaskGroup = currentStatus.nodes.find(n => n.id === depId);
              // 严格检查依赖任务组必须是成功完成状态，失败的依赖不能满足条件
              return depTaskGroup && depTaskGroup.status === 'completed';
            });

          if (isReady) {
            this.logger.log(`✅ 任务组 ${taskGroup.name} (ID: ${taskGroup.id}) 已准备就绪`);
            this.logger.log(`   - 状态: ${taskGroup.status}`);
            this.logger.log(`   - 依赖数量: ${taskGroup.dependencies.length}`);
            if (taskGroup.dependencies.length > 0) {
              this.logger.log(`   - 依赖状态:`);
              taskGroup.dependencies.forEach(depId => {
                const depTaskGroup = currentStatus.nodes.find(n => n.id === depId);
                this.logger.log(`     * ${depId}: ${depTaskGroup?.status || 'not found'} (${depTaskGroup?.name || '未知'})`);
              });
            }
          } else if (taskGroup.status === 'pending' && taskGroup.dependencies.length > 0) {
            // 检查依赖状态，给出更详细的日志
            const depStatuses = taskGroup.dependencies.map(depId => {
              const depTaskGroup = currentStatus.nodes.find(n => n.id === depId);
              return `${depId}: ${depTaskGroup?.status || 'not found'} (${depTaskGroup?.name || '未知'})`;
            });
            this.logger.log(`⏳ 任务组 ${taskGroup.name} (ID: ${taskGroup.id}) 等待依赖完成:`);
            this.logger.log(`   - 当前状态: ${taskGroup.status}`);
            this.logger.log(`   - 依赖状态: ${depStatuses.join(', ')}`);
          } else if (taskGroup.status === 'pending') {
            this.logger.log(`📋 任务组 ${taskGroup.name} (ID: ${taskGroup.id}) 无依赖但状态为pending，应该准备就绪`);
          }

          return isReady;
        });

        // 在每次循环中都同步状态，确保及时检测到外部任务的失败
        const beforeSyncStatus = await this.getInternalWorkflowStatus(workflowId);
        const beforeFinishedCount = beforeSyncStatus.nodes.filter(n =>
          n.status === 'completed' || n.status === 'failed' || n.status === 'skipped'
        ).length;

        this.logger.log(`🔄 第${iterationCount}轮开始前状态同步:`);
        this.logger.log(`   - 同步前已结束任务组: ${beforeFinishedCount}`);

        await this.syncWorkflowSubTaskStatus(workflowId);

        const afterSyncStatus = await this.getInternalWorkflowStatus(workflowId);
        const afterFinishedCount = afterSyncStatus.nodes.filter(n =>
          n.status === 'completed' || n.status === 'failed' || n.status === 'skipped'
        ).length;

        this.logger.log(`🔄 第${iterationCount}轮状态同步结果:`);
        this.logger.log(`   - 同步前已结束任务组: ${beforeFinishedCount}`);
        this.logger.log(`   - 同步后已结束任务组: ${afterFinishedCount}`);

        // 如果状态同步导致了任务组状态变化，认为有进展
        if (afterFinishedCount > beforeFinishedCount) {
          hasProgress = true;
          this.logger.log(`📊 状态同步检测到进展: ${beforeFinishedCount} -> ${afterFinishedCount} 个任务组完成`);

          // 记录状态变化的详细信息
          const beforeStatusMap = new Map(beforeSyncStatus.nodes.map(n => [n.id, n.status]));
          afterSyncStatus.nodes.forEach(node => {
            const beforeStatus = beforeStatusMap.get(node.id);
            if (beforeStatus !== node.status) {
              this.logger.log(`   - 任务组状态变化: ${node.name} (${node.id}): ${beforeStatus} -> ${node.status}`);
            }
          });
        } else {
          this.logger.log(`📊 状态同步未检测到进展`);
        }

        // 并发执行所有准备好的任务组
        if (readyTaskGroups.length > 0) {
          hasProgress = true;
          this.logger.log(`📋 发现${readyTaskGroups.length}个可执行任务组: ${readyTaskGroups.map(n => n.name).join(', ')}`);

          // 逐个执行任务组（避免并发导致的资源竞争）
          for (const taskGroup of readyTaskGroups) {
            try {
              this.logger.log(`🚀 开始执行任务组: ${taskGroup.name} (ID: ${taskGroup.id})`);
              await this.executeWorkflowNode(workflowId, taskGroup.id);
              this.logger.log(`✅ 任务组执行成功: ${taskGroup.name}`);
            } catch (error) {
              this.logger.error(`❌ 任务组 ${taskGroup.name} 执行失败: ${error.message}`);
              this.logger.error(`❌ 任务组失败详细信息:`, error);

              // 立即同步工作流状态，确保失败状态被正确记录
              try {
                await this.syncWorkflowSubTaskStatus(workflowId);
                this.logger.log(`🔄 已同步工作流状态，任务组失败已记录`);
              } catch (syncError) {
                this.logger.error(`❌ 同步工作流状态失败:`, syncError);
              }

              // 继续执行其他任务组，不中断整个工作流
              // 但是失败的任务组状态已经被正确设置，工作流最终会检测到并设为失败
            }
          }
        }

        // 检查是否所有任务组都已完成
        const updatedStatus = await this.getInternalWorkflowStatus(workflowId);
        const allTaskGroups = updatedStatus.nodes;
        const completedTaskGroups = allTaskGroups.filter(taskGroup =>
          taskGroup.status === 'completed' || taskGroup.status === 'failed' || taskGroup.status === 'skipped'
        );
        const failedTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'failed');
        const successfulTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'completed');
        const skippedTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'skipped');
        const pendingTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'pending');
        const processingTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'processing');

        this.logger.log(`📊 工作流状态统计:`);
        this.logger.log(`  - 总任务组数: ${allTaskGroups.length}`);
        this.logger.log(`  - 已完成: ${successfulTaskGroups.length} (${successfulTaskGroups.map(tg => tg.name).join(', ') || '无'})`);
        this.logger.log(`  - 失败: ${failedTaskGroups.length} (${failedTaskGroups.map(tg => tg.name).join(', ') || '无'})`);
        this.logger.log(`  - 跳过: ${skippedTaskGroups.length} (${skippedTaskGroups.map(tg => tg.name).join(', ') || '无'})`);
        this.logger.log(`  - 待处理: ${pendingTaskGroups.length} (${pendingTaskGroups.map(tg => tg.name).join(', ') || '无'})`);
        this.logger.log(`  - 处理中: ${processingTaskGroups.length} (${processingTaskGroups.map(tg => tg.name).join(', ') || '无'})`);
        this.logger.log(`  - 已结束总数: ${completedTaskGroups.length}/${allTaskGroups.length}`);

        if (completedTaskGroups.length === allTaskGroups.length) {
          const completedSuccessGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'completed');
          const failedGroups = failedTaskGroups.length;
          const successGroups = completedSuccessGroups.length;

          this.logger.log(`🏁 工作流所有任务组已结束，开始计算最终状态:`);
          this.logger.log(`  - 成功任务组: ${successGroups}`);
          this.logger.log(`  - 失败任务组: ${failedGroups}`);
          this.logger.log(`  - 跳过任务组: ${skippedTaskGroups.length}`);

          // 只要有一个任务节点失败，整个工作流的状态就为失败
          let finalStatus: 'completed' | 'failed' | 'partial-success';
          if (failedGroups > 0) {
            // 有任何失败的任务组，整个工作流就是失败
            finalStatus = 'failed';
            this.logger.log(`❌ 工作流最终状态: 失败 (因为有${failedGroups}个任务组失败)`);

            // 详细记录失败的任务组信息
            failedTaskGroups.forEach(failedGroup => {
              this.logger.error(`❌ 失败任务组详情: ${failedGroup.name} (ID: ${failedGroup.id})`);
              this.logger.error(`   - 错误信息: ${failedGroup.error || '未知错误'}`);
              this.logger.error(`   - 子任务状态:`);
              failedGroup.subTasks.forEach(subTask => {
                this.logger.error(`     * ${subTask.name}: ${subTask.status} ${subTask.error ? `(错误: ${subTask.error})` : ''}`);
              });
            });
          } else {
            // 全部成功
            finalStatus = 'completed';
            this.logger.log(`✅ 工作流最终状态: 成功 (所有任务组都成功完成)`);
          }

          // 进度计算应该考虑失败的情况
          const actualProgress = finalStatus === 'failed' ?
            Math.round((completedTaskGroups.length / allTaskGroups.length) * 100) :
            100;

          this.logger.log(`📊 最终进度: ${actualProgress}%`);

          await this.updateWorkflowStatus(workflowId, {
            status: finalStatus,
            progress: actualProgress,
            endTime: new Date(),
            error: failedGroups > 0 ? `${failedGroups}个任务组执行失败` : undefined,
          });

          this.logger.log(`✅ 工作流执行完成: ${workflowId} (状态: ${finalStatus})`);
          break;
        }

        // 避免无限循环，如果没有进展且未完成，说明有问题
        if (!hasProgress && completedTaskGroups.length < allTaskGroups.length) {
          const pendingTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'pending');
          const processingTaskGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'processing');

          // 检查待处理的任务组是否因为依赖失败而无法执行（这是正常情况，不是死锁）
          const blockedByFailedDeps = pendingTaskGroups.filter(taskGroup =>
            taskGroup.dependencies.some(depId => {
              const depTaskGroup = allTaskGroups.find(n => n.id === depId);
              return depTaskGroup && depTaskGroup.status === 'failed';
            })
          );

          const realDeadlockedTasks = pendingTaskGroups.filter(taskGroup =>
            !taskGroup.dependencies.some(depId => {
              const depTaskGroup = allTaskGroups.find(n => n.id === depId);
              return depTaskGroup && depTaskGroup.status === 'failed';
            })
          );

          if (realDeadlockedTasks.length > 0 || processingTaskGroups.length > 0) {
            this.logger.error(`⚠️ 工作流执行陷入死锁: 真正死锁=${realDeadlockedTasks.length}, 处理中=${processingTaskGroups.length}, 因依赖失败被阻塞=${blockedByFailedDeps.length}`);
            realDeadlockedTasks.forEach(taskGroup => {
              this.logger.error(`  - 死锁任务组: ${taskGroup.name}, 依赖: ${taskGroup.dependencies.join(', ')}`);
            });

            throw new Error(`工作流执行陷入死锁，可能存在循环依赖或任务组处理异常。真正死锁任务组: ${realDeadlockedTasks.length}, 处理中任务组: ${processingTaskGroups.length}`);
          } else {
            // 所有待处理的任务组都是因为依赖失败而被阻塞，这是正常情况
            this.logger.log(`📋 所有待处理任务组都因依赖失败而被阻塞，工作流可以结束: 阻塞任务组=${blockedByFailedDeps.length}`);
            blockedByFailedDeps.forEach(taskGroup => {
              this.logger.log(`  - 被阻塞任务组: ${taskGroup.name}, 依赖: ${taskGroup.dependencies.join(', ')}`);
            });

            // 当工作流因依赖失败而无法继续时，立即设置最终状态
            this.logger.log(`🏁 工作流因依赖失败而无法继续，开始计算最终状态:`);

            const completedSuccessGroups = allTaskGroups.filter(taskGroup => taskGroup.status === 'completed');
            const failedGroups = failedTaskGroups.length;
            const successGroups = completedSuccessGroups.length;
            const blockedGroups = blockedByFailedDeps.length;

            this.logger.log(`  - 成功任务组: ${successGroups}`);
            this.logger.log(`  - 失败任务组: ${failedGroups}`);
            this.logger.log(`  - 被阻塞任务组: ${blockedGroups}`);

            // 有失败的任务组，整个工作流就是失败
            const finalStatus = 'failed';
            this.logger.log(`❌ 工作流最终状态: 失败 (有${failedGroups}个任务组失败，${blockedGroups}个任务组被阻塞)`);

            // 详细记录失败的任务组信息
            failedTaskGroups.forEach(failedGroup => {
              this.logger.error(`❌ 失败任务组详情: ${failedGroup.name} (ID: ${failedGroup.id})`);
              this.logger.error(`   - 错误信息: ${failedGroup.error || '未知错误'}`);
              this.logger.error(`   - 子任务状态:`);
              failedGroup.subTasks.forEach(subTask => {
                this.logger.error(`     * ${subTask.name}: ${subTask.status} ${subTask.error ? `(错误: ${subTask.error})` : ''}`);
              });
            });

            // 记录被阻塞的任务组信息
            blockedByFailedDeps.forEach(blockedGroup => {
              this.logger.warn(`⚠️ 被阻塞任务组: ${blockedGroup.name} (ID: ${blockedGroup.id})`);
              this.logger.warn(`   - 依赖: ${blockedGroup.dependencies.join(', ')}`);
            });

            // 计算进度：已完成的任务组（成功+失败）/ 总任务组
            const finishedTaskGroups = allTaskGroups.filter(taskGroup =>
              taskGroup.status === 'completed' || taskGroup.status === 'failed' || taskGroup.status === 'skipped'
            );
            const actualProgress = Math.round((finishedTaskGroups.length / allTaskGroups.length) * 100);

            this.logger.log(`📊 最终进度: ${actualProgress}%`);

            await this.updateWorkflowStatus(workflowId, {
              status: finalStatus,
              progress: actualProgress,
              endTime: new Date(),
              error: `${failedGroups}个任务组执行失败，${blockedGroups}个任务组被依赖阻塞`,
            });

            this.logger.log(`✅ 工作流执行完成: ${workflowId} (状态: ${finalStatus})`);

            // 跳出循环，让工作流正常结束
            break;
          }
        }

        // 短暂延迟，避免过于频繁的状态检查
        if (hasProgress) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (iterationCount >= maxIterations) {
        throw new Error(`工作流执行超过最大迭代次数 (${maxIterations})，可能存在死循环`);
      }

    } catch (error) {
      this.logger.error(`❌ 工作流执行失败: ${workflowId}`, error);

      try {
        await this.updateWorkflowStatus(workflowId, {
          status: 'failed',
          endTime: new Date(),
          error: error.message,
        });
      } catch (updateError) {
        this.logger.error(`❌ 更新工作流失败状态时出错: ${workflowId}`, updateError);
      }

      // 重新抛出错误
      throw error;
    }
  }

  /**
   * 同步工作流子任务状态
   */
  private async syncWorkflowSubTaskStatus(workflowId: string): Promise<void> {
    try {
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (!workflow || !workflow.metadata) {
        return;
      }

      const metadata = workflow.metadata as any;
      const workflowStatus = metadata.workflowStatus as WorkflowStatus;
      let hasUpdates = false;

      // 遍历所有任务组
      for (const taskGroup of workflowStatus.nodes) {
        if (taskGroup.taskGroupType === 'lanhu-transcode-group') {
          // 检查转码子任务状态，包括失败情况
          // 无论任务组状态如何，都要检查子任务的实际状态
          for (const subTask of taskGroup.subTasks) {
            // 检查所有未最终确定的转码子任务，而不仅仅是pending状态
            if (subTask.type === 'lanhu-transcode' &&
              !['completed', 'failed', 'skipped'].includes(subTask.status)) {
              const prototypeId = subTask.metadata.prototypeId;

              // 查询原型的最新状态
              const prototype = await this.prisma.designPagePrototype.findUnique({
                where: { id: prototypeId },
              });

              if (prototype) {
                if (prototype.resultHtml) {
                  // 成功情况：更新子任务状态为完成
                  subTask.status = 'completed';
                  subTask.progress = 100;
                  subTask.endTime = new Date();
                  subTask.result = {
                    success: true,
                    message: '转码已完成',
                    playgroundId: prototype.playgroundId,
                  };
                  hasUpdates = true;
                  this.logger.log(`🔄 同步子任务状态: ${subTask.name} -> completed`);
                } else if (prototype.status === 'failed') {
                  // 失败情况，更新子任务状态为失败
                  subTask.status = 'failed';
                  subTask.progress = 0;
                  subTask.endTime = new Date();
                  subTask.error = '原型转码失败';
                  subTask.result = {
                    success: false,
                    error: '原型转码失败',
                  };
                  hasUpdates = true;
                  this.logger.log(`🔄 同步子任务状态: ${subTask.name} -> failed`);
                }
              }
            }
          }

          // 无论是否有子任务状态更新，都要重新计算任务组状态
          // 这样可以确保即使子任务已经失败但任务组状态未更新的情况也能被正确处理
          const completedSubTasks = taskGroup.subTasks.filter(t => t.status === 'completed').length;
          const failedSubTasks = taskGroup.subTasks.filter(t => t.status === 'failed').length;
          const totalSubTasks = taskGroup.subTasks.length;
          const finishedSubTasks = completedSubTasks + failedSubTasks;

          if (finishedSubTasks === totalSubTasks) {
            // 所有子任务都完成了（成功或失败）
            if (failedSubTasks > 0) {
              // 有失败的子任务，任务组失败
              if (taskGroup.status !== 'failed') {
                taskGroup.status = 'failed';
                taskGroup.progress = 0;
                taskGroup.endTime = new Date();
                taskGroup.error = `${failedSubTasks}个转码子任务失败`;
                hasUpdates = true;
                this.logger.log(`🔄 同步任务组状态: ${taskGroup.name} -> failed (${failedSubTasks}个子任务失败)`);
              }
            } else {
              // 全部成功
              if (taskGroup.status !== 'completed') {
                taskGroup.status = 'completed';
                taskGroup.progress = 100;
                taskGroup.endTime = new Date();
                hasUpdates = true;
                this.logger.log(`🔄 同步任务组状态: ${taskGroup.name} -> completed`);
              }
            }
          } else {
            // 还有任务在进行中，更新进度
            const newProgress = Math.round((finishedSubTasks / totalSubTasks) * 100);
            if (taskGroup.progress !== newProgress) {
              taskGroup.progress = newProgress;
              hasUpdates = true;
            }
          }
        }

        // 恢复合并任务组状态同步，基于页面的resultHtml判断完成状态
        // 但只有在任务组处于processing或已完成状态时才同步，避免过早同步
        if (taskGroup.taskGroupType === 'merge-group') {
          if (taskGroup.status === 'processing' || taskGroup.status === 'completed') {
            for (const subTask of taskGroup.subTasks) {
              if (subTask.type === 'merge' && subTask.status !== 'completed') {
                const pageId = subTask.metadata.pageId;

                // 查询页面的最新状态
                const page = await this.prisma.designPage.findUnique({
                  where: { id: pageId },
                });

                if (page && page.resultHtml) {
                  // 更新子任务状态为完成
                  subTask.status = 'completed';
                  subTask.progress = 100;
                  subTask.endTime = new Date();
                  subTask.result = {
                    success: true,
                    message: '合并已完成',
                    playgroundId: page.playgroundId,
                  };

                  taskGroup.status = 'completed';
                  taskGroup.progress = 100;
                  taskGroup.endTime = new Date();
                  hasUpdates = true;
                  this.logger.log(`🔄 同步合并任务状态: ${subTask.name} -> completed`);
                }
              }
            }
          }
        }

        // 同步spec-to-prod-code任务组状态
        if (taskGroup.taskGroupType === 'spec-to-prod-code-group') {
          for (const subTask of taskGroup.subTasks) {
            if (subTask.type === 'spec-to-prod-code' && subTask.status === 'pending') {
              const pageId = subTask.metadata.pageId;

              // 通过工作流内部的taskId来查询任务状态
              // 检查当前子任务是否有关联的taskId，如果有则查询其状态
              if (subTask.taskId) {
                const relatedTask = await this.prisma.backgroundTask.findUnique({
                  where: { id: subTask.taskId },
                });

                if (relatedTask) {
                  // 正确处理完成和失败状态
                  if (relatedTask.status === 'completed') {
                    // 检查任务metadata中是否有错误，如果有错误就是失败
                    const taskMetadata = relatedTask.metadata as any;
                    const hasError = taskMetadata?.error || taskMetadata?.result?.error;

                    if (hasError) {
                      // 任务虽然完成但有错误，标记为失败
                      subTask.status = 'failed';
                      subTask.progress = 0;
                      subTask.endTime = new Date();
                      subTask.error = hasError;
                      subTask.result = {
                        success: false,
                        error: hasError,
                        taskId: relatedTask.id,
                      };

                      taskGroup.status = 'failed';
                      taskGroup.progress = 0;
                      taskGroup.endTime = new Date();
                      taskGroup.error = hasError;
                      hasUpdates = true;
                      this.logger.log(`🔄 同步生产级代码生成任务状态: ${subTask.name} -> failed (有错误: ${hasError})`);
                    } else {
                      // 真正成功完成
                      subTask.status = 'completed';
                      subTask.progress = 100;
                      subTask.endTime = new Date();
                      subTask.result = {
                        success: true,
                        message: '生产级代码生成已完成',
                        taskId: relatedTask.id,
                      };

                      taskGroup.status = 'completed';
                      taskGroup.progress = 100;
                      taskGroup.endTime = new Date();
                      hasUpdates = true;
                      this.logger.log(`🔄 同步生产级代码生成任务状态: ${subTask.name} -> completed (taskId: ${relatedTask.id})`);
                    }
                  } else if (relatedTask.status === 'failed') {
                    // 处理失败状态
                    const taskMetadata = relatedTask.metadata as any;
                    const errorMessage = taskMetadata?.error || taskMetadata?.result?.error || '任务执行失败';

                    subTask.status = 'failed';
                    subTask.progress = 0;
                    subTask.endTime = new Date();
                    subTask.error = errorMessage;
                    subTask.result = {
                      success: false,
                      error: errorMessage,
                      taskId: relatedTask.id,
                    };

                    taskGroup.status = 'failed';
                    taskGroup.progress = 0;
                    taskGroup.endTime = new Date();
                    taskGroup.error = errorMessage;
                    hasUpdates = true;
                    this.logger.log(`🔄 同步生产级代码生成任务状态: ${subTask.name} -> failed (taskId: ${relatedTask.id})`);
                  }
                }
              } else {
                // 如果没有taskId，尝试查找最近的相关任务
                // 可以通过页面相关的其他条件来判断
                this.logger.log(`⚠️ 生产级代码生成子任务 ${subTask.name} 没有关联的taskId，跳过状态同步`);
              }
            }
          }
        }
      }

      // 如果有更新，保存到数据库
      if (hasUpdates) {
        const newMetadata = {
          ...metadata,
          workflowStatus: workflowStatus,
        };

        await this.prisma.backgroundTask.update({
          where: { id: workflowId },
          data: {
            metadata: newMetadata as any,
            updated: new Date(),
          },
        });

        this.logger.log(`✅ 工作流子任务状态同步完成: ${workflowId}`);
      }
    } catch (error) {
      this.logger.error(`❌ 同步工作流子任务状态失败: ${workflowId}`, error);
      // 不抛出错误，避免影响工作流执行
    }
  }

  /**
   * 获取内部工作流状态（任务组结构）
   */
  private async getInternalWorkflowStatus(workflowId: string): Promise<WorkflowStatus> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow) {
      throw new Error(`工作流 ${workflowId} 不存在`);
    }

    if (!workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 状态信息不存在`);
    }

    const metadata = workflow.metadata as any;
    return metadata.workflowStatus as WorkflowStatus;
  }

  /**
   * 公共方法：同步工作流状态
   */
  async syncWorkflowStatus(workflowId: string): Promise<void> {
    return this.syncWorkflowSubTaskStatus(workflowId);
  }

  /**
   * 同步多页面工作流状态（立即执行一次状态检查）
   */
  async syncMultiPageWorkflowStatus(workflowId: string): Promise<void> {
    this.logger.log(`🔄 立即同步多页面工作流状态: ${workflowId}`);

    try {
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (!workflow || !workflow.metadata) {
        this.logger.warn(`⚠️ 工作流不存在或缺少元数据: ${workflowId}`);
        return;
      }

      const currentStatus = (workflow.metadata as any).workflowStatus as MultiPageWorkflowStatus;

      // 查询所有子工作流的最新状态
      const subWorkflowIds = currentStatus.pageWorkflows.map(pw => pw.workflowId).filter(Boolean);
      const subWorkflows = await this.prisma.backgroundTask.findMany({
        where: { id: { in: subWorkflowIds } },
      });

      let needsUpdate = false;
      let completedCount = 0;
      let failedCount = 0;
      let processingCount = 0;

      // 更新每个页面工作流的状态
      currentStatus.pageWorkflows.forEach(pageWorkflow => {
        if (!pageWorkflow.workflowId) return;

        const subWorkflow = subWorkflows.find(sw => sw.id === pageWorkflow.workflowId);
        if (subWorkflow) {
          const oldStatus = pageWorkflow.status;
          const oldProgress = pageWorkflow.progress;
          const newStatus = subWorkflow.status as any;
          const newProgress = subWorkflow.progress;

          if (oldStatus !== newStatus || oldProgress !== newProgress) {
            pageWorkflow.status = newStatus;
            pageWorkflow.progress = newProgress;
            needsUpdate = true;
            this.logger.log(`📝 立即同步页面状态: ${pageWorkflow.pageName} ${oldStatus} -> ${newStatus} (${newProgress}%)`);
          }
        }

        // 统计各种状态的数量
        if (pageWorkflow.status === 'completed') completedCount++;
        else if (pageWorkflow.status === 'failed') failedCount++;
        else if (pageWorkflow.status === 'processing') processingCount++;
      });

      // 计算整体状态
      const totalPages = currentStatus.totalPages;
      const finishedPages = completedCount + failedCount;
      const newProgress = Math.round((finishedPages / totalPages) * 100);
      let newStatus: MultiPageWorkflowStatus['status'];

      if (finishedPages === totalPages) {
        // 修复：根据完成和失败情况确定状态
        if (failedCount > 0 && completedCount > 0) {
          // 有成功有失败 = 部分成功
          newStatus = 'partial-success';
        } else if (failedCount > 0 && completedCount === 0) {
          // 全部失败
          newStatus = 'failed';
        } else {
          // 全部成功
          newStatus = 'completed';
        }
      } else if (processingCount > 0) {
        newStatus = 'processing';
      } else {
        newStatus = currentStatus.status;
      }

      // 更新状态
      const statusChanged = currentStatus.status !== newStatus;
      const progressChanged = currentStatus.progress !== newProgress;
      const countsChanged = currentStatus.completedPages !== completedCount || currentStatus.failedPages !== failedCount;

      if (needsUpdate || statusChanged || progressChanged || countsChanged) {
        currentStatus.progress = newProgress;
        currentStatus.status = newStatus;
        currentStatus.completedPages = completedCount;
        currentStatus.failedPages = failedCount;

        await this.prisma.backgroundTask.update({
          where: { id: workflowId },
          data: {
            metadata: {
              ...(workflow.metadata as any),
              workflowStatus: currentStatus,
            } as any,
            status: newStatus,
            progress: newProgress,
            updated: new Date(),
          },
        });

        this.logger.log(`✅ 多页面工作流状态立即同步完成: ${workflowId}, 状态: ${newStatus}, 进度: ${newProgress}%, 完成: ${completedCount}, 失败: ${failedCount}, 处理中: ${processingCount}`);
      } else {
        this.logger.log(`📝 多页面工作流状态无需更新: ${workflowId}`);
      }

    } catch (error) {
      this.logger.error(`❌ 立即同步多页面工作流状态失败: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * 重试现有工作流
   * 将失败或完成的工作流重新设置为处理中状态并重新执行
   */
  async restartWorkflow(workflowId: string, user: string): Promise<void> {
    this.logger.log(`🔄 重试工作流: ${workflowId}, 操作用户: ${user}`);

    try {
      // 1. 验证工作流是否存在
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (!workflow) {
        throw new Error(`工作流 ${workflowId} 不存在`);
      }

      if (!workflow.metadata) {
        throw new Error(`工作流 ${workflowId} 状态信息不存在`);
      }

      // 2. 检查工作流类型，确保是可重试的工作流
      const allowedTaskTypes = [
        'page-code-generation-workflow',
        'multi-page-code-generation-workflow'
      ];

      if (!allowedTaskTypes.includes(workflow.taskType)) {
        throw new Error(`工作流类型 ${workflow.taskType} 不支持重试`);
      }

      // 根据工作流类型采用不同的重启策略
      if (workflow.taskType === 'multi-page-code-generation-workflow') {
        // 多页面工作流：查找失败的子工作流并重启它们
        this.logger.log(`📊 处理多页面工作流重启: ${workflowId}`);

        const metadata = workflow.metadata as any;
        const multiWorkflowStatus = metadata.workflowStatus as MultiPageWorkflowStatus;

        // 找出失败的页面工作流
        const failedPageWorkflows = multiWorkflowStatus.pageWorkflows.filter(
          pw => pw.status === 'failed' && pw.workflowId
        );

        if (failedPageWorkflows.length === 0) {
          this.logger.log(`📝 没有失败的子工作流需要重试`);
          return;
        }

        this.logger.log(`🔄 即将重试 ${failedPageWorkflows.length} 个失败的子工作流`);

        // 重启失败的子工作流（单页面工作流）
        for (const pageWorkflow of failedPageWorkflows) {
          try {
            this.logger.log(`🔄 重试单页工作流: ${pageWorkflow.workflowId} (页面: ${pageWorkflow.pageName})`);
            await this.restartSinglePageWorkflow(pageWorkflow.workflowId, user);
            this.logger.log(`✅ 单页工作流重试成功: ${pageWorkflow.workflowId}`);
          } catch (error) {
            this.logger.error(`❌ 重试单页工作流失败: ${pageWorkflow.workflowId}`, error);
          }
        }

        // 启动多页面工作流监控
        setTimeout(() => {
          this.monitorMultiPageWorkflow(workflowId).catch(error => {
            this.logger.error(`❌ 重试后监控多页面工作流失败: ${workflowId}`, error);
          });
        }, 2000);

        this.logger.log(`✅ 多页面工作流重试完成: ${workflowId}`);

      } else {
        // 单页面工作流：重置状态并重新执行
        this.logger.log(`📊 处理单页面工作流重启: ${workflowId}`);
        await this.restartSinglePageWorkflow(workflowId, user);
      }

    } catch (error) {
      this.logger.error(`❌ 重试工作流失败: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * 重试单页面工作流的具体实现
   */
  private async restartSinglePageWorkflow(workflowId: string, user: string): Promise<void> {
    this.logger.log(`🔄 重试单页面工作流: ${workflowId}`);

    // 获取工作流信息
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow || !workflow.metadata) {
      throw new Error(`单页面工作流 ${workflowId} 信息不存在`);
    }

    // 重置工作流状态
    const metadata = workflow.metadata as any;
    const workflowStatus = metadata.workflowStatus as WorkflowStatus;

    // 重置所有失败的任务组状态为pending
    const resetNodes = workflowStatus.nodes.map(node => {
      if (node.status === 'failed') {
        this.logger.log(`📝 重置失败任务组: ${node.name}`);
        return {
          ...node,
          status: 'pending' as const,
          progress: 0,
          startTime: undefined,
          endTime: undefined,
          error: undefined,
          // 重置所有子任务状态
          subTasks: node.subTasks.map(subTask => ({
            ...subTask,
            status: 'pending' as const,
            progress: 0,
            startTime: undefined,
            endTime: undefined,
            error: undefined,
          }))
        };
      }
      return node;
    });

    // 更新工作流状态
    const updatedWorkflowStatus = {
      ...workflowStatus,
      status: 'processing' as const,
      progress: 0,
      startTime: new Date(),
      endTime: undefined,
      error: undefined,
      nodes: resetNodes,
    };

    // 更新数据库中的工作流状态
    await this.prisma.backgroundTask.update({
      where: { id: workflowId },
      data: {
        status: 'processing',
        progress: 0,
        metadata: {
          ...metadata,
          workflowStatus: updatedWorkflowStatus,
          restartedBy: user,
          restartedAt: new Date(),
          startTime: new Date(),
          endTime: null,
          error: null,
        } as any,
        updated: new Date(),
      },
    });

    this.logger.log(`✅ 单页面工作流状态重置完成，开始重新执行: ${workflowId}`);

    // 重新执行工作流
    setTimeout(() => {
      this.executeWorkflow(workflowId).catch(error => {
        this.logger.error(`❌ 重试单页面工作流执行失败: ${workflowId}`, error);
      });
    }, 1000);
  }



  /**
   * 执行单个工作流节点（任务组）
   */
  private async executeWorkflowNode(workflowId: string, nodeId: string): Promise<void> {
    this.logger.log(`🔄 开始执行任务组: ${nodeId}`);

    try {
      // 更新节点状态为处理中
      await this.updateNodeStatus(workflowId, nodeId, {
        status: 'processing',
        startTime: new Date(),
        progress: 0,
      });

      // 获取工作流信息
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (!workflow || !workflow.metadata) {
        throw new Error('工作流信息不存在');
      }

      const metadata = workflow.metadata as any;
      const workflowStatus = metadata.workflowStatus as WorkflowStatus;
      const node = workflowStatus.nodes.find(n => n.id === nodeId);

      if (!node) {
        throw new Error(`任务组 ${nodeId} 不存在`);
      }

      this.logger.log(`🎯 执行任务组: ${node.name} (类型: ${node.taskGroupType}, 子任务数: ${node.subTasks.length})`);

      // 根据任务组类型执行相应的逻辑
      let taskGroupResult: any;

      switch (node.taskGroupType) {
        case 'lanhu-transcode-group':
          taskGroupResult = await this.executeTranscodeTaskGroup(workflow, node);
          break;
        case 'merge-group':
          taskGroupResult = await this.executeMergeTaskGroup(workflow, node);
          break;
        case 'spec-to-prod-code-group':
          taskGroupResult = await this.executeSpecToProdCodeTaskGroup(workflow, node);
          break;
        default:
          throw new Error(`不支持的任务组类型: ${node.taskGroupType}`);
      }

      // 检查任务组执行结果，如果失败则抛出错误
      // 统一判断逻辑：只要result中有error信息，任务就失败，不管status是什么
      let isSuccess = false;
      let errorMessage = '';

      // 统一的错误检查逻辑：检查所有可能的错误字段
      const hasError = taskGroupResult.error ||
        taskGroupResult.waitCompletionFailed ||
        taskGroupResult.result?.error ||
        taskGroupResult.result?.waitCompletionFailed;

      if (node.taskGroupType === 'lanhu-transcode-group') {
        // 转码任务组返回 { success: boolean, ... }
        isSuccess = taskGroupResult.success === true && !hasError;
        errorMessage = taskGroupResult.error || taskGroupResult.message || '转码任务组执行失败';
      } else if (node.taskGroupType === 'merge-group') {
        // 合并任务组返回 backgroundTaskService 的结果
        isSuccess = taskGroupResult.status === 'completed' && !hasError;
        if (hasError) {
          errorMessage = taskGroupResult.error ||
            taskGroupResult.result?.error ||
            taskGroupResult.reason ||
            '代码合并任务执行失败';
        } else {
          errorMessage = `合并任务状态: ${taskGroupResult.status}`;
        }
      } else if (node.taskGroupType === 'spec-to-prod-code-group') {
        // 生产级代码生成任务组返回 backgroundTaskService 的结果
        isSuccess = taskGroupResult.status === 'completed' && !hasError;
        if (hasError) {
          errorMessage = taskGroupResult.error ||
            taskGroupResult.result?.error ||
            taskGroupResult.reason ||
            '生产级代码生成任务执行失败';
        } else {
          errorMessage = `生产级代码生成任务状态: ${taskGroupResult.status}`;
        }
      } else {
        // 默认检查
        isSuccess = taskGroupResult.success === true && !hasError;
        errorMessage = taskGroupResult.error || taskGroupResult.message || '任务组执行失败';
      }

      if (!isSuccess) {
        this.logger.error(`❌ 任务组执行结果检查失败:`);
        this.logger.error(`   - 任务组类型: ${node.taskGroupType}`);
        this.logger.error(`   - 返回结果: ${JSON.stringify(taskGroupResult, null, 2)}`);
        this.logger.error(`   - 错误信息: ${errorMessage}`);
        this.logger.error(`   - 检测到的错误字段:`);
        this.logger.error(`     * taskGroupResult.error: ${taskGroupResult.error}`);
        this.logger.error(`     * taskGroupResult.waitCompletionFailed: ${taskGroupResult.waitCompletionFailed}`);
        this.logger.error(`     * taskGroupResult.result?.error: ${taskGroupResult.result?.error}`);
        this.logger.error(`     * taskGroupResult.result?.waitCompletionFailed: ${taskGroupResult.result?.waitCompletionFailed}`);
        throw new Error(errorMessage);
      }

      this.logger.log(`✅ 任务组执行结果检查通过: ${node.taskGroupType}`);
      this.logger.log(`📋 任务组结果: ${JSON.stringify(taskGroupResult, null, 2)}`);

      // 更新节点状态为完成
      await this.updateNodeStatus(workflowId, nodeId, {
        status: 'completed',
        progress: 100,
        endTime: new Date(),
        taskId: taskGroupResult?.taskId || 'completed',
        result: taskGroupResult,
      });

      // 重要修复：同时更新子任务的taskId，以便状态同步机制能正确工作
      if (taskGroupResult?.taskId) {
        await this.updateSubTaskStatusByTaskGroup(workflowId, nodeId, node.taskGroupType, {
          taskId: taskGroupResult.taskId,
          status: 'completed',
          progress: 100,
          endTime: new Date(),
          result: taskGroupResult,
        });
      }

      this.logger.log(`✅ 任务组执行完成: ${nodeId}`);

    } catch (error) {
      this.logger.error(`❌ 任务组执行失败: ${nodeId}`, error);

      try {
        // 详细记录失败信息
        this.logger.error(`❌ 任务组失败详细信息:`);
        this.logger.error(`   - 任务组ID: ${nodeId}`);
        this.logger.error(`   - 错误类型: ${error.constructor.name}`);
        this.logger.error(`   - 错误消息: ${error.message}`);
        this.logger.error(`   - 错误堆栈: ${error.stack}`);

        await this.updateNodeStatus(workflowId, nodeId, {
          status: 'failed',
          progress: 0,
          endTime: new Date(),
          error: error.message,
        });

        // 当任务组失败时，同时更新所有子任务为失败状态（重要）
        await this.updateAllSubTasksToFailed(workflowId, nodeId, error.message);

        this.logger.log(`✅ 任务组失败状态已更新: ${nodeId}`);

        // 立即验证状态更新是否成功
        try {
          const updatedWorkflow = await this.getInternalWorkflowStatus(workflowId);
          const updatedNode = updatedWorkflow.nodes.find(n => n.id === nodeId);
          if (updatedNode) {
            this.logger.log(`🔍 验证任务组状态更新: ${updatedNode.name} -> ${updatedNode.status}`);
            if (updatedNode.status !== 'failed') {
              this.logger.error(`❌ 任务组状态更新失败！期望: failed, 实际: ${updatedNode.status}`);
            }
          }
        } catch (verifyError) {
          this.logger.error(`❌ 验证任务组状态更新失败:`, verifyError);
        }

      } catch (updateError) {
        this.logger.error(`❌ 更新任务组失败状态时出错: ${nodeId}`, updateError);
      }

      // 重新抛出错误，让调用方决定如何处理
      throw error;
    }
  }

  /**
   * 执行设计稿转码任务组（并发执行所有转码子任务）
   */
  private async executeTranscodeTaskGroup(workflow: any, node: WorkflowNode): Promise<any> {
    this.logger.log(`🔄 执行设计稿转码任务组: ${node.subTasks.length}个子任务`);

    // 注意：原型状态已经在工作流创建时重置过了，这里不需要重复重置
    this.logger.log(`📋 原型状态已在工作流创建时重置，开始检查原型HTML代码情况`);
    const allSubTasks = node.subTasks;

    // 检查原型是否有HTML代码，分别处理有HTML和只有图片的原型
    const prototypeIdsToCheck = allSubTasks.map(subTask => subTask.metadata.prototypeId);

    this.logger.log(`🔍 开始检查${prototypeIdsToCheck.length}个原型的HTML代码情况`);

    // 查询所有相关原型的详细信息
    const prototypes = await this.prisma.designPagePrototype.findMany({
      where: { id: { in: prototypeIdsToCheck } },
    });

    // 分类原型：有HTML代码的和只有图片的
    const prototypesWithHtml: any[] = [];
    const prototypesWithImageOnly: any[] = [];

    prototypes.forEach(prototype => {
      const hasHtml = !!(prototype.htmlContent && prototype.htmlContent.trim().length > 0);
      const hasImage = !!(prototype.imgFileLink);

      this.logger.log(`📋 原型 ${prototype.id} (${prototype.prototypeName}) 状况:`, {
        hasHtml,
        hasImage,
        htmlLength: prototype.htmlContent?.length || 0,
        imgFileLink: prototype.imgFileLink?.substring(0, 50) + (prototype.imgFileLink?.length > 50 ? '...' : ''),
      });

      if (hasHtml) {
        prototypesWithHtml.push(prototype);
      } else if (hasImage) {
        prototypesWithImageOnly.push(prototype);
      } else {
        this.logger.warn(`⚠️ 原型 ${prototype.id} 既没有HTML代码也没有图片，将跳过处理`);
      }
    });

    this.logger.log(`📊 原型分类结果:`, {
      总数: prototypes.length,
      有HTML代码: prototypesWithHtml.length,
      只有图片: prototypesWithImageOnly.length,
      无内容: prototypes.length - prototypesWithHtml.length - prototypesWithImageOnly.length,
    });

    // 先处理只有图片的原型，将图片转换为HTML代码
    if (prototypesWithImageOnly.length > 0) {
      this.logger.log(`🖼️ 开始处理${prototypesWithImageOnly.length}个只有图片的原型，先转换为HTML代码`);

      const imageToCodeResult = await this.executeImageToCodeForPrototypes(prototypesWithImageOnly, workflow);

      if (!imageToCodeResult.success) {
        this.logger.error(`❌ 图片转HTML步骤失败: ${imageToCodeResult.error}`);
        // 如果图片转HTML失败，整个转码任务组失败
        return {
          success: false,
          error: `图片转HTML步骤失败: ${imageToCodeResult.error}`,
          message: `转码任务组快速失败: 图片转HTML步骤失败`,
          completedSubTasks: 0,
          totalSubTasks: node.subTasks.length,
          failedSubTasks: prototypesWithImageOnly.length,
          results: [],
        };
      } else {
        this.logger.log(`✅ 图片转HTML步骤完成: 成功${imageToCodeResult.successPrototypes.length}个，失败${imageToCodeResult.failedPrototypes.length}个`);

        // 如果有失败的，记录但继续处理成功的
        if (imageToCodeResult.failedPrototypes.length > 0) {
          this.logger.warn(`⚠️ ${imageToCodeResult.failedPrototypes.length}个原型的图片转HTML失败，这些原型将被跳过转码处理`);
        }
      }
    } else {
      this.logger.log(`📋 没有只有图片的原型，直接进行转码处理`);
    }

    this.logger.log(`📋 开始并发执行${allSubTasks.length}个转码子任务`);

    // 检查原型是否仍然存在且有有效的HTML代码
    const validSubTasks: typeof allSubTasks = [];
    const skippedSubTasks: typeof allSubTasks = [];

    for (const subTask of allSubTasks) {
      const prototypeId = subTask.metadata.prototypeId;

      // 重新查询原型最新状态（可能在图片转HTML步骤中已更新）
      const prototype = await this.prisma.designPagePrototype.findUnique({
        where: { id: prototypeId },
      });

      if (!prototype) {
        this.logger.warn(`⚠️ 原型 ${prototypeId} 已被删除，跳过转码子任务: ${subTask.name}`);
        skippedSubTasks.push(subTask);
      } else if ((!prototype.htmlContent || prototype.htmlContent.trim().length === 0) && !(prototype?.slicedAssets as any)?.html?.content) {
        this.logger.warn(`⚠️ 原型 ${prototypeId} 没有HTML代码（可能是图片转HTML失败），跳过转码子任务: ${subTask.name}`);
        skippedSubTasks.push(subTask);
      } else {
        this.logger.log(`✅ 原型 ${prototypeId} 有有效的HTML代码（长度: ${prototype?.htmlContent?.length || (prototype?.slicedAssets as any)?.html?.content?.length}），将执行转码任务`);
        validSubTasks.push(subTask);
      }
    }

    // 处理跳过的子任务
    if (skippedSubTasks.length > 0) {
      this.logger.log(`📋 跳过${skippedSubTasks.length}个无效原型的转码子任务（删除或缺少HTML代码）`);
      // 注意：这里不需要更新子任务状态，因为我们要保持内部结构的一致性
    }

    // 不依赖子任务的当前状态，基于实际执行结果计算
    let successCount = 0; // 重置为0，基于实际执行结果计算
    let results: any[] = [];

    // 实现快速失败策略 - 任何一个子任务失败，整个任务组立即失败
    if (validSubTasks.length > 0) {
      this.logger.log(`🚀 开始快速失败模式执行${validSubTasks.length}个转码子任务`);

      try {
        const transcodePromises = validSubTasks.map(async (subTask, index) => {
          const prototypeId = subTask.metadata.prototypeId;
          this.logger.log(`🔄 执行转码子任务: ${subTask.name} (原型ID: ${prototypeId})`);

          const result = await this.backgroundTaskService.createAndExecuteTranscodeTask({
            projectId: workflow.designProjectId,
            prototypeIds: [prototypeId],
            user: workflow.user,
            model: workflow.model,
            enableAutoIteration: workflow.enableAutoIteration,
            enableStepByStep: workflow.enableStepByStep,
            waitForCompletion: true,
          });

          // 检查任务执行结果的实际状态，失败时立即抛出错误
          const resultAny = result as any;
          const taskFailed = resultAny.status === 'failed' || resultAny.error || !resultAny.taskCompleted;

          if (taskFailed) {
            const errorMsg = `转码子任务执行失败: ${subTask.name}, 状态: ${resultAny.status}, 错误: ${resultAny.error}`;
            this.logger.error(`❌ ${errorMsg}`);
            // 立即抛出错误，触发快速失败
            throw new Error(errorMsg);
          }

          this.logger.log(`✅ 转码子任务完成: ${subTask.name}`);
          return {
            subTaskId: subTask.id,
            success: true,
            result,
          };
        });

        // 使用Promise.all等待所有任务完成，任何一个失败都会立即抛出错误
        results = await Promise.all(transcodePromises);

        // 所有任务成功完成，更新子任务状态
        const newSuccessCount = results.filter(r => r.success).length;
        successCount = newSuccessCount;

        this.logger.log(`📊 设计稿转码任务组全部成功: 完成${newSuccessCount}, 跳过${skippedSubTasks.length}`);

        // 更新子任务状态为成功
        for (const result of results) {
          const subTask = node.subTasks.find(st => st.id === result.subTaskId);
          if (subTask) {
            subTask.status = 'completed';
            subTask.progress = 100;
            subTask.endTime = new Date();
            subTask.result = {
              success: true,
              message: '转码已完成',
              ...result.result,
            };
            this.logger.log(`🔄 直接更新子任务状态: ${subTask.name} -> completed`);
          }
        }

        // 保存子任务状态更新到数据库
        try {
          const newMetadata = {
            ...workflow.metadata,
            workflowStatus: workflow.metadata.workflowStatus,
          };

          await this.prisma.backgroundTask.update({
            where: { id: workflow.id },
            data: {
              metadata: newMetadata as any,
              updated: new Date(),
            },
          });

          this.logger.log(`💾 已保存子任务状态更新到数据库`);
        } catch (error) {
          this.logger.error(`❌ 保存子任务状态更新失败:`, error);
        }

      } catch (error) {
        // 任何子任务失败都导致整个任务组立即失败
        this.logger.error(`❌ 设计稿转码任务组快速失败: ${error.message}`);

        // 不修改其他子任务状态，让它们保持原有状态
        // 只有实际失败的子任务会由状态同步机制更新为failed
        this.logger.log(`📋 任务组快速失败，其他子任务保持原有状态，由状态同步机制处理`);

        // 立即返回失败结果，不再执行后续逻辑
        return {
          success: false,
          error: `转码任务组快速失败: ${error.message}`,
          message: `转码任务组快速失败: ${error.message}`,
          completedSubTasks: 0,
          totalSubTasks: node.subTasks.length,
          skippedSubTasks: skippedSubTasks.length,
          failedSubTasks: 1, // 只有1个实际失败的子任务
          results: [],
        };
      }
    }

    // 计算总体结果，基于实际执行结果判断
    const totalTasks = node.subTasks.length;
    const effectiveCompletedTasks = successCount + skippedSubTasks.length; // 已完成 + 跳过的
    const actualFailedTasks = results.filter(r => !r.success).length; // 基于实际执行结果
    const failedTasks = actualFailedTasks; // 使用实际失败数量

    // 🔧 修复判断逻辑：只有当所有有效任务都失败时，任务组才失败
    // 如果有部分成功，任务组应该是成功的，让后续任务可以继续
    if (failedTasks > 0 && successCount === 0 && validSubTasks.length > 0) {
      // 所有有效的转码任务都失败了，任务组失败
      return {
        success: false,
        error: `转码任务组执行失败: 所有${failedTasks}个有效子任务都失败`,
        message: `转码任务组执行失败: 成功${successCount}, 跳过${skippedSubTasks.length}, 失败${failedTasks}`,
        completedSubTasks: effectiveCompletedTasks,
        totalSubTasks: totalTasks,
        skippedSubTasks: skippedSubTasks.length,
        failedSubTasks: failedTasks,
        results,
      };
    }

    // 部分成功或全部成功，任务组都算成功
    const statusMessage = failedTasks > 0
      ? `转码任务组部分成功: 成功${successCount}, 跳过${skippedSubTasks.length}（含图片转HTML失败）, 失败${failedTasks}`
      : `转码任务组执行完成: 成功${successCount}, 跳过${skippedSubTasks.length}（含图片转HTML失败）`;

    return {
      success: true,
      message: statusMessage,
      completedSubTasks: effectiveCompletedTasks,
      totalSubTasks: totalTasks,
      skippedSubTasks: skippedSubTasks.length,
      failedSubTasks: failedTasks,
      results,
    };
  }

  /**
   * 执行代码合并任务组
   */
  private async executeMergeTaskGroup(workflow: any, node: WorkflowNode): Promise<any> {
    this.logger.log(`🔄 执行代码合并任务组`);

    // 合并任务组通常只有一个子任务
    const mergeSubTask = node.subTasks.find(subTask => subTask.type === 'merge');
    if (!mergeSubTask) {
      const error = '代码合并任务组中找不到合并子任务';
      this.logger.error(`❌ ${error}`);
      throw new Error(error);
    }

    const pageId = mergeSubTask.metadata.pageId;
    const pageName = mergeSubTask.metadata.pageName;

    this.logger.log(`🔄 开始执行代码合并任务: 页面ID=${pageId}, 页面名称=${pageName}`);
    this.logger.log(`📋 页面合并状态已在工作流创建时重置，直接开始执行合并任务`);

    let prototypeIds = mergeSubTask.metadata.prototypeIds || [];
    this.logger.log(`📋 原始原型ID列表: [${prototypeIds.join(', ')}] (数量: ${prototypeIds.length})`);

    // 验证原型是否仍然存在
    if (prototypeIds.length > 0) {
      this.logger.log(`🔍 验证原型是否存在...`);
      try {
        const existingPrototypes = await this.prisma.designPagePrototype.findMany({
          where: { id: { in: prototypeIds } },
        });

        this.logger.log(`📋 数据库中找到的原型数量: ${existingPrototypes.length}`);
        existingPrototypes.forEach(proto => {
          this.logger.log(`  - 原型ID: ${proto.id}, 名称: ${proto.prototypeName}, 状态: ${proto.status}, 有转码结果: ${!!proto.resultHtml}`);
        });

        const existingIds = new Set(existingPrototypes.map(p => p.id));
        const originalCount = prototypeIds.length;
        prototypeIds = prototypeIds.filter((id: string) => existingIds.has(id));

        if (prototypeIds.length < originalCount) {
          const deletedCount = originalCount - prototypeIds.length;
          const deletedIds = mergeSubTask.metadata.prototypeIds.filter((id: string) => !existingIds.has(id));
          this.logger.warn(`⚠️ 合并任务中发现${deletedCount}个已删除的原型，已排除: 页面${pageId}`);
          this.logger.warn(`⚠️ 已删除的原型ID: [${deletedIds.join(', ')}]`);
        }

        if (prototypeIds.length === 0) {
          const errorMsg = `所有原型都已被删除，无法执行合并任务: 页面${pageId}`;
          this.logger.error(`❌ ${errorMsg}`);
          // 所有原型都被删除是一个错误情况，不应该跳过
          throw new Error(errorMsg);
        }

        // 检查现有原型是否有转码结果
        const prototypesWithResults = existingPrototypes.filter(p => p.resultHtml);
        const prototypesWithoutResults = existingPrototypes.filter(p => !p.resultHtml);

        this.logger.log(`📊 原型转码结果统计:`);
        this.logger.log(`  - 有转码结果的原型: ${prototypesWithResults.length}个`);
        this.logger.log(`  - 没有转码结果的原型: ${prototypesWithoutResults.length}个`);

        if (prototypesWithoutResults.length > 0) {
          this.logger.warn(`⚠️ 以下原型缺少转码结果:`);
          prototypesWithoutResults.forEach(proto => {
            this.logger.warn(`  - 原型ID: ${proto.id}, 名称: ${proto.prototypeName}, 状态: ${proto.status}`);
          });
        }

        if (prototypesWithResults.length === 0) {
          const errorMsg = `所有原型转码都失败了，无法执行合并任务: 页面${pageId}`;
          this.logger.error(`❌ ${errorMsg}`);
          // 如果所有原型转码都失败，那确实是一个错误情况
          throw new Error(errorMsg);
        }

        // 如果只有部分原型转码成功，使用成功的原型进行合并
        if (prototypesWithoutResults.length > 0) {
          this.logger.warn(`⚠️ ${prototypesWithoutResults.length}个原型转码失败，将只使用${prototypesWithResults.length}个成功的原型进行合并`);
        }

        // 只使用有转码结果的原型进行合并
        prototypeIds = prototypesWithResults.map(p => p.id);
        this.logger.log(`📋 将使用有转码结果的原型进行合并: [${prototypeIds.join(', ')}]`);

      } catch (error) {
        if (error.message.includes('所有原型都')) {
          // 重新抛出我们自己的错误
          throw error;
        }
        const errorMsg = `查询原型信息失败: ${error.message}`;
        this.logger.error(`❌ ${errorMsg}`);
        throw new Error(errorMsg);
      }
    } else {
      const errorMsg = `合并任务没有配置原型ID列表: 页面${pageId}`;
      this.logger.error(`❌ ${errorMsg}`);
      throw new Error(errorMsg);
    }

    this.logger.log(`🚀 开始执行代码合并: 页面${pageId}, 有效原型数量${prototypeIds.length}`);
    this.logger.log(`📋 有效原型ID列表: [${prototypeIds.join(', ')}]`);

    try {
      const result = await this.backgroundTaskService.createAndExecuteMergeTask({
        projectId: workflow.designProjectId,
        prototypeIds,
        projectName: `${pageName}_合并结果`,
        user: workflow.user,
        model: workflow.model,
        enableAutoIteration: workflow.enableAutoIteration,
        enableStepByStep: workflow.enableStepByStep,
        waitForCompletion: true,
      });

      this.logger.log(`✅ 代码合并任务执行完成: 页面${pageId}`);
      this.logger.log(`📋 合并结果: ${JSON.stringify(result, null, 2)}`);

      return result;
    } catch (error) {
      const errorMsg = `代码合并任务执行失败: 页面${pageId}, 错误: ${error.message}`;
      this.logger.error(`❌ ${errorMsg}`);
      this.logger.error(`❌ 详细错误信息:`, error);
      throw new Error(errorMsg);
    }
  }

  /**
   * 执行生产级代码生成任务组
   */
  private async executeSpecToProdCodeTaskGroup(workflow: any, node: WorkflowNode): Promise<any> {
    this.logger.log(`🔄 执行生产级代码生成任务组`);

    // 生产级代码生成任务组通常只有一个子任务
    const prodCodeSubTask = node.subTasks.find(subTask => subTask.type === 'spec-to-prod-code');
    if (!prodCodeSubTask) {
      throw new Error('生产级代码生成任务组中找不到代码生成子任务');
    }

    const pageId = prodCodeSubTask.metadata.pageId;

    // 获取页面信息
    const page = await this.prisma.designPage.findUnique({
      where: { id: pageId },
    });

    if (!page) {
      throw new Error(`页面 ${pageId} 不存在`);
    }

    if (!page.resultHtml) {
      throw new Error(`页面 ${pageId} 没有合并结果，需要先执行合并任务`);
    }

    // 从 workflow.metadata 中获取配置信息
    const metadata = workflow.metadata as any;

    this.logger.log(`🔄 执行页面级代码生成: ${pageId}`);

    return await this.backgroundTaskService.createAndExecuteGenericTask({
      taskType: 'spec-to-prod-code',
      taskName: `页面级代码生成 - ${page.name}`,
      items: [{
        id: `page-${pageId}`,
        name: page.name,
        metadata: {
          gitUrl: metadata.gitUrl,
          branch: metadata.branch || 'master',
          projectId: workflow.designProjectId,
          page: {
            name: page.name,
            htmlContent: page.resultHtml,
            description: page.description,
          },
          taskName: `页面级代码生成 - ${page.name}`,
          pageIndex: 0,
          totalPages: 1,
          // 关键：传递工作流ID信息，用于中间分支命名
          workflowId: workflow.id, // 单页面工作流ID
          workflowType: 'page-code-generation-workflow',
        },
      }],
      user: workflow.user,
      model: workflow.model,
      enableAutoIteration: workflow.enableAutoIteration,
      enableStepByStep: workflow.enableStepByStep,
      metadata: {
        projectId: workflow.designProjectId,
        pageId,
        // 关键：在任务级别的metadata中也添加工作流信息
        workflowId: workflow.id,
        workflowType: 'page-code-generation-workflow',
      },
      waitForCompletion: true,
    });
  }

  /**
   * 更新工作流状态
   */
  private async updateWorkflowStatus(
    workflowId: string,
    updates: Partial<WorkflowStatus>
  ): Promise<void> {
    this.logger.log(`🔄 开始更新工作流状态: ${workflowId}`);
    this.logger.log(`📝 状态更新内容: ${JSON.stringify(updates, null, 2)}`);

    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow || !workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 不存在或状态信息缺失`);
    }

    const metadata = workflow.metadata as any;
    const current = metadata.workflowStatus as WorkflowStatus;
    const updated = { ...current, ...updates };

    this.logger.log(`📊 状态更新前: ${current.status} -> 更新后: ${updated.status}`);
    this.logger.log(`📊 进度更新前: ${current.progress}% -> 更新后: ${updates.progress !== undefined ? updates.progress : '计算中'}%`);

    // 计算整体进度
    if (updates.progress === undefined) {
      const completedNodes = updated.nodes.filter(n => n.status === 'completed').length;
      updated.progress = Math.round((completedNodes / updated.nodes.length) * 100);
      this.logger.log(`📊 自动计算进度: ${updated.progress}% (${completedNodes}/${updated.nodes.length} 个节点完成)`);
    }

    // 更新metadata中的workflowStatus
    const newMetadata = {
      ...metadata,
      workflowStatus: updated,
    };

    try {
      await this.prisma.backgroundTask.update({
        where: { id: workflowId },
        data: {
          metadata: newMetadata as any,
          status: updated.status,
          progress: updated.progress,
          updated: new Date(),
        },
      });

      this.logger.log(`✅ 工作流状态更新成功: ${workflowId}`);
      this.logger.log(`📝 最终状态: ${updated.status}, 进度: ${updated.progress}%`);
      if (updated.error) {
        this.logger.log(`❌ 错误信息: ${updated.error}`);
      }

    } catch (error) {
      this.logger.error(`❌ 工作流状态更新失败: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * 更新节点状态
   */
  private async updateNodeStatus(
    workflowId: string,
    nodeId: string,
    updates: Partial<WorkflowNode>
  ): Promise<void> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow || !workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 不存在或状态信息缺失`);
    }

    const metadata = workflow.metadata as any;
    const current = metadata.workflowStatus as WorkflowStatus;

    // 在任务组结构中查找节点
    const taskGroupIndex = current.nodes.findIndex(n => n.id === nodeId);

    if (taskGroupIndex === -1) {
      throw new Error(`任务组 ${nodeId} 不存在`);
    }

    // 更新任务组状态
    current.nodes[taskGroupIndex] = { ...current.nodes[taskGroupIndex], ...updates };

    // 重新计算整体进度，包含已完成和失败的任务组
    const finishedTaskGroups = current.nodes.filter(n =>
      n.status === 'completed' || n.status === 'failed' || n.status === 'skipped'
    ).length;
    const updatedProgress = Math.round((finishedTaskGroups / current.nodes.length) * 100);

    // 更新整个工作流状态
    await this.updateWorkflowStatus(workflowId, {
      nodes: current.nodes,
      progress: updatedProgress,
    });
  }

  /**
   * 更新子任务状态
   */
  private async updateSubTaskStatus(
    workflowId: string,
    taskGroupId: string,
    updates: Partial<WorkflowSubTask>
  ): Promise<void> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow || !workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 不存在或状态信息缺失`);
    }

    const metadata = workflow.metadata as any;
    const current = metadata.workflowStatus as WorkflowStatus;

    // 在任务组结构中查找任务组
    const taskGroupIndex = current.nodes.findIndex(n => n.id === taskGroupId);

    if (taskGroupIndex === -1) {
      throw new Error(`任务组 ${taskGroupId} 不存在`);
    }

    // 在任务组中查找子任务
    const subTaskIndex = current.nodes[taskGroupIndex].subTasks.findIndex(st => st.id === updates.id);

    if (subTaskIndex === -1) {
      throw new Error(`子任务 ${updates.id} 不存在于任务组 ${taskGroupId}`);
    }

    // 更新子任务状态
    current.nodes[taskGroupIndex].subTasks[subTaskIndex] = { ...current.nodes[taskGroupIndex].subTasks[subTaskIndex], ...updates };

    // 重新计算任务组进度
    const completedSubTasks = current.nodes[taskGroupIndex].subTasks.filter(st => st.status === 'completed').length;
    const totalSubTasks = current.nodes[taskGroupIndex].subTasks.length;
    const updatedProgress = Math.round((completedSubTasks / totalSubTasks) * 100);

    // 更新任务组状态
    await this.updateNodeStatus(workflowId, taskGroupId, {
      progress: updatedProgress,
    });
  }

  /**
   * 将任务组的所有子任务状态设置为失败
   */
  private async updateAllSubTasksToFailed(
    workflowId: string,
    taskGroupId: string,
    errorMessage: string
  ): Promise<void> {
    try {
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
      });

      if (!workflow || !workflow.metadata) {
        return;
      }

      const metadata = workflow.metadata as any;
      const current = metadata.workflowStatus as WorkflowStatus;

      // 在任务组结构中查找任务组
      const taskGroupIndex = current.nodes.findIndex(n => n.id === taskGroupId);

      if (taskGroupIndex === -1) {
        return;
      }

      const taskGroup = current.nodes[taskGroupIndex];
      let hasUpdates = false;

      // 更新所有子任务为失败状态
      for (const subTask of taskGroup.subTasks) {
        if (subTask.status !== 'failed') {
          subTask.status = 'failed';
          subTask.progress = 0;
          subTask.endTime = new Date();
          subTask.error = errorMessage;
          subTask.result = {
            success: false,
            error: errorMessage,
          };
          hasUpdates = true;
          this.logger.log(`🔄 更新子任务为失败状态: ${subTask.name} -> failed`);
        }
      }

      if (hasUpdates) {
        const newMetadata = {
          ...metadata,
          workflowStatus: current,
        };

        await this.prisma.backgroundTask.update({
          where: { id: workflowId },
          data: {
            metadata: newMetadata as any,
            updated: new Date(),
          },
        });

        this.logger.log(`✅ 任务组 ${taskGroupId} 的所有子任务状态已更新为失败`);
      }
    } catch (error) {
      this.logger.error(`❌ 更新任务组子任务失败状态时出错: ${taskGroupId}`, error);
    }
  }

  /**
   * 根据任务组类型更新子任务状态
   */
  private async updateSubTaskStatusByTaskGroup(
    workflowId: string,
    taskGroupId: string,
    taskGroupType: WorkflowNode['taskGroupType'],
    updates: Partial<WorkflowSubTask>
  ): Promise<void> {
    const workflow = await this.prisma.backgroundTask.findUnique({
      where: { id: workflowId },
    });

    if (!workflow || !workflow.metadata) {
      throw new Error(`工作流 ${workflowId} 不存在或状态信息缺失`);
    }

    const metadata = workflow.metadata as any;
    const current = metadata.workflowStatus as WorkflowStatus;

    // 在任务组结构中查找任务组
    const taskGroupIndex = current.nodes.findIndex(n => n.id === taskGroupId);

    if (taskGroupIndex === -1) {
      throw new Error(`任务组 ${taskGroupId} 不存在`);
    }

    const taskGroup = current.nodes[taskGroupIndex];
    let hasUpdates = false;

    // 根据任务组类型更新对应的子任务
    if (taskGroupType === 'spec-to-prod-code-group') {
      // 更新spec-to-prod-code类型的子任务
      for (const subTask of taskGroup.subTasks) {
        if (subTask.type === 'spec-to-prod-code') {
          Object.assign(subTask, updates);
          hasUpdates = true;
          this.logger.log(`🔄 更新生产级代码生成子任务状态: ${subTask.name} -> ${updates.status} (taskId: ${updates.taskId})`);
        }
      }
    } else if (taskGroupType === 'merge-group') {
      // 更新merge类型的子任务
      for (const subTask of taskGroup.subTasks) {
        if (subTask.type === 'merge') {
          Object.assign(subTask, updates);
          hasUpdates = true;
          this.logger.log(`🔄 更新合并子任务状态: ${subTask.name} -> ${updates.status} (taskId: ${updates.taskId})`);
        }
      }
    }
    // 对于lanhu-transcode-group，由于有多个子任务，需要特殊处理，这里暂时跳过

    if (hasUpdates) {
      // 重新计算任务组进度
      const completedSubTasks = taskGroup.subTasks.filter(st => st.status === 'completed').length;
      const totalSubTasks = taskGroup.subTasks.length;
      const updatedProgress = Math.round((completedSubTasks / totalSubTasks) * 100);

      // 保存更新到数据库
      const newMetadata = {
        ...metadata,
        workflowStatus: current,
      };

      await this.prisma.backgroundTask.update({
        where: { id: workflowId },
        data: {
          metadata: newMetadata as any,
          updated: new Date(),
        },
      });

      this.logger.log(`✅ 子任务状态更新完成: ${taskGroupId}, 任务组进度: ${updatedProgress}%`);
    }
  }

  /**
   * 将任务组依赖关系转换为子任务依赖关系
   */
  private convertTaskGroupDependenciesToSubTaskDependencies(
    currentTaskGroup: WorkflowNode,
    allTaskGroups: WorkflowNode[],
    currentSubTask: WorkflowSubTask
  ): string[] {
    const dependencies: string[] = [];

    // 遍历当前任务组的依赖
    currentTaskGroup.dependencies.forEach(depTaskGroupId => {
      const depTaskGroup = allTaskGroups.find(tg => tg.id === depTaskGroupId);
      if (depTaskGroup) {
        // 将依赖任务组的所有子任务作为当前子任务的依赖
        depTaskGroup.subTasks.forEach(depSubTask => {
          dependencies.push(depSubTask.id);
        });
      }
    });

    // 如果当前任务组内有多个子任务，需要处理组内依赖关系
    // 对于合并任务，它依赖于同组内所有转码任务（虽然这种情况在当前设计中不存在）
    // 对于代码生成任务，它依赖于合并任务

    return dependencies;
  }

  /**
   * 获取工作流相关的Merge Request列表
   */
  async getWorkflowMergeRequests(projectId: string, workflowId: string): Promise<any[]> {
    try {
      this.logger.log(`🔍 开始查询工作流 ${workflowId} 的MR列表`);

      // 1. 获取工作流信息
      const workflow = await this.prisma.backgroundTask.findUnique({
        where: { id: workflowId },
        include: {
          items: true,
        },
      });

      if (!workflow) {
        throw new Error(`工作流 ${workflowId} 不存在`);
      }

      // 2. 获取项目的Git配置
      const project = await this.prisma.project.findUnique({
        where: { id: projectId },
      });

      if (!project) {
        throw new Error(`项目 ${projectId} 不存在`);
      }

      const gitUrl = project.gitUrl;
      const gitBranch = project.gitBranch || 'dev';

      if (!gitUrl) {
        throw new Error(`项目 ${projectId} 未配置Git仓库`);
      }

      // 3. 根据工作流类型查询MR
      if (workflow.taskType === 'page-code-generation-workflow') {
        // 单页面工作流：查找包含该工作流ID的MR
        return await this.findMergeRequestsByWorkflowId(gitUrl, gitBranch, workflowId);
      } else if (workflow.taskType === 'multi-page-code-generation-workflow') {
        // 多页面工作流：先查找该多页面工作流ID相关的MR，再查找其子工作流的MR
        const metadata = workflow.metadata as any;
        const workflowStatus = metadata?.workflowStatus;
        if (!workflowStatus?.pageWorkflows) {
          throw new Error(`工作流 ${workflowId} 缺少页面信息`);
        }

        const allMRs = [];

        // 查找多页面工作流本身的MR（如果有的话）
        const multiWorkflowMRs = await this.findMergeRequestsByWorkflowId(gitUrl, gitBranch, workflowId);
        allMRs.push(...multiWorkflowMRs);

        // 查找其子页面工作流的MR
        for (const pageWorkflow of workflowStatus.pageWorkflows) {
          if (pageWorkflow.workflowId) {
            const pageMRs = await this.findMergeRequestsByWorkflowId(
              gitUrl,
              gitBranch,
              pageWorkflow.workflowId
            );
            allMRs.push(...pageMRs);
          }
        }

        return allMRs;
      } else {
        throw new Error(`不支持的工作流类型: ${workflow.taskType}`);
      }

    } catch (error) {
      this.logger.error(`❌ 查询工作流MR列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取页面相关的Merge Request列表
   */
  async getPageMergeRequests(projectId: string, pageId: string): Promise<any[]> {
    try {
      this.logger.log(`🔍 开始查询页面 ${pageId} 的MR列表`);

      // 1. 获取页面信息
      const page = await this.prisma.designPage.findUnique({
        where: { id: pageId },
      });

      if (!page) {
        throw new Error(`页面 ${pageId} 不存在`);
      }

      // 2. 获取项目的Git配置
      const project = await this.prisma.project.findUnique({
        where: { id: projectId },
      });

      if (!project) {
        throw new Error(`项目 ${projectId} 不存在`);
      }

      const gitUrl = project.gitUrl;
      const gitBranch = project.gitBranch || 'dev';

      if (!gitUrl) {
        throw new Error(`项目 ${projectId} 未配置Git仓库`);
      }

      // 3. 查找该页面相关的所有工作流，然后查找这些工作流的MR
      const pageWorkflows = await this.prisma.backgroundTask.findMany({
        where: {
          designProjectId: projectId,
          taskType: {
            in: ['page-code-generation-workflow', 'multi-page-code-generation-workflow']
          }
        },
        orderBy: { created: 'desc' },
      });

      // 过滤出包含该页面ID的工作流
      const filteredWorkflows = pageWorkflows.filter(workflow => {
        const metadata = workflow.metadata as any;
        return metadata?.pageId === pageId;
      });

      this.logger.log(`📋 找到页面 ${pageId} 相关的 ${filteredWorkflows.length} 个工作流`);

      const allMRs = [];
      for (const workflow of filteredWorkflows) {
        const workflowMRs = await this.findMergeRequestsByWorkflowId(gitUrl, gitBranch, workflow.id);
        allMRs.push(...workflowMRs);
      }

      return allMRs;

    } catch (error) {
      this.logger.error(`❌ 查询页面MR列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 根据工作流ID查找相关的Merge Request
   * 分支命名规则：feature/${pageName}-${currentVersion}-${workflowId}
   */
  private async findMergeRequestsByWorkflowId(
    gitUrl: string,
    targetBranch: string,
    workflowId: string
  ): Promise<any[]> {
    try {
      // 解析Git URL，使用GitlabUtil的现有方法
      const gitUrlInfo = GitlabUtil.parseGitUrl(gitUrl);
      if (!gitUrlInfo) {
        throw new Error(`无法解析Git URL: ${gitUrl}`);
      }

      const gitlabEnv = GitlabUtil.getGitlabEnv(gitUrl);
      if (!gitlabEnv) {
        throw new Error(`无法识别GitLab环境: ${gitUrl}`);
      }

      const token = GitlabUtil.getToken(gitlabEnv);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      // 获取项目ID，使用GitlabUtil的现有方法
      const projectId = await GitlabUtil.getProjectId(
        gitlabEnv,
        gitUrlInfo.namespace,
        gitUrlInfo.projectName
      );

      if (!projectId) {
        throw new Error(`无法获取项目ID: ${gitUrlInfo.namespace}/${gitUrlInfo.projectName}`);
      }

      const gitDomain = GitlabUtil.getGitDomainForEnv(gitlabEnv);
      const searchMRUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests`;

      this.logger.log(`🔍 搜索工作流 ${workflowId} 相关的MR: ${searchMRUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(searchMRUrl, {
        params: {
          target_branch: targetBranch,
          state: 'all', // 查找所有状态的MR（opened, closed, merged）
          per_page: 100, // 增加返回数量
        },
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 30000
      });

      const allMRs = response.data || [];

      // 过滤出与该工作流ID相关的MR
      // 分支命名规则：feature/${pageName}-${currentVersion}-${workflowId}
      const workflowRelatedMRs = allMRs.filter(mr => {
        if (!mr.source_branch) return false;

        // 检查分支名称是否以工作流ID结尾
        return mr.source_branch.endsWith(`-${workflowId}`);
      });

      // 增强MR信息
      const enhancedMRs = workflowRelatedMRs.map(mr => {
        // 从分支名称中提取页面名称
        // 分支格式：feature/${pageName}-${currentVersion}-${workflowId}
        let pageName = '未知页面';
        if (mr.source_branch) {
          // 提取 feature/ 后面到最后一个连字符之前的部分，然后去掉版本号部分
          const match = mr.source_branch.match(/^feature\/(.+)-([^-]+)-([^-]+)$/);
          if (match) {
            pageName = match[1]; // 页面名称部分
          }
        }

        return {
          ...mr,
          pageName,
          workflowId,
          gitlabEnv,
          projectInfo: {
            namespace: gitUrlInfo.namespace,
            projectName: gitUrlInfo.projectName,
          }
        };
      });

      this.logger.log(`📋 找到 ${enhancedMRs.length} 个与工作流 "${workflowId}" 相关的MR`);
      if (enhancedMRs.length > 0) {
        this.logger.log(`📝 相关分支: ${enhancedMRs.map(mr => mr.source_branch).join(', ')}`);
      }

      return enhancedMRs;

    } catch (error) {
      this.logger.error(`❌ 查找工作流MR失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 执行图片转HTML任务（用于没有HTML代码的原型）
   * @param prototypes 需要处理的原型列表
   * @param workflow 工作流信息
   * @returns 返回执行结果，包括成功和失败的原型信息
   */
  private async executeImageToCodeForPrototypes(
    prototypes: any[],
    workflow: any
  ): Promise<{
    success: boolean;
    successPrototypes: string[];
    failedPrototypes: string[];
    error?: string;
  }> {
    this.logger.log(`🖼️ 开始执行图片转HTML任务，原型数量: ${prototypes.length}`);

    if (prototypes.length === 0) {
      return {
        success: true,
        successPrototypes: [],
        failedPrototypes: [],
      };
    }

    try {
      this.logger.log(`🚀 开始快速失败模式执行${prototypes.length}个图片转HTML任务`);

      // 为每个原型创建单独的任务，使用Promise.all并发执行
      const imageToCodePromises = prototypes.map(async (prototype) => {
        this.logger.log(`🔄 执行原型 ${prototype.id} 的图片转HTML任务: ${prototype.prototypeName}`);

        // 准备单个原型的图片转代码任务参数
        const resolvedImageContent = await this.designProjectService.resolveImageContent(
          prototype.imageContent,
          prototype.imgFileLink,
        );

        const singleItem = {
          id: prototype.id,
          name: `图片转HTML_${prototype.prototypeName}`,
          metadata: {
            imageContent: resolvedImageContent,
            // 移除imageUrl，避免重复
            imageUrl: undefined,
          },
        };

        // 调用单个原型的图片转代码任务
        const imgToCodeResult = await this.backgroundTaskService.createAndExecuteGenericTask({
          taskType: 'img-to-code',
          taskName: `工作流图片转HTML任务_${prototype.prototypeName}_${workflow.id}`,
          items: [singleItem], // 只包含一个任务
          user: workflow.user,
          model: workflow.model,
          enableAutoIteration: workflow.enableAutoIteration || false,
          enableStepByStep: workflow.enableStepByStep || false,
          metadata: {
            projectId: workflow.designProjectId, // 项目id
            workflowId: workflow.id,
            workflowType: 'page-code-generation-workflow',
            isImageToCodeStep: true, // 标识这是图片转HTML步骤
            prototypeId: prototype.id, // 添加原型ID标识
          },
          waitForCompletion: true, // 等待任务完成
        });


        this.logger.log('图片转代码任务执行结果：', imgToCodeResult)

        // 检查任务执行结果 - 适配不同的返回类型
        const taskResult = imgToCodeResult as any;
        const isCompleted = taskResult.taskCompleted || taskResult.success;
        const hasError = taskResult.error || taskResult.waitCompletionFailed;

        if (!isCompleted || hasError) {
          const errorMsg = `原型 ${prototype.prototypeName} 图片转HTML任务失败: ${taskResult.error || taskResult.reason || '任务未完成'}`;
          this.logger.error(`❌ ${errorMsg}`);
          // 立即抛出错误，触发快速失败
          throw new Error(errorMsg);
        }
        // 解析任务结果，直接从taskResult中获取files
        if (!taskResult.files || Object.keys(taskResult.files).length === 0) {
          const errorMsg = `原型 ${prototype.prototypeName} 图片转HTML任务未返回文件结果`;
          this.logger.error(`❌ ${errorMsg}`);
          throw new Error(errorMsg);
        }

        this.logger.log(`📝 处理原型 ${prototype.id} 的任务结果:`, {
          hasPlaygroundId: !!taskResult.playgroundId,
          status: taskResult.status,
          hasFiles: !!taskResult.files,
          fileCount: taskResult.fileCount,
        });

        // 检查是否有playground ID
        if (!taskResult.playgroundId) {
          const errorMsg = `原型 ${prototype.prototypeName} 图片转HTML任务结果中没有playground ID`;
          this.logger.error(`❌ ${errorMsg}`);
          throw new Error(errorMsg);
        }

        // 直接从files中获取HTML文件
        const htmlFileName = Object.keys(taskResult.files).find(fileName => fileName.endsWith('.html'));
        if (!htmlFileName) {
          const errorMsg = `原型 ${prototype.prototypeName} 图片转HTML任务未生成HTML代码文件`;
          this.logger.error(`❌ ${errorMsg}`);
          throw new Error(errorMsg);
        }

        const htmlContent = taskResult.files[htmlFileName]?.content;
        if (!htmlContent) {
          const errorMsg = `原型 ${prototype.prototypeName} 图片转HTML任务生成的HTML代码文件内容为空`;
          this.logger.error(`❌ ${errorMsg}`);
          throw new Error(errorMsg);
        }

        // 更新原型的HTML内容
        await this.prisma.designPagePrototype.update({
          where: { id: prototype.id },
          data: {
            slicedAssets: {
              html: {
                name: htmlFileName,
                content: htmlContent,
              },
            },
            // 保留原有的playgroundId如果存在
            playgroundId: taskResult.playgroundId,
          },
        });

        this.logger.log(`✅ 原型 ${prototype.prototypeName} 图片转HTML任务完成`);
        return {
          prototypeId: prototype.id,
          success: true,
          result: imgToCodeResult,
        };
      });

      // 使用Promise.all等待所有任务完成，任何一个失败都会立即抛出错误
      const results = await Promise.all(imageToCodePromises);

      // 所有任务成功完成
      const successPrototypes = results.map(r => r.prototypeId);

      this.logger.log(`📊 图片转HTML任务组全部成功: 完成${successPrototypes.length}个原型`);

      return {
        success: true,
        successPrototypes,
        failedPrototypes: [],
      };

    } catch (error) {
      // 任何任务失败都导致整个任务组立即失败
      this.logger.error(`❌ 图片转HTML任务组快速失败: ${error.message}`);

      // 立即返回失败结果，不再执行后续逻辑
      return {
        success: false,
        successPrototypes: [],
        failedPrototypes: prototypes.map(p => p.id),
        error: `图片转HTML任务组快速失败: ${error.message}`,
      };
    }
  }
} 
