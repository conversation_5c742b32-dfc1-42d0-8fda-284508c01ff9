import { Injectable, Logger } from '@nestjs/common';
import { AiCodingService } from '../ai-coding/ai-coding.service'
import { executeCommand } from '../ai-coding/playground';
import { PrismaService } from '../prisma/prisma.service';
import { GitlabUtil } from '../utils/gitlab.util';
import { DesignProjectService } from './design-project.service';

interface PageGenerationInfo {
  pageId: string;
  pageName: string;
  latestWorkflow: any;
  playgroundId?: string;
  branchName?: string;
}

interface ProjectPreviewRequest {
  projectId: string;
  user: string;
}

interface ProjectPreviewResult {
  success: boolean;
  message: string;
  previewBranch?: string;
  previewBranchUrl?: string;
  forkUrl?: string;
  forkProjectUrl?: string;
  // webideUrl?: string;
  pm2PreviewUrl?: string;
  originalRepoUrl?: string;
  originalRepoBranchUrl?: string;
  pageGenerations?: PageGenerationInfo[];
  mergeResults?: Array<{
    pageId: string;
    pageName: string;
    sourceBranch: string;
    sourceBranchUrl?: string;
    success: boolean;
    error?: string;
  }>;
}

// 项目预览标签
const PROJECT_PREVIEW_TAG = 'project-preview'

@Injectable()
export class ProjectPreviewService {
  private readonly logger = new Logger(ProjectPreviewService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly designProjectService: DesignProjectService,
    private readonly aiCodingService: AiCodingService,
  ) { }

  /**
   * 创建项目预览
   * 1. 查询应用级代码生成历史
   * 2. Fork原始仓库到目标group（如果尚未fork）
   * 3. 在fork仓库中按时间顺序创建预览分支
   * 4. 合并各个生码分支到预览分支
   * 5. 启动WebIDE
   */
  async createProjectPreview(request: ProjectPreviewRequest): Promise<ProjectPreviewResult> {
    const { projectId, user } = request;

    try {
      this.logger.log(`🚀 [ProjectPreview] 开始创建项目预览: ${projectId}, 用户: ${user}`);

      // 1. 获取项目的代码生成历史
      const history = await this.designProjectService.getProjectCodeGenerationHistory(projectId);

      if (history.pageGenerations.length === 0) {
        return {
          success: false,
          message: '该项目还没有已完成的代码生成记录，无法创建预览',
        };
      }

      if (!history.project.gitUrl) {
        return {
          success: false,
          message: '项目未配置Git仓库，无法创建预览分支',
        };
      }

      this.logger.log(`📋 [ProjectPreview] 找到 ${history.pageGenerations.length} 个页面的代码生成记录`);

      // 2. 使用GitlabUtil处理fork逻辑
      const forkUrl = await this.processGitUrlForFork(history.project.gitUrl);
      this.logger.log(`🔧 [ProjectPreview] 使用fork仓库: ${forkUrl}`);

      // 安全检查：确保fork仓库URL与原始仓库URL不同
      if (forkUrl === history.project.gitUrl) {
        throw new Error('Fork仓库URL与原始仓库URL相同，为防止污染用户仓库，停止项目预览流程');
      }

      // 解析fork仓库信息用于生成URL
      const forkGitInfo = GitlabUtil.parseGitUrl(forkUrl);
      const forkProjectUrl = forkGitInfo ? GitlabUtil.generateProjectUrl(forkGitInfo.gitlabEnv, forkGitInfo.namespace, forkGitInfo.projectName) : undefined;

      // 解析原始仓库信息用于生成URL
      const originalGitInfo = GitlabUtil.parseGitUrl(history.project.gitUrl);
      const originalRepoUrl = originalGitInfo ? GitlabUtil.generateProjectUrl(originalGitInfo.gitlabEnv, originalGitInfo.namespace, originalGitInfo.projectName) : undefined;
      const originalRepoBranchUrl = originalGitInfo ? GitlabUtil.generateBranchUrl(originalGitInfo.gitlabEnv, originalGitInfo.namespace, originalGitInfo.projectName, history.project.gitBranch || 'master') : undefined;

      // 详细记录仓库信息
      this.logger.log(`📋 [ProjectPreview] 仓库信息对比:`);
      this.logger.log(`   原始仓库: ${history.project.gitUrl} -> ${originalRepoUrl}`);
      this.logger.log(`   Fork仓库: ${forkUrl} -> ${forkProjectUrl}`);
      this.logger.log(`⚠️ [ProjectPreview] 所有分支操作将在Fork仓库中进行，不会影响原始仓库`);

      // 3. 等待fork仓库分支同步完成
      await this.waitForForkRepoSync(forkGitInfo, history.pageGenerations);

      // 4. 按时间排序页面生成记录并查找实际分支
      const sortedGenerations = await this.sortAndFindBranches(history.pageGenerations, forkGitInfo);

      // 5. 检查是否有足够的分支进行合并
      const validGenerations = sortedGenerations.filter(gen => gen.branchName && gen.branchName !== '未找到');
      if (validGenerations.length === 0) {
        return {
          success: false,
          message: '没有找到任何可用的生码分支，无法创建项目预览。请确保生码任务已完成并且分支已同步到Fork仓库。',
          originalRepoUrl,
          originalRepoBranchUrl,
        };
      }

      this.logger.log(`📊 [ProjectPreview] 分支统计: 总计${sortedGenerations.length}个，可用${validGenerations.length}个`);

      // 6. 创建预览分支名称
      const previewBranch = this.generatePreviewBranchName(validGenerations);
      this.logger.log(`🌿 [ProjectPreview] 预览分支名称: ${previewBranch}`);

      // 生成预览分支URL
      const previewBranchUrl = forkGitInfo ? GitlabUtil.generateBranchUrl(forkGitInfo.gitlabEnv, forkGitInfo.namespace, forkGitInfo.projectName, previewBranch) : undefined;

      // 7. 在fork仓库中处理Git分支创建和合并（只处理有效的分支）
      const mergeResults = await this.createPreviewBranchAndMerge(
        forkUrl,
        history.project.gitBranch || 'master',
        previewBranch,
        validGenerations,
        user
      );

      // 8. 为未找到分支的页面添加失败记录
      const failedGenerations = sortedGenerations.filter(gen => gen.branchName === '未找到');
      failedGenerations.forEach(gen => {
        mergeResults.push({
          pageId: gen.pageId,
          pageName: gen.pageName,
          sourceBranch: '分支未找到',
          success: false,
          error: '在Fork仓库中未找到对应的生码分支，可能是分支同步延迟或生码任务未完成'
        });
      });

      // // 9. 启动WebIDE
      // const webideUrl = await this.startWebIDE(projectId, previewBranch);

      // 9. 启动PM2预览
      const pm2PreviewUrl = await this.startPm2Preview(projectId, previewBranch);

      // 10. 计算成功率
      const successCount = mergeResults.filter(r => r.success).length;
      const totalCount = mergeResults.length;
      const successRate = totalCount > 0 ? successCount / totalCount : 0;

      // 11. 生成详细的结果报告
      const syncedCount = validGenerations.length;
      const totalPages = history.pageGenerations.length;

      this.logger.log(`📊 [ProjectPreview] 最终结果统计:`);
      this.logger.log(`   📄 总页面数: ${totalPages}`);
      this.logger.log(`   🔗 同步成功: ${syncedCount} (${(syncedCount / totalPages * 100).toFixed(1)}%)`);
      this.logger.log(`   ✅ 合并成功: ${successCount} (${(successCount / totalCount * 100).toFixed(1)}%)`);
      this.logger.log(`   ❌ 合并失败: ${totalCount - successCount}`);

      return {
        success: successRate >= 0.5 || successCount === totalCount,
        message: successCount === totalCount
          ? `项目预览创建成功，所有 ${totalCount} 个页面合并完成`
          : successCount > 0
            ? `项目预览部分成功，${successCount}/${totalCount} 个页面合并完成 (${totalPages - syncedCount}个页面分支同步失败)`
            : `项目预览创建失败，所有页面合并失败。其中 ${totalPages - syncedCount} 个页面的分支未能同步到Fork仓库`,
        previewBranch,
        previewBranchUrl,
        forkUrl,
        forkProjectUrl,
        pm2PreviewUrl,
        originalRepoUrl,
        originalRepoBranchUrl,
        pageGenerations: sortedGenerations,
        mergeResults,
      };

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 创建项目预览失败: ${projectId}`, error);
      return {
        success: false,
        message: `创建项目预览失败: ${error.message}`,
      };
    }
  }

  /**
   * 处理Git URL的fork逻辑，使用GitlabUtil通用方法
   */
  private async processGitUrlForFork(originalGitUrl: string): Promise<string> {
    try {
      this.logger.log(`🔧 [ProjectPreview-Fork] 开始处理仓库fork逻辑: ${originalGitUrl}`);

      // 使用GitlabUtil的通用方法处理fork
      const forkedUrl = await GitlabUtil.forkRepositoryToTargetGroup(originalGitUrl);

      this.logger.log(`✅ [ProjectPreview-Fork] Fork处理完成: ${originalGitUrl} -> ${forkedUrl}`);
      return forkedUrl;

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview-Fork] Fork处理失败: ${originalGitUrl}`, error);
      // 如果fork失败，必须抛出错误，停止项目预览流程
      throw new Error(`Fork仓库失败，无法创建项目预览。原因: ${error.message}`);
    }
  }

  /**
   * 带重试机制的分支查找
   */
  private async findBranchWithRetry(forkGitInfo: any, pageName: string, workflowId?: string, maxRetries: number = 3, retryDelay: number = 2000): Promise<string | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      this.logger.log(`🔍 [ProjectPreview-Retry] 查找页面 ${pageName} 的分支 (尝试 ${attempt}/${maxRetries})${workflowId ? ` (工作流ID: ${workflowId})` : ''}`);

      const branch = await GitlabUtil.findFeatureBranchInForkRepo(
        forkGitInfo.gitlabEnv,
        forkGitInfo.namespace,
        forkGitInfo.projectName,
        pageName,
        workflowId
      );

      if (branch) {
        this.logger.log(`✅ [ProjectPreview-Retry] 页面 ${pageName} 分支查找成功: ${branch} (尝试 ${attempt})`);
        return branch;
      }

      if (attempt < maxRetries) {
        this.logger.warn(`⚠️ [ProjectPreview-Retry] 页面 ${pageName} 分支查找失败，${retryDelay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    this.logger.error(`❌ [ProjectPreview-Retry] 页面 ${pageName} 分支查找失败，已尝试 ${maxRetries} 次`);
    return null;
  }

  /**
   * 等待Fork仓库分支同步完成
   */
  private async waitForForkRepoSync(forkGitInfo: any, pageGenerations: PageGenerationInfo[]): Promise<void> {
    this.logger.log(`⏳ [ProjectPreview-Sync] 等待Fork仓库分支同步...`);

    const maxWaitTime = 60000; // 最长等待60秒
    const checkInterval = 3000; // 每3秒检查一次
    const startTime = Date.now();

    // 需要检查的页面名称列表
    const pageNames = pageGenerations.map(gen => gen.pageName);
    this.logger.log(`📋 [ProjectPreview-Sync] 需要同步的页面: ${pageNames.join(', ')}`);

    while (Date.now() - startTime < maxWaitTime) {
      let allBranchesSynced = true;
      const foundBranches: string[] = [];
      const missingBranches: string[] = [];

      // 检查每个页面的feature分支是否已同步，使用工作流ID进行精确匹配
      for (const generation of pageGenerations) {
        const branch = await GitlabUtil.findFeatureBranchInForkRepo(
          forkGitInfo.gitlabEnv,
          forkGitInfo.namespace,
          forkGitInfo.projectName,
          generation.pageName,
          generation.latestWorkflow.id
        );

        if (branch) {
          foundBranches.push(`${generation.pageName}→${branch}`);
        } else {
          missingBranches.push(generation.pageName);
          allBranchesSynced = false;
        }
      }

      this.logger.log(`🔄 [ProjectPreview-Sync] 同步状态检查:`);
      this.logger.log(`   ✅ 已同步: ${foundBranches.length} 个 [${foundBranches.join(', ')}]`);
      this.logger.log(`   ⏳ 等待中: ${missingBranches.length} 个 [${missingBranches.join(', ')}]`);

      if (allBranchesSynced) {
        this.logger.log(`✅ [ProjectPreview-Sync] 所有分支同步完成，用时: ${Date.now() - startTime}ms`);
        return;
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }

    // 超时后记录警告，但不抛出错误，让后续逻辑处理未找到的分支
    this.logger.warn(`⚠️ [ProjectPreview-Sync] 分支同步等待超时 (${maxWaitTime}ms)，将继续执行但可能有部分分支未同步`);
  }

  /**
   * 按时间排序页面生成记录并查找实际分支
   */
  private async sortAndFindBranches(pageGenerations: PageGenerationInfo[], forkGitInfo: any): Promise<PageGenerationInfo[]> {
    const sortedGenerations = pageGenerations.sort((a, b) => {
      const timeA = new Date(a.latestWorkflow.created).getTime();
      const timeB = new Date(b.latestWorkflow.created).getTime();
      return timeA - timeB; // 按时间升序排列，早的在前
    });

    for (const generation of sortedGenerations) {
      // 从fork仓库中查找实际的feature分支，使用重试机制
      // 传递工作流ID以精确匹配对应的分支
      const actualBranch = await this.findBranchWithRetry(
        forkGitInfo,
        generation.pageName,
        generation.latestWorkflow.id
      );
      if (actualBranch) {
        generation.branchName = actualBranch;
        this.logger.log(`✅ [ProjectPreview] 页面 ${generation.pageName} 找到分支: ${actualBranch}`);
      } else {
        this.logger.warn(`⚠️ [ProjectPreview] 未找到页面 ${generation.pageName} 的feature分支，将跳过合并`);
        generation.branchName = '未找到';
      }
    }
    return sortedGenerations;
  }

  /**
   * 生成预览分支名称（基于最新成功的页面生码任务序列）
   */
  private generatePreviewBranchName(validGenerations: PageGenerationInfo[]): string {
    // 基于所有最新成功的页面生码任务，按照任务执行的前后顺序生成分支名
    // validGenerations 已经是过滤后的有效生码任务，包含所有找到分支的任务
    const taskIds = validGenerations
      .map(gen => gen.latestWorkflow.id)
      .join('-');

    return `temp/preview-${taskIds}`;
  }

  /**
   * 检查预览分支是否需要创建（始终按照当前任务序列）
   */
  private async shouldCreatePreviewBranch(
    gitInfo: any,
    previewBranch: string,
    validGenerations: PageGenerationInfo[]
  ): Promise<{ shouldCreate: boolean; allMerged: boolean }> {
    try {
      // 1. 检查预览分支是否已存在
      const branchExists = await GitlabUtil.checkBranchExists(
        gitInfo.gitlabEnv,
        gitInfo.namespace,
        gitInfo.projectName,
        previewBranch
      );

      if (!branchExists) {
        this.logger.log(`🌿 [ProjectPreview] 预览分支 ${previewBranch} 不存在，需要创建`);
        return { shouldCreate: true, allMerged: false };
      }

      this.logger.log(`🔍 [ProjectPreview] 预览分支 ${previewBranch} 已存在，检查当前任务序列是否完全合并`);

      // 2. 检查当前任务序列的所有生码分支是否都已合并到预览分支
      let allBranchesMerged = true;
      const unmergedBranches: string[] = [];

      for (const generation of validGenerations) {
        const actualBranch = generation.branchName;
        if (!actualBranch || actualBranch === '未找到') {
          continue; // 跳过没有分支的页面
        }

        const alreadyMerged = await this.checkIfBranchAlreadyMerged(gitInfo, actualBranch, previewBranch);
        if (!alreadyMerged) {
          allBranchesMerged = false;
          unmergedBranches.push(`${generation.pageName}(${actualBranch})`);
        }
      }

      if (allBranchesMerged) {
        this.logger.log(`✅ [ProjectPreview] 当前任务序列的所有分支都已合并到预览分支，直接使用`);
        return { shouldCreate: false, allMerged: true }; // 不需要重新创建，所有分支已合并
      } else {
        this.logger.log(`📋 [ProjectPreview] 发现 ${unmergedBranches.length} 个未合并的分支: ${unmergedBranches.join(', ')}`);
        this.logger.log(`🔄 [ProjectPreview] 需要继续合并流程以完成当前任务序列`);
        return { shouldCreate: false, allMerged: false }; // 不重新创建分支，但需要继续合并流程
      }

    } catch (error) {
      this.logger.warn(`检查预览分支需求时发生错误:`, error.message);
      return { shouldCreate: true, allMerged: false }; // 出错时假设需要创建
    }
  }

  /**
   * 在fork仓库中创建预览分支并合并各个生码分支
   */
  private async createPreviewBranchAndMerge(
    forkGitUrl: string,
    baseBranch: string,
    previewBranch: string,
    validGenerations: PageGenerationInfo[],
    user: string
  ): Promise<Array<{
    pageId: string;
    pageName: string;
    sourceBranch: string;
    sourceBranchUrl?: string;
    success: boolean;
    error?: string;
  }>> {
    const mergeResults: Array<{
      pageId: string;
      pageName: string;
      sourceBranch: string;
      sourceBranchUrl?: string;
      success: boolean;
      error?: string;
    }> = [];

    try {
      this.logger.log(`🌿 [ProjectPreview] 在fork仓库中创建预览分支并合并代码: ${previewBranch}`);

      // 使用GitlabUtil解析fork仓库的Git URL信息
      const gitInfo = GitlabUtil.parseGitUrl(forkGitUrl);
      if (!gitInfo) {
        throw new Error(`无法解析fork Git URL: ${forkGitUrl}`);
      }

      // 🔒 安全验证：确保操作的是fork仓库，不是原始仓库
      // 通过检查namespace来验证，fork仓库的namespace应该是目标group
      const expectedTargetGroup = GitlabUtil.getTargetGroupForEnv(gitInfo.gitlabEnv);
      if (gitInfo.namespace !== expectedTargetGroup) {
        this.logger.warn(`⚠️ [ProjectPreview-Security] 仓库namespace验证失败:`);
        this.logger.warn(`   当前namespace: ${gitInfo.namespace}`);
        this.logger.warn(`   预期namespace: ${expectedTargetGroup}`);
        this.logger.warn(`   这可能意味着正在操作原始仓库而不是fork仓库！`);
        throw new Error('安全检查失败：操作的可能不是fork仓库，为防止污染用户仓库，停止操作');
      }

      this.logger.log(`✅ [ProjectPreview-Security] Fork仓库验证通过: ${gitInfo.namespace}/${gitInfo.projectName}`);

      // 1. 检查是否需要创建预览分支（按照当前任务序列）
      const branchStatus = await this.shouldCreatePreviewBranch(gitInfo, previewBranch, validGenerations);

      if (!branchStatus.shouldCreate && branchStatus.allMerged) {
        this.logger.log(`✅ [ProjectPreview] 预览分支 ${previewBranch} 已存在且当前任务序列所有分支都已合并，直接使用`);

        // 标记所有页面为成功（因为分支已存在且所有分支都已合并）
        for (const generation of validGenerations) {
          mergeResults.push({
            pageId: generation.pageId,
            pageName: generation.pageName,
            sourceBranch: generation.branchName || '已合并',
            success: true,
          });
        }
        return mergeResults;
      }

      // 2. 根据需要创建预览分支
      if (branchStatus.shouldCreate) {
        this.logger.log(`🌿 [ProjectPreview] 创建预览分支: ${previewBranch}`);
        await this.createBranch(gitInfo, baseBranch, previewBranch);

        // 2.1. 等待分支创建完全生效
        await this.waitForBranchReady(gitInfo, previewBranch);
      } else {
        this.logger.log(`🔄 [ProjectPreview] 预览分支已存在，继续合并未合并的生码分支`);
      }

      // 3. 遍历每个页面，查找对应的feature分支并合并到预览分支
      for (const generation of validGenerations) {
        const actualBranch = generation.branchName; // 使用已找到的实际分支

        try {
          if (!actualBranch || actualBranch === '未找到') {
            this.logger.warn(`⚠️ [ProjectPreview] 未找到页面 ${generation.pageName} 的feature分支，跳过`);
            mergeResults.push({
              pageId: generation.pageId,
              pageName: generation.pageName,
              sourceBranch: '未找到',
              success: false,
              error: '未找到对应的feature分支'
            });
            continue;
          }

          this.logger.log(`✅ [ProjectPreview] 页面 ${generation.pageName} 找到feature分支: ${actualBranch}`);

          // 检查该分支是否已经合并过
          const alreadyMerged = await this.checkIfBranchAlreadyMerged(gitInfo, actualBranch, previewBranch);
          if (alreadyMerged) {
            this.logger.log(`✅ [ProjectPreview] 分支 ${actualBranch} 已合并，跳过`);

            const sourceBranchUrl = GitlabUtil.generateBranchUrl(gitInfo.gitlabEnv, gitInfo.namespace, gitInfo.projectName, actualBranch);
            mergeResults.push({
              pageId: generation.pageId,
              pageName: generation.pageName,
              sourceBranch: actualBranch,
              sourceBranchUrl,
              success: true,
            });
            continue;
          }

          // 尝试合并分支
          await this.mergeBranch(gitInfo, actualBranch, previewBranch, generation.pageName);

          // 在MR操作之间添加适当延迟，避免GitLab 422错误（最长3秒）
          await this.waitForMergeStabilization();

          // 生成分支URL
          const sourceBranchUrl = GitlabUtil.generateBranchUrl(gitInfo.gitlabEnv, gitInfo.namespace, gitInfo.projectName, actualBranch);

          mergeResults.push({
            pageId: generation.pageId,
            pageName: generation.pageName,
            sourceBranch: actualBranch,
            sourceBranchUrl,
            success: true,
          });

          this.logger.log(`✅ [ProjectPreview] 成功合并: ${actualBranch} -> ${previewBranch}`);

        } catch (error) {
          this.logger.error(`❌ [ProjectPreview] 合并页面 ${generation.pageName} 失败:`, error.message);

          // 检查是否是因为已经合并导致的冲突
          if (this.isMergeConflictDueToAlreadyMerged(error)) {
            this.logger.log(`✅ [ProjectPreview] 分支已合并，标记为成功: ${generation.pageName}`);

            const sourceBranchUrl = actualBranch && actualBranch !== '未找到'
              ? GitlabUtil.generateBranchUrl(gitInfo.gitlabEnv, gitInfo.namespace, gitInfo.projectName, actualBranch)
              : undefined;

            mergeResults.push({
              pageId: generation.pageId,
              pageName: generation.pageName,
              sourceBranch: actualBranch || '已合并',
              sourceBranchUrl,
              success: true,
            });
          } else {
            const sourceBranchUrl = actualBranch && actualBranch !== '未找到'
              ? GitlabUtil.generateBranchUrl(gitInfo.gitlabEnv, gitInfo.namespace, gitInfo.projectName, actualBranch)
              : undefined;

            mergeResults.push({
              pageId: generation.pageId,
              pageName: generation.pageName,
              sourceBranch: actualBranch || '未知',
              sourceBranchUrl,
              success: false,
              error: error.message,
            });
          }
        }
      }

      return mergeResults;

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 创建预览分支和合并过程失败:`, error);
      throw error;
    }
  }



  /**
   * 检查源分支是否已经合并到目标分支
   * 通过两步检查：1. MR记录检查 2. Commit包含关系检查
   */
  private async checkIfBranchAlreadyMerged(gitInfo: any, sourceBranch: string, targetBranch: string): Promise<boolean> {
    try {
      const token = GitlabUtil.getToken(gitInfo.gitlabEnv as any);
      if (!token) {
        return false;
      }

      const forkProjectId = await GitlabUtil.getProjectId(
        gitInfo.gitlabEnv,
        gitInfo.namespace,
        gitInfo.projectName
      );
      if (!forkProjectId) {
        return false;
      }

      const axios = await import('axios');
      const baseUrl = `http://${gitInfo.gitDomain}/api/v4/projects/${forkProjectId}`;

      // 第1步：检查是否有从源分支到目标分支的已合并MR
      const mergeRequestsUrl = `${baseUrl}/merge_requests?source_branch=${encodeURIComponent(sourceBranch)}&target_branch=${encodeURIComponent(targetBranch)}&state=merged&per_page=1`;

      const mrResponse = await axios.default.get(mergeRequestsUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      const hasMergedMR = mrResponse.data && mrResponse.data.length > 0;

      // 第2步：检查源分支的最新commit是否在目标分支中
      // 2.1 获取源分支的最新commit
      const sourceBranchUrl = `${baseUrl}/repository/branches/${encodeURIComponent(sourceBranch)}`;
      const sourceBranchResponse = await axios.default.get(sourceBranchUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 30000,
      });

      if (!sourceBranchResponse.data || !sourceBranchResponse.data.commit) {
        this.logger.warn(`无法获取源分支 ${sourceBranch} 的commit信息`);
        return hasMergedMR; // 降级到MR检查结果
      }

      const sourceCommitSha = sourceBranchResponse.data.commit.id;
      this.logger.debug(`[MergeCheck] 源分支 ${sourceBranch} 最新commit: ${sourceCommitSha}`);

      // 2.2 检查该commit是否在目标分支中
      const commitInTargetUrl = `${baseUrl}/repository/merge_base?refs[]=${encodeURIComponent(targetBranch)}&refs[]=${sourceCommitSha}`;
      
      try {
        const mergeBaseResponse = await axios.default.get(commitInTargetUrl, {
          headers: {
            'PRIVATE-TOKEN': token,
          },
          timeout: 30000,
        });

        // 如果merge base等于源分支的commit，说明源分支已被完全合并
        const mergeBaseSha = mergeBaseResponse.data?.id;
        const commitFullyMerged = mergeBaseSha === sourceCommitSha;
        
        this.logger.debug(`[MergeCheck] ${sourceBranch} -> ${targetBranch}: MR记录=${hasMergedMR}, Commit合并=${commitFullyMerged}`);

        // 双重验证：MR记录存在 且 commit确实在目标分支中
        return hasMergedMR && commitFullyMerged;

      } catch (mergeBaseError) {
        // 如果merge_base API失败，尝试用commits API检查
        this.logger.debug(`[MergeCheck] merge_base检查失败，尝试commits检查: ${mergeBaseError.message}`);
        
        const commitExistsUrl = `${baseUrl}/repository/commits/${sourceCommitSha}/refs?type=branch`;
        try {
          const commitRefsResponse = await axios.default.get(commitExistsUrl, {
            headers: {
              'PRIVATE-TOKEN': token,
            },
            timeout: 30000,
          });

          // 检查该commit是否被目标分支引用
          const isInTargetBranch = commitRefsResponse.data?.some((ref: any) => ref.name === targetBranch);
          
          this.logger.debug(`[MergeCheck] ${sourceBranch} -> ${targetBranch}: MR记录=${hasMergedMR}, Commit在目标分支=${isInTargetBranch}`);
          
          return hasMergedMR && isInTargetBranch;

        } catch (commitRefsError) {
          this.logger.warn(`[MergeCheck] commit引用检查也失败，降级到MR检查: ${commitRefsError.message}`);
          return hasMergedMR; // 最终降级到MR检查结果
        }
      }

    } catch (error) {
      this.logger.warn(`检查分支合并状态时发生错误: ${sourceBranch} -> ${targetBranch}`, error.message);
      return false;
    }
  }

  /**
   * 检查合并错误是否是因为分支已经合并
   */
  private isMergeConflictDueToAlreadyMerged(error: any): boolean {
    const errorMessage = error.message || error.response?.data?.message || '';
    return errorMessage.includes('already merged') ||
      errorMessage.includes('already exists') ||
      errorMessage.includes('nothing to merge') ||
      errorMessage.includes('已合并') ||
      errorMessage.includes('无需合并');
  }

  /**
   * 在fork仓库中创建分支
   */
  private async createBranch(gitInfo: any, baseBranch: string, newBranch: string): Promise<void> {
    try {
      this.logger.log(`🌿 [ProjectPreview] 在fork仓库中创建分支: ${newBranch} (基于 ${baseBranch})`);

      // 使用GitlabUtil的通用分支创建方法
      await GitlabUtil.createBranch(
        gitInfo.gitlabEnv,
        gitInfo.namespace,
        gitInfo.projectName,
        newBranch,
        baseBranch
      );

      this.logger.log(`✅ [ProjectPreview] fork仓库中分支创建成功: ${newBranch}`);

    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
        this.logger.log(`⚠️ [ProjectPreview] fork仓库中分支已存在，将使用现有分支: ${newBranch}`);
        return;
      }
      throw error;
    }
  }

  /**
   * 在fork仓库中合并分支（使用改进的MR逻辑，带重试机制）
   */
  private async mergeBranch(gitInfo: any, sourceBranch: string, targetBranch: string, pageName: string): Promise<void> {
    const maxRetries = 2; // 最多重试2次
    const retryDelay = 1000; // 每次重试间隔1秒
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        this.logger.log(`🔄 [ProjectPreview] 在fork仓库中合并分支: ${sourceBranch} -> ${targetBranch} (页面: ${pageName}) (尝试 ${attempt}/${maxRetries + 1})`);

        // 使用GitlabUtil的统一MR处理方法
        await GitlabUtil.createOrMergeMR(
          gitInfo.gitlabEnv,
          gitInfo.namespace,
          gitInfo.projectName,
          sourceBranch,
          targetBranch,
          `[项目预览] 合并页面 ${pageName} 的代码`,
          `自动合并页面 ${pageName} 的生码分支到项目预览分支`
        );

        this.logger.log(`✅ [ProjectPreview] fork仓库中分支合并成功: ${sourceBranch} -> ${targetBranch} (尝试 ${attempt})`);
        return; // 成功后立即返回

      } catch (error) {
        const is422Error = error.response?.status === 422 || error.code === 'ERR_BAD_REQUEST';
        
        if (is422Error && attempt <= maxRetries) {
          this.logger.warn(`⚠️ [ProjectPreview] 合并遇到422错误，${retryDelay}ms后重试 (尝试 ${attempt}/${maxRetries + 1}): ${error.message}`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          continue; // 继续重试
        }
        
        // 最后一次尝试失败或非422错误，抛出异常
        this.logger.error(`❌ [ProjectPreview] fork仓库中分支合并失败: ${sourceBranch} -> ${targetBranch} (尝试 ${attempt}/${maxRetries + 1})`, error);
        throw error;
      }
    }
  }

  /**
   * 启动WebIDE
   */
  private async startWebIDE(projectId: string, previewBranch: string): Promise<string | undefined> {
    try {
      this.logger.log(`🚀 [ProjectPreview] 准备启动WebIDE: 项目=${projectId}, 分支=${previewBranch}`);

      // 返回WebIDE启动页面的URL，传递必要的参数
      const webideStartUrl = `/webide-start?projectId=${encodeURIComponent(projectId)}&branch=${encodeURIComponent(previewBranch)}`;

      this.logger.log(`✅ [ProjectPreview] WebIDE启动页面URL: ${webideStartUrl}`);

      return webideStartUrl;

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] WebIDE启动失败:`, error);
      return undefined;
    }
  }

  /**
   * 启动PM2预览 - 完整实现
   * 1. 创建空会话
   * 2. 在playground中拉取预览分支
   * 3. 启动PM2预览进程
   * 4. 返回chat会话页面URL
   */
  private async startPm2Preview(projectId: string, previewBranch: string): Promise<string | undefined> {
    try {
      this.logger.log(`🚀 [ProjectPreview] 开始启动PM2预览: 项目=${projectId}, 分支=${previewBranch}`);

      // 1. 获取项目信息
      const project = await this.prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        throw new Error(`项目不存在: ${projectId}`);
      }

      if (!project.gitUrl) {
        throw new Error('项目未配置Git仓库');
      }

      this.logger.log(`📋 [ProjectPreview] 项目信息: ${project.name}, Git: ${project.gitUrl}`);

      // 2. 获取fork仓库URL
      const forkUrl = await this.processGitUrlForFork(project.gitUrl);
      this.logger.log(`🔧 [ProjectPreview] 使用fork仓库: ${forkUrl}`);

      // 3. 先检查有没有对应的项目预览playground工作区
      // 如果有直接用已有的工作区
      const projectPreviewPlayground = await this.hasProjectPreviewPlayground(projectId);
      let playgroundId = projectPreviewPlayground?.id;

      if (!playgroundId) {
        // 4. 创建空会话
        const createChatDto = {
          user: project.user,
          projectId: project.id,
          createEmptySession: 'true',
          sessionName: `项目预览 - ${project.name} (${previewBranch})`,
          tags: 'project-preview',
          type: 'html', // 自定义项目都是html工作区类型
          model: 'openrouter::google/gemini-2.5-pro-preview',
          isPublic: 'true',
          enableAutoIteration: 'false',
          enableStepByStep: 'false',
          enableCustomPreview: 'true', // 使用自定义预览功能
        };
        const chatSession = await this.aiCodingService.createCommonChat(createChatDto);
        playgroundId = chatSession.chatId;
        this.logger.log(`✅ [ProjectPreview] 创建空会话成功: ${chatSession.chatId}`);
      }

      // 5. 在playground中设置预览环境
      // 区分已有分支的情况
      await this.setupPreviewEnvironmentInPlayground(
        playgroundId, // playgroundId
        forkUrl,
        previewBranch,
        project.gitBranch || 'master',
        project.name
      );

      // 6. 启动PM2预览进程
      await this.startPM2PreviewProcess(
        playgroundId, // playgroundId
        project.name,
        project.user
      );

      // 6. 返回chat会话页面URL
      const chatUrl = `/chat/${playgroundId}`;
      this.logger.log(`✅ [ProjectPreview] PM2预览启动完成，跳转到: ${chatUrl}`);

      return chatUrl;

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] PM2预览启动失败:`, error);
      throw error;
    }
  }

  /**
   * 在playground中设置预览环境
   */
  private async setupPreviewEnvironmentInPlayground(
    playgroundId: string,
    forkUrl: string,
    previewBranch: string,
    baseBranch: string,
    projectName: string
  ): Promise<void> {
    try {
      this.logger.log(`🔧 [ProjectPreview] 设置playground预览环境: ${playgroundId}`);

      // 1. 清空playground工作区（保留必要文件）
      this.logger.log(`🔄 [ProjectPreview] 清空playground工作区...`);
      let cleanResult;
      try {
        cleanResult = await executeCommand(
          playgroundId,
          `find . -mindepth 1 -maxdepth 1 ! -name 'src' ! -name 'package.json' ! -name '.env' ! -name 'index.html' ! -name '.git' -exec rm -rf {} +`,
          { dir: '.' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] 清空工作区命令执行失败:`, error);
        throw new Error(`清空工作区命令执行失败: ${error.message}`);
      }

      if (cleanResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [ProjectPreview] 清空工作区警告: ${cleanResult.stderr}`);
      }

      // 2. 转换为带token的http URL
      const httpUrl = GitlabUtil.convertTohttpWithToken(forkUrl);
      if (!httpUrl) {
        throw new Error(`无法为Git URL生成带token的http地址: ${forkUrl}`);
      }

      this.logger.log(`🔐 [ProjectPreview] 使用带token的http方式克隆仓库`);

      // 3. 克隆fork仓库到src目录
      this.logger.log(`🔄 [ProjectPreview] 克隆fork仓库: ${forkUrl}`);

      let cloneResult;
      try {
        cloneResult = await executeCommand(
          playgroundId,
          `git clone ${httpUrl} src`,
          { dir: '.' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] 克隆仓库命令执行失败:`, error);
        throw new Error(`克隆仓库命令执行失败: ${error.message}`);
      }

      if (cloneResult.exitCode !== 0) {
        // 检查src是否存在
        const srcExists = cloneResult?.stderr.includes('src') && cloneResult?.stderr.includes('already exists');
        // 如果src存在，则说明仓库已经存在，直接跳过克隆
        if (!srcExists)
          throw new Error(`克隆仓库失败: ${cloneResult.stderr}`);
      }

      // 4. 配置Git用户信息
      await this.configureGitUser(playgroundId, forkUrl);

      // 5. 切换到预览分支
      this.logger.log(`🔄 [ProjectPreview] 切换到预览分支: ${previewBranch}`);

      // 先获取远程分支信息
      let fetchResult;
      try {
        fetchResult = await executeCommand(
          playgroundId,
          `git fetch origin`,
          { dir: 'src' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] Git fetch命令执行失败:`, error);
        throw new Error(`Git fetch命令执行失败: ${error.message}`);
      }

      if (fetchResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [ProjectPreview] Git fetch警告: ${fetchResult.stderr}`);
      }

      // 切换到预览分支
      let checkoutResult;
      try {
        checkoutResult = await executeCommand(
          playgroundId,
          `git checkout -b ${previewBranch} origin/${previewBranch} || git checkout ${previewBranch}`,
          { dir: 'src' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] Git checkout命令执行失败:`, error);
        throw new Error(`Git checkout命令执行失败: ${error.message}`);
      }

      if (checkoutResult.exitCode !== 0) {
        throw new Error(`切换到预览分支失败: ${checkoutResult.stderr}`);
      }

      // 6. 验证分支切换成功
      let branchResult;
      try {
        branchResult = await executeCommand(
          playgroundId,
          `git branch --show-current`,
          { dir: 'src' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] 验证分支命令执行失败:`, error);
        throw new Error(`验证分支命令执行失败: ${error.message}`);
      }

      this.logger.log(`✅ [ProjectPreview] 当前分支: ${branchResult.stdout.trim()}`);

      this.logger.log(`✅ [ProjectPreview] playground预览环境设置完成`);

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 设置playground预览环境失败:`, error);
      throw error;
    }
  }

  /**
   * 配置Git用户信息
   */
  private async configureGitUser(playgroundId: string, gitUrl: string): Promise<void> {
    try {
      this.logger.log(`👤 [ProjectPreview] 配置Git用户信息...`);

      // 获取用户信息
      const userInfo = await GitlabUtil.getUserInfoByUrl(gitUrl);

      if (!userInfo) {
        this.logger.warn(`⚠️ [ProjectPreview] 无法获取用户信息，使用默认配置`);
        // 使用默认配置
        try {
          await executeCommand(
            playgroundId,
            `git config user.name "gitlab-ci-token"`,
            { dir: 'src' }
          );

          await executeCommand(
            playgroundId,
            `git config user.email "<EMAIL>"`,
            { dir: 'src' }
          );
        } catch (error) {
          this.logger.error(`❌ [ProjectPreview] 设置默认Git用户配置失败:`, error);
          throw new Error(`设置默认Git用户配置失败: ${error.message}`);
        }
        return;
      }

      this.logger.log(`📋 [ProjectPreview] 获取到用户信息: ${userInfo.name} (${userInfo.email})`);

      // 设置用户名和邮箱
      try {
        await executeCommand(
          playgroundId,
          `git config user.name "${userInfo.name}"`,
          { dir: 'src' }
        );

        await executeCommand(
          playgroundId,
          `git config user.email "${userInfo.email}"`,
          { dir: 'src' }
        );
      } catch (error) {
        this.logger.error(`❌ [ProjectPreview] 设置Git用户配置失败:`, error);
        throw new Error(`设置Git用户配置失败: ${error.message}`);
      }

      this.logger.log(`✅ [ProjectPreview] Git用户配置完成`);

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 配置Git用户失败:`, error);
      // 降级到默认配置
      try {
        await executeCommand(
          playgroundId,
          `git config user.name "gitlab-ci-token"`,
          { dir: 'src' }
        );

        await executeCommand(
          playgroundId,
          `git config user.email "<EMAIL>"`,
          { dir: 'src' }
        );

        this.logger.log(`🔄 [ProjectPreview] 已降级到默认Git用户配置`);
      } catch (fallbackError) {
        this.logger.error(`❌ [ProjectPreview] 降级Git用户配置也失败:`, fallbackError);
        throw new Error(`Git用户配置完全失败: ${fallbackError.message}`);
      }
    }
  }


  /**
   * 是否存在对应的项目预览Playground
   * @param projectId 
   */
  private async hasProjectPreviewPlayground(projectId: string) {
    const playground = await this.prisma.playground.findFirst({
      where: {
        projectId,
        tags: {
          has: PROJECT_PREVIEW_TAG
        }
      }
    })

    return playground;
  }

  /**
   * 启动PM2预览进程
   */
  private async startPM2PreviewProcess(
    playgroundId: string,
    projectName: string,
    userId: string
  ): Promise<void> {
    try {
      this.logger.log(`🚀 [ProjectPreview] 启动PM2预览进程...`);

      // 1. 安装依赖
      await this.installDependencies(playgroundId);

      // 2. 启动PM2预览服务
      const { PM2PreviewClientService } = await import('../pm2-preview-client/pm2-preview-client.service');
      const pm2PreviewService = new PM2PreviewClientService();

      // 启动PM2预览服务
      const startResult = await pm2PreviewService.startCustomPreviewWithPM2(
        playgroundId,
        projectName,
        userId,
        'src'
      );

      if (startResult.processId) {
        this.logger.log(`✅ [ProjectPreview] PM2预览服务启动成功`);
        this.logger.log(`📋 [ProjectPreview] 进程ID: ${startResult.processId}`);
        if (startResult.url) {
          this.logger.log(`🔗 [ProjectPreview] 预览URL: ${startResult.url}`);
        }
        if (startResult.port) {
          this.logger.log(`🔌 [ProjectPreview] 端口: ${startResult.port}`);
        }
      } else {
        this.logger.warn(`⚠️ [ProjectPreview] PM2预览服务启动状态未知`);
      }

      this.logger.log(`✅ [ProjectPreview] PM2预览进程启动完成`);

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 启动PM2预览进程失败:`, error);
      throw error;
    }
  }

  /**
   * 安装项目依赖
   */
  private async installDependencies(playgroundId: string): Promise<void> {
    try {
      this.logger.log(`🔧 [ProjectPreview] 安装项目依赖...`);

      // 检查package.json是否存在
      const checkPackageResult = await executeCommand(
        playgroundId,
        `test -f package.json && echo "exists" || echo "not found"`,
        { dir: 'src' }
      );

      if (!checkPackageResult.stdout.includes('exists')) {
        this.logger.warn(`⚠️ [ProjectPreview] 未找到package.json，跳过依赖安装`);
        return;
      }

      // 安装依赖
      const installResult = await executeCommand(
        playgroundId,
        `pnpm install --prod=false --frozen-lockfile --prefer-offline --silent`,
        { dir: 'src' }
      );

      if (installResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [ProjectPreview] 依赖安装警告: ${installResult.stderr}`);
        // 尝试使用npm安装
        const npmInstallResult = await executeCommand(
          playgroundId,
          `npm install --silent`,
          { dir: 'src' }
        );

        if (npmInstallResult.exitCode !== 0) {
          throw new Error(`依赖安装失败: ${npmInstallResult.stderr}`);
        }
      }

      this.logger.log(`✅ [ProjectPreview] 依赖安装完成`);

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 安装依赖失败:`, error);
      // 依赖安装失败不阻塞预览启动，继续执行`
      this.logger.warn(`⚠️ [ProjectPreview] 依赖安装失败，但继续执行预览启动`);
    }
  }

  /**
   * 等待分支创建完全生效，确保GitLab API能够正确处理该分支的后续操作
   */
  private async waitForBranchReady(gitInfo: any, branchName: string): Promise<void> {
    const maxWaitTime = 15000; // 最长等待15秒
    const checkInterval = 1000; // 每1秒检查一次
    const startTime = Date.now();

    this.logger.log(`⏳ [ProjectPreview-BranchReady] 等待分支完全生效: ${branchName}`);

    while (Date.now() - startTime < maxWaitTime) {
      try {
        // 验证分支是否可以正常访问和操作
        const isReady = await this.verifyBranchReadiness(gitInfo, branchName);
        
        if (isReady) {
          const elapsedTime = Date.now() - startTime;
          this.logger.log(`✅ [ProjectPreview-BranchReady] 分支已完全生效: ${branchName} (用时: ${elapsedTime}ms)`);
          return;
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        
      } catch (error) {
        this.logger.warn(`⚠️ [ProjectPreview-BranchReady] 检查分支状态时出错: ${error.message}`);
        // 继续等待，不因单次检查失败而中断
      }
    }

    // 超时后记录警告但不抛出错误，让后续逻辑尝试继续执行
    this.logger.warn(`⚠️ [ProjectPreview-BranchReady] 分支准备就绪检查超时 (${maxWaitTime}ms)，继续执行后续操作`);
  }

  /**
   * 验证分支是否已准备好进行MR操作
   */
  private async verifyBranchReadiness(gitInfo: any, branchName: string): Promise<boolean> {
    try {
      const token = GitlabUtil.getToken(gitInfo.gitlabEnv as any);
      if (!token) {
        return false;
      }

      const projectId = await GitlabUtil.getProjectId(
        gitInfo.gitlabEnv,
        gitInfo.namespace,
        gitInfo.projectName
      );
      if (!projectId) {
        return false;
      }

      const axios = await import('axios');

      // 1. 检查分支是否能正常获取详细信息
      const branchUrl = `http://${gitInfo.gitDomain}/api/v4/projects/${projectId}/repository/branches/${encodeURIComponent(branchName)}`;
      
      const branchResponse = await axios.default.get(branchUrl, {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 10000,
      });

      // 分支存在且有commit信息
      if (!branchResponse.data || !branchResponse.data.commit) {
        return false;
      }

      // 2. 尝试检查该分支是否可以用于MR创建（通过检查可能的目标分支）
      // 这里我们通过检查分支的最新提交时间来判断分支是否稳定
      const commitDate = new Date(branchResponse.data.commit.committed_date);
      const now = new Date();
      const timeDiff = now.getTime() - commitDate.getTime();
      
      // 如果分支的最新提交时间距离现在超过1秒，认为分支已经稳定
      // 这是一个保守的检查，确保分支元数据已经完全同步
      return timeDiff >= 1000;

    } catch (error) {
      // 如果API调用失败，说明分支可能还没有完全准备好
      this.logger.debug(`[ProjectPreview-BranchReady] 分支验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 等待MR操作稳定，避免GitLab 422错误（最长3秒）
   */
  private async waitForMergeStabilization(): Promise<void> {
    // 在MR操作之间添加1.5秒延迟，避免GitLab并发限制
    const delay = 1500; // 1.5秒，保持在3秒以内
    this.logger.log(`⏳ [ProjectPreview-MergeStabilization] 等待MR操作稳定，延迟 ${delay}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}