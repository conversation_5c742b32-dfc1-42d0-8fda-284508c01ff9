import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  AsyncSplitImgService,
  ICreateAsyncImgToCodeTask,
  ICreateAsyncImgToCodeTaskRes,
} from './async-split-img.service';
import { DesignProjectBackgroundTaskService } from './background-task.service';
import { DesignProjectService } from './design-project.service';
import { CreateDesignPageDto } from './dto/create-design-page.dto';
import { CreateDesignProjectDto } from './dto/create-design-project.dto';
import { UpdateDesignPageDto } from './dto/update-design-page.dto';
import { UpdateDesignProjectDto } from './dto/update-design-project.dto';
import {
  TASK_TYPE_DEFINITIONS,
  getAllTaskTypes,
  getTaskTypesByCategory,
  isValidTaskType,
} from './enums/task-types.enum';
import { ProjectPreviewService } from './project-preview.service';
import { WorkflowService } from './workflow.service';

// 任务响应接口定义
interface TaskResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  message: string;
  waitForCompletion?: boolean;
  // 等待完成时的额外数据
  playgroundId?: string;
  playgroundIds?: string[];
  files?: any[];
  coordinates?: any;
  // 并行任务的特殊字段
  executionMode?: 'parallel';
  pageCount?: number;
  pageResults?: any[];
  successCount?: number;
  failureCount?: number;
}

@Controller('design-project')
export class DesignProjectController {
  private readonly logger = new Logger(DesignProjectController.name);
  constructor(
    private readonly designProjectService: DesignProjectService,
    private readonly projectPreviewService: ProjectPreviewService,
    private readonly backgroundTaskService: DesignProjectBackgroundTaskService,
    private readonly workflowService: WorkflowService,
    private readonly asyncSplitImgService: AsyncSplitImgService,
    private readonly prisma: PrismaService,
  ) {}

  // 设计稿转码工程相关接口
  @Post()
  create(@Body() createDesignProjectDto: CreateDesignProjectDto) {
    return this.designProjectService.createProject(createDesignProjectDto);
  }

  @Get()
  findAll(@Query('user') user?: string) {
    return this.designProjectService.findAllProjects(user);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.designProjectService.findOneProject(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateDesignProjectDto: UpdateDesignProjectDto) {
    return this.designProjectService.updateProject(id, updateDesignProjectDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.designProjectService.removeProject(id);
  }

  // 设计稿页面相关接口
  @Post(':projectId/pages')
  createPage(@Param('projectId') projectId: string, @Body() createDesignPageDto: CreateDesignPageDto) {
    return this.designProjectService.createPage({
      ...createDesignPageDto,
      designProjectId: projectId,
    });
  }

  @Get(':projectId/pages')
  findAllPages(@Param('projectId') projectId: string) {
    return this.designProjectService.findAllPages(projectId);
  }

  @Get(':projectId/pages/:pageId')
  findOnePage(@Param('pageId') pageId: string) {
    return this.designProjectService.findOnePage(pageId);
  }

  @Patch(':projectId/pages/:pageId')
  updatePage(@Param('pageId') pageId: string, @Body() updateDesignPageDto: UpdateDesignPageDto) {
    return this.designProjectService.updatePage(pageId, updateDesignPageDto);
  }

  @Delete(':projectId/pages/:pageId')
  removePage(@Param('pageId') pageId: string) {
    return this.designProjectService.removePage(pageId);
  }

  // 页面原型相关接口
  @Post(':projectId/pages/:pageId/prototypes')
  createPagePrototype(@Param('pageId') pageId: string, @Body() prototypeData: any) {
    return this.designProjectService.createPagePrototype(pageId, prototypeData);
  }

  @Patch(':projectId/pages/:pageId/prototypes/:prototypeId')
  updatePagePrototype(@Param('prototypeId') prototypeId: string, @Body() prototypeData: any) {
    return this.designProjectService.updatePagePrototype(prototypeId, prototypeData);
  }

  @Delete(':projectId/pages/:pageId/prototypes/:prototypeId')
  removePagePrototype(@Param('prototypeId') prototypeId: string) {
    return this.designProjectService.removePagePrototype(prototypeId);
  }

  // ========================= 任务管理接口 =========================

  // 1. 查询项目下的所有任务列表
  @Get(':projectId/tasks/list')
  async getProjectTasks(
    @Param('projectId') projectId: string,
    @Query('taskType') taskType?: string,
    @Query('taskIds') taskIds?: string,
  ) {
    try {
      this.logger.log(
        `🔍 查询项目任务列表: ${projectId}${taskType ? `, 任务类型: ${taskType}` : ''}${taskIds ? `, 任务ID: ${taskIds}` : ''}`,
      );
      const result = await this.backgroundTaskService.getProjectTasks(projectId, taskType, taskIds);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询项目任务列表失败:`, error);
      throw error;
    }
  }

  // 2. 查询任务详情
  @Get('task/:taskId')
  async getTaskDetail(@Param('taskId') taskId: string) {
    try {
      this.logger.log(`🔍 查询任务详情: ${taskId}`);
      const result = await this.backgroundTaskService.getTaskStatus(taskId);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询任务详情失败:`, error);
      throw error;
    }
  }

  // 3. 查询playground工作区中的文件内容
  @Get('playground/:playgroundId/files/:filePath(*)')
  async getPlaygroundFile(@Param('playgroundId') playgroundId: string, @Param('filePath') filePath: string) {
    try {
      this.logger.log(`🔍 查询工作区文件内容:`, {
        playgroundId,
        filePath,
      });

      const result = await this.backgroundTaskService.getPlaygroundFile(playgroundId, filePath);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询工作区文件内容失败:`, error);
      throw error;
    }
  }

  @Get('playground/:playgroundId/files')
  async listPlaygroundFiles(@Param('playgroundId') playgroundId: string) {
    try {
      this.logger.log(`🔍 查询工作区所有文件:`, {
        playgroundId,
      });

      const files = await this.backgroundTaskService.listPlaygroundFiles(playgroundId);
      return {
        playgroundId,
        files,
        total: files.length,
      };
    } catch (error) {
      this.logger.error(`❌ 查询工作区文件列表失败:`, error);
      throw error;
    }
  }

  // ========================= 任务创建接口 =========================

  /**
   * 构建标准化的任务响应
   */
  private buildTaskResponse(result: any, waitForCompletion: boolean = false, taskType?: string): TaskResponse {
    let status: string;
    let message: string;

    if (waitForCompletion) {
      // 等待完成模式：根据实际结果状态返回
      status = result.status || 'failed'; // 如果没有状态，默认为失败

      if (status === 'completed') {
        message = '任务已完成';
      } else if (status === 'failed') {
        // 尝试从结果中获取错误信息
        const errorMessage = result.error || result.message || '任务执行失败';
        message = typeof errorMessage === 'string' ? errorMessage : '任务执行失败';
      } else {
        message = `任务状态: ${status}`;
      }
    } else {
      // 异步模式：任务已创建
      status = 'pending';
      message = '任务已创建并开始执行';
    }

    const response: TaskResponse = {
      taskId: result.taskId,
      status: status as any,
      message,
      waitForCompletion,
    };

    // 如果等待完成且有额外数据，添加到响应中
    if (waitForCompletion && typeof result === 'object' && result !== null) {
      // 优先处理顶层的playgroundIds字段（新版本已经在createAndExecuteGenericTask中处理）
      if ('playgroundIds' in result && Array.isArray(result.playgroundIds) && result.playgroundIds.length > 0) {
        response.playgroundIds = result.playgroundIds;
        response.playgroundId = result.playgroundIds[0]; // 第一个作为主要ID
        this.logger.log(`✅ [Controller] 从顶层提取到playgroundIds: ${result.playgroundIds.length}个`);
      }

      // 处理pageResults字段
      if ('pageResults' in result && Array.isArray(result.pageResults)) {
        response.pageResults = result.pageResults;
      }

      // 处理pageCount字段
      if ('pageCount' in result && typeof result.pageCount === 'number') {
        response.pageCount = result.pageCount;
      }

      // 处理executionMode字段
      if ('executionMode' in result && result.executionMode) {
        response.executionMode = result.executionMode;
      }

      // 处理successCount和failureCount
      if ('successCount' in result && typeof result.successCount === 'number') {
        response.successCount = result.successCount;
      }
      if ('failureCount' in result && typeof result.failureCount === 'number') {
        response.failureCount = result.failureCount;
      }

      // 处理coordinates字段（用于图片视觉分割任务等）
      if ('coordinates' in result && result.coordinates) {
        response.coordinates = result.coordinates;
      }

      // 如果顶层没有playgroundIds，再尝试从嵌套结构中提取（兼容旧版本）
      if (!response.playgroundIds) {
        // 特殊处理spec-to-prod-code任务的playgroundIds提取（从嵌套结构）
        if (taskType === 'spec-to-prod-code' && result.result?.result) {
          const taskResult = result.result.result;

          // 从results数组中提取所有页面的playgroundId（包括成功和失败的）
          if (taskResult.results && Array.isArray(taskResult.results)) {
            const allPlaygroundIds = taskResult.results
              .filter((pageResult: any) => pageResult.playgroundId) // 只要有playgroundId就包含
              .map((pageResult: any) => pageResult.playgroundId);

            if (allPlaygroundIds.length > 0) {
              response.playgroundIds = allPlaygroundIds;
              response.playgroundId = allPlaygroundIds[0]; // 兼容性：设置第一个作为主要playgroundId
              this.logger.log(`✅ [Controller] 从嵌套结构提取到playgroundIds: ${allPlaygroundIds.length}个`);
            }
          }

          // 如果results数组没有提取到，尝试从playgroundIds数组提取
          if (!response.playgroundIds && taskResult.playgroundIds && Array.isArray(taskResult.playgroundIds)) {
            response.playgroundIds = taskResult.playgroundIds;
            response.playgroundId = taskResult.playgroundIds[0];
            this.logger.log(`✅ [Controller] 从嵌套playgroundIds数组提取到: ${taskResult.playgroundIds.length}个`);
          }

          // 添加页面结果详情
          if (taskResult.results) {
            response.pageResults = taskResult.results;
          }
          if (typeof taskResult.successCount === 'number') {
            response.successCount = taskResult.successCount;
          }
          if (typeof taskResult.failureCount === 'number') {
            response.failureCount = taskResult.failureCount;
          }
          if (taskResult.executionMode) {
            response.executionMode = taskResult.executionMode;
          }
        } else {
          // 其他任务类型的常规处理
          if ('playgroundId' in result && result.playgroundId) {
            response.playgroundId = result.playgroundId;
          }
          // 对于其他任务类型，检查result.result层级
          if ('result' in result && result.result && typeof result.result === 'object') {
            const taskResult = result.result as any;
            if ('results' in taskResult) {
              response.pageResults = taskResult.results;
            }
            if ('successCount' in taskResult) {
              response.successCount = taskResult.successCount;
            }
            if ('failureCount' in taskResult) {
              response.failureCount = taskResult.failureCount;
            }
          }
        }
      }

      // 这里是问题所在 - 对于 spec-to-prod-code 任务，我们不应该返回 files
      if ('files' in result && result.files && taskType !== 'spec-to-prod-code') {
        response.files = result.files;
      }
      if ('coordinates' in result && result.coordinates) {
        response.coordinates = result.coordinates;
      }
      // 并行任务的特殊字段
      if ('executionMode' in result) {
        response.executionMode = result.executionMode;
      }
      if ('pageCount' in result) {
        response.pageCount = result.pageCount;
      }
    }

    return response;
  }

  // 1. 创建蓝湖设计稿转码任务（蓝湖设计稿代码生成高质量HTML代码）
  @Post(':projectId/tasks/lanhu-transcode')
  async createTranscodeTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      // 方式1: 使用原型IDs
      prototypeIds?: string[];
      // 方式2: 直接提供HTML/CSS代码
      htmlContent?: string;
      cssContent?: string;
      name?: string; // 自定义名称（仅直接提供代码时使用）
      // 通用参数
      user: string; // 用户ID (必填)
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选，默认false)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选，默认false)
      metadata?: {}; // 任务元数据 (可选)
      waitForCompletion?: boolean; // 是否等待任务完成 (可选，默认false)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到设计稿转码任务请求:`, {
      projectId,
      ...(body.prototypeIds ? { prototypeIds: body.prototypeIds } : { directCodeInput: true }),
      waitForCompletion: body.waitForCompletion || false,
    });

    try {
      let result: any;

      // 处理两种不同的输入方式
      if (body.prototypeIds && body.prototypeIds.length > 0) {
        // 方式1：使用原型IDs（原有方式）
        result = await this.backgroundTaskService.createAndExecuteTranscodeTask({
          projectId,
          prototypeIds: body.prototypeIds,
          user: body.user,
          model: body.model,
          enableAutoIteration: body.enableAutoIteration ?? false,
          enableStepByStep: body.enableStepByStep ?? false,
          waitForCompletion: body.waitForCompletion,
        });
      } else if (body.htmlContent) {
        // 方式2：直接提供HTML/CSS代码
        result = await this.backgroundTaskService.createAndExecuteDirectTranscodeTask({
          projectId,
          htmlContent: body.htmlContent,
          cssContent: body.cssContent || '',
          name: body.name || '直接代码输入',
          user: body.user,
          model: body.model,
          enableAutoIteration: body.enableAutoIteration ?? false,
          enableStepByStep: body.enableStepByStep ?? false,
          waitForCompletion: body.waitForCompletion,
        });
      } else {
        throw new Error('必须提供 prototypeIds 或 htmlContent');
      }

      const response = this.buildTaskResponse(result, body.waitForCompletion, 'design-transcode');
      this.logger.log(`✅ 设计稿转码任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 设计稿转码任务创建失败:`, error);
      throw error;
    }
  }

  // 2. 创建设计稿原型代码合并任务（多个页面的转码后的原型HTML代码合并任务）
  @Post(':projectId/tasks/merge')
  async createMergeTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      prototypeIds: string[]; // 原型ID数组
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据 (可选)
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到设计稿合并任务请求:`, {
      projectId,
      prototypeCount: body.prototypeIds.length,
      waitForCompletion: body.waitForCompletion || false,
    });

    try {
      // 验证原型是否存在并获取详细信息
      const prototypes = await this.designProjectService.validateAndGetPrototypes(body.prototypeIds);

      // 智能检测：如果只有一个原型，提示用户这将直接复制结果而非合并
      if (prototypes.length === 1) {
        this.logger.log(`🎯 检测到单个原型，将直接复制转码结果到页面而非执行合并`);

        const prototype = prototypes[0];
        this.logger.log(`📋 单个原型信息:`, {
          prototypeId: prototype.id,
          prototypeName: prototype.prototypeName,
          designPageId: prototype.designPageId,
          designPageName: prototype.designPage.name,
          playgroundId: prototype.playgroundId,
        });
      }

      // 检查所有原型是否属于同一个页面
      const uniquePageIds = [...new Set(prototypes.map((p) => p.designPageId))];
      if (uniquePageIds.length > 1) {
        this.logger.warn(`⚠️ 发现原型属于多个页面 (${uniquePageIds.length} 个页面)，将继续处理`);
      } else {
        this.logger.log(`📄 所有原型均属于页面: ${uniquePageIds[0]} (${prototypes[0].designPage.name})`);
      }

      const result = await this.backgroundTaskService.createAndExecuteMergeTask({
        projectId,
        prototypeIds: body.prototypeIds,
        projectName: `${prototypes.length === 1 ? '单原型结果复制' : '合并页面原型代码'}_${new Date().getTime()}`,
        user: body.user,
        model: body.model,
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        waitForCompletion: body.waitForCompletion,
      });

      // 构建响应
      const response = this.buildTaskResponse(result, body.waitForCompletion, 'design-merge');

      // 为单个原型的情况添加特殊标识
      if (prototypes.length === 1) {
        response.message = body.waitForCompletion
          ? '单个原型结果已直接复制到页面'
          : '单个原型结果复制任务已创建并开始执行';

        // 添加额外的响应字段
        if (body.waitForCompletion && typeof result === 'object' && result !== null) {
          if ('designPageId' in result) {
            (response as any).designPageId = result.designPageId;
          }
          if ('singlePrototypeMode' in result) {
            (response as any).singlePrototypeMode = result.singlePrototypeMode;
          }
        } else {
          // 异步模式，添加预期的结果信息
          (response as any).singlePrototypeMode = true;
          (response as any).designPageId = prototypes[0].designPageId;
          (response as any).prototypeName = prototypes[0].prototypeName;
        }
      }

      this.logger.log(`✅ 设计稿合并任务创建成功:`, {
        taskId: response.taskId,
        prototypeCount: prototypes.length,
        singlePrototypeMode: prototypes.length === 1,
        targetPageId: prototypes[0].designPageId,
      });

      return response;
    } catch (error) {
      this.logger.error(`❌ 设计稿合并任务创建失败:`, error);
      throw error;
    }
  }

  // 3. 创建图片转代码任务
  @Post(':projectId/tasks/img-to-code')
  async createImgToCodeTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      items: Array<{
        prototypeId: string; // 原型ID
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据 (可选)
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到图片转代码任务请求:`, {
      projectId,
      itemCount: body.items.length,
      waitForCompletion: body.waitForCompletion || false,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl,
          );

          return {
            id: item.prototypeId,
            name: `图片转代码_${item.prototypeId}`,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
            },
          };
        }),
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-to-code',
        taskName: `图片转代码任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: body.metadata,
        waitForCompletion: body.waitForCompletion,
      });

      const response = this.buildTaskResponse(result, body.waitForCompletion, 'img-to-code');
      this.logger.log(`✅ 图片转代码任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 图片转代码任务创建失败:`, error);
      throw error;
    }
  }

  // 4. 创建图片视觉分割任务
  @Post(':projectId/tasks/img-visual-split')
  async createImgVisualSplitTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      items: Array<{
        prototypeId: string; // 原型ID
        name?: string; // 项目名称 (可选)
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到图片视觉分割任务请求:`, {
      projectId,
      itemCount: body.items.length,
      splitOptions: body.metadata,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl,
          );

          return {
            id: item.prototypeId,
            name: item.name || `图片视觉分割_${item.prototypeId}`, // 如果没有name字段，自动生成
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
            },
          };
        }),
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-visual-split',
        taskName: `图片视觉分割任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: body.metadata,
        waitForCompletion: body.waitForCompletion,
      });

      const response = this.buildTaskResponse(result, body.waitForCompletion, 'img-visual-split');
      this.logger.log(`✅ 图片视觉分割任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 图片视觉分割任务创建失败:`, error);
      throw error;
    }
  }

  // 5. 创建从分割图生成代码任务
  @Post(':projectId/tasks/img-split-to-code')
  async createImgSplitToCodeTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      items: Array<{
        prototypeId: string; // 原型ID
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到从分割图生成代码任务请求:`, {
      projectId,
      itemCount: body.items.length,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl,
          );

          return {
            id: item.prototypeId,
            name: `从分割图生成代码_${item.prototypeId}`,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
            },
          };
        }),
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-split-to-code',
        taskName: `从分割图生成代码任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview',
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: body.metadata,
        waitForCompletion: body.waitForCompletion,
      });

      const response = this.buildTaskResponse(result, body.waitForCompletion, 'img-split-to-code');
      this.logger.log(`✅ 从分割图生成代码任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 从分割图生成代码任务创建失败:`, error);
      throw error;
    }
  }

  // 6. 创建从切图坐标生成布局任务
  @Post(':projectId/tasks/coords-to-layout')
  async createCoordsToLayoutTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      items: Array<{
        prototypeId: string; // 原型ID
        metadata: {
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
          coordinates?: Array<{
            // 组件坐标数组
            name: string;
            x1: number;
            y1: number;
            x2: number;
            y2: number;
          }>;
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到从切图坐标生成布局任务请求:`, {
      projectId,
      itemCount: body.items.length,
      layoutOptions: body.metadata,
    });

    try {
      // 转换为通用任务项格式
      const taskItems = body.items.map((item) => ({
        id: item.prototypeId,
        name: `坐标生成布局_${item.prototypeId}`,
        metadata: item.metadata,
      }));

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'coords-to-layout',
        taskName: `从切图坐标生成布局任务_${new Date().getTime()}`,
        items: taskItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: body.metadata,
        waitForCompletion: body.waitForCompletion,
      });

      const response = this.buildTaskResponse(result, body.waitForCompletion, 'coords-to-layout');
      this.logger.log(`✅ 从切图坐标生成布局任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 从切图坐标生成布局任务创建失败:`, error);
      throw error;
    }
  }

  // 7. 创建生产级代码生成任务（并行处理）
  @Post(':projectId/tasks/spec-to-prod-code')
  async createSpecToProdCodeTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      taskName: string; // 任务名称
      gitUrl: string; // 项目框架Git地址 (必填)
      branch?: string; // Git分支 (可选，默认master)
      pages: Array<{
        // 页面数组 (必填) - 支持多页面并行处理
        name: string; // 页面名称
        htmlContent: string; // HTML内容
        description?: string; // 页面描述 (可选)
      }>;
      user: string; // 用户ID (必填)
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到并行生产级代码生成任务请求:`, {
      taskName: body.taskName,
      gitUrl: body.gitUrl,
      branch: body.branch || 'master',
      pageCount: body.pages.length,
      executionMode: 'parallel',
    });

    try {
      // 验证输入参数
      if (!body.gitUrl) {
        throw new Error('Git地址不能为空');
      }
      if (!body.pages || body.pages.length === 0) {
        throw new Error('至少需要一个页面');
      }

      // 🔧 关键修改：为每个页面创建独立的BackgroundTaskItem
      const taskItems = body.pages.map((page, index) => ({
        id: `spec-to-prod-code-page-${index + 1}`, // 为每个页面生成唯一ID
        name: `${body.taskName} - ${page.name}`, // 包含页面名称的任务项名称
        metadata: {
          gitUrl: body.gitUrl,
          branch: body.branch || 'master',
          projectId: projectId,
          // 🔧 关键修改：每个TaskItem只包含一个页面的信息
          page: {
            name: page.name,
            htmlContent: page.htmlContent,
            description: page.description,
          },
          // 保留全局页面信息用于协调
          totalPages: body.pages.length,
          pageIndex: index,
          taskName: body.taskName,
        },
      }));

      this.logger.log(`📋 创建了 ${taskItems.length} 个独立的任务项，每个页面对应一个BackgroundTaskItem`);

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'spec-to-prod-code',
        taskName: body.taskName,
        items: taskItems, // 🔧 关键修改：传递多个TaskItem而不是一个
        user: body.user,
        model: body.model,
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: {
          projectId: projectId,
          executionMode: 'parallel',
          pageCount: body.pages.length,
        },
        waitForCompletion: body.waitForCompletion,
      });

      // 构建响应，包含并行任务的特殊字段
      const response = this.buildTaskResponse(result, body.waitForCompletion, 'spec-to-prod-code');
      response.executionMode = 'parallel';
      response.pageCount = body.pages.length;

      this.logger.log(`✅ 并行生产级代码生成任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 并行生产级代码生成任务创建失败:`, error);
      throw error;
    }
  }

  // ========================= 工作流任务 =========================
  // 启动页面级代码生成任务工作流
  @Post(':projectId/tasks/page-code-generation-workflow')
  async createPageCodeGenerationWorkflowTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      pageId: string; // 页面ID
      user: string; // 用户ID
      model?: string; // AI模型
      enableAutoIteration?: boolean; // 是否启用自动迭代
      enableStepByStep?: boolean; // 是否启用分步执行
      gitUrl?: string; // Git仓库地址
      branch?: string; // Git分支
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到页面级代码生成工作流请求:`, {
      projectId,
      pageId: body.pageId,
      user: body.user,
    });

    try {
      const result = await this.workflowService.createPageCodeGenerationWorkflow(projectId, body.pageId, body.user, {
        model: body.model,
        enableAutoIteration: body.enableAutoIteration,
        enableStepByStep: body.enableStepByStep,
        gitUrl: body.gitUrl,
        branch: body.branch,
      });

      this.logger.log(`✅ 页面级代码生成工作流创建成功: ${result.workflowId}`);

      return {
        taskId: result.workflowId,
        status: result.status.status as any,
        message: '页面级代码生成工作流已启动',
        waitForCompletion: false,
      };
    } catch (error) {
      this.logger.error(`❌ 页面级代码生成工作流创建失败:`, error);
      throw error;
    }
  }

  // 查询工作流状态
  @Get(':projectId/workflows/:workflowId/status')
  async getWorkflowStatus(@Param('projectId') projectId: string, @Param('workflowId') workflowId: string) {
    try {
      this.logger.log(`🔍 查询工作流状态: ${workflowId}`);
      const status = await this.workflowService.getWorkflowStatus(workflowId);
      return status;
    } catch (error) {
      this.logger.error(`❌ 查询工作流状态失败:`, error);
      throw error;
    }
  }

  // 启动图生码工作流状态
  @Post('async-img-to-code/task/create')
  async executeAsyncImgToCode(
    @Body()
    body: ICreateAsyncImgToCodeTask,
  ): Promise<ICreateAsyncImgToCodeTaskRes> {
    this.logger.log(`🎯 收到图生码工作流请求:`, {
      ...body,
    });

    try {
      const result = await this.asyncSplitImgService.createAsyncImgToCodeTask(body);

      this.logger.log(`✅ 图生码工作流创建成功: ${result.taskId}`);

      return result;
    } catch (error) {
      this.logger.error(`❌ 图生码工作流创建失败:`, error);
      throw error;
    }
  }

  // 查询图生码工作流状态
  @Get('async-img-to-code/:taskId/status')
  async getAsyncImgToCodeStatus(@Param('taskId') taskId: string) {
    try {
      this.logger.log(`🔍 查询图生码工作流状态: ${taskId}`);
      const asyncSplitImgTask = await this.asyncSplitImgService.getAsyncImgToCodeStatus(taskId);
      return asyncSplitImgTask;
    } catch (error) {
      this.logger.error(`❌ 查询图生码工作流状态失败:`, error);
      throw error;
    }
  }

  // 根据chatId获取图转码任务
  @Get('async-img-to-code/task/:chatId')
  async getAsyncImgToCodeTaskByChatId(@Param('chatId') chatId: string) {
    try {
      this.logger.log(`🔍 查询图生码任务: ${chatId}`);
      const asyncSplitImgTask = await this.asyncSplitImgService.getAsyncImgToCodeTaskByChatId(chatId);
      return asyncSplitImgTask;
    } catch (error) {
      this.logger.error(`❌ 查询图生码任务失败:`, error);
      throw error;
    }
  }

  // 手动同步工作流状态
  @Post(':projectId/workflows/:workflowId/sync')
  async syncWorkflowStatus(@Param('projectId') projectId: string, @Param('workflowId') workflowId: string) {
    try {
      this.logger.log(`🔄 手动同步工作流状态: ${workflowId}`);
      await this.workflowService.syncWorkflowStatus(workflowId);

      // 同步后返回最新状态
      const status = await this.workflowService.getWorkflowStatus(workflowId);
      return {
        message: '工作流状态同步完成',
        status,
      };
    } catch (error) {
      this.logger.error(`❌ 同步工作流状态失败:`, error);
      throw error;
    }
  }

  // 重试现有工作流
  @Post(':projectId/workflows/:workflowId/restart')
  async restartWorkflow(
    @Param('projectId') projectId: string,
    @Param('workflowId') workflowId: string,
    @Body()
    body: {
      user: string; // 重试操作的用户
    },
  ) {
    try {
      this.logger.log(`🔄 重试工作流: ${workflowId}, 用户: ${body.user}`);

      // 调用工作流服务的重试方法
      await this.workflowService.restartWorkflow(workflowId, body.user);

      // 返回重试后的状态
      const status = await this.workflowService.getWorkflowStatus(workflowId);
      return {
        message: '工作流重试成功',
        workflowId,
        status,
      };
    } catch (error) {
      this.logger.error(`❌ 重试工作流失败: ${workflowId}`, error);
      throw error;
    }
  }

  // 启动多页面级代码生成工作流
  @Post(':projectId/tasks/multi-page-code-generation-workflow')
  async createMultiPageCodeGenerationWorkflowTask(
    @Param('projectId') projectId: string,
    @Body()
    body: {
      pageIds: string[]; // 页面ID数组
      user: string; // 用户ID
      model?: string; // AI模型
      enableAutoIteration?: boolean; // 是否启用自动迭代
      enableStepByStep?: boolean; // 是否启用分步执行
      gitUrl?: string; // Git仓库地址
      branch?: string; // Git分支
    },
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到多页面代码生成工作流请求:`, {
      projectId,
      pageCount: body.pageIds.length,
      user: body.user,
    });

    try {
      const result = await this.workflowService.createMultiPageCodeGenerationWorkflow(
        projectId,
        body.pageIds,
        body.user,
        {
          model: body.model,
          enableAutoIteration: body.enableAutoIteration,
          enableStepByStep: body.enableStepByStep,
          gitUrl: body.gitUrl,
          branch: body.branch,
        },
      );

      this.logger.log(`✅ 多页面代码生成工作流创建成功: ${result.workflowId}`);

      return {
        taskId: result.workflowId,
        status: result.status.status as any,
        message: `多页面代码生成工作流已启动，共${body.pageIds.length}个页面`,
        waitForCompletion: false,
      };
    } catch (error) {
      this.logger.error(`❌ 多页面代码生成工作流创建失败:`, error);
      throw error;
    }
  }

  // 查询多页面工作流状态
  @Get(':projectId/workflows/:workflowId/multi-page-status')
  async getMultiPageWorkflowStatus(@Param('projectId') projectId: string, @Param('workflowId') workflowId: string) {
    try {
      this.logger.log(`🔍 查询多页面工作流状态: ${workflowId}`);
      const status = await this.workflowService.getMultiPageWorkflowStatus(workflowId);
      return status;
    } catch (error) {
      this.logger.error(`❌ 查询多页面工作流状态失败:`, error);
      throw error;
    }
  }

  // 查询项目的所有工作流
  @Get(':projectId/workflows')
  async getProjectWorkflows(@Param('projectId') projectId: string) {
    try {
      this.logger.log(`🔍 查询项目工作流列表: ${projectId}`);
      const workflows = await this.workflowService.getProjectWorkflows(projectId);
      return workflows;
    } catch (error) {
      this.logger.error(`❌ 查询项目工作流列表失败:`, error);
      throw error;
    }
  }

  /**
   * 获取工作流相关的Merge Request列表
   */
  @Get(':projectId/workflows/:workflowId/merge-requests')
  async getWorkflowMergeRequests(
    @Param('projectId') projectId: string,
    @Param('workflowId') workflowId: string,
  ) {
    try {
      const mergeRequests = await this.workflowService.getWorkflowMergeRequests(projectId, workflowId);
      return mergeRequests;
    } catch (error) {
      throw new BadRequestException(`获取工作流MR列表失败: ${error.message}`);
    }
  }

  /**
   * 获取页面相关的Merge Request列表（用于页面级工作流）
   */
  @Get(':projectId/pages/:pageId/merge-requests')
  async getPageMergeRequests(
    @Param('projectId') projectId: string,
    @Param('pageId') pageId: string,
  ) {
    try {
      const mergeRequests = await this.workflowService.getPageMergeRequests(projectId, pageId);
      return mergeRequests;
    } catch (error) {
      throw new BadRequestException(`获取页面MR列表失败: ${error.message}`);
    }
  }

  // 根据页面ID查询项目ID（辅助接口）
  @Get('page/:pageId/project')
  async getProjectIdByPageId(@Param('pageId') pageId: string) {
    try {
      this.logger.log(`🔍 查询页面所属项目: ${pageId}`);

      const page = await this.prisma.designPage.findUnique({
        where: { id: pageId },
        select: {
          id: true,
          name: true,
          designProjectId: true,
          designProject: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!page) {
        throw new Error(`页面 ${pageId} 不存在`);
      }

      return {
        pageId: page.id,
        pageName: page.name,
        projectId: page.designProjectId,
        projectName: page.designProject.name,
      };
    } catch (error) {
      this.logger.error(`❌ 查询页面所属项目失败:`, error);
      throw error;
    }
  }

  // ========================= 兼容性接口 (保持原有功能) =========================

  // 查询任务状态（兼容原有接口）
  @Get('tasks/:taskId/status')
  async getTaskStatus(@Param('taskId') taskId: string) {
    try {
      const result = await this.backgroundTaskService.getTaskStatus(taskId);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询任务状态失败:`, error);
      throw error;
    }
  }

  // 查询用户任务列表（兼容原有接口）
  @Get('tasks/user/:userId')
  async getUserTasks(@Param('userId') userId: string, @Query('taskType') taskType?: string) {
    try {
      const result = await this.backgroundTaskService.getUserTasks(userId, taskType);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询用户任务列表失败:`, error);
      throw error;
    }
  }

  // 获取所有任务类型定义
  @Get('task-types/definitions')
  async getTaskTypeDefinitions() {
    return TASK_TYPE_DEFINITIONS;
  }

  // 获取所有任务类型
  @Get('task-types/all')
  async getAllTaskTypes() {
    return getAllTaskTypes();
  }

  // 获取特定类别的任务类型
  @Get('task-types/category/:category')
  async getTaskTypesByCategory(@Param('category') category: string) {
    const validCategories = ['image-processing', 'code-generation', 'design-optimization', 'custom'];
    if (!validCategories.includes(category)) {
      throw new Error(`无效的任务类别: ${category}，有效类别: ${validCategories.join(', ')}`);
    }
    return getTaskTypesByCategory(
      category as 'image-processing' | 'code-generation' | 'design-optimization' | 'custom',
    );
  }

  // 验证任务类型是否有效
  @Get('task-types/validate/:taskType')
  async validateTaskType(@Param('taskType') taskType: string) {
    return {
      taskType,
      isValid: isValidTaskType(taskType),
    };
  }

  // ========================= 项目基于Webide预览功能 =========================

  /**
   * 创建项目基于Webide预览
   * 查询应用级代码生成历史，按页面维度分组找出最新记录，创建预览分支并合并
   */
  @Post(':projectId/preview')
  async createProjectPreview(@Param('projectId') projectId: string, @Body() body: { user: string }): Promise<any> {
    try {
      this.logger.log(`🎯 [API] 收到项目预览请求: ${projectId}, 用户: ${body.user}`);

      const result = await this.projectPreviewService.createProjectPreview({
        projectId,
        user: body.user,
      });

      this.logger.log(`✅ [API] 项目预览请求处理完成: ${projectId}, 成功: ${result.success}`);

      return result;
    } catch (error) {
      this.logger.error(`❌ [API] 项目预览请求失败: ${projectId}`, error);
      throw error;
    }
  }

  /**
   * 查询项目代码生成历史（用于预览前的信息展示）
   */
  @Get(':projectId/code-generation-history')
  async getProjectCodeGenerationHistory(@Param('projectId') projectId: string): Promise<any> {
    try {
      this.logger.log(`🔍 [API] 查询项目代码生成历史: ${projectId}`);

      const history = await this.designProjectService.getProjectCodeGenerationHistory(projectId);

      this.logger.log(`✅ [API] 项目代码生成历史查询完成: ${projectId}, 页面数: ${history.pageGenerations.length}`);

      return history;
    } catch (error) {
      this.logger.error(`❌ [API] 查询项目代码生成历史失败: ${projectId}`, error);
      throw error;
    }
  }
}
