import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { TaskType } from './enums/task-types.enum';
import { 
  PromptManagementService, 
  CreatePromptTemplateDto, 
  UpdatePromptTemplateDto,
  ProjectPromptTemplate,
  PromptTemplateSummary,
} from './prompt-management.service';
import { PromptTemplateManager } from './prompts/template-manager';

export interface CreatePromptDto {
  projectId: string;
  taskType: string;
  promptType: 'main' | 'check' | 'target';
  content: string;
}

export interface UpdatePromptDto {
  content: string;
}

export interface DefaultPromptResponse {
  taskType: string;
  promptType: 'main' | 'check' | 'target';
  content: string | null;
}

export interface TargetPromptDto {
  content: string;
}

/**
 * 提示词管理控制器
 * 提供项目自定义提示词模板的REST API接口
 */
@Controller('design-project/prompt-management')
export class PromptManagementController {
  constructor(
    private readonly promptManagementService: PromptManagementService,
    private readonly promptTemplateManager: PromptTemplateManager,
  ) {}

  /**
   * 获取项目提示词模板概览
   */
  @Get('projects/:projectId/summary')
  async getProjectPromptSummary(
    @Param('projectId') projectId: string,
  ): Promise<PromptTemplateSummary[]> {
    return this.promptManagementService.getProjectPromptSummary(projectId);
  }

  /**
   * 获取项目的所有自定义提示词模板
   */
  @Get('projects/:projectId/templates')
  async getProjectPromptTemplates(
    @Param('projectId') projectId: string,
  ): Promise<ProjectPromptTemplate[]> {
    return this.promptManagementService.getProjectPromptTemplates(projectId);
  }

  /**
   * 获取特定的提示词模板
   */
  @Get('projects/:projectId/templates/:taskType/:promptType')
  async getPromptTemplate(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
    @Param('promptType') promptType: 'main' | 'check',
  ): Promise<{ content: string | null; isCustom: boolean; template?: ProjectPromptTemplate }> {
    const customTemplates = await this.promptManagementService.getProjectPromptTemplates(projectId);
    const customTemplate = customTemplates.find(
      t => t.taskType === taskType && t.promptType === promptType
    );

    if (customTemplate) {
      return {
        content: customTemplate.content,
        isCustom: true,
        template: customTemplate,
      };
    }

    // 获取默认内容
    const defaultContent = await this.promptManagementService.getDefaultPromptContent(taskType, promptType);
    return {
      content: defaultContent,
      isCustom: false,
    };
  }

  /**
   * 创建或更新项目自定义提示词模板
   */
  @Post('projects/:projectId/templates')
  async createOrUpdatePromptTemplate(
    @Param('projectId') projectId: string,
    @Body() body: CreatePromptDto,
    @Query('user') user: string = 'system',
  ): Promise<ProjectPromptTemplate> {
    const data: CreatePromptTemplateDto = {
      ...body,
      projectId,
      createdBy: user,
    };

    return this.promptManagementService.upsertPromptTemplate(data);
  }

  /**
   * 更新提示词模板
   */
  @Put('templates/:id')
  async updatePromptTemplate(
    @Param('id') id: string,
    @Body() body: UpdatePromptDto,
    @Query('user') user: string = 'system',
  ): Promise<ProjectPromptTemplate> {
    const data: UpdatePromptTemplateDto = {
      content: body.content,
      updatedBy: user,
    };

    return this.promptManagementService.updatePromptTemplate(id, data);
  }

  /**
   * 删除项目自定义提示词模板
   */
  @Delete('templates/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePromptTemplate(@Param('id') id: string): Promise<void> {
    return this.promptManagementService.deletePromptTemplate(id);
  }

  /**
   * 重置项目提示词为默认值
   */
  @Post('projects/:projectId/templates/:taskType/:promptType/reset')
  @HttpCode(HttpStatus.NO_CONTENT)
  async resetToDefault(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
    @Param('promptType') promptType: 'main' | 'check',
  ): Promise<void> {
    return this.promptManagementService.resetToDefault(projectId, taskType, promptType);
  }

  /**
   * 获取默认提示词内容
   */
  @Get('defaults/:taskType/:promptType')
  async getDefaultPromptContent(
    @Param('taskType') taskType: string,
    @Param('promptType') promptType: 'main' | 'check',
  ): Promise<DefaultPromptResponse> {
    const content = await this.promptManagementService.getDefaultPromptContent(taskType, promptType);
    
    return {
      taskType,
      promptType,
      content,
    };
  }

  /**
   * 获取有效提示词内容（项目自定义优先，否则默认）
   */
  @Get('projects/:projectId/effective/:taskType/:promptType')
  async getEffectivePromptContent(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
    @Param('promptType') promptType: 'main' | 'check',
  ): Promise<{ content: string | null; isCustom: boolean }> {
    const content = await this.promptManagementService.getEffectivePromptContent(
      projectId, 
      taskType, 
      promptType
    );

    // 检查是否是自定义内容
    const customTemplates = await this.promptManagementService.getProjectPromptTemplates(projectId);
    const hasCustom = customTemplates.some(
      t => t.taskType === taskType && t.promptType === promptType
    );

    return {
      content,
      isCustom: hasCustom,
    };
  }

  /**
   * 获取spec-to-prod-code任务的target提示词
   * 注意：target提示词目前只支持默认模板，不支持项目自定义
   */
  @Get('projects/:projectId/templates/:taskType/target')
  async getTargetPromptTemplate(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
  ): Promise<{ content: string | null; isCustom: boolean }> {
    // 只有spec-to-prod-code任务支持target提示词
    if (taskType !== TaskType.SPEC_TO_PROD_CODE) {
      return {
        content: null,
        isCustom: false,
      };
    }

    // 获取默认的target提示词内容
    const content = this.promptTemplateManager.getTargetPromptContent(taskType);
    
    return {
      content,
      isCustom: false, // 目前target提示词不支持自定义
    };
  }

  /**
   * 更新spec-to-prod-code任务的target提示词
   */
  @Post('projects/:projectId/templates/:taskType/target')
  async updateTargetPromptTemplate(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
    @Body() body: TargetPromptDto,
  ): Promise<{ message: string }> {
    // 只有spec-to-prod-code任务支持target提示词
    if (taskType !== TaskType.SPEC_TO_PROD_CODE) {
      throw new Error(`任务类型 ${taskType} 不支持 target 提示词`);
    }

    // 使用通用服务保存target提示词
    const dto: CreatePromptTemplateDto = {
      projectId,
      taskType,
      promptType: 'target',
      content: body.content,
      createdBy: 'system', // 后续可以从JWT中获取用户ID
    };

    await this.promptManagementService.upsertPromptTemplate(dto);

    return {
      message: 'target提示词已保存',
    };
  }

  /**
   * 重置spec-to-prod-code任务的target提示词
   * 注意：target提示词目前只支持默认模板，此接口暂时返回成功
   */
  @Post('projects/:projectId/templates/:taskType/target/reset')
  @HttpCode(HttpStatus.NO_CONTENT)
  async resetTargetToDefault(
    @Param('projectId') projectId: string,
    @Param('taskType') taskType: string,
  ): Promise<void> {
    // 只有spec-to-prod-code任务支持target提示词
    if (taskType !== TaskType.SPEC_TO_PROD_CODE) {
      throw new Error(`任务类型 ${taskType} 不支持 target 提示词`);
    }

    // target提示词本身就是默认的，无需重置操作
    return;
  }
} 