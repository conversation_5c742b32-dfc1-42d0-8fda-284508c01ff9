export class CreateDesignProjectDto {
  name: string;
  description: string;
  projectId?: string; // 关联的项目代码模板ID
  user: string;
  config?: any; // 转码配置

  // 技术栈配置
  framework?: string; // 框架
  componentLibrary?: string; // UI组件库
  llmstxt?: string; // 自定义组件说明

  // Git配置
  gitUrl?: string; // Git仓库地址
  gitBranch?: string; // Git分支（生码分支）
  gitCodeDir?: string; // 代码生成目录

  // 预览服务配置
  previewStartCommand?: string; // 自定义预览启动命令

  // 蓝湖配置
  lanhuProjectId?: string; // 蓝湖项目ID
  lanhuToken?: string; // 蓝湖Token
}