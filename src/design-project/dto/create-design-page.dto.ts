
interface ISegment {
  img_url: string, // 组件的图片地址
  name: string,
  id: string,
  position: number[] // 组件坐标
}

interface SlicedAssets {
  // 生成的 html 内容
  html:{
    name: string,
    content: string
  },
  fullImgUrl: string // 用户上传的原始图片地址
  segment?: ISegment[]; // 存储组件信息
  [key: string]: any; // 添加索引签名以兼容 Prisma InputJsonValue
}

export class CreateDesignPagePrototypeDto {
  prototypeName: string; // 原型名称，如A、B、C
  englishName?: string; // 英文名
  htmlContent?: string;
  cssContent?: string;
  htmlFileName?: string;
  cssFileName?: string;
  imgFileName?: string;
  imgFileLink?: string;
  lanhuVersionId?: string; // 蓝湖版本ID
  imgWidth?: string;
  imgHeight?: string;
  slicedAssets?: SlicedAssets; // 智能切图资产信息
}

export class CreateDesignPageDto {
  name: string;
  description?: string;
  designProjectId: string;
  prototypes: CreateDesignPagePrototypeDto[]; // 支持多个原型
}