import { PartialType } from '@nestjs/mapped-types';
import { CreateDesignPageDto, CreateDesignPagePrototypeDto } from './create-design-page.dto';

export class UpdateDesignPagePrototypeDto extends PartialType(CreateDesignPagePrototypeDto) {
  id?: string; // 原型ID（更新时需要）
  englishName?: string; // 英文名
  status?: string; // 允许更新状态
  resultHtml?: string; // 允许更新转码结果
  resultCss?: string;
}

export class UpdateDesignPageDto {
  name?: string;
  englishName?: string; // 英文名
  description?: string;
  prototypes?: UpdateDesignPagePrototypeDto[]; // 支持原型更新
}