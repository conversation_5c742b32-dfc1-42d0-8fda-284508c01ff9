import type { CreateCommonChatDto } from '../ai-coding/dto/create-ai-coding.dto';
import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';
import { Project } from '@prisma/client';
import { AiCodingService } from '../ai-coding/ai-coding.service';
import { BackgroundTaskService } from '../background-task/background-task.service';
import { TaskWatcherService } from '../background-task/task-watcher.service';
import { PrismaService } from '../prisma/prisma.service';
import { coordinatesToHtml } from '../utils';
import { DesignProjectBackgroundTaskService } from './background-task.service';
import { DesignProjectService } from './design-project.service';

export enum ETaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface AsyncSplitImgTask {
  taskId: string;
  status: ETaskStatus;
  progress: number;
  nodes: AsyncSplitImgTaskNode[][];
  startTime?: Date;
  endTime?: Date;
  error?: string;
}

export enum ETaskType {
  DIRECT_IMG_TO_CODE = 'direct-img-to-code', // 直接图转码
  SPLIT_IMG_TO_CODE = 'split-img-to-code', // 拆图(智能拆分/手动拆分)转码
}

export enum ESplitImgTaskNodeType {
  IMG_TO_CODE = 'img-to-code', // 图转码(html)
  SPLIT_IMG_TO_CODE = 'split-img-to-code', // 拆的单图转码(html)
}

export interface AsyncSplitImgTaskNode {
  id: string;
  name: string;
  type: ESplitImgTaskNodeType; // 具体的任务类型
  status: ETaskStatus;
  progress: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  result?: any;
  metadata?: ICreateAsyncImgToCodeTask; // 具体的任务需要的信息
}

type TCoordinate = {
  name: string;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
};

interface IAsyncImgToCodeItem {
  prototypeId?: string; // 原型ID
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
    imgHeight?: string; // 图片高度
    imgWidth?: string; // 图片宽度
    coordinates?: TCoordinate[]; // 图片坐标
  };
}

interface IImgSplitItems {
  prototypeId?: string; // 原型ID
  metadata: {
    imageContent?: string; // Base64编码的图片内容 (可选)
    imageUrl?: string; // 图片URL (可选)
    imageName?: string; // 图片文件名 (可选)
    imageType?: string; // 图片类型 (可选)
    imgHeight: string; // 图片高度
    imgWidth: string; // 图片宽度
  };
}

export interface ICreateAsyncImgToCodeTask {
  user: string;
  items: IAsyncImgToCodeItem[];
  type: ETaskType;
  imgSplitItems?: IImgSplitItems[];
  model?: string;
  projectId?: string; // 项目ID (可选)
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
}

export interface ICreateAsyncImgToCodeTaskRes extends AsyncSplitImgTask {
  chatId: string;
}

// 任务响应接口定义
interface TaskResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  message: string;
  waitForCompletion?: boolean;
  // 等待完成时的额外数据
  playgroundId?: string;
  playgroundIds?: string[];
  files?: any[];
  coordinates?: any;
  // 并行任务的特殊字段
  executionMode?: 'parallel';
  pageCount?: number;
  pageResults?: any[];
  successCount?: number;
  failureCount?: number;
}

@Injectable()
export class AsyncSplitImgService {
  private readonly logger = new Logger(AsyncSplitImgService.name);
  private readonly TASK_TYPE = 'async-split-img';

  constructor(
    private readonly prisma: PrismaService,
    private readonly taskWatcher: TaskWatcherService,
    private readonly aiCodingService: AiCodingService,
    private readonly designProjectService: DesignProjectService,
    private readonly designProjectBackgroundTaskService: DesignProjectBackgroundTaskService,
    private readonly backgroundTaskService: BackgroundTaskService, // 新增：注入BackgroundTaskService
  ) {}

  /**
   * 分析图生码场景并创建对应的节点
   */
  private async createAsyncImgToCodeNodes(params: ICreateAsyncImgToCodeTask): Promise<AsyncSplitImgTaskNode[][]> {
    const { type } = params;
    const nodes: AsyncSplitImgTaskNode[][] = [];

    if (type === ETaskType.DIRECT_IMG_TO_CODE) {
      // 直接图转码
      nodes[nodes.length] = [
        {
          id: `img_to_code_${generateId()}`,
          name: '直接图转码',
          type: ESplitImgTaskNodeType.IMG_TO_CODE,
          status: ETaskStatus.PENDING,
          progress: 0,
          metadata: params,
        },
      ];
    } else if (type === ETaskType.SPLIT_IMG_TO_CODE) {
      // 拆图(智能拆分/手动拆分)转码
      nodes[nodes.length] = [
        {
          id: `split_img_to_code${generateId()}`,
          name: '拆的单图转码',
          type: ESplitImgTaskNodeType.SPLIT_IMG_TO_CODE,
          status: ETaskStatus.PENDING,
          progress: 0,
          metadata: params,
        },
      ];
    }

    if (nodes.length === 0) {
      throw new Error('未知的图生码场景');
    }

    return nodes;
  }

  /**
   * 更新主任务状态
   */
  private async updateAsyncImgToCodeStatus(taskId: string, updates: Partial<AsyncSplitImgTask>): Promise<void> {
    const asyncImgToCode = await this.prisma.backgroundTask.findUnique({
      where: { id: taskId },
    });

    if (!asyncImgToCode || !asyncImgToCode.metadata) {
      throw new Error(`主任务 ${taskId} 不存在或状态信息缺失`);
    }

    const metadata = asyncImgToCode.metadata as any;
    const current = metadata.asyncImgToCodeTask as AsyncSplitImgTask;
    const newAsyncImgToCodeTask = { ...current, ...updates };

    // 计算整体进度
    if (updates.progress === undefined) {
      const { progress, hasError } = this.calcTaskProgress(newAsyncImgToCodeTask);
      newAsyncImgToCodeTask.progress = progress;
      if (progress === 100) {
        newAsyncImgToCodeTask.status = hasError ? ETaskStatus.FAILED : ETaskStatus.COMPLETED;
      }
    }

    // 更新metadata中的asyncImgToCodeTask
    const newMetadata = {
      ...metadata,
      asyncImgToCodeTask: newAsyncImgToCodeTask,
    };

    await this.prisma.backgroundTask.update({
      where: { id: taskId },
      data: {
        metadata: newMetadata as any,
        status: newAsyncImgToCodeTask.status,
        progress: newAsyncImgToCodeTask.progress,
        updated: new Date(),
      },
    });
  }

  private calcTaskProgress(task: AsyncSplitImgTask): {
    progress: number;
    hasError: boolean;
  } {
    let hasError = false;
    let taskNodeLen = 0;
    let completedNodes = 0;
    task.nodes.forEach((nodeList) => {
      nodeList.forEach((nodeItem) => {
        taskNodeLen++;
        if (nodeItem.status === ETaskStatus.COMPLETED || nodeItem.status === ETaskStatus.FAILED) {
          completedNodes++;
        }
        if (nodeItem.status === ETaskStatus.FAILED) {
          hasError = true;
        }
      });
    });

    return {
      progress: Math.round((completedNodes / taskNodeLen) * 100),
      hasError,
    };
  }

  /**
   * 更新节点状态
   */
  private async updateNodeStatus(
    taskId: string,
    nodeId: string,
    updates: Partial<AsyncSplitImgTaskNode>,
  ): Promise<void> {
    const asyncImgToCode = await this.prisma.backgroundTask.findUnique({
      where: { id: taskId },
    });

    if (!asyncImgToCode || !asyncImgToCode.metadata) {
      throw new Error(`工作流 ${taskId} 不存在或状态信息缺失`);
    }

    const metadata = asyncImgToCode.metadata as any;
    const current = metadata.asyncImgToCodeTask as AsyncSplitImgTask;
    const newAsyncImgToCodeTask = { ...current };

    let groupIndex = -1;
    let taskIndex = -1;

    // 在任务组结构中查找节点
    current.nodes.forEach((nodeList, index) => {
      nodeList.forEach((nodeItem, taskIndexItem) => {
        if (nodeItem.id === nodeId) {
          groupIndex = index;
          taskIndex = taskIndexItem;
        }
      });
    });

    if (groupIndex === -1 || taskIndex === -1) {
      throw new Error(`节点 ${nodeId} 不存在`);
    }

    const currentNode = current.nodes[groupIndex][taskIndex];
    // 更新节点状态
    newAsyncImgToCodeTask.nodes[groupIndex][taskIndex] = { ...currentNode, ...updates };

    // 更新主任务状态
    await this.updateAsyncImgToCodeStatus(taskId, {
      nodes: newAsyncImgToCodeTask.nodes,
    });
  }

  // ========================= 辅助方法 =========================

  /**
   * 构建任务响应对象
   * 复用 design-project.controller 中的逻辑
   */
  private async buildTaskResponse(
    result: any,
    waitForCompletion: boolean = false,
    taskType: string,
  ): Promise<TaskResponse> {
    const baseResponse: TaskResponse = {
      taskId: result.taskId || result.id,
      status: result.status || 'pending',
      message: `${taskType} 任务已创建`,
      waitForCompletion: waitForCompletion,
    };

    // 如果需要等待完成，使用TaskWatcherService等待并获取结果
    if (waitForCompletion) {
      try {
        this.logger.log(`⏳ 等待任务完成并获取结果: ${baseResponse.taskId}`);

        // 使用TaskWatcherService等待任务完成并获取文件
        const completionResult = await this.taskWatcher.waitForTaskCompletionAndGetResults(
          baseResponse.taskId,
          undefined, // playgroundId会自动从任务状态中获取
          {
            maxAttempts: 600, // 最大尝试次数，30分钟
            pollInterval: 3000, // 每3秒检查一次
            resetState: true, // 重置检查状态
            skipFileTraversal: false, // 不跳过文件遍历，确保获取文件
          },
        );

        // 更新响应状态
        baseResponse.status = (completionResult.status as any) || 'completed';
        baseResponse.message = `${taskType} 任务已完成`;

        if (completionResult.playgroundId) {
          baseResponse.playgroundId = completionResult.playgroundId;
        }

        // 处理文件结果
        if (completionResult.files && Object.keys(completionResult.files).length > 0) {
          // 转换文件格式以匹配预期的结构
          baseResponse.files = Object.entries(completionResult.files).map(([path, fileData]: [string, any]) => ({
            name: path.split('/').pop() || path,
            path: path,
            content: fileData.content,
            contentType: fileData.contentType,
            previewUrl: fileData.previewUrl,
            size: fileData.content ? fileData.content.length : 0,
          }));

          this.logger.log(`✅ 成功获取 ${baseResponse.files.length} 个文件`);
        } else {
          this.logger.warn(`⚠️ 任务完成但未获取到文件`);
          baseResponse.files = [];
        }

        // 如果有多个playground信息（并行任务）
        if (completionResult.allPlaygroundInfo && completionResult.allPlaygroundInfo.length > 0) {
          baseResponse.playgroundIds = completionResult.allPlaygroundInfo.map((info) => info.playgroundId);
          baseResponse.pageResults = completionResult.allPlaygroundInfo;
        }

        // 处理错误情况
        if (completionResult.fileError) {
          this.logger.warn(`⚠️ 文件获取失败: ${completionResult.fileError}`);
        }

        if (!completionResult.taskCompleted) {
          baseResponse.status = 'failed';
          baseResponse.message = `${taskType} 任务执行超时或失败`;
        }
      } catch (error) {
        this.logger.error(`❌ 等待任务完成失败:`, error);
        baseResponse.status = 'failed';
        baseResponse.message = `${taskType} 任务等待失败: ${error.message}`;
      }
    }

    return baseResponse;
  }

  private async imgToCode(
    taskId: string,
    node: AsyncSplitImgTaskNode,
    params: ICreateAsyncImgToCodeTask,
  ): Promise<string> {
    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        params.items.map(async (item, index) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl,
          );

          // 生成唯一的任务项ID和名称
          const itemId = `img-to-code-${Date.now()}-${index}`;
          const itemName = `图片转代码_${itemId}`;

          return {
            id: itemId,
            name: itemName,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
              // 保留关联信息
              prototypeId: item.prototypeId,
            },
          };
        }),
      );

      const waitForCompletion = true;
      const result = await this.designProjectBackgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-to-code',
        taskName: `图片转代码任务_${new Date().getTime()}`,
        items: processedItems,
        user: params.user,
        model: 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: params.enableAutoIteration ?? false,
        enableStepByStep: params.enableStepByStep ?? false,
        metadata: {
          projectId: params.projectId, // 可选的项目关联
        },
        waitForCompletion,
      });

      const response = await this.buildTaskResponse(result, waitForCompletion, 'img-to-code');
      this.logger.log(`✅ 图片转代码任务创建成功: ${response.taskId}`);
      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.COMPLETED,
        startTime: new Date(),
        progress: 100,
        result: response,
      });
      return response?.files?.[0]?.content;
    } catch (error) {
      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.FAILED,
        startTime: new Date(),
        progress: 0,
      });
      this.logger.error(`❌ 图片转代码任务创建失败:`, error);
      throw error;
    }
  }

  private async imgSplitToCode(
    taskId: string,
    node: AsyncSplitImgTaskNode,
    params: ICreateAsyncImgToCodeTask,
  ): Promise<TaskResponse> {
    try {
      const { imgSplitItems } = params;
      if (!imgSplitItems?.length) {
        return;
      }
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        imgSplitItems.map(async (item, index) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl,
          );

          // 生成唯一的任务项ID和名称
          const itemId = `img-split-to-code-${Date.now()}-${index}`;
          const itemName = `从分割图生成代码_${itemId}`;

          return {
            id: itemId,
            name: itemName,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
              // 保留关联信息
              prototypeId: item.prototypeId,
            },
          };
        }),
      );

      const waitForCompletion = true;
      const resultList = await Promise.all(
        processedItems.map(async (item) => {
          return await this.designProjectBackgroundTaskService.createAndExecuteGenericTask({
            taskType: 'img-split-to-code',
            taskName: `从分割图生成代码任务_${new Date().getTime()}`,
            items: [item],
            user: params.user,
            model: 'openrouter::google/gemini-2.5-pro-preview',
            enableAutoIteration: params.enableAutoIteration ?? false,
            enableStepByStep: params.enableStepByStep ?? false,
            metadata: {
              projectId: params.projectId, // 可选的项目关联
            },
            waitForCompletion,
          });
        }),
      );

      let response = await this.buildTaskResponse(resultList[0], waitForCompletion, 'img-split-to-code');
      this.logger.log(`✅ 从分割图生成代码任务创建成功: ${response.taskId}`);

      response = {
        ...response,
        files: resultList.map((current: any, currentIndex) => {
          const { files } = current;

          const fileObj = files['index.html'];

          const name = `index_${currentIndex + 1}.html`;
          return {
            name,
            path: fileObj.path,
            content: fileObj.content,
            contentType: fileObj.contentType,
            previewUrl: fileObj.previewUrl,
            size: fileObj.content ? fileObj.content.length : 0,
          };
        }),
      };

      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.COMPLETED,
        startTime: new Date(),
        progress: 100,
        result: response,
      });
      return response;
    } catch (error) {
      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.FAILED,
        startTime: new Date(),
        progress: 0,
      });
      this.logger.error(`❌ 从分割图生成代码任务创建失败:`, error);
      throw error;
    }
  }

  private async executeDirectImgToCodeTask(taskId: string, params: ICreateAsyncImgToCodeTask): Promise<string> {
    this.logger.log(`🔄 开始执行直接图转码任务: ${taskId}`);

    const asyncImgToCode = await this.prisma.backgroundTask.findUnique({
      where: { id: taskId },
    });
    const metadata = asyncImgToCode.metadata as any;
    const current = metadata.asyncImgToCodeTask as AsyncSplitImgTask;

    try {
      const node = current.nodes[0][0];
      // 更新节点状态为处理中
      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.PROCESSING,
        startTime: new Date(),
        progress: 0,
      });

      // 执行图转码任务
      return await this.imgToCode(taskId, node, params);
    } catch (error) {
      this.logger.error(`❌ 图片转代码任务创建失败:`, error);
      throw error;
    }
  }

  // 正则表达式匹配body标签内的内容
  private getBodyContent = (htmlString: string) => {
    const bodyContentRegex = /<body[^>]*>([\s\S]*?)<\/body>/i;
    // 提取body内容
    const match = htmlString.match(bodyContentRegex);

    return match ? match[1].trim() : '';
  };

  // 将每个图的HTML内容插入到layoutContent对应位置中
  private insertComponentsIntoLayout = (bodyContentList: string[], coordinates: TCoordinate[]): string => {
    let finalHtml = coordinatesToHtml(coordinates.map((item) => [item.x1, item.y1, item.x2, item.y2]));

    // 为每个组件创建一个占位符到实际内容的映射
    coordinates.forEach((rect, index) => {
      const componentContent = bodyContentList[index] || '';
      const { x1: left, y1: top, x2: right, y2: bottom } = rect;

      // 根据坐标生成容器class名称，格式：container_left_top_right_bottom
      const containerClassName = `container_${left}_${top}_${right}_${bottom}`;

      // console.log(`正在查找容器: ${containerClassName}`);

      // 查找对应的容器元素并替换其内容
      // 匹配格式：<div class="container_left_top_right_bottom"></div>
      const containerPattern = new RegExp(
        `(<div\\s+class=["'][^"']*${containerClassName}[^>]*>)([\\s\\S]*?)(<\\/div>)`,
        'gi',
      );

      const match = finalHtml.match(containerPattern);

      if (match) {
        // 找到匹配的容器，替换其内容
        finalHtml = finalHtml.replace(containerPattern, `$1${componentContent}$3`);
        // console.log(`成功替换容器 ${containerClassName} 的内容`);
      }
    });

    return finalHtml;
  };

  // 发送最终生成的HTML内容到任务聊天
  private async sendMessageToTaskChat(chatId: string, htmlContent: string, params: ICreateAsyncImgToCodeTask) {
    try {
      // 1. 根据chatId查找关联的后台任务
      const taskInfo = await this.backgroundTaskService.getTaskByPlaygroundId(chatId);

      if (!taskInfo) {
        return {
          success: false,
          error: `未找到与chat ${chatId} 关联的后台任务`,
          chatId,
          suggestion: '请确认该chat是通过后台任务创建的，或者该任务仍然存在',
        };
      }

      // 2. 解析请求参数
      const { user } = params;
      const messageContent = '设计稿还原\n设计稿图片已转为html码，请你实现为组件代码';
      const options: any = {
        waitForCompletion: false, // 不等待AI回复
        messageRole: 'user',
        attachments: [
          {
            name: 'index.html',
            content: htmlContent,
            contentType: 'text/html',
          },
        ],
      };

      if (user) {
        options.userId = user;
      }

      // 3. 调用BackgroundTaskService发送消息到对应的任务
      const result = await this.backgroundTaskService.sendMessageToTaskChat(taskInfo.taskId, messageContent, options);

      // 4. 返回成功结果，包含任务信息
      return {
        ...result,
        // 添加chat相关的信息
        chatInfo: {
          chatId,
          taskId: taskInfo.taskId,
          taskName: taskInfo.taskName,
          taskType: taskInfo.taskType,
          taskStatus: taskInfo.status,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: `向chat发送消息失败: ${error.message}`,
        chatId,
        details: error.message,
      };
    }
  }

  private async getSplitImgToCode(imgSplitToCodeRes: TaskResponse, params: ICreateAsyncImgToCodeTask) {
    const { items } = params;

    const coordinates = items[0].metadata.coordinates;
    const bodyContentList = imgSplitToCodeRes?.files?.map((file) => this.getBodyContent(file.content));

    if (!bodyContentList?.length) {
      throw new Error('拆分图转码任务执行失败');
    }

    return this.insertComponentsIntoLayout(bodyContentList, coordinates);
  }

  private async executeSplitImgToCode(taskId: string, params: ICreateAsyncImgToCodeTask): Promise<string> {
    this.logger.log(`🔄 开始执行拆图(智能拆分/手动拆分)转码: ${taskId}`);
    const asyncImgToCode = await this.prisma.backgroundTask.findUnique({
      where: { id: taskId },
    });
    const metadata = asyncImgToCode.metadata as any;
    const current = metadata.asyncImgToCodeTask as AsyncSplitImgTask;

    try {
      const node = current.nodes[0][0];

      // 更新节点状态为处理中
      await this.updateNodeStatus(taskId, node.id, {
        status: ETaskStatus.PROCESSING,
        startTime: new Date(),
        progress: 0,
      });

      const imgSplitToCodeRes = await this.imgSplitToCode(taskId, node, params);

      return this.getSplitImgToCode(imgSplitToCodeRes, params);
    } catch (error) {
      this.logger.error(`❌ 图片转代码任务创建失败:`, error);
      throw error;
    }
  }

  /**
   * 执行主任务
   */
  private async executeAsyncImgToCode(
    taskId: string,
    params: ICreateAsyncImgToCodeTask,
    chatId: string,
  ): Promise<void> {
    const { type } = params;
    this.logger.log(`🔄 开始执行主任务: ${taskId}`);

    try {
      // 更新主任务状态为处理中
      await this.updateAsyncImgToCodeStatus(taskId, { status: ETaskStatus.PROCESSING });

      let htmlContent = '';
      // 执行图转码任务
      switch (type) {
        case ETaskType.DIRECT_IMG_TO_CODE:
          htmlContent = await this.executeDirectImgToCodeTask(taskId, params);
          break;
        case ETaskType.SPLIT_IMG_TO_CODE:
          htmlContent = await this.executeSplitImgToCode(taskId, params);
          break;
        default:
          throw new Error(`不支持的节点类型: ${type}`);
      }

      // console.log('---------------htmlContent-------------------');
      // console.log(htmlContent);

      await this.sendMessageToTaskChat(chatId, htmlContent, params);
    } catch (error) {
      this.logger.error(`❌ 异步图转码主任务执行失败: ${taskId}`, error);
      try {
        await this.updateAsyncImgToCodeStatus(taskId, {
          status: ETaskStatus.FAILED,
          endTime: new Date(),
          error: error.message,
        });
      } catch (updateError) {
        this.logger.error(`❌ 更新异步图转码主任务失败状态时出错: ${taskId}`, updateError);
      }

      // 重新抛出错误
      throw error;
    }
  }

  /**
   * 获取图转码状态
   */
  async getAsyncImgToCodeStatus(taskId: string): Promise<AsyncSplitImgTask> {
    const asyncImgToCode = await this.prisma.backgroundTask.findUnique({
      where: { id: taskId },
    });

    if (!asyncImgToCode) {
      throw new Error(`图转码 ${taskId} 不存在`);
    }

    if (!asyncImgToCode.metadata) {
      throw new Error(`图转码 ${taskId} 状态信息不存在`);
    }

    const metadata = asyncImgToCode.metadata as any;
    const asyncImgToCodeTask = metadata.asyncImgToCodeTask as AsyncSplitImgTask;

    return asyncImgToCodeTask;
  }

  /**
   * 异步图转码任务
   */
  async createAsyncImgToCodeTask(params: ICreateAsyncImgToCodeTask): Promise<ICreateAsyncImgToCodeTaskRes> {
    const { projectId, user, model, type, enableAutoIteration, enableStepByStep } = params;
    this.logger.log(`🚀 开始创建图转码任务: projectId=${projectId}, user=${user}, type=${type}`);

    // 1. 分析数据并创建节点
    const asyncImgToCodeNodes = await this.createAsyncImgToCodeNodes(params);

    // 2. 创建主任务状态
    const taskId = generateId();
    const asyncImgToCodeTask: AsyncSplitImgTask = {
      taskId,
      status: ETaskStatus.PENDING,
      progress: 0,
      nodes: asyncImgToCodeNodes,
      startTime: new Date(),
    };

    // 3. 异步创建chat会话，参数是taskId，返回值是chatId
    const createCommonChatParams: CreateCommonChatDto = {
      user,
      projectId,
      createEmptySession: 'true',
      sessionName: '设计稿还原任务',
      tags: 'img-to-code',
      model,
    };

    let project: Project;
    if (projectId) {
      project = await this.prisma.project.findUnique({
        where: {
          id: projectId,
        },
      });
    }
    createCommonChatParams.type = project?.framework ?? 'react';

    const res = await this.aiCodingService.createCommonChat(createCommonChatParams);
    const chatId = res?.chatId;
    if (!chatId) {
      throw new Error(`创建chat会话失败: ${taskId}`);
    }

    // 4. 创建异步图转码主任务，使用 metadata 存储任务数据
    await this.prisma.backgroundTask.create({
      data: {
        id: taskId,
        taskType: this.TASK_TYPE,
        taskName: `图转码 - ${type}`,
        user,
        status: ETaskStatus.PENDING,
        progress: 0,
        designProjectId: projectId || '',
        model: model || '',
        enableAutoIteration,
        enableStepByStep,
        metadata: {
          asyncImgToCodeTask,
        } as any,
        parentTaskId: chatId,
      },
    });

    this.logger.log(`✅ 图转码主任务创建成功: ${taskId}`);

    // 5. 开始执行主任务
    this.executeAsyncImgToCode(taskId, params, chatId).catch((error) => {
      this.logger.error(`❌ 图转码主任务执行失败: ${taskId}`, error);
    });

    return {
      ...asyncImgToCodeTask,
      chatId,
    };
  }

  /**
   * 根据chatId获取图转码任务
   */
  async getAsyncImgToCodeTaskByChatId(chatId: string): Promise<{}> {
    const where: any = {};
    where.parentTaskId = chatId;
    const asyncImgToCode = await this.prisma.backgroundTask.findMany({
      where,
    });

    if (!asyncImgToCode) {
      return null;
    }

    return asyncImgToCode;
  }
}
