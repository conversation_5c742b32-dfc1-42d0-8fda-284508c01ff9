import { minify } from 'html-minifier-terser';
import * as juice from 'juice';

/**
 * HTML和CSS合并压缩工具类
 * 用于将分离的HTML和CSS文件合并为单个压缩的HTML文件，减少上下文长度
 */
export class HtmlCssMerger {
  /**
   * 将HTML和CSS内容合并并压缩
   * @param htmlContent HTML文件内容
   * @param cssContent CSS文件内容
   * @param options 压缩选项
   * @returns 合并压缩后的HTML内容
   */
  static async mergeAndMinify(
    htmlContent: string,
    cssContent: string,
    options?: {
      removeComments?: boolean;
      collapseWhitespace?: boolean;
      minifyCSS?: boolean;
      minifyJS?: boolean;
      removeRedundantAttributes?: boolean;
    }
  ): Promise<string> {
    try {
      // 设置默认压缩选项
      const defaultOptions = {
        removeComments: true,
        collapseWhitespace: true,
        minifyCSS: true,
        minifyJS: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true,
        ...options,
      };

      // 将CSS内联到HTML中
      const inlinedHtml = juice.inlineContent(htmlContent, cssContent);

      // 移除HTML中的外部CSS链接声明
      const cleanedHtml = this.removeCSSLinks(inlinedHtml);

      // 压缩HTML
      const minifiedHtml = await minify(cleanedHtml, defaultOptions);

      return minifiedHtml;
    } catch (error) {
      throw new Error(`HTML/CSS合并压缩失败: ${error.message}`);
    }
  }

  /**
   * 从文件路径读取并合并压缩HTML和CSS
   * @param htmlFilePath HTML文件路径
   * @param cssFilePath CSS文件路径
   * @param options 压缩选项
   * @returns 合并压缩后的HTML内容
   */
  static async mergeAndMinifyFromFiles(
    htmlFilePath: string,
    cssFilePath: string,
    options?: Parameters<typeof HtmlCssMerger.mergeAndMinify>[2]
  ): Promise<string> {
    const fs = require('fs').promises;

    try {
      // 读取HTML和CSS文件
      const [htmlContent, cssContent] = await Promise.all([
        fs.readFile(htmlFilePath, 'utf8'),
        fs.readFile(cssFilePath, 'utf8')
      ]);

      return await this.mergeAndMinify(htmlContent, cssContent, options);
    } catch (error) {
      throw new Error(`从文件合并压缩失败: ${error.message}`);
    }
  }

  /**
   * 批量处理多个HTML/CSS文件对
   * @param filePairs HTML和CSS文件对的数组
   * @param options 压缩选项
   * @returns 合并压缩后的结果数组
   */
  static async batchMergeAndMinify(
    filePairs: Array<{
      htmlContent: string;
      cssContent: string;
      name?: string;
    }>,
    options?: Parameters<typeof HtmlCssMerger.mergeAndMinify>[2]
  ): Promise<Array<{
    name?: string;
    content: string;
    success: boolean;
    error?: string;
  }>> {
    const results = await Promise.allSettled(
      filePairs.map(async (pair) => {
        const content = await this.mergeAndMinify(pair.htmlContent, pair.cssContent, options);
        return {
          name: pair.name,
          content,
          success: true,
        };
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name: filePairs[index].name,
          content: '',
          success: false,
          error: result.reason?.message || 'Unknown error',
        };
      }
    });
  }

  /**
   * 检查HTML内容是否已经包含内联CSS
   * @param htmlContent HTML内容
   * @returns 是否包含内联样式
   */
  static hasInlineCSS(htmlContent: string): boolean {
    return /<style[^>]*>[\s\S]*?<\/style>/i.test(htmlContent);
  }

  /**
   * 提取HTML中的CSS链接
   * @param htmlContent HTML内容
   * @returns CSS链接数组
   */
  static extractCSSLinks(htmlContent: string): string[] {
    const linkRegex = /<link[^>]*rel=['"]stylesheet['"][^>]*href=['"]([^'"]*)['"]/gi;
    const links: string[] = [];
    let match;

    while ((match = linkRegex.exec(htmlContent)) !== null) {
      links.push(match[1]);
    }

    return links;
  }

  /**
   * 移除HTML中的CSS链接
   * @param htmlContent HTML内容
   * @returns 移除CSS链接后的HTML内容
   */
  static removeCSSLinks(htmlContent: string): string {
    // 匹配各种格式的CSS链接，包括：
    // <link rel="stylesheet" href="...">
    // <link href="..." rel="stylesheet">
    // <link type="text/css" rel="stylesheet" href="...">
    // 支持单引号、双引号和属性顺序变化
    return htmlContent.replace(
      /<link[^>]*(?:rel=['"]stylesheet['"]|type=['"]text\/css['"])[^>]*>/gi,
      ''
    );
  }

  /**
   * 智能合并：如果HTML已经包含样式，则只压缩；否则合并CSS后压缩
   * @param htmlContent HTML内容
   * @param cssContent CSS内容（可选）
   * @param options 压缩选项
   * @returns 处理后的HTML内容
   */
  static async smartMergeAndMinify(
    htmlContent: string,
    cssContent?: string,
    options?: Parameters<typeof HtmlCssMerger.mergeAndMinify>[2]
  ): Promise<string> {
    try {
      // 如果HTML已经有内联样式且没有外部CSS，直接压缩
      if (this.hasInlineCSS(htmlContent) && !cssContent) {
        // 移除可能存在的外部CSS链接
        const cleanedHtml = this.removeCSSLinks(htmlContent);
        return await minify(cleanedHtml, {
          removeComments: true,
          collapseWhitespace: true,
          minifyCSS: true,
          minifyJS: true,
          removeRedundantAttributes: true,
          removeScriptTypeAttributes: true,
          removeStyleLinkTypeAttributes: true,
          useShortDoctype: true,
          ...options,
        });
      }

      // 如果有CSS内容，进行合并
      if (cssContent) {
        return await this.mergeAndMinify(htmlContent, cssContent, options);
      }

      // 否则只压缩HTML，但仍需移除外部CSS链接
      const cleanedHtml = this.removeCSSLinks(htmlContent);
      return await minify(cleanedHtml, {
        removeComments: true,
        collapseWhitespace: true,
        minifyCSS: true,
        minifyJS: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true,
        ...options,
      });
    } catch (error) {
      throw new Error(`智能合并压缩失败: ${error.message}`);
    }
  }
} 