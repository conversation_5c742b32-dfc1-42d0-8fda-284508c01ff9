import * as fs from 'fs';
import * as path from 'path';
import { Injectable } from '@nestjs/common';
import * as nunjucks from 'nunjucks';
import {
  getAllPromptTemplates,
  getPromptTemplateConfig,
  PromptTemplateConfig
} from './prompt-templates.const';

// 提示词上下文接口
export interface PromptContext {
  taskType: string;
  items: any[]; // 更准确的类型定义
  options: Record<string, any>;
  metadata: Record<string, any>;
}

// 提示词模板接口
export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  buildMessage(context: PromptContext): string;
  validateContext(context: PromptContext): void;
}

// 基于文件的提示词模板实现
class FileBasedPromptTemplate implements PromptTemplate {
  constructor(private config: PromptTemplateConfig) { }

  get id(): string {
    return this.config.id;
  }

  get name(): string {
    return this.config.name;
  }

  get description(): string {
    return this.config.description;
  }

  validateContext(context: PromptContext): void {
    // 基于配置的验证逻辑
    if (this.config.id === 'design-transcode') {
      if (!context.items || context.items.length === 0) {
        throw new Error('转码任务需要至少一个设计稿原型');
      }
    } else if (this.config.id === 'design-merge') {
      if (!context.items || context.items.length < 2) {
        throw new Error('合并任务需要至少两个设计稿结果');
      }
    } else if (this.config.id === 'custom') {
      if (!context.options.customPrompt) {
        throw new Error('自定义提示词任务需要提供 customPrompt');
      }
    }
  }

  buildMessage(context: PromptContext): string {
    // 准备模板变量
    const variables = this.prepareTemplateVariables(context);

    // 使用模板渲染器渲染提示词
    return TemplateRenderer.render(this.config.id, variables);
  }

  private prepareTemplateVariables(context: PromptContext): Record<string, any> {
    const { options, items, metadata } = context;

    // 基础变量
    const variables: Record<string, any> = {
      ...options,
      ...metadata, // 将metadata中的占位符数据展开到变量中
      items,
      itemCount: items.length,
    };

    // 根据模板类型添加特殊变量
    if (this.config.id === 'design-merge') {
      // 合并任务的默认选项
      variables.projectName = options.projectName || '合并项目';
      variables.mergeType = options.mergeType || 'navigation';
    }

    return variables;
  }
}

// 模板渲染器
export class TemplateRenderer {
  private static templateCache: Map<string, string> = new Map();

  static render(templateId: string, variables: Record<string, any>): string {
    // 从缓存获取模板
    let template = this.templateCache.get(templateId);

    if (!template) {
      // 从文件系统加载模板
      try {
        const config = getPromptTemplateConfig(templateId);
        // 使用 __dirname 来构建与当前文件相对的路径
        const templatePath = path.join(__dirname, `design-project/${config.filePath}`);
        template = fs.readFileSync(templatePath, 'utf-8');
        console.log(`✅ 成功从 ${templatePath} 加载模板`);
      } catch (error) {
        console.error(`❌ 加载模板 ${templateId} 失败:`, error);
        // 如果加载失败，返回一个错误提示，而不是错误的JSON
        template = `错误：无法加载模板 ${templateId}。请检查文件是否存在。`;
      }

      // 缓存模板
      this.templateCache.set(templateId, template);
    }

    // 使用 Nunjucks 渲染模板
    try {
      return nunjucks.renderString(template, variables);
    } catch (error) {
      console.error(`❌ 渲染模板 ${templateId} 失败:`, error);
      return `错误：渲染模板 ${templateId} 失败。请检查模板语法。`;
    }
  }

  static clearCache(): void {
    this.templateCache.clear();
  }
}

// 提示词模板管理器
@Injectable()
export class PromptTemplateManager {
  private templates: Map<string, PromptTemplate> = new Map();
  private promptManagementService: any; // 延迟注入以避免循环依赖
  private prismaService: any; // 延迟注入Prisma服务

  constructor() {
    // 从配置文件加载所有模板
    this.loadTemplatesFromConfig();
  }

  // 设置提示词管理服务（用于延迟注入）
  setPromptManagementService(service: any): void {
    this.promptManagementService = service;
  }

  // 设置Prisma服务（用于延迟注入）
  setPrismaService(service: any): void {
    this.prismaService = service;
  }

  private loadTemplatesFromConfig(): void {
    const templateConfigs = getAllPromptTemplates();

    templateConfigs.forEach(config => {
      const template = new FileBasedPromptTemplate(config);
      this.templates.set(template.id, template);
    });

    console.log(`📝 已加载 ${this.templates.size} 个提示词模板`);
  }

  registerTemplate(template: PromptTemplate): void {
    this.templates.set(template.id, template);
  }

  getTemplate(id: string): PromptTemplate {
    const template = this.templates.get(id);
    if (!template) {
      throw new Error(`提示词模板 ${id} 不存在`);
    }
    return template;
  }

  listTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  async buildPrompt(templateId: string, context: PromptContext): Promise<string> {
    const template = this.getTemplate(templateId);
    template.validateContext(context);

    // 检查是否有projectId & 对应的项目是否有自定义的任务提示词
    if (context?.options?.projectId && this.promptManagementService) {
      // 根据templateId获取任务类型
      const taskType = templateId;

      // 尝试获取项目自定义的main类型提示词内容
      try {
        const customContent = await this.promptManagementService.getEffectivePromptContent(
          context.options.projectId,
          taskType,
          'main'
        );

        if (customContent) {
          // 如果有自定义内容，使用nunjucks渲染
          const variables = this.prepareTemplateVariables(context, templateId);
          const nunjucks = require('nunjucks');
          return nunjucks.renderString(customContent, variables);
        }
      } catch (error) {
        console.error(`❌ 获取项目 ${context.options?.projectId} 的自定义提示词失败，使用默认模板: ${taskType}`, error);
      }
    }

    const result = template.buildMessage(context);
    return typeof result === 'object' && result && typeof (result as any).then === 'function' ? await (result as Promise<string>) : result as string;
  }

  // 准备模板变量
  private prepareTemplateVariables(context: PromptContext, templateId: string): Record<string, any> {
    const { options, items, metadata } = context;

    // 基础变量
    const variables: Record<string, any> = {
      ...options,
      ...metadata,
      items,
      itemCount: items.length,
    };

    // 根据模板类型添加特殊变量
    if (templateId === 'design-merge') {
      variables.projectName = options.projectName || '合并项目';
      variables.mergeType = options.mergeType || 'navigation';
    }

    return variables;
  }

  // 重新加载模板（用于开发时热更新）
  reloadTemplates(): void {
    // 清除模板渲染器缓存
    TemplateRenderer.clearCache();

    // 重新加载模板
    this.templates.clear();
    this.loadTemplatesFromConfig();

    console.log('📝 提示词模板已重新加载');
  }

  // 获取模板配置信息
  getTemplateConfig(id: string): PromptTemplateConfig {
    return getPromptTemplateConfig(id);
  }

  // 获取自检查提示词内容
  async getCheckPromptContent(templateId: string, context?: PromptContext): Promise<string | null> {

    // 检查是否有projectId & 对应的项目是否有自定义的检查提示词
    if (context?.options?.projectId && this.promptManagementService) {
      // 根据templateId获取任务类型
      const taskType = templateId;

      // 尝试获取项目自定义的check类型提示词内容
      try {
        const customContent = await this.promptManagementService.getEffectivePromptContent(
          context.options.projectId,
          taskType,
          'check'
        );

        if (customContent) {
          // // 如果有自定义内容，使用nunjucks渲染
          // const variables = this.prepareTemplateVariables(context, templateId);
          // const nunjucks = require('nunjucks');
          // try {
          //   return nunjucks.renderString(customContent, variables);
          // } catch (error) {
          //   // 自检查的提示词一般无需注入变量，如果解析失败，则直接使用提示词文本内容
          //   console.error(`❌ 渲染自定义检查提示词失败，将跳过提示词渲染，直接使用提示词文本内容: ${taskType}`, error);
          //   return customContent;
          // }
          
          // 自检查的提示词一般无需注入变量，直接返回提示词文本内容
          return customContent;
        }
      } catch (error) {
        console.error(`❌ 获取项目 ${context.options?.projectId} 的自定义检查提示词失败，使用默认模板: ${taskType}`, error);
      }
    }

    const config = this.getTemplateConfig(templateId);
    if (!config.selfCheckPromptPath) {
      return null;
    }

    try {
      // 使用 __dirname 来构建与当前文件相对的路径
      const promptPath = path.join(__dirname, `design-project/${config.selfCheckPromptPath}`);
      const content = fs.readFileSync(promptPath, 'utf-8');
      return content;
    } catch (error) {
      console.error(`❌ 同步加载自检查提示词失败: ${config.selfCheckPromptPath}`, error);
      return null;
    }
  }

  // 异步获取自检查提示词内容，支持项目自定义
  async getCheckPromptContentAsync(templateId: string, projectId?: string): Promise<string | null> {
    // 如果有项目ID，尝试使用自定义模板内容
    if (projectId && this.promptManagementService) {
      try {
        const customContent = await this.promptManagementService?.getEffectivePromptContent?.(
          projectId,
          templateId,
          'check'
        );

        if (customContent) {
          return customContent;
        }
      } catch (error) {
        console.error(`❌ 获取自定义自检查提示词失败，回退到默认: ${templateId}`, error);
      }
    }

    // 回退到默认行为
    return this.getCheckPromptContent(templateId);
  }

  // 同步获取任务目标提示词内容
  getTargetPromptContent(templateId: string): string | null {
    const config = this.getTemplateConfig(templateId);
    if (!config.targetPromptPath) {
      return null;
    }

    try {
      // 使用 __dirname 来构建与当前文件相对的路径
      const promptPath = path.join(__dirname, `design-project/${config.targetPromptPath}`);
      const content = fs.readFileSync(promptPath, 'utf-8');
      return content;
    } catch (error) {
      console.error(`❌ 同步加载任务目标提示词失败: ${config.targetPromptPath}`, error);
      return null;
    }
  }

  // 异步获取任务目标提示词内容，仅用于spec-to-prod-code任务
  async getTargetPromptContentAsync(templateId: string, projectId?: string): Promise<string | null> {
    // target类型只有spec-to-prod-code任务有，不通过通用提示词管理系统处理

    // 检查是否有对应项目的自定义任务目标提示词
    const customContent = await this.promptManagementService?.getEffectivePromptContent?.(
      projectId,
      templateId,
      'target'
    );

    if (customContent) {
      return customContent;
    }

    // 直接使用默认的target模板文件
    return this.getTargetPromptContent(templateId);
  }

} 