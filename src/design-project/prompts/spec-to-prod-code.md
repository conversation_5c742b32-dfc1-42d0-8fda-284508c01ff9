# EAM Web App

## 技术选型

- **构建工具**: oula - 基于rsbuild的高性能打包框架， 文档地址： http://oula.sit.saas.htsc/。
- **核心框架**: React 17
- **页面**: 使用mpa方案，每个页面单独生成一个html，非必要不使用路由。
- **样式方案**: 使用less作为预编译语言，开启cssmodule。
- **代码规范**: ESLint - 静态代码分析工具，用于识别和报告 ECMAScript/JavaScript 代码中的模式。
- **语言**: TypeScript - 为 JavaScript 添加了类型系统，增强了代码的可维护性和健壮性。
- **包管理器**: pnpm - 快速、节省磁盘空间。
- **menorepo方案**: pnpm + turborepo

## 核心架构理念

本项目的规范与架构决策遵循以下核心理念：

- **高内聚，低耦合 (High Cohesion, Low Coupling)**: 每个页面模块 (`projects/*`) 都被设计成一个自给自足的"微型应用"。其内部的组件、状态、服务和类型都应优先为自身服务，并尽可能减少对外部的依赖。
- **关注点分离 (Separation of Concerns)**: 严格区分**视图与业务逻辑 (Components/Hooks)**和**数据服务 (Service)**。这使得代码更易于理解、测试和维护。
- **组件自治 (Component Autonomy)**:

  - 每个需要独立获取数据的业务组件（例如列表、卡片等）都**必须**是"自给自足"的。它应该在内部调用自己的 Hook 来获取数据，并管理自己的错误状态。
  - **严禁**引入`Loading`组件或任何`isLoading`之类的状态逻辑。此阶段的核心目标是**精确实现最终的、数据加载完成后的UI布局和样式**。加载、错误等中间状态应在后续的逻辑开发阶段处理。这样做可以确保UI还原的纯粹性和高效性，避免将数据流逻辑与视图结构过早耦合。

- **禁止的反模式：页面组件包揽数据获取**

  - 父组件（如 `*Page.tsx`）**严禁**负责获取其子组件所需的数据，然后通过 props 向下传递。
  - 这种"顶层获取、层层传递 (prop-drilling)"的模式是**严格禁止**的，因为它破坏了组件的独立性和可复用性，导致了紧密耦合。父组件的主要职责是布局和协调。

- **逻辑封装 (自定义 Hook)**:

  - 组件的业务逻辑，包括：异步数据请求、错误状态 (`error`) 以及其他UI状态，都**必须**封装在自定义 Hook (`use*` hook) 中。
  - **自定义 Hook 是实现"组件自治"的唯一手段。** 任何组件如果需要与数据交互，都必须通过调用其**私有的、与组件同级的 Hook** 来完成。
  - 这些页面私有的 Hooks **必须**直接存放在其关联的组件目录中，使其与组件、样式成为一个内聚的整体。例如：`/projects/holdings/components/StocksList/useStocks.ts`。

- **服务层模块化 (Service Modularity)**:
  - 数据服务层 (位于 `service/` 目录) **必须**保持模块化。每个数据实体或业务功能都应该有其独立的 service 文件。
  - **目录结构扁平化**: `service` 目录内部**严禁**创建任何子目录。所有与数据实体相关的 service 文件（例如 `account.ts`, `overview.ts`）都必须直接存放在 `service/` 的根级别。
  - **异步返回**: 所有服务层的函数都**必须**返回一个 `Promise`，以模拟真实的异步 API 请求。即使是使用模拟数据，也应通过 `setTimeout` 等方式将其封装在 Promise 中。

## 项目结构

本项目采用 monorepo 结构，核心代码分为 `apps` 和 `packages` 两个顶级目录。

```
├── .husky/                       # Git Hooks配置目录，用于代码提交前的检查和格式化
├── .npmrc                        # NPM配置文件
├── .vscode/                      # VSCode编辑器配置目录
├── README.md                     # 项目说明文档
├── apps/                         # 应用程序目录
│   └── client/                   # 客户端应用
│       ├── public/               # 存放无需编译的静态资源，如 `index.html` 和图标
│       ├── scripts/              # 存放项目构建或辅助脚本
│       ├── src/                  # 客户端应用的核心源代码
│       │   ├── models/           # 存放**通用**的状态管理逻辑（如 Redux, Zustand store）
│       │   ├── projects/         # **MPA 页面模块目录**。每个子目录代表一个独立的页面
│       │   │   ├── holdings/       # 一个页面模块示例
│       │   │   │   ├── components/   # 存放该页面**私有**的、不可复用的业务组件
│       │   │   │   │   └── StocksList/ # 自治组件目录，包含自身逻辑
│       │   │   │   │       ├── index.tsx # 组件视图
│       │   │   │   │       ├── index.module.less # 组件样式
│       │   │   │   │       └── useStocks.ts # 组件业务逻辑 (Hook)
│       │   │   │   ├── service/      # 存放该页面的服务层代码
│       │   │   │   ├── types/        # 存放该页面**私有**的 TypeScript 类型定义
│       │   │   │   ├── index.tsx     # 页面的渲染入口
│       │   │   │   ├── HoldingsPage.tsx # 页面主组件
│       │   │   │   └── HoldingsPage.module.less # 页面主组件样式
│       │   └── setupTests.ts     # Jest 测试环境的配置文件
├── packages/                     # 存放可在多个应用或页面之间共享的通用模块
│   ├── eslint-config/            # 共享的 ESLint 规范配置
│   ├── typescript-config/        # 共享的 TypeScript 编译配置
│   ├── ui/                       # **通用 UI 组件库**，包含可在整个项目中复用的基础组件
│   └── utils/                    # **通用工具函数库**，包含可在整个项目中复用的函数
├── pnpm-lock.yaml                # pnpm依赖锁定文件
├── pnpm-workspace.yaml           # pnpm工作区配置
└── turbo.json                    # Turborepo配置文件
```

## 开发说明

### 本地开发

```
pnpm install

pnpm run start
```

### 第三方库使用规范

**严禁**引入任何新的第三方库。所有功能都必须基于项目已有的通用模块或原生 API 实现。在任何情况下，添加新的依赖都是不被允许的。

### 新建页面

本工程为 MPA (多页面应用)。构建工具 Oula 会自动扫描 `apps/client/src/projects` 目录下的所有子文件夹，并将每个文件夹内的 `index.tsx` 识别为一个独立的页面入口。

因此，在新建页面时，开发者只需按照现有结构创建新的页面文件夹即可，**禁止**手动修改 `.oularc.ts` 配置文件，构建系统会自动完成入口的注册。

### 业务组件

维护对应的页面下的`components`组件下，比如页面 holdings 的业务组件统一维护在 `/projects/holdings/components`

### 组件导出规范

为了提高代码的可维护性和导入的便利性，组件目录应遵循以下导出规范：

- **组件目录结构**：在页面模块的 `components` 目录下，每个组件都应有自己独立的文件夹，例如 `PageHeader/`、`StocksList/`。
- **禁止嵌套 `components` 目录**: 为保持组件结构的扁平、清晰，页面 `components` 目录下的任何业务组件文件夹（如 `StocksList/`）内部，**严禁**再次创建名为 `components` 的子目录。如果一个业务组件需要拆分，其所有子组件都应在顶层 `components` 目录中平级放置，以确保依赖关系的清晰。
- **统一导出文件**：`components` 目录本身必须包含一个 `index.ts` 文件，作为该页面所有私有组件的统一导出入口。
- **导出格式**：在根 `index.ts` 文件中，使用 `export { default as ComponentName } from './ComponentName'` 格式从对应的组件目录中导出组件。

**错误结构 ❌:**

```
components/
├── AccountListView/
│   ├── index.tsx
│   └── components/         // 错误: 业务组件内不应再有 components 目录
│       └── AccountCard/
│           └── index.tsx
└── index.ts
```

**正确结构 ✅:**

```
components/
├── AccountListView/      // 复合组件
│   └── index.tsx
├── AccountCard/          // 基础原子组件，被 AccountListView 使用
│   └── index.tsx
└── index.ts              # 统一导出文件
```

**示例目录结构 (`projects/holdings/components/`)：**

```
components/
├── PageHeader/
│   ├── index.tsx
│   └── index.module.less
│   └── useTotalAssetsData.ts
├── StocksList/
│   ├── index.tsx
│   └── index.module.less
│   └── useStockData.ts
└── index.ts              # 统一导出文件
```

**index.ts 示例：**

```typescript
export { default as PageHeader } from "./PageHeader";
export { default as StocksList } from "./StocksList";
```

**导入方式：**

```typescript
// 推荐：从统一入口导入
import { PageHeader, StocksList } from "@/projects/holdings/components";

// 不推荐：分散导入
import PageHeader from "@/projects/holdings/components/PageHeader";
import StocksList from "@/projects/holdings/components/StocksList";
```

### 命名规范

统一的命名规范能够提高代码的可读性。

- **目录**: 使用 `kebab-case` (短横线分隔命名)，例如 `account-list`。
- **组件文件**: 使用 `PascalCase` (大驼峰命名)，例如 `AssetSummaryCard.tsx`。如果一个目录代表一个组件，则主文件为 `index.tsx`。
- **非组件 JS/TS 文件**: 使用 `camelCase` (小驼峰命名)，例如 `accountList.ts`。
- **变量/函数**: 使用 `camelCase`，例如 `const userProfile = {}`。
- **常量**: 使用 `UPPER_SNAKE_CASE` (大写下划线命名)，例如 `const MAX_COUNT = 10`。
- **自定义 Hooks**: 使用 `use` 前缀，后跟 `PascalCase`，例如 `useToggle`。
- **类型/接口**: 使用 `PascalCase`，必须使用 `T` 或 `I` 前缀，例如 `interface IUserProfile` 或 `type TUserProfile`。
- **类型定义文件 (.d.ts)**: 存放在页面的 `types` 目录内，使用 `camelCase` 命名，例如 `api.d.ts` 或 `component.d.ts`。同时，`types` 目录内部**严禁**创建任何子目录，所有类型文件都必须直接存放在 `types/` 的根级别。
- **页面路由文件**: 直接放在 `projects/` 目录下，使用 `PascalCase` + `Page` 后缀，例如 `PortfolioPage.tsx`、`HomePage.tsx`
- **样式文件**： 项目统一使用与组件同名的 `.module.less` 文件进行样式开发，通过 CSS Modules 实现样式隔离，并允许在页面入口 `index.module.less` 中使用 `:global()` 定义全局样式。

### 颜色规范

所有页面模块的颜色定义都必须遵循统一的规范，以确保视觉一致性和可维护性。

- **全局颜色系统**:

  - 每个页面模块 **必须** 拥有一个 `global.css` 文件，用于定义该页面的全局样式和颜色系统。
  - 颜色值 **不应** 硬编码在组件的样式文件（`.module.less`）中。
  - 所有颜色都应在 `global.css` 的 `:root` 选择器中以 CSS 自定义属性（变量）的形式定义。

- **定义方法**:

  ```css
  /* apps/client/src/projects/some-page/global.css */
  :root {
    --primary-text: #030303;
    --secondary-text: #16213b;
    --brand-color: #0054ff;
    --border-color: #e9ebf1;
  }
  ```

- **使用方法**:
  - 在组件的 `.module.less` 文件中，可以直接使用 `var()` 函数来调用这些全局颜色变量。无需额外 `@import`。
  ```less
  /* apps/client/src/projects/some-page/components/MyComponent/index.module.less */
  .title {
    color: var(--primary-text);
    border: 1px solid var(--border-color);
  }
  ```

### 路径别名 (Path Alias)

为了提高代码的可读性、可维护性并避免混乱的相对路径（如 `../`），项目中的模块导入路径**必须**遵循以下两条核心规则：

1.  **同级或子级目录引用**: 当引用同一目录下或其子目录下的文件时，**必须**使用相对路径 `./`。
2.  **跨目录引用**: 只要跨出当前目录去引用其他任何位置的文件（无论是上级目录还是其他模块），都**必须**使用 `@` 别名。`@` 指向 `apps/client/src/`。

**简单来说，项目中只允许出现 `./` 和 `@/` 开头的导入路径。任何 `../` 形式的路径都是严禁的。**

**示例：**

假设我们当前的文件位于 `apps/client/src/projects/holdings/components/StocksList/index.tsx`。

```typescript
// ✅ 正确: 引用同一目录下的 Hook
import { useStocks } from "./useStocks";

// ✅ 正确: 引用模块内的 service (跨目录，使用别名)
import { getHoldingsService } from "@/projects/holdings/service/holdings";

// ❌ 错误: 使用了 '../' 向上查找 service，这是禁止的
import { getHoldingsService } from "../../service/holdings";

// ✅ 正确: 引用同模块内的兄弟组件，只要跨出当前目录，就必须用别名
import { PageHeader } from "@/projects/holdings/components/PageHeader";

// ❌ 错误: PageHeader 的错误引用方式
import { PageHeader } from "../PageHeader";
```

## 页面模块开发规范

我们确立了以下开发规范，以确保所有页面模块结构清晰、健壮且易于维护。

### 1. 结构分离：页面组件与渲染入口

每个独立的页面模块（位于 `apps/client/src/projects/`下）必须遵循关注点分离原则：

- **页面组件 (`*Page.tsx`)**:

  - 这是模块的核心，例如 `HoldingsPage.tsx`。
  - 它包含了页面的所有UI、状态和业务逻辑。
  - 它必须是一个标准的React组件，并作为模块的默认导出 (`export default HoldingsPage`)。
  - **严禁**在此文件中调用 `ReactDOM.render`。

- **渲染入口 (`index.tsx`)**:
  - 这是MPA页面的唯一入口文件。
  - 它的**唯一职责**是导入页面组件，并使用 `ReactDOM.render` 将其挂载到DOM上。
  - 这样可以保持页面组件的纯粹性，使其易于测试和复用。

### 2. 强制错误边界 (Error Boundary)

为了提升应用的健壮性，防止因单个组件的渲染错误导致整个页面白屏，每个页面的渲染入口 (`index.tsx`) 都**必须**使用 `ErrorBoundary` 组件包裹根组件。

**示例 (`index.tsx`):**

```typescript
import React from 'react';
import ReactDOM from 'react-dom';
import { ErrorBoundary } from '@repo/ui'; // 通用错误边界组件
import HoldingsPage from './HoldingsPage';
import './global.css' // 不需要模块化的样式，内容只允许html标签相关的样式用于覆盖默认样式

ReactDOM.render(
  <React.StrictMode>
    <ErrorBoundary>
      <HoldingsPage />
    </ErrorBoundary>
  </React.StrictMode>,
  document.getElementById('root')
);
```

### 3. 样式文件命名

- 页面级组件 (`*Page.tsx`) 的样式文件必须与其组件名保持一致，例如 `HoldingsPage.module.less` 对应 `HoldingsPage.tsx`。
- 遵循此约定可以清晰地反映文件之间的关系。

### 4. 数据获取与状态管理规范

#### 核心原则：组件自治，禁止顶层获取数据

这是本项目**最重要**的架构原则。为了实现高度内聚和低耦合，页面内部的数据获取和状态管理**必须**遵循"职责下沉"和"组件自治"的原则。

- **组件自治 (Component Autonomy)**:

  - 每个需要独立获取数据的业务组件（例如列表、卡片等）都**必须**是"自给自足"的。它应该在内部调用自己的 Hook 来获取数据，并管理自己的加载和错误状态。

- **禁止的反模式：页面组件包揽数据获取**

  - 父组件（如 `*Page.tsx`）**严禁**负责获取其子组件所需的数据，然后通过 props 向下传递。
  - 这种"顶层获取、层层传递 (prop-drilling)"的模式是**严格禁止**的，因为它破坏了组件的独立性和可复用性，导致了紧密耦合。父组件的主要职责是布局和协调。

- **逻辑封装 (自定义 Hook)**:

  - 组件的业务逻辑，包括：异步数据请求、错误状态 (`error`) 以及其他UI状态，都**必须**封装在自定义 Hook (`use*` hook) 中。
  - **自定义 Hook 是实现"组件自治"的唯一手段。** 任何组件如果需要与数据交互，都必须通过调用其**私有的、与组件同级的 Hook** 来完成。
  - 这些页面私有的 Hooks **必须**直接存放在其关联的组件目录中，使其与组件、样式成为一个内聚的整体。例如：`/projects/holdings/components/StocksList/useStocks.ts`。

- **服务层模块化 (Service Modularity)**:
  - 数据服务层 (位于 `service/` 目录) **必须**保持模块化。每个数据实体或业务功能都应该有其独立的 service 文件。
  - **目录结构扁平化**: `service` 目录内部**严禁**创建任何子目录。所有与数据实体相关的 service 文件（例如 `account.ts`, `overview.ts`）都必须直接存放在 `service/` 的根级别。
  - **异步返回**: 所有服务层的函数都**必须**返回一个 `Promise`，以模拟真实的异步 API 请求。即使是使用模拟数据，也应通过 `setTimeout` 等方式将其封装在 Promise 中。

**正确结构 ✅:**

```
service/
├── account.ts
├── overview.ts
└── index.ts
```

**错误结构 ❌:**

```
service/
├── account/          // 错误: service目录下不应再有子目录
│   └── detail.ts
└── overview.ts
```

**示例：清晰的"正确"与"错误"对比**

下面展示了如何正确地构建一个页面，以及需要避免的错误做法。

---

#### 错误的做法 (禁止的模式) ❌

```typescript
// /projects/holdings/HoldingsPage.tsx (错误示例)

// 页面组件包揽了所有数据获取逻辑, 破坏了子组件的独立性
import { useStocks } from './components/StocksList/useStocks'; // 错误：父组件不应关心子组件的 Hook
import { useBonds } from './components/BondsList/useBonds';   // 错误：父组件不应关心子组件的 Hook
import { StocksList } from './components/StocksList';
import { BondsList } from './components/BondsList';

const HoldingsPage = () => {
  const { stocks } = useStocks(); // 错误：在页面中获取数据
  const { bonds } = useBonds();   // 错误：在页面中获取数据

  // 错误：通过 props 将数据层层传递下去
  return (
    <div>
      <StocksList stocks={stocks} />
      <BondsList bonds={bonds} />
    </div>
  );
};
```

---

#### 正确的做法 (推荐的模式) ✅

```typescript
// /projects/holdings/components/StocksList/useStocks.ts (Hook 与组件同在一个目录下)

import { getAsyncStocks } from '@/projects/holdings/service'; // 向上查找 service
// ... Hook 实现

// /projects/holdings/components/StocksList/index.tsx (自治组件)
import { useStocks } from './useStocks'; // Hook 与组件同级，通过相对路径导入

const StocksList = () => {
  // 正确：组件内部调用自己的 Hook 获取数据
  const { stocks, error } = useStocks();

  if (error) return <div>Error!</div>;
  // ...渲染列表
};

// /projects/holdings/HoldingsPage.tsx (正确的页面组件)
import { StocksList } from './components/StocksList';
import { BondsList } from './components/BondsList';
// ...其他只负责布局的组件

const HoldingsPage = () => {
  // 正确：页面组件非常"干净"，只负责布局和组合。
  // 它不关心子组件的数据来源和加载状态。
  return (
    <div className="layout-container">
      <h1>我的持仓</h1>
      <StocksList />
      <BondsList />
    </div>
  );
};
```

- **数据源质量**:
  - **严禁**在渲染列表时使用 `index` 作为 `key`。
  - **严禁**为了规避 `key` 重复警告而在组件中拼接字符串作为 `key`。
  - `key` 值重复的警告**必须**通过修正数据源来解决，确保数据提供方给予的 id 或 code 是唯一的。

### 5. 全局样式与CSS重置 (Global Styles & CSS Reset)

为确保页面视觉统一性并消除浏览器默认样式差异，每个页面模块 **必须** 包含一个 `global.css` 全局样式文件。

- **职责**: 此文件专门用于定义CSS重置 (`margin: 0; padding: 0;` 等)、页面级的基础样式 (如 `body` 的 `font-family`, `background-color`) 以及页面的 **颜色系统** (详见颜色规范)。
- **引入方式**: 全局样式文件必须在页面的渲染入口 (`index.tsx`) 中优先导入，以确保它在所有组件样式之前生效。
- **关注点分离**: 这种做法将不属于任何特定组件的全局样式与组件自身的模块化样式 (`.module.less`) 明确分离开来，提高了代码的清晰度和可维护性。
