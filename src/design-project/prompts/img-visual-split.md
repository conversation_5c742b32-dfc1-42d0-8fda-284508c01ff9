你需要分析用户提供的图片，识别其中的主要UI区块并计算每个区块的精确坐标位置。重点是恢复整体布局结构，而不是细节组件。

**图片信息：**

- 图片名称：{{ imageName | default('设计图') }}
- 图片尺寸：{{ imageWidth | default('未知') }}px × {{ imageHeight | default('未知') }}px
- 图片格式：{{ imageType | default('PNG') }}
- 这是个移动端设计稿，(0,0)(750,88)是顶部系统状态栏，底部(0,2168)(750,2236)是底部home指示器

**智能拆分场景：**
官网场景：将顶部导航栏、图文区块、底部导航栏分别拆分成原子
中后台场景：将顶部导航栏、左侧菜单、中间内容区域和右侧导航栏分别拆分成原子组件
区块场景：将标题、横幅、筛选面板和服务列表分别拆分成原子组件
移动端场景：将顶部状态栏、导航栏、内容区域和底部导航栏分别拆分成原子组件

**核心原则：**
完整性优先：绝对不能割裂完整的功能区块，宁可区块大一些也要保持完整
布局恢复：目标是恢复整体页面布局，不是详细的组件拆解
视觉分组：按照视觉上的自然分组来划分区块
功能完整：每个区块都应该是一个完整的功能单元

**分析要求：**

1. 识别图片中的所有UI组件（如状态栏、标题栏、按钮、输入框、列表项等）
2. 以图片左上角为原点(0,0)建立坐标系，精准计算每个组件的坐标位置
3. 坐标格式为 x1, y1（左上角）到 x2, y2（右下角）
4. 组件命名要准确描述其功能和内容
   {% if imageWidth and imageHeight %}
5. 请注意当前图片尺寸为 {{ imageWidth }}px × {{ imageHeight }}px，所有坐标都应在此范围内
{% endif %}
6. 请提取出明显的区块，对于模糊的区块请忽略，区域的边界要清晰
7. 使用矩形拆分，且拆成大的区块即可，一定要保证拆分的是一个完整的区块，不能截断实际的区块
8. 按照经验，最佳区块数量为3-5个，可以适当根据实际情况调整
8. 请确保每个组件的宽高加起来和输入图片一致，要考虑留白空间，不一致请不断自迭代调整，直到与输入图片宽高一致为止


**输出要求：**
请创建一个名为 `index.json` 的文件，将分析结果以JSON数组形式保存到文件中。每个组件包含：

- name: 组件名称（中文，描述准确）
- x1, y1: 左上角坐标
- x2, y2: 右下角坐标

**文件创建指令：**
请使用以下步骤创建文件：

1. 创建 `index.json` 文件
2. 将分析结果按照以下JSON格式写入文件
3. 以下是示例数据，仅用于提供输出结构示意，请你严格根据实际情况并结合分析结果进行修改

```json
[
    { "name": "顶部系统状态栏", "x1": 0, "y1": 0, "x2": {{ imageWidth | default(375) }}, "y2": 88 },
    { "name": "标题栏", "x1": 0, "y1": 88, "x2": {{ imageWidth | default(375) }}, "y2": 110 },
    { "name": "返回按钮", "x1": 16, "y1": 110, "x2": {{ imageWidth | default(375) }}, "y2": 230 },
    { "name": "页面标题", "x1": 50, "y1": 230, "x2": {{ imageWidth | default(375) }}, "y2": 350 },
    { "name": "主要内容区域", "x1": 0, "y1": 350, "x2": {{ imageWidth | default(375) }}, "y2": 2168 },
    { "name": "底部home指示器", "x1": 0, "y1": 2168, "x2": {{ imageWidth | default(375) }}, "y2": {{ imageHeight | default(600) }} }
]
```

**重要说明：**

- 你必须检查顶部系统状态栏和底部home指示器的坐标是否符合要求
- 所有的组件区域合并后必须覆盖输入图片的所有区域
- 必须创建 `index.json` 文件并将完整的JSON数组写入文件
- 不要在回复中直接显示JSON内容，而是将其保存到文件中
- 确保JSON格式正确，可以被程序解析

**注意事项：**

- 坐标必须准确，确保组件边界清晰
- 组件划分要合理，既不要过于细碎也不要过于粗糙
- 相同类型的组件请统一命名规范

请开始分析图片并创建 `index.json` 文件。
