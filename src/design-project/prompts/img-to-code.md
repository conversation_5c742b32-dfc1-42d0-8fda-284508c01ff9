请按以下要求使用html代码100%视觉还原设计稿图片，直接输出html代码。
### rules.md
# UI 还原与开发规范

## 1. 目标与范围

### 1.1. 核心目标

- 本规范适用于移动端设计稿。所有页面和组件开发需严格按照移动端设计稿进行，还原移动端布局、尺寸、交互和视觉效果。
- 必须保证良好的响应式适配，适应主流手机屏幕尺寸。
- 优化触控体验，确保按钮、交互区域符合移动端可用性标准。
- 字体、间距、颜色等需与设计稿保持一致。

### 1.2. 最高准则

- 精确还原设计稿的所有细节，包括背景色、文字颜色、字号、字体、间距、边框等。
- 所有界面元素（如头部、底部、侧边栏、内容区等）必须完整实现。
- 所有文案需与设计稿保持一致。
- 图表类组件需使用 echarts 实现。
- 图片使用 base64 格式空白图片 data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs= 代替，使用 alt 属性指明图片名称，如<img alt="香港">
- 图标使用 svg 实现。
- 还原后的 html 必须完整可运行。
- **以设计稿标注宽度为准**：如果设计稿是 375px 宽度，必须确保在 375px 下的还原效果与设计稿完全一致。
- **流式布局不能偏离设计稿**：流式适配是在保证设计稿宽度完美还原基础上的扩展，不能因为实现了流式布局就忽略设计稿基准宽度的准确性。
- **禁止假设代码正确**：必须在设计稿指定宽度下逐一验证。

### 1.3. 范围 (In Scope)

- **响应式自适应规范**
  - **禁止固定宽度设计**：不得按照设计稿的固定宽度（如 375px）进行开发，必须实现真正的响应式布局。
  - **使用相对单位**：优先使用百分比、vw/vh、rem/em 等相对单位，避免使用绝对像素值。
  - **容器自适应**：根容器使用 `width: 100%` 而非固定最大宽度，让内容充满整个屏幕。
  - **内边距适配**：不同屏幕尺寸使用不同内边距。
  - **字体大小适配**：在不同屏幕尺寸下使用合适的字体大小。
  - **组件尺寸适配**：图标、按钮、卡片等组件应根据屏幕尺寸调整大小。
- **组件与文件命名**
  - 组件与文件需按业务语义进行逻辑划分与命名。
  - 所有注释必须使用中文。

### 1.4. 非目标范围 (Out of Scope)

- 本次规范不包含严格的 HTML 语义化改造。
- 不要求拆分 CSS 到独立文件。
- 不要求资源本地化（图片、字体等）。
- 不要求可访问性（Accessibility）优化。

### 1.5. 禁止项 (Prohibitions)

- **严禁实现移动设备系统 UI 元素**：在代码中禁止出现任何硬编码的系统 UI 元素。
- **CSS 规范**
  - **禁止使用内联样式**：禁止在 HTML 元素上使用 `style` 属性来添加内联样式。
  - **样式分离**：禁止使用外部 CSS 文件，所有样式必须在 `<style>` 标签内定义。
  - **设计系统**：禁止建立设计系统（如在 `:root` 中定义 CSS 变量），应使用 `style` 标签。
  - **CSS 框架**：禁止使用任何 CSS 框架（如 Bootstrap, Tailwind CSS），所有样式需手写。
  - **字体规范**：全局字体应在 `body` 元素上通过 `font-family` 统一设置为 `PingFang SC", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif`，以确保整体视觉风格的一致性。所有元素继承此字体，禁止在单个元素上覆盖 `font-family` 属性，以避免样式碎片化。
- **JavaScript 规范**
  - **仅允许**使用 Vue 的数据绑定（`v-text`, `v-bind`）和列表渲染（`v-for`）功能。
  - **禁止使用**其他 Vue 功能，如计算属性、方法、组件系统、生命周期钩子等。
- **Vue.js 模板要求**
  - **禁止使用 `<template>` 标签**进行包裹。
- **Vue.js 数据管理**
  - Vue 的 `data` 对象**只应包含**需要响应式更新的动态数据。
  - 所有**静态资源**（如图标 URL、固定的标题文本）**必须**直接在 HTML 模板中硬编码。

## 2. 实施策略与规范

### 策略一：使用 Flexbox 现代化布局

- **目标**：用 Flexbox 的 `justify-content` 和 `gap` 属性，替换所有基于固定边距（`margin`）的"魔法数字"布局，提升代码健壮性。
- **实施步骤**：
  1. 定位使用 `margin` 进行元素定位的父容器。
  2. 移除子元素上的 `margin` 属性。
  3. 在父容器上应用 `display: flex` 和 `justify-content`（如 `space-between`）来控制主轴对齐。
  4. 使用 `gap` 属性来控制元素之间的间距。

### 策略二：精简 HTML 结构与合并 CSS

- **目标**：移除冗余的 HTML 包装元素，合并功能和样式完全相同的 CSS 类，提升代码可读性和可维护性。
- **实施步骤**：
  1. 识别并合并样式完全相同的 CSS 类，减少代码重复。
  2. 在确保布局和样式不变的前提下，移除没有实际作用、纯粹用于包裹的 `<div>` 标签，降低 HTML 的嵌套深度。

### 策略三：实现流式布局

- **目标**：移除固定宽度，让布局更具弹性，为响应式设计打下基础。
- **实施步骤**：
  1. 全局审查 CSS 中的固定 `width` 声明。
  2. 使用流式布局方案（如 `max-width` 配合 `width: 100%`）或利用块级元素的流式特性进行替换。
  3. 在 375px 视口下，反复比对视觉稿，确保布局的精确还原。

### 策略四：技术栈要求与命名规范

- **技术栈**
  - 使用现代 CSS（如 Flexbox、Grid）进行样式开发。
  - 使用 Vue.js 实现数据绑定和视图更新。
- **命名规范**
  - 组件需按业务语义进行逻辑划分，禁止使用通用词（如 header、page、index、nav、table、xxx-header 等）。
  - 每个组件的根元素需加上 class，格式为 class="${业务语义组件名}"，如 class="asset-overview-card"。
  - HTML 文件名、JS 文件名、CSS 文件名、变量、函数、组件名均需体现业务含义，推荐使用有业务意义的英文命名。
  - 禁止出现任何无实际业务意义的通用英文命名。

### 策略五：代码示例

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
      /* 基础样式 */
      body {
        font-family: PingFang SC", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial,sans-serif;
        margin: 0;
        background-color: #f0f2f5;
      }
      .asset-overview-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 16px;
        margin: 16px;
      }

      /* 布局样式 */
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .content {
        margin-top: 8px;
      }
      .footer {
        display: flex;
        gap: 16px;
        margin-top: 16px;
      }

      /* 文本样式 */
      .title {
        font-size: 14px;
        color: #64748b;
      }
      .total-amount {
        font-size: 24px;
        font-weight: bold;
        color: #1e293b;
      }
      .item-label {
        font-size: 12px;
        color: #94a3b8;
      }
      .item-value {
        font-size: 14px;
        color: #1e293b;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="asset-overview-card">
        <div class="header">
          <span class="title">总资产(元)</span>
          <!-- 静态图标直接在 HTML 中 -->
          <img src="path/to/arrow-right.svg" alt="详情" />
        </div>
        <div class="content">
          <div class="total-amount" v-text="assetTotal"></div>
        </div>
        <div class="footer">
          <div class="footer-item" v-for="item in assetItems">
            <div class="item-label" v-text="item.label"></div>
            <div class="item-value" v-text="item.value"></div>
          </div>
        </div>
      </div>
    </div>

    <script>
      const { createApp } = Vue;

      createApp({
        data() {
          // 只包含动态数据
          return {
            assetTotal: "1,234,567.89",
            assetItems: [
              { label: "昨日收益(元)", value: "+1,234.56" },
              { label: "累计收益(元)", value: "+12,345.67" },
            ],
          };
        },
      }).mount("#app");
    </script>
  </body>
</html>
```

### 策略六：还原验证规范

- **验证检查清单**
  - [ ] 在设计稿指定宽度（如 375px）下，布局与设计稿完全一致
  - [ ] 关键组件的位置、大小、间距与设计稿精确匹配
  - [ ] 文字大小、颜色、行高与设计稿一致
  - [ ] 所有文字内容与设计稿完全一致
  - [ ] 数值、百分比、金额等数据准确匹配
  - [ ] 图标、颜色、样式细节无遗漏
- **验证工具建议**
  - **浏览器开发者工具**：使用设备模拟器精确验证指定宽度下的效果
  - **设计稿对比**：使用屏幕截图与设计稿进行像素级对比
- **验证责任**
  - **开发者责任**：在提交代码前，必须完成上述验证清单的所有项目
  - **代码审查**：审查者需要重点检查设计稿基准宽度下的还原准确性
  - **测试验证**：测试时优先验证设计稿指定宽度下的效果
