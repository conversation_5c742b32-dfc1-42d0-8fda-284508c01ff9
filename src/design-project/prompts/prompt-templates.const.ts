import { TaskType } from '../enums/task-types.enum';

// 提示词模板配置接口
export interface PromptTemplateConfig {
  id: string;
  name: string;
  description: string;
  filePath?: string;
  selfCheckPromptPath?: string;
  targetPromptPath?: string; // 任务目标提示词路径
  supportedOptions?: string[];
}

// 默认模板配置
const DEFAULT_TEMPLATES: PromptTemplateConfig[] = [
  {
    id: TaskType.IMG_TO_CODE,
    name:"图片转代码任务",
    description:"将图片转换为代码",
    filePath: "prompts/img-to-code.md",
    selfCheckPromptPath: "prompts/img-to-code-check.md",
    supportedOptions: ["model", "enableAutoIteration", "enableStepByStep"]
  },
  {
    id: TaskType.IMG_VISUAL_SPLIT,
    name:"图片视觉分割任务",
    description:"将图片分割为多个区域部分",
    filePath: "prompts/img-visual-split.md",
    selfCheckPromptPath: "prompts/img-visual-split-check.md",
    supportedOptions: ["model", "enableAutoIteration", "enableStepByStep"]
  },
  {
    id: TaskType.IMG_SPLIT_TO_CODE,
    name: '从分割图生成代码',
    description: '根据智能分割后的图片区域，自动生成对应的组件化代码。',
    filePath: 'prompts/img-split-to-code.md',
    selfCheckPromptPath: 'prompts/img-split-to-code-check.md',
    supportedOptions: ['model', 'enableAutoIteration', 'enableStepByStep']
  },
  {
    id: TaskType.COORDS_TO_LAYOUT,
    name: '从切图坐标生成布局',
    description: '根据提供的多个切图区域坐标和原始图片，生成相应的布局代码。',
    filePath: 'prompts/coords-to-layout.md',
    selfCheckPromptPath: 'prompts/coords-to-layout-check.md',
    supportedOptions: ['model', 'enableAutoIteration', 'enableStepByStep']
  },
  {
    id: TaskType.DESIGN_TRANSCODE,
    name: '设计稿代码优化任务',
    description: '将设计稿代码转换为更高质量的代码',
    filePath: 'prompts/design-transcode.md',
    selfCheckPromptPath: 'prompts/design-transcode-check.md',
    supportedOptions: ['model', 'enableAutoIteration', 'enableStepByStep']
  },
  {
    id: TaskType.DESIGN_MERGE,
    name: '设计稿合并',
    description: '合并多个设计稿结果',
    filePath: 'prompts/design-merge.md',
    selfCheckPromptPath: 'prompts/design-merge-check.md',
    supportedOptions: ['model', 'enableAutoIteration', 'enableStepByStep']
  },
  {
    id: TaskType.SPEC_TO_PROD_CODE,
    name: '生成生产级代码',
    description: '依据详细的项目规范、设计系统和代码标准，生成高质量的生产级代码。',
    filePath: 'prompts/spec-to-prod-code.md',
    selfCheckPromptPath: 'prompts/spec-to-prod-code-check.md',
    targetPromptPath: 'prompts/spec-to-prod-code-target.md', // 新增：任务目标提示词
    supportedOptions: ['model', 'enableAutoIteration', 'enableStepByStep']
  },
  {
    id: TaskType.CUSTOM,
    name: '自定义任务',
    description: '使用自定义提示词的任务',
    supportedOptions: ['customPrompt']
  }
];

// 获取所有提示词模板配置
export function getAllPromptTemplates(): PromptTemplateConfig[] {
  return DEFAULT_TEMPLATES;
}

// 获取指定ID的提示词模板配置
export function getPromptTemplateConfig(id: string): PromptTemplateConfig {
  const config = DEFAULT_TEMPLATES.find(template => template.id === id);
  if (!config) {
    throw new Error(`提示词模板配置 ${id} 不存在`);
  }
  return config;
} 