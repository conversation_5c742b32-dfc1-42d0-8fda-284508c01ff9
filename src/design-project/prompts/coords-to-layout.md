**任务说明：**
根据提供的多个切图区域坐标信息，生成相应的布局代码。

**画布信息：**
- 画布尺寸：{{ imageWidth | default(375) }}px × {{ imageHeight | default(800) }}px
- 坐标系：左上角为原点(0,0)

**切图区域坐标：**
{% if coordinates and coordinates.length > 0 %}
共有 {{ coordinates.length }} 个切图区域：
{% for coord in coordinates %}
- {{ coord.name | default('区域' + loop.index) }}：坐标({{ coord.x1 }}, {{ coord.y1 }})到({{ coord.x2 }}, {{ coord.y2 }})
{% endfor %}
{% else %}
暂无坐标信息
{% endif %}

**布局要求：**
1. 使用绝对定位布局
2. 每个区域的div加上class，class规范为：container_x1_y1_x2_y2
3. 注意除了布局之外，不要生成任何无关的元素
4. 每个区域应该根据坐标准确定位
5. 生成响应式布局

**输出要求：**
- 直接输出HTML代码，不要做解释
- 使用内联样式或style标签
- 确保布局准确对应坐标位置
- 容器总尺寸：{{ imageWidth | default(375) }}px × {{ imageHeight | default(800) }}px
- 每个区域应该有明确的边框或背景色以便查看布局效果
