**任务说明：**
根据提供的切图，自动生成对应的HTML-BODY代码。

**图片信息：**

- 图片名称：{{ imageName | default('切图') }}
- 图片尺寸：{{ imageWidth | default('未知') }}px × {{ imageHeight | default('未知') }}px
- 图片格式：{{ imageType | default('PNG') }}

**代码生成要求：**

1. 使用HTML代码100%还原设计图，要求所有样式使用内联样式
2. 直接输出html中body标签内部分(body上不允许有样式)
3. body容器不要出现任何的boder-color等border样式

**输出要求：**

- 直接输出HTML中的body代码，只能输出body部分的代码，不要做解释
- 确保代码可以直接在浏览器中运行
- 保持原设计的视觉效果和布局比例
  {% if imageWidth and imageHeight %}
- 容器总尺寸：{{ imageWidth }}px × {{ imageHeight }}px
  {% endif %}

**强调**：

1. 输出的html文件中，仅能有body标签的代码，不能输出 `<!DOCTYPE html>`、`<html>`、`<head>`等标签。
2. 必须要要有body代码输出到index.html中
3. 输出结果中不要有示例代码

请你严格按照以上要求，输出对应的body标签代码，切记仅将body标签代码写入index.html文件中。
