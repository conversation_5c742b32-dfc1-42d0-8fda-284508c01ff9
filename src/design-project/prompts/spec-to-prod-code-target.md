任务目标：
请根据如下的应用规范，将设计稿 {{ page.name }} 页面的html代码 实现为项目代码，并写入 {{ codeDir }}/{{ page.name }} 目录中。核心开发要求：为确保代码质量和一致性，请在执行时【务必遵守】以下核心规范：
1. 通用组件使用规范（重要！）：
强制使用： 【必须】使用 @repo/ui 中已有的通用组件，使用组件之前请仔细阅读组件规范。
核心哲学： 最大化地、语义化地使用通用组件的 Props。
具体到 Card 组件： 即使卡片头部包含多行复杂信息，也必须将最顶层、最核心的标题和右侧操作，分别传入 title 和 headerRight 属性。其余内容则应作为 children 的一部分，在 Card Body 区域内进行布局。严禁为了图方便而将整个头部自定义为 children 的一部分。
使用NavHeader组件前，分析头部导航的元素是上下布局还是左右布局，这将决定title props如何使用
2. 页面结构：
在 {{ codeDir }}/{{ page.name }} 目录中创建页面，并遵循规范在其中组织 components, service, types 等子目录。
每个组件都需要在components下创建一个子目录，严禁多个组件共享一个子目录
3. 数据获取：
严格遵循"组件自治"原则。每个需要数据的业务组件都应通过其自身的自定义 Hook (use...) 来获取和管理状态。
【严禁】在父页面组件中统一请求数据，然后通过 props 传递给子组件。
4. 样式与命名：
所有组件的样式文件【必须】严格命名为 *.module.less。
其他文件和变量的命名请严格遵守 rules.md 中的规范。
5. 边界限制：
【严禁】修改非 {{ codeDir }}/{{ page.name }} 目录下的文件。
6. 强调：原有html中的业务、交互逻辑、颜色都必须要在生成的组件代码中100%还原，严禁任何遗漏。

注意：{{ codeDir }}/{{ page.name }} 目录路径中的{{ page.name }}如果是中文，需要你替换为对应的英文。
