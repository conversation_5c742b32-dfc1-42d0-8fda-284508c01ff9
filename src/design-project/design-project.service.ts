import { Injectable, OnModuleInit, Logger, BadRequestException } from '@nestjs/common';
import axios from 'axios';
import { PrismaService } from '../prisma/prisma.service';
import { compressImageLossless } from '../utils/image.util';
import { CreateDesignPageDto } from './dto/create-design-page.dto';
import { CreateDesignProjectDto } from './dto/create-design-project.dto';
import { UpdateDesignPageDto } from './dto/update-design-page.dto';
import { UpdateDesignProjectDto } from './dto/update-design-project.dto';

@Injectable()
export class DesignProjectService implements OnModuleInit {
  private readonly logger = new Logger(DesignProjectService.name);

  constructor(
    private prisma: PrismaService,
  ) { }

  async onModuleInit() {
    // 模块初始化
    console.log('DesignProjectService initialized');
  }

  /**
   * 公共方法：将图片URL转换为base64格式
   * @param imageUrl 图片URL
   * @returns base64格式的图片内容
   */
  private async convertImageUrlToBase64(imageUrl: string): Promise<string> {
    try {
      this.logger.log(`🔄 开始转换图片URL为base64: ${imageUrl}`);

      // 发起HTTP请求获取图片数据
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // 检查响应是否成功
      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 获取内容类型，如果无法获取则使用默认值
      const contentType = response.headers['content-type'] || 'image/jpeg';

      // 验证是否为图片类型
      if (!contentType.startsWith('image/')) {
        throw new Error(`URL返回的不是图片类型: ${contentType}`);
      }

      // 转换为base64
      const buffer = Buffer.from(response.data);
      const base64Content = buffer.toString('base64');

      this.logger.log(`✅ 图片转换成功: ${imageUrl}, 大小: ${buffer.length} bytes, 类型: ${contentType}`);

      return base64Content;
    } catch (error) {
      this.logger.error(`❌ 图片URL转换失败: ${imageUrl}`, error.stack);
      throw new BadRequestException(`无法获取图片: ${error.message}`);
    }
  }

  /**
   * 域名替换工具：将蓝湖图片域名替换为 IP
   */
  private replaceLanhuDomain(url?: string): string | undefined {
    if (!url) return url;
    return url.replace('http://lanhu.htsc.com.cn:8089', 'http://************:8089');
  }

  /**
   * 统一处理图片内容的方法
   * @param imageContent 已有的base64图片内容
   * @param imageUrl 图片URL（当imageContent为空时使用）
   * @returns base64格式的图片内容（经过无损压缩）
   */
  async resolveImageContent(imageContent?: string, imageUrl?: string): Promise<string> {
    let originalImageContent: string;

    if (imageContent) {
      this.logger.log(`📄 使用已提供的图片内容 (${imageContent.length} 字符)`);
      originalImageContent = imageContent;
    } else if (imageUrl) {
      this.logger.log(`🔗 使用图片URL: ${imageUrl}`);
      originalImageContent = await this.convertImageUrlToBase64(imageUrl);
    } else {
      throw new BadRequestException('必须提供 imageContent 或 imageUrl 参数');
    }

    // 对图片进行无损压缩
    try {
      this.logger.log(`🗜️ 开始对图片进行无损压缩`);
      const compressedImageContent = await compressImageLossless(originalImageContent);
      this.logger.log(`✅ 图片无损压缩完成`);
      return compressedImageContent;
    } catch (error) {
      this.logger.warn(`⚠️ 图片压缩失败，使用原始图片: ${error.message}`);
      // 如果压缩失败，返回原始图片，确保不影响业务流程
      return originalImageContent;
    }
  }
  // 设计稿转码工程相关方法
  async createProject(createDesignProjectDto: CreateDesignProjectDto) {
    return this.prisma.project.create({
      data: {
        ...createDesignProjectDto,
      },
    });
  }

  async findAllProjects(user?: string) {
    const where = user ? { user } : {};
    return this.prisma.project.findMany({
      where,
      include: {
        pages: {
          select: {
            id: true,
            name: true,
            created: true,
            prototypes: {
              select: {
                id: true,
                prototypeName: true,
                status: true,
              },
            },
          },
        },
      },
      orderBy: { created: 'desc' },
    });
  }

  async findOneProject(id: string) {

    const project = await this.prisma.project.findUnique({
      where: { id },
      include: {
        pages: {
          include: {
            prototypes: true,
          },
        },
      },
    });

    // 处理 imgFileLink 域名替换
    if (project?.pages) {
      project.pages.forEach(page => {
        if (page.prototypes) {
          page.prototypes.forEach(prototype => {
            if (prototype.imgFileLink) {
              prototype.imgFileLink = this.replaceLanhuDomain(prototype.imgFileLink);
            }
          });
        }
      });
    }

    return project;
  }

  async updateProject(id: string, updateDesignProjectDto: UpdateDesignProjectDto) {
    return this.prisma.project.update({
      where: { id },
      data: {
        ...updateDesignProjectDto,
      },
    });
  }

  async removeProject(id: string) {
    return this.prisma.project.delete({
      where: { id },
    });
  }

  // 设计稿页面相关方法
  async createPage(createDesignPageDto: CreateDesignPageDto) {
    const { prototypes, ...pageData } = createDesignPageDto;

    return this.prisma.designPage.create({
      data: {
        ...pageData,
        prototypes: {
          create: prototypes.map(prototype => ({
            ...prototype,
          })),
        },
      },
      include: {
        prototypes: true,
      },
    });
  }

  async findAllPages(designProjectId: string) {
    return this.prisma.designPage.findMany({
      where: { designProjectId },
      include: {
        prototypes: true,
      },
      orderBy: { created: 'desc' },
    });
  }

  async findOnePage(id: string) {
    return this.prisma.designPage.findUnique({
      where: { id },
      include: {
        designProject: true,
        prototypes: true,
      },
    });
  }

  async updatePage(id: string, updateDesignPageDto: UpdateDesignPageDto) {
    const { prototypes, ...pageData } = updateDesignPageDto;

    return this.prisma.designPage.update({
      where: { id },
      data: {
        ...pageData,
      },
      include: {
        prototypes: true,
      },
    });
  }

  async removePage(id: string) {
    return this.prisma.designPage.delete({
      where: { id },
    });
  }

  // 页面原型相关方法
  async createPagePrototype(pageId: string, prototypeData: any) {
    return this.prisma.designPagePrototype.create({
      data: {
        ...prototypeData,
        designPageId: pageId,
      },
    });
  }

  async updatePagePrototype(prototypeId: string, prototypeData: any) {
    return this.prisma.designPagePrototype.update({
      where: { id: prototypeId },
      data: {
        ...prototypeData,
      },
    });
  }

  async removePagePrototype(prototypeId: string) {
    return this.prisma.designPagePrototype.delete({
      where: { id: prototypeId },
    });
  }

  // 批量选择页面进行转码（现在基于原型）
  async selectPagesForTranscode(designProjectId: string, prototypeIds: string[]) {
    return this.prisma.designPagePrototype.findMany({
      where: {
        id: { in: prototypeIds },
        designPage: {
          designProjectId,
        },
      },
      include: {
        designPage: {
          include: {
            designProject: true,
          },
        },
      },
    });
  }

  // 转码任务相关方法
  async getProjectTranscodeTasks(projectId: string) {
    try {
      // 查询backgroundTask表
      return this.prisma.backgroundTask.findMany({
        where: {
          taskType: 'design-transcode',
          designProjectId: projectId,
        },
        include: {
          items: {
            orderBy: { created: 'asc' },
          },
        },
        orderBy: { created: 'desc' },
      });
    } catch (error) {
      console.error(`❌ [Service] 查询项目转码任务失败:`, error);
      // 返回空数组作为默认值
      return [];
    }
  }

  async getTranscodeTask(taskId: string) {
    try {
      // 查询backgroundTask表
      return this.prisma.backgroundTask.findUnique({
        where: { id: taskId },
        include: {
          items: {
            orderBy: { created: 'asc' },
          },
        }
      });
    } catch (error) {
      console.error(`❌ [Service] 查询转码任务失败:`, error);
      return null;
    }
  }

  async updateTranscodeTaskStatus(taskId: string, data: { status: string; progress?: number }) {
    try {
      // 更新backgroundTask表
      return this.prisma.backgroundTask.update({
        where: { id: taskId },
        data: {
          status: data.status,
          progress: data.progress,
        },
      });
    } catch (error) {
      console.error(`❌ [Service] 更新转码任务状态失败:`, error);
      throw error;
    }
  }

  async updateTranscodeTaskItem(itemId: string, data: {
    status?: string;
    progress?: number;
    stage?: string;
    playgroundId?: string;
    error?: string;
  }) {
    return this.prisma.backgroundTaskItem.update({
      where: { id: itemId },
      data: {
        ...data,
        updated: new Date(),
      },
    });
  }

  /**
   * 验证并获取原型信息（用于合并任务）
   */
  async validateAndGetPrototypes(prototypeIds: string[]) {
    const prototypes = await this.prisma.designPagePrototype.findMany({
      where: {
        id: { in: prototypeIds },
        playgroundId: { not: null } // 确保原型已经有转码结果
      },
      include: {
        designPage: {
          select: {
            id: true,
            name: true,
            status: true,
          }
        }
      },
    });

    if (prototypes.length === 0) {
      throw new Error('没有找到有效的已转码原型');
    }

    if (prototypes.length !== prototypeIds.length) {
      const foundIds = prototypes.map(p => p.id);
      const missingIds = prototypeIds.filter(id => !foundIds.includes(id));
      throw new Error(`以下原型不存在或尚未转码: ${missingIds.join(', ')}`);
    }

    return prototypes;
  }

  /**
   * 项目预览：获取项目的应用级代码生成历史，按页面维度分组找出最新记录
   */
  async getProjectCodeGenerationHistory(projectId: string) {
    try {
      this.logger.log(`🔍 [ProjectPreview] 查询项目代码生成历史: ${projectId}`);

      // 1. 查询项目及其页面信息
      const project = await this.prisma.project.findUnique({
        where: { id: projectId },
        include: {
          pages: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!project) {
        this.logger.error(`❌ [ProjectPreview] 项目 ${projectId} 不存在`);
        throw new Error(`项目 ${projectId} 不存在`);
      }

      this.logger.log(`✅ [ProjectPreview] 找到项目: ${project.name}, 页面数: ${project.pages.length}`);
      this.logger.log(`📋 [ProjectPreview] 项目页面列表:`);
      project.pages.forEach((page, index) => {
        this.logger.log(`   ${index + 1}. ${page.name} (ID: ${page.id})`);
      });

      // 2. 查询所有相关的代码生成工作流（包括单页面和多页面工作流）
      const workflows = await this.prisma.backgroundTask.findMany({
        where: {
          designProjectId: projectId,
          taskType: {
            in: ['page-code-generation-workflow', 'multi-page-code-generation-workflow'],
          },
          status: 'completed',
        },
        orderBy: {
          created: 'desc',
        },
      });

      this.logger.log(`📊 [ProjectPreview] 找到 ${workflows.length} 个已完成的工作流:`);
      workflows.forEach((workflow, index) => {
        this.logger.log(`   ${index + 1}. ${workflow.taskName} (${workflow.taskType}) - ${workflow.created}`);
        if (workflow.metadata) {
          const metadata = workflow.metadata as any;
          this.logger.log(`      元数据: pageId=${metadata.pageId}, pageName=${metadata.pageName}, playgroundId=${metadata.playgroundId}`);
        }
      });

      // 3. 按页面分组，找出每个页面的最新工作流记录
      const pageGenerationMap = new Map<string, {
        pageId: string;
        pageName: string;
        latestWorkflow: any;
        playgroundId?: string;
        branchName?: string;
      }>();

      for (const workflow of workflows) {
        const metadata = workflow.metadata as any;
        if (!metadata) {
          this.logger.warn(`⚠️ [ProjectPreview] 工作流 ${workflow.id} 缺少metadata，跳过`);
          continue;
        }

        let pageId: string | undefined;
        let pageName: string | undefined;

        // 尝试从metadata中获取页面信息
        if (metadata.pageId) {
          pageId = metadata.pageId;
          pageName = metadata.pageName;
        } else if (metadata.workflowStatus?.pages) {
          // 多页面工作流可能包含多个页面
          const pages = metadata.workflowStatus.pages;
          if (Array.isArray(pages) && pages.length > 0) {
            // 为多页面工作流的每个页面创建记录
            for (const page of pages) {
              if (page.id && page.name) {
                this.logger.log(`📋 [ProjectPreview] 多页面工作流包含页面: ${page.name} (${page.id})`);

                if (!pageGenerationMap.has(page.id)) {
                  pageGenerationMap.set(page.id, {
                    pageId: page.id,
                    pageName: page.name,
                    latestWorkflow: workflow,
                    playgroundId: metadata?.playgroundId,
                    branchName: '待查找', // 分支名称将在项目预览时实时查找
                  });
                  this.logger.log(`✅ [ProjectPreview] 为页面 ${page.name} 设置工作流记录`);
                }
              }
            }
            continue; // 多页面工作流处理完毕，继续下一个
          }
        }

        if (pageId) {
          this.logger.log(`📋 [ProjectPreview] 处理单页面工作流: pageId=${pageId}, pageName=${pageName}`);

          if (!pageGenerationMap.has(pageId)) {
            pageGenerationMap.set(pageId, {
              pageId,
              pageName: pageName || '未知页面',
              latestWorkflow: workflow,
              playgroundId: (workflow.metadata as any)?.playgroundId,
              branchName: '待查找', // 分支名称将在项目预览时实时查找
            });
            this.logger.log(`✅ [ProjectPreview] 为页面 ${pageName} 设置工作流记录`);
          } else {
            this.logger.log(`⚠️ [ProjectPreview] 页面 ${pageName} 已有更新的工作流记录，跳过`);
          }
        } else {
          this.logger.warn(`⚠️ [ProjectPreview] 工作流 ${workflow.id} 无法确定对应的页面，跳过`);
        }
      }

      this.logger.log(`📊 [ProjectPreview] 页面分组统计:`);
      this.logger.log(`   - 总页面数: ${project.pages.length}`);
      this.logger.log(`   - 有代码生成记录的页面数: ${pageGenerationMap.size}`);

      // 4. 为没有工作流记录的页面查找spec-to-prod-code任务
      for (const page of project.pages) {
        if (!pageGenerationMap.has(page.id)) {
          this.logger.log(`🔍 [ProjectPreview] 页面 ${page.name} 没有工作流记录，查找spec-to-prod-code任务`);

          const codeGenTask = await this.findLatestCodeGenerationTask('', page.id);
          if (codeGenTask) {
            this.logger.log(`✅ [ProjectPreview] 为页面 ${page.name} 找到spec-to-prod-code任务`);

            pageGenerationMap.set(page.id, {
              pageId: page.id,
              pageName: page.name,
              latestWorkflow: {
                id: codeGenTask.taskId,
                created: new Date(),
                taskType: 'spec-to-prod-code',
                metadata: { playgroundId: codeGenTask.playgroundId }
              },
              playgroundId: codeGenTask.playgroundId,
              branchName: '待查找', // 分支名称将在项目预览时实时查找
            });
          } else {
            this.logger.warn(`⚠️ [ProjectPreview] 页面 ${page.name} 没有找到任何代码生成记录`);
          }
        }
      }

      const pageGenerations = Array.from(pageGenerationMap.values());

      this.logger.log(`🎯 [ProjectPreview] 最终结果:`);
      this.logger.log(`   - 项目: ${project.name}`);
      this.logger.log(`   - Git仓库: ${project.gitUrl}`);
      this.logger.log(`   - 目标分支: ${project.gitBranch}`);
      this.logger.log(`   - 有生成记录的页面: ${pageGenerations.length}`);

      pageGenerations.forEach((gen, index) => {
        this.logger.log(`   ${index + 1}. ${gen.pageName} | Playground: ${gen.playgroundId} | 工作流: ${gen.latestWorkflow.created}`);
      });

      return {
        project: {
          id: project.id,
          name: project.name,
          gitUrl: project.gitUrl,
          gitBranch: project.gitBranch,
        },
        pageGenerations,
      };

    } catch (error) {
      this.logger.error(`❌ [ProjectPreview] 获取项目代码生成历史失败: ${projectId}`, error);
      throw error;
    }
  }

  /**
   * 查找页面的最新代码生成任务（spec-to-prod-code类型）
   */
  private async findLatestCodeGenerationTask(workflowId: string, pageId: string): Promise<any> {
    try {
      this.logger.log(`🔍 [CodeGen] 查找工作流 ${workflowId} 的页面 ${pageId} 最新代码生成任务`);

      // 先尝试查找直接关联的spec-to-prod-code任务
      // 由于MongoDB的JSON查询限制，我们需要获取所有spec-to-prod-code任务然后过滤
      const allSpecTasks = await this.prisma.backgroundTask.findMany({
        where: {
          taskType: 'spec-to-prod-code',
          status: 'completed',
        },
        orderBy: {
          created: 'desc',
        },
        take: 50, // 限制查询数量以提高性能
      });

      // 过滤出匹配的任务
      const matchedTasks = allSpecTasks.filter(task => {
        const metadata = task.metadata as any;
        if (!metadata) return false;

        // 尝试多种匹配方式
        const matchesPageId = metadata.pageId === pageId;
        const matchesWorkflowId = metadata.workflowId === workflowId;
        const matchesTaskId = task.id === workflowId; // 有时workflowId就是taskId

        // 检查任务项中是否包含页面信息
        const hasPageInItems = metadata.pages &&
          Array.isArray(metadata.pages) &&
          metadata.pages.some((p: any) => p.id === pageId);

        this.logger.log(`🔍 [CodeGen] 检查任务 ${task.id}: pageId匹配=${matchesPageId}, workflowId匹配=${matchesWorkflowId}, taskId匹配=${matchesTaskId}, 包含页面=${hasPageInItems}`);

        return matchesPageId || matchesWorkflowId || matchesTaskId || hasPageInItems;
      });

      if (matchedTasks.length > 0) {
        const task = matchedTasks[0];
        const metadata = task.metadata as any;

        this.logger.log(`✅ [CodeGen] 找到匹配的spec-to-prod-code任务: ${task.id}`);

        return {
          taskId: task.id,
          playgroundId: metadata?.playgroundId,
          branchName: '待查找', // 分支名称将在项目预览时实时查找
        };
      }

      // 如果没有找到直接匹配的任务，尝试查找相关的子任务
      this.logger.log(`🔍 [CodeGen] 未找到直接匹配的任务，尝试查找子任务`);

      const taskItems = await this.prisma.backgroundTaskItem.findMany({
        where: {
          backgroundTaskId: workflowId,
          status: 'completed',
        },
        orderBy: {
          created: 'desc',
        },
        take: 10,
      });

      for (const item of taskItems) {
        const metadata = item.metadata as any;
        if (metadata && (metadata.pageId === pageId || metadata.itemId === pageId)) {
          this.logger.log(`✅ [CodeGen] 从子任务中找到匹配项: ${item.id}`);

          return {
            taskId: item.id,
            playgroundId: metadata?.playgroundId,
            branchName: '待查找', // 分支名称将在项目预览时实时查找
          };
        }
      }

      this.logger.warn(`⚠️ [CodeGen] 未找到页面 ${pageId} 的代码生成任务`);
      return null;

    } catch (error) {
      this.logger.error(`❌ [CodeGen] 查找代码生成任务失败: workflowId=${workflowId}, pageId=${pageId}`, error);
      return null;
    }
  }

  /**
   * 从任务元数据中提取分支名称
   */
  private extractBranchNameFromMetadata(metadata: any): string | undefined {
    if (!metadata) {
      this.logger.warn('extractBranchNameFromMetadata: metadata为空');
      return undefined;
    }

    // 按优先级尝试从不同字段提取分支名称
    const branchName = metadata.branchName ||
      metadata.featureBranch ||
      metadata.gitBranch ||
      metadata.branch;

    if (branchName) {
      this.logger.log(`✅ 从metadata中提取到分支名称: ${branchName}`);
      return branchName;
    }

    // 尝试从result字段中提取
    if (metadata.result) {
      const resultBranchName = metadata.result.branchName ||
        metadata.result.featureBranch ||
        metadata.result.gitBranch;
      if (resultBranchName) {
        this.logger.log(`✅ 从metadata.result中提取到分支名称: ${resultBranchName}`);
        return resultBranchName;
      }
    }

    // 尝试从playgroundId生成分支名称
    if (metadata.playgroundId && metadata.pageName) {
      const fallbackBranch = `feature/${metadata.pageName}-${metadata.playgroundId.slice(-8)}`;
      this.logger.log(`⚠️ 未找到明确的分支名称，使用fallback: ${fallbackBranch}`);
      return fallbackBranch;
    }

    this.logger.warn('extractBranchNameFromMetadata: 无法从任何字段提取分支名称', {
      availableFields: Object.keys(metadata),
      metadata: JSON.stringify(metadata, null, 2)
    });
    return undefined;
  }
} 