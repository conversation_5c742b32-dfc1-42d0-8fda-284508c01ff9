/**
 * 设计项目任务类型枚举
 * 用于定义系统支持的所有任务类型
 */
export enum TaskType {
  /** 图片转代码 */
  IMG_TO_CODE = 'img-to-code',
  
  /** 图片视觉分割 */
  IMG_VISUAL_SPLIT = 'img-visual-split',
  
  /** 分割图转代码 */
  IMG_SPLIT_TO_CODE = 'img-split-to-code',
  
  /** 坐标转布局 */
  COORDS_TO_LAYOUT = 'coords-to-layout',
  
  /** 根据应用规范生成生产级代码 */
  SPEC_TO_PROD_CODE = 'spec-to-prod-code',
  
  /** 设计稿转码 */
  DESIGN_TRANSCODE = 'design-transcode',
  
  /** 设计稿合并 */
  DESIGN_MERGE = 'design-merge',
  
  /** 自定义任务 */
  CUSTOM = 'custom',
  
  /** 页面级代码生成工作流 */
  PAGE_CODE_GENERATION_WORKFLOW = 'page-code-generation-workflow',
  
  /** 多页面代码生成工作流 */
  MULTI_PAGE_CODE_GENERATION_WORKFLOW = 'multi-page-code-generation-workflow',
}

/**
 * 任务类型元数据接口
 */
export interface TaskTypeMetadata {
  /** 任务类型标识 */
  type: TaskType;
  
  /** 任务类型名称 */
  name: string;
  
  /** 任务类型描述 */
  description: string;
  
  /** 是否需要图片输入 */
  requiresImages?: boolean;
  
  /** 是否需要代码输入 */
  requiresCode?: boolean;
  
  /** 是否支持自动迭代 */
  supportsAutoIteration?: boolean;
  
  /** 是否支持分步执行 */
  supportsStepByStep?: boolean;
  
  /** 任务类别 */
  category: 'image-processing' | 'code-generation' | 'design-optimization' | 'custom';
}

/**
 * 任务类型定义映射
 */
export const TASK_TYPE_DEFINITIONS: Record<TaskType, TaskTypeMetadata> = {
  [TaskType.IMG_TO_CODE]: {
    type: TaskType.IMG_TO_CODE,
    name: '图片转代码',
    description: '将设计图片直接转换为HTML/CSS代码',
    requiresImages: true,
    requiresCode: false,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'code-generation',
  },
  
  [TaskType.IMG_VISUAL_SPLIT]: {
    type: TaskType.IMG_VISUAL_SPLIT,
    name: '图片视觉分割',
    description: '对设计图片进行智能视觉分割，识别组件区域',
    requiresImages: true,
    requiresCode: false,
    supportsAutoIteration: false,
    supportsStepByStep: false,
    category: 'image-processing',
  },
  
  [TaskType.IMG_SPLIT_TO_CODE]: {
    type: TaskType.IMG_SPLIT_TO_CODE,
    name: '分割图转代码',
    description: '将已分割的图片区域转换为对应的代码组件',
    requiresImages: true,
    requiresCode: false,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'code-generation',
  },
  
  [TaskType.COORDS_TO_LAYOUT]: {
    type: TaskType.COORDS_TO_LAYOUT,
    name: '坐标转布局',
    description: '根据元素坐标信息生成响应式布局代码',
    requiresImages: false,
    requiresCode: true,
    supportsAutoIteration: true,
    supportsStepByStep: false,
    category: 'code-generation',
  },
  
  [TaskType.SPEC_TO_PROD_CODE]: {
    type: TaskType.SPEC_TO_PROD_CODE,
    name: '根据应用规范生成生产级代码',
    description: '根据应用规范将设计稿代码转换为生产级别的代码',
    requiresImages: false,
    requiresCode: true,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'code-generation',
  },
  
  [TaskType.DESIGN_TRANSCODE]: {
    type: TaskType.DESIGN_TRANSCODE,
    name: '设计稿转码',
    description: '优化和转换现有的设计稿代码',
    requiresImages: false,
    requiresCode: true,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'design-optimization',
  },
  
  [TaskType.DESIGN_MERGE]: {
    type: TaskType.DESIGN_MERGE,
    name: '设计稿合并',
    description: '合并多个设计稿或代码片段',
    requiresImages: false,
    requiresCode: true,
    supportsAutoIteration: true,
    supportsStepByStep: false,
    category: 'design-optimization',
  },
  
  [TaskType.CUSTOM]: {
    type: TaskType.CUSTOM,
    name: '自定义任务',
    description: '使用自定义提示词执行特定的设计或代码任务',
    requiresImages: false,
    requiresCode: false,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'custom',
  },
  
  [TaskType.PAGE_CODE_GENERATION_WORKFLOW]: {
    type: TaskType.PAGE_CODE_GENERATION_WORKFLOW,
    name: '页面级代码生成工作流',
    description: '完整的页面级代码生成工作流，包括设计稿转码、合并和生产级代码生成',
    requiresImages: false,
    requiresCode: false,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'code-generation',
  },
  
  [TaskType.MULTI_PAGE_CODE_GENERATION_WORKFLOW]: {
    type: TaskType.MULTI_PAGE_CODE_GENERATION_WORKFLOW,
    name: '多页面代码生成工作流',
    description: '同时对多个页面执行代码生成工作流，支持并行处理和统一监控',
    requiresImages: false,
    requiresCode: false,
    supportsAutoIteration: true,
    supportsStepByStep: true,
    category: 'code-generation',
  },
};

/**
 * 获取所有任务类型
 */
export function getAllTaskTypes(): TaskType[] {
  return Object.values(TaskType);
}

/**
 * 获取任务类型元数据
 */
export function getTaskTypeMetadata(taskType: TaskType): TaskTypeMetadata {
  return TASK_TYPE_DEFINITIONS[taskType];
}

/**
 * 根据类别筛选任务类型
 */
export function getTaskTypesByCategory(category: TaskTypeMetadata['category']): TaskType[] {
  return getAllTaskTypes().filter(
    taskType => TASK_TYPE_DEFINITIONS[taskType].category === category
  );
}

/**
 * 检查任务类型是否有效
 */
export function isValidTaskType(taskType: string): taskType is TaskType {
  return Object.values(TaskType).includes(taskType as TaskType);
} 