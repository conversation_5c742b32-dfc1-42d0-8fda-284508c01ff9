import { Module, forwardRef } from '@nestjs/common';
import { AiCodingService } from '../ai-coding/ai-coding.service'
import { BackgroundTaskModule } from '../background-task/background-task.module';
import { TaskWatcherService } from '../background-task/task-watcher.service';
import { PM2PreviewClientModule } from '../pm2-preview-client/pm2-preview-client.module';
import { PrismaModule } from '../prisma/prisma.module';
import { AsyncSplitImgService } from './async-split-img.service';
import { DesignProjectBackgroundTaskService } from './background-task.service';
import { DesignProjectController } from './design-project.controller';
import { DesignProjectService } from './design-project.service';
import { ProjectPreviewService } from './project-preview.service';
import { PromptManagementController } from './prompt-management.controller';
import { PromptManagementService } from './prompt-management.service';
import { PromptTemplateManager } from './prompts/template-manager';
import { TaskHandlerRegistry } from './tasks';
import { WorkflowService } from './workflow.service';

@Module({
  imports: [
    PrismaModule,
    forwardRef(() => BackgroundTaskModule),
    PM2PreviewClientModule, // PM2预览客户端模块，用于调用独立的预览服务
  ],
  controllers: [
    DesignProjectController,
    PromptManagementController,
  ],
  providers: [
    DesignProjectService,
    ProjectPreviewService,
    DesignProjectBackgroundTaskService,
    WorkflowService,
    AsyncSplitImgService,
    TaskWatcherService,
    PromptTemplateManager,
    PromptManagementService,
    TaskHandlerRegistry,
    AiCodingService,
  ],
  exports: [
    DesignProjectService,
    ProjectPreviewService,
    DesignProjectBackgroundTaskService,
    WorkflowService,
    AsyncSplitImgService,
    TaskHandlerRegistry,
    PromptManagementService,
  ],
})
export class DesignProjectModule { }
