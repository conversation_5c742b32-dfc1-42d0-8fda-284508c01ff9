import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig } from '../../background-task';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class CoordsToLayoutHandler extends BaseTaskHandler {
  private readonly logger = new Logger(CoordsToLayoutHandler.name);
  constructor(private readonly promptManager: PromptTemplateManager) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.COORDS_TO_LAYOUT;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('从切图坐标生成布局任务需要至少一个项目');
    }

    for (const item of items) {
      this.logger.log(`🔍 验证项目 ${item.name}:`, {
        hasCoordinates: !!item.metadata?.coordinates,
        coordinateCount: item.metadata?.coordinates?.length || 0,
        imageWidth: item.metadata?.imageWidth || item.metadata?.imgWidth,
        imageHeight: item.metadata?.imageHeight || item.metadata?.imgHeight,
      });

      // 检查是否有坐标信息
      if (!item.metadata?.coordinates) {
        throw new Error(`项目 ${item.name} 缺少坐标信息`);
      }

      // 检查坐标信息是否为空
      const coordinates = item.metadata?.coordinates || [];
      
      if (coordinates.length === 0) {
        throw new Error(`项目 ${item.name} 的坐标信息为空，请提供有效的坐标数据`);
      }

      // 验证坐标数据格式
      for (const coord of coordinates) {
        if (typeof coord.x1 !== 'number' || typeof coord.y1 !== 'number' || 
            typeof coord.x2 !== 'number' || typeof coord.y2 !== 'number') {
          throw new Error(`项目 ${item.name} 中的坐标数据格式不正确，坐标值必须为数字`);
        }
        if (coord.x1 >= coord.x2 || coord.y1 >= coord.y2) {
          throw new Error(`项目 ${item.name} 中的坐标数据不合理，x1应小于x2，y1应小于y2`);
        }
      }
    }
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];

    // 处理原始图片（如果有）
    if (item.metadata?.originalImage) {
      files.push({
        name: item.metadata.imageName || 'original.png',
        contentType: item.metadata.imageType || 'image/png',
        url: item.metadata.originalImage.startsWith('data:') 
          ? item.metadata.originalImage 
          : `data:${item.metadata.imageType || 'image/png'};base64,${item.metadata.originalImage}`,
      });
    }

    // 准备提示词上下文
    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: [item],
      options: config.metadata || {},
      metadata: { 
        item: item,
        hasImages: files.length > 0,
        // 画布信息
        imageWidth: item.metadata?.imgWidth || item.metadata?.imageWidth || 375,
        imageHeight: item.metadata?.imgHeight || item.metadata?.imageHeight || 800,
        // 坐标信息
        coordinates: item.metadata?.coordinates?.map((coord: any) => ({
          name: coord.name || '未命名区域',
          x1: coord.x1 || coord.left || 0,
          y1: coord.y1 || coord.top || 0,
          x2: coord.x2 || coord.right || 0,
          y2: coord.y2 || coord.bottom || 0,
        })) || [],
      },
    };

    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    return { initialMessage, files };
  }
} 