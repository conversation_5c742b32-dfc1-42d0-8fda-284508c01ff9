import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig } from '../../background-task';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class ImgVisualSplitHandler extends BaseTaskHandler {
  private readonly logger = new Logger(ImgVisualSplitHandler.name);
  constructor(private readonly promptManager: PromptTemplateManager) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.IMG_VISUAL_SPLIT;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('图片视觉分割任务需要至少一个图片项目');
    }

    for (const item of items) {
      this.logger.log(`🔍 验证项目 ${item.name}:`, {
        hasImageContent: !!item.metadata?.imageContent,
        hasImageUrl: !!item.metadata?.imageUrl,
        imageContentLength: item.metadata?.imageContent?.length || 0,
        imageType: item.metadata?.imageType,
        imageName: item.metadata?.imageName,
      });

      if (!item.metadata?.imageContent && !item.metadata?.imageUrl) {
        throw new Error(`项目 ${item.name} 缺少图片内容或图片URL`);
      }

      // 检查图片内容是否为空字符串
      if (item.metadata?.imageContent === '') {
        throw new Error(`项目 ${item.name} 的图片内容为空，请提供有效的Base64编码图片数据`);
      }
    }
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];

    this.logger.log(`📋 准备任务数据:`, {
      itemName: item.name,
      hasImageContent: !!item.metadata?.imageContent,
      hasImageUrl: !!item.metadata?.imageUrl,
      imageContentLength: item.metadata?.imageContent?.length || 0,
      imageType: item.metadata?.imageType,
      imageName: item.metadata?.imageName,
    });

    // 处理图片数据
    if (item.metadata?.imageContent) {
      const imageType = item.metadata.imageType || 'png';
      const imageName = item.metadata.imageName || 'design';
      
      // 确保图片名称有正确的扩展名
      const finalImageName = imageName.includes('.') 
        ? imageName 
        : `${imageName}.${imageType}`;
      
      files.push({
        name: finalImageName,
        contentType: `image/${imageType}`,
        url: item.metadata.imageContent.startsWith('data:') 
          ? item.metadata.imageContent 
          : `data:image/${imageType};base64,${item.metadata.imageContent}`,
      });
    } else if (item.metadata?.imageUrl) {
      const imageType = item.metadata.imageType || 'png';
      const imageName = item.metadata.imageName || 'design';
      
      // 确保图片名称有正确的扩展名
      const finalImageName = imageName.includes('.') 
        ? imageName 
        : `${imageName}.${imageType}`;
      
      files.push({
        name: finalImageName,
        contentType: `image/${imageType}`,
        url: item.metadata.imageUrl,
      });
    }

    // 准备提示词上下文 - 添加图片相关占位符数据
    const sanitizedItem = {
      ...item,
      metadata: {
        ...item.metadata,
        imageContent: undefined, // 移除大文件内容
      }
    };

    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: [sanitizedItem],
      options: config.metadata || {},
      metadata: { 
        item: sanitizedItem,
        hasImages: files.length > 0,
        // 图片信息占位符
        imageName: item.metadata?.imageName || 'design.png',
        imageWidth: item.metadata?.imgWidth || item.metadata?.imageWidth,
        imageHeight: item.metadata?.imgHeight || item.metadata?.imageHeight,
        imageType: item.metadata?.imageType || 'PNG',
        // 分割选项占位符
        maxRegions: config.metadata?.maxRegions || 20,
        minRegionSize: config.metadata?.minRegionSize || 50,
        splitOptions: {
          // 分割相关选项
          enableSemanticSplit: config.metadata?.enableSemanticSplit || true,
          enableLayoutSplit: config.metadata?.enableLayoutSplit || true,
          minRegionSize: config.metadata?.minRegionSize || 50,
          maxRegions: config.metadata?.maxRegions || 20,
        }
      },
    };

    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    return { initialMessage, files };
  }
} 