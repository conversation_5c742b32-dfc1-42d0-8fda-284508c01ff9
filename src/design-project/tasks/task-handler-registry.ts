import { Injectable } from '@nestjs/common';
import { TaskWorkerService } from '../../background-task/task-worker.service';
import { PrismaService } from '../../prisma/prisma.service';
import { PromptTemplateManager } from '../prompts/template-manager';
import { BaseTaskHandler } from './base-task-handler';
import { CoordsToLayoutHandler } from './coords-to-layout.handler';
import { CustomTaskHandler } from './custom.handler';
import { DesignMergeHandler } from './design-merge.handler';
import { DesignTranscodeHandler } from './design-transcode.handler';
import { ImgSplitToCodeHandler } from './img-split-to-code.handler';
import { ImgToCodeHandler } from './img-to-code.handler';
import { ImgVisualSplitHandler } from './img-visual-split.handler';
import { SpecToProdCodeHandler } from './spec-to-prod-code.handler';

/**
 * 任务处理器注册表
 * 负责管理所有可用的任务处理器实例
 */
@Injectable()
export class TaskHandlerRegistry {
  private readonly handlers = new Map<string, BaseTaskHandler>();

  constructor(
    private readonly promptManager: PromptTemplateManager,
    private readonly prisma: PrismaService,
    private readonly taskWorker: TaskWorkerService
  ) {
    this.registerHandlers();
  }

  /**
   * 注册所有任务处理器
   */
  private registerHandlers(): void {
    // 注册图片转代码处理器
    this.registerHandler(new ImgToCodeHandler(this.promptManager));
    
    // 注册图片视觉分割处理器
    this.registerHandler(new ImgVisualSplitHandler(this.promptManager));
    
    // 注册分割图转代码处理器
    this.registerHandler(new ImgSplitToCodeHandler(this.promptManager));
    
    // 注册坐标转布局处理器
    this.registerHandler(new CoordsToLayoutHandler(this.promptManager));
    
    // 注册生产级代码生成处理器
    this.registerHandler(new SpecToProdCodeHandler(this.promptManager, this.prisma, this.taskWorker));
    
    // 注册设计稿代码优化处理器
    this.registerHandler(new DesignTranscodeHandler(this.promptManager, this.prisma, this.taskWorker));
    
    // 注册设计稿合并处理器
    this.registerHandler(new DesignMergeHandler(this.promptManager, this.prisma, this.taskWorker));
    
    // 注册自定义任务处理器
    this.registerHandler(new CustomTaskHandler(this.promptManager, this.taskWorker));

    console.log(`📋 已注册 ${this.handlers.size} 个任务处理器:`, Array.from(this.handlers.keys()));
  }

  /**
   * 注册单个任务处理器
   */
  private registerHandler(handler: BaseTaskHandler): void {
    const taskType = handler.getSupportedTaskType();
    this.handlers.set(taskType, handler);
  }

  /**
   * 获取指定任务类型的处理器
   */
  getHandler(taskType: string): BaseTaskHandler | null {
    return this.handlers.get(taskType) || null;
  }

  /**
   * 检查是否支持指定的任务类型
   */
  supportsTaskType(taskType: string): boolean {
    return this.handlers.has(taskType);
  }

  /**
   * 获取所有支持的任务类型
   */
  getSupportedTaskTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * 移除指定的任务处理器
   */
  removeHandler(taskType: string): boolean {
    return this.handlers.delete(taskType);
  }

  /**
   * 获取所有注册的处理器数量
   */
  getHandlerCount(): number {
    return this.handlers.size;
  }
} 