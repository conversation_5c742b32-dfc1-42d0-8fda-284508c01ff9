import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig } from '../../background-task';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';
export class ImgSplitToCodeHandler extends BaseTaskHandler {
  private readonly logger = new Logger(ImgSplitToCodeHandler.name);
  constructor(private readonly promptManager: PromptTemplateManager) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.IMG_SPLIT_TO_CODE;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('从切图生成代码任务需要至少一个项目');
    }

    for (const item of items) {
      this.logger.log(`🔍 验证项目 ${item.name}:`, {
        hasOriginalImage: !!item.metadata?.originalImage,
        hasImageContent: !!item.metadata?.imageContent,
        imageType: item.metadata?.imageType,
        imageName: item.metadata?.imageName,
      });

      // 检查是否有图片数据 - 切图任务只需要图片内容
      const hasImageData = !!(
        item.metadata?.originalImage || 
        item.metadata?.imageContent
      );

      if (!hasImageData) {
        throw new Error(`项目 ${item.name} 缺少图片数据，请提供 originalImage 或 imageContent`);
      }

      // 检查图片内容是否为空字符串
      const imageContent = item.metadata?.originalImage || item.metadata?.imageContent;
      if (imageContent === '') {
        throw new Error(`项目 ${item.name} 的图片内容为空，请提供有效的Base64编码图片数据`);
      }
    }
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];

    this.logger.log(`📋 准备任务数据:`, {
      itemName: item.name,
      hasOriginalImage: !!item.metadata?.originalImage,
      hasImageContent: !!item.metadata?.imageContent,
      imageType: item.metadata?.imageType,
      imageName: item.metadata?.imageName,
      imageWidth: item.metadata?.imgWidth || item.metadata?.imageWidth,
      imageHeight: item.metadata?.imgHeight || item.metadata?.imageHeight,
    });

    // 处理原始图片 - 支持多种字段名
    const imageData = item.metadata?.originalImage || item.metadata?.imageContent;
    if (imageData) {
      // 根据metadata中的信息确定文件名和类型
      const imageName = item.metadata?.imageName || 'original';
      const imageType = item.metadata?.imageType || 'png';
      const fileName = `${imageName}.${imageType}`;
      
      files.push({
        name: fileName,
        contentType: `image/${imageType}`,
        url: imageData.startsWith('data:') 
          ? imageData 
          : `data:image/${imageType};base64,${imageData}`,
      });
    }

    // 切图任务不需要处理分割区域图片，只处理单个切图

    // 准备提示词上下文 - 切图任务只需要图片信息
    const sanitizedItem = {
      ...item,
      metadata: {
        ...item.metadata,
        // 移除大文件内容
        originalImage: undefined,
        imageContent: undefined,
      }
    };

    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: [sanitizedItem],
      options: config.metadata || {},
      metadata: { 
        item: sanitizedItem,
        hasImages: files.length > 0,
        // 图片信息
        imageName: item.metadata?.imageName || 'cut-image.png',
        imageWidth: item.metadata?.imgWidth || item.metadata?.imageWidth,
        imageHeight: item.metadata?.imgHeight || item.metadata?.imageHeight,
        imageType: item.metadata?.imageType || 'PNG',
        // 代码生成选项
        framework: config.metadata?.framework || 'html',
        styleFramework: config.metadata?.styleFramework || 'css',
        codeOptions: {
          framework: config.metadata?.framework || 'html',
          styleFramework: config.metadata?.styleFramework || 'css',
          componentType: config.metadata?.componentType || 'functional',
          generateTests: config.metadata?.generateTests || false,
        }
      },
    };

    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    return { initialMessage, files };
  }
} 