import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig, BackgroundTaskResult } from '../../background-task';
import { TaskWorkerService } from '../../background-task/task-worker.service';
import { PrismaService } from '../../prisma/prisma.service';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class DesignMergeHandler extends BaseTaskHandler {
  private readonly logger = new Logger(DesignMergeHandler.name);
  constructor(
    private readonly promptManager: PromptTemplateManager,
    private readonly prisma: PrismaService,
    private readonly taskWorker: TaskWorkerService
  ) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.DESIGN_MERGE;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('设计稿合并任务需要至少一个项目');
    }

    for (const item of items) {
      if (!item.metadata?.playgroundIds || !Array.isArray(item.metadata.playgroundIds)) {
        throw new Error(`项目 ${item.name} 缺少有效的 playgroundIds`);
      }

      // 移除原有的至少2个playground限制，现在支持1个或多个playground
      if (item.metadata.playgroundIds.length < 1) {
        throw new Error(`项目 ${item.name} 至少需要1个playground`);
      }
    }
  }

  /**
   * 支持自定义执行逻辑
   */
  supportsCustomExecution(): boolean {
    return true;
  }

  /**
   * 自定义执行方法：当只有单个原型时，直接复制转码结果而非执行合并
   */
  async executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: any,
    updateProgress: (progress: any) => Promise<void>
  ): Promise<BackgroundTaskResult> {
    const playgroundIds = item.metadata?.playgroundIds || [];
    const prototypes = item.metadata?.prototypes || [];

    this.logger.log(`🔍 开始执行合并任务，playground数量: ${playgroundIds.length}`);

    try {
      // 更新进度：开始分析
      await updateProgress({
        stage: '分析任务内容',
        progress: 10,
      });

      // 特殊处理：单个原型的情况
      if (playgroundIds.length === 1 && prototypes.length === 1) {
        this.logger.log(`🎯 检测到单个原型，直接复制转码结果而非合并`);
        return await this.handleSinglePrototype(item, config, updateProgress);
      }

      // 多个原型的情况：执行标准合并流程
      this.logger.log(`🔄 检测到多个原型(${playgroundIds.length}个)，执行标准合并流程`);
      return await this.handleMultiplePrototypes(item, config, updateProgress);

    } catch (error) {
      this.logger.error(`❌ 执行合并任务失败:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * 处理单个原型的情况：直接复制转码结果到DesignPage
   */
  private async handleSinglePrototype(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    updateProgress: (progress: any) => Promise<void>
  ): Promise<BackgroundTaskResult> {
    const prototype = item.metadata?.prototypes[0];
    const playgroundId = item.metadata?.playgroundIds[0];

    this.logger.log(`📋 处理单个原型:`, {
      prototypeId: prototype.id,
      prototypeName: prototype.name,
      designPageId: prototype.designPageId,
      playgroundId: playgroundId
    });

    try {
      // 更新进度：获取原型结果
      await updateProgress({
        stage: '获取原型转码结果',
        progress: 30,
      });

      // 获取原型的转码结果
      const playgroundFiles = await this.taskWorker.listPlaygroundFiles(playgroundId);
      this.logger.log(`📁 获取到 ${playgroundFiles.length} 个文件`);

      let resultHtml = null;
      let resultCss = null;

      // 获取HTML文件
      const htmlFile = playgroundFiles.find(f =>
        f.path === 'index.html' ||
        f.path?.endsWith('.html') ||
        f.name === 'index.html' ||
        f.name?.endsWith('.html')
      );

      if (htmlFile) {
        const filePath = htmlFile.path || htmlFile.name;
        this.logger.log(`📄 读取HTML文件: ${filePath}`);
        const htmlData = await this.taskWorker.getPlaygroundFile(playgroundId, filePath);
        resultHtml = htmlData.content;
      }

      // 获取CSS文件
      const cssFile = playgroundFiles.find(f =>
        f.path === 'styles.css' ||
        f.path?.endsWith('.css') ||
        f.name === 'styles.css' ||
        f.name?.endsWith('.css')
      );

      if (cssFile) {
        const filePath = cssFile.path || cssFile.name;
        this.logger.log(`🎨 读取CSS文件: ${filePath}`);
        const cssData = await this.taskWorker.getPlaygroundFile(playgroundId, filePath);
        resultCss = cssData.content;
      }

      // 更新进度：更新页面结果
      await updateProgress({
        stage: '更新页面结果',
        progress: 70,
      });

      // 直接更新DesignPage的结果
      await this.prisma.designPage.update({
        where: { id: prototype.designPageId },
        data: {
          status: 'completed',
          playgroundId: playgroundId,
          resultHtml: resultHtml,
          resultCss: resultCss,
        },
      });

      this.logger.log(`✅ 单个原型处理完成`, {
        designPageId: prototype.designPageId,
        hasHtml: !!resultHtml,
        hasCss: !!resultCss,
        htmlLength: resultHtml?.length || 0,
        cssLength: resultCss?.length || 0,
      });

      // 更新进度：完成
      await updateProgress({
        stage: '任务完成',
        progress: 100,
      });

      return {
        success: true,
        result: {
          message: '单个原型结果已直接复制到页面',
          designPageId: prototype.designPageId,
          prototypeName: prototype.name,
          hasHtml: !!resultHtml,
          hasCss: !!resultCss,
          singlePrototypeMode: true,
        },
        metadata: {
          playgroundId: playgroundId,
          designPageId: prototype.designPageId,
          singlePrototypeMode: true,
        }
      };

    } catch (error) {
      this.logger.error(`❌ 单个原型处理失败:`, error);

      // 更新页面状态为失败
      try {
        await this.prisma.designPage.update({
          where: { id: prototype.designPageId },
          data: {
            status: 'failed',
          },
        });
      } catch (updateError) {
        this.logger.error(`❌ 更新页面失败状态出错:`, updateError);
      }

      throw error;
    }
  }

  /**
   * 处理多个原型的情况：执行标准合并流程
   */
  private async handleMultiplePrototypes(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    updateProgress: (progress: any) => Promise<void>
  ): Promise<BackgroundTaskResult> {
    this.logger.log(`🔄 执行标准多原型合并流程`);

    try {
      // 更新进度：准备合并数据
      await updateProgress({
        stage: '准备合并数据',
        progress: 20,
      });

      // 准备任务数据（使用现有的prepareTaskData方法）
      const taskData = await this.prepareTaskData(item, config);

      // 更新进度：创建合并工作区
      await updateProgress({
        stage: '创建合并工作区',
        progress: 40,
      });

      // 创建新的playground用于合并结果
      const playgroundId = await this.createMergePlayground(item, config);

      // 更新进度：发送合并请求
      await updateProgress({
        stage: '执行AI合并',
        progress: 60,
      });

      // 创建用户消息
      const attachments = taskData.files.map(file => ({
        name: file.name,
        contentType: file.contentType,
        url: file.url,
      }));

      const messageId = await this.taskWorker.createUserMessage(
        playgroundId,
        taskData.initialMessage,
        attachments
      );

      // 更新进度：等待AI响应
      await updateProgress({
        stage: '等待AI响应',
        progress: 80,
      });

      // 执行AI对话
      await this.taskWorker.executeAIDialog(playgroundId, {
        enableAutoIteration: config.metadata?.enableAutoIteration || false,
        enableStepByStep: config.metadata?.enableStepByStep || false,
      });

     // 执行自检查流程（仅在用户启用时）
      const enableAutoIteration = config.metadata?.enableAutoIteration === true;
      if (enableAutoIteration) {
        const templateId = config.metadata?.promptTemplate || config.taskType;
        
        // 准备context，包含projectId以支持项目自定义检查提示词
        const checkContext: any = {
          taskType: config.taskType,
          items: [],
          options: config.metadata || {},
          metadata: config.metadata || {},
        };
        
        const checkPromptContent = await this.promptManager.getCheckPromptContent(templateId, checkContext);

        if (checkPromptContent && checkPromptContent.trim().length > 0) {
          this.logger.log(`🔍 开始执行自检查流程 (playground: ${playgroundId})`);
          await this.taskWorker.createUserMessage(playgroundId, checkPromptContent);
          await this.taskWorker.executeAIDialog(playgroundId, {
            enableAutoIteration: false, // 自检查时不启用自动迭代，避免无限循环
            enableStepByStep: config.metadata?.enableStepByStep || false,
          });
          this.logger.log(`✅ 自检查流程完成 (playground: ${playgroundId})`);
        } else {
          this.logger.log(`ℹ️ 自检查已启用，但模板 ${templateId} 未配置检查提示词`);
        }
      } else {
        this.logger.log(`ℹ️ 自检查未启用，跳过自检查流程 (enableAutoIteration: ${config.metadata?.enableAutoIteration})`);
      }

      // 更新进度：完成
      await updateProgress({
        stage: '任务完成',
        progress: 100,
      });

      return {
        success: true,
        result: {
          message: '多原型合并完成',
          playgroundId: playgroundId,
          mergedPrototypes: item.metadata?.prototypes?.length || 0,
        },
        metadata: {
          playgroundId: playgroundId,
          multiPrototypeMode: true,
        }
      };

    } catch (error) {
      this.logger.error(`❌ 多原型合并失败:`, error);
      throw error;
    }
  }

  /**
   * 创建合并工作区
   */
  private async createMergePlayground(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<string> {
    const prototypes = item.metadata?.prototypes || [];
    const projectName = config.metadata?.projectName || item.name || '设计稿合并';

    return await this.taskWorker.createPlayground({
      name: `merge-${projectName}-${Date.now()}`,
      desc: `合并${prototypes.length}个原型的HTML代码`,
      user: config.user,
      model: config.metadata?.model,
      enableAutoIteration: config.metadata?.enableAutoIteration || false,
      enableStepByStep: config.metadata?.enableStepByStep || false,
    });
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const playgroundIds = item.metadata?.playgroundIds || [];
    const prototypes = item.metadata?.prototypes || [];

    // 获取所有playground的信息
    const playgrounds = await this.prisma.playground.findMany({
      where: { id: { in: playgroundIds } },
      select: {
        id: true,
        name: true,
        desc: true,
        model: true,
        created: true,
      }
    });

    if (playgrounds.length !== playgroundIds.length) {
      const foundIds = playgrounds.map(p => p.id);
      const missingIds = playgroundIds.filter(id => !foundIds.includes(id));
      throw new Error(`找不到以下playground: ${missingIds.join(', ')}`);
    }

    // 创建 playground ID 到原型信息的映射
    const playgroundToPrototypeMap = new Map();
    prototypes.forEach(proto => {
      if (proto.playgroundId) {
        playgroundToPrototypeMap.set(proto.playgroundId, proto);
      }
    });

    this.logger.log(`📋 原型到playground映射:`,
      Array.from(playgroundToPrototypeMap.entries()).map(([pgId, proto]) => ({
        playgroundId: pgId,
        prototypeName: proto.name,
        prototypeId: proto.id
      }))
    );

    // 获取每个playground的文件内容
    const files: Array<{ name: string; contentType: string; url: string }> = [];
    const playgroundItems: BackgroundTaskItem[] = [];

    for (const playground of playgrounds) {
      try {
        // 获取对应的原型信息
        const prototypeInfo = playgroundToPrototypeMap.get(playground.id);
        const prototypeName = prototypeInfo?.name || playground.name || 'Unknown';

        // 清理原型名称，确保文件名安全
        const sanitizedPrototypeName = prototypeName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5_-]/g, '_');

        this.logger.log(`📄 处理playground ${playground.id}, 原型名称: ${prototypeName}`);

        // 获取playground的文件列表
        const fileList = await this.taskWorker.listPlaygroundFiles(playground.id);

        // 查找HTML文件（一定会有一个）
        const htmlFiles = fileList.filter(file =>
          file.path.endsWith('.html') && file.fileType === 1 // FileType.File = 1
        );

        // 查找CSS文件（可能有0个或1个）
        const cssFiles = fileList.filter(file =>
          file.path.endsWith('.css') && file.fileType === 1
        );

        let htmlFileName = '';
        let cssFileName = '';

        // 获取HTML文件内容（必须有）
        if (htmlFiles.length > 0) {
          const htmlFilePath = htmlFiles[0].path; // 取第一个HTML文件
          const htmlFile = await this.taskWorker.getPlaygroundFile(playground.id, htmlFilePath);

          // 使用原型名称作为前缀
          htmlFileName = `${sanitizedPrototypeName}_${htmlFilePath.replace(/[/\\]/g, '_')}`;
          files.push(this.createFileFromContent(
            htmlFile.content,
            htmlFileName,
            'text/html'
          ));

          this.logger.log(`📎 生成HTML附件: ${htmlFileName}`);
        } else {
          throw new Error(`playground ${playground.id} 中没有找到HTML文件`);
        }

        // 获取CSS文件内容（可选）
        if (cssFiles.length > 0) {
          const cssFilePath = cssFiles[0].path; // 取第一个CSS文件
          try {
            const cssFile = await this.taskWorker.getPlaygroundFile(playground.id, cssFilePath);

            // 使用原型名称作为前缀
            cssFileName = `${sanitizedPrototypeName}_${cssFilePath.replace(/[/\\]/g, '_')}`;
            files.push(this.createFileFromContent(
              cssFile.content,
              cssFileName,
              'text/css'
            ));

            this.logger.log(`📎 生成CSS附件: ${cssFileName}`);
          } catch (cssError) {
            this.logger.warn(`⚠️ 获取 CSS 文件 ${cssFilePath} 失败:`, cssError);
          }
        }

        // 创建playground项，包含文件信息
        playgroundItems.push({
          id: playground.id,
          name: playground.name || 'Unnamed Playground',
          metadata: {
            desc: playground.desc,
            model: playground.model,
            created: playground.created,
            htmlFileName: htmlFileName,
            cssFileName: cssFileName || null,
            hasHtmlContent: true,
            hasCssContent: !!cssFileName,
            fileCount: (htmlFileName ? 1 : 0) + (cssFileName ? 1 : 0),
          },
        });

      } catch (error) {
        this.logger.warn(`⚠️ 无法获取playground ${playground.id} 的文件内容:`, error);
        // 即使无法获取文件内容，也要添加playground项
        playgroundItems.push({
          id: playground.id,
          name: playground.name || 'Unnamed Playground',
          metadata: {
            desc: playground.desc,
            model: playground.model,
            created: playground.created,
            hasHtmlContent: false,
            hasCssContent: false,
            fileCount: 0,
            error: error.message,
          },
        });
      }
    }

    // 准备合并配置信息
    const mergeConfig = {
      mergeType: config.metadata?.mergeType,
      projectName: config.metadata?.projectName || item.name,
      navigationStyle: config.metadata?.navigationStyle || 'horizontal',
      layout: config.metadata?.layout || 'responsive',
      theme: config.metadata?.theme || 'modern',
      responsive: config.metadata?.responsive !== false,
      includeSearch: config.metadata?.includeSearch || false,
      includeBreadcrumb: config.metadata?.includeBreadcrumb || false,
    };

    // 准备提示词上下文，使用playgroundItems作为items而不是原始的item
    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: playgroundItems, // 这里改为使用多个playground items
      options: config.metadata || {},
      metadata: {
        playgrounds: playgrounds.map(p => ({
          id: p.id,
          name: p.name,
          desc: p.desc,
          model: p.model,
          created: p.created.toISOString(),
        })),
        mergeConfig: mergeConfig,
        totalPlaygrounds: playgrounds.length,
        totalFiles: files.length,
      },
    };

    // 生成合并任务的提示词
    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    this.logger.log(`📝 生成合并任务提示词，模板: ${templateId}, 文件数: ${files.length}`);

    return {
      initialMessage,
      files,
    };
  }

  async updateItemStatus(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    // 更新所有相关原型的状态
    const playgroundIds = item.metadata?.playgroundIds || [];
    if (playgroundIds.length > 0) {
      await this.prisma.designPagePrototype.updateMany({
        where: { playgroundId: { in: playgroundIds } },
        data: { status: status },
      });
      this.logger.log(`🔄 已将 ${playgroundIds.length} 个原型状态更新为 ${status}`);
    }
  }

  async onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void> {
    this.logger.log(`🏁 合并任务完成回调开始`);

    // 获取任务项
    const taskItems = await this.prisma.backgroundTaskItem.findMany({
      where: { backgroundTaskId: taskId },
    });

    this.logger.log(`📋 找到 ${taskItems.length} 个任务项，${results.length} 个结果`);

    // 处理每个任务项的结果
    for (let i = 0; i < results.length && i < taskItems.length; i++) {
      const result = results[i];
      const taskItem = taskItems[i];

      try {
        if (result.success) {
          this.logger.log(`🔄 处理成功的合并任务项: ${taskItem.itemId}`);

          // 检查是否是单个原型模式（已经在executeItem中处理过了）
          if (result.metadata?.singlePrototypeMode) {
            this.logger.log(`✅ 单个原型模式，结果已在执行阶段处理完毕`);
            continue;
          }

          // 多原型合并的结果处理
          await this.updateDesignPageResults(taskItem, result, config);
        } else {
          this.logger.log(`❌ 处理失败的合并任务项: ${taskItem.itemId}, 错误: ${result.error}`);
          await this.updateDesignPageFailure(taskItem, config);
        }
      } catch (error) {
        this.logger.error(`❌ 处理合并任务项结果时出错: ${taskItem.itemId}`, error);
      }
    }

    this.logger.log(`✅ 合并任务完成回调处理完毕`);
  }

  /**
   * 更新成功合并任务的页面结果
   */
  private async updateDesignPageResults(
    taskItem: any,
    result: BackgroundTaskResult,
    config: BackgroundTaskConfig
  ): Promise<void> {
    try {
      // 从任务项元数据中获取原型信息
      const prototypes = taskItem.metadata?.prototypes || [];
      if (!prototypes || prototypes.length === 0) {
        this.logger.warn(`⚠️ 任务项 ${taskItem.itemId} 没有原型信息，无法确定页面`);
        return;
      }

      this.logger.log(`🔍 从元数据获取原型信息:`, prototypes.map(p => ({
        id: p.id,
        name: p.name,
        designPageId: p.designPageId,
        designPageName: p.designPageName
      })));

      // 确定要更新的页面（优先使用第一个原型的页面）
      const targetPageId = prototypes[0].designPageId;
      const targetPageName = prototypes[0].designPageName;

      // 检查所有原型是否属于同一个页面
      const allSamePage = prototypes.every(p => p.designPageId === targetPageId);
      if (!allSamePage) {
        this.logger.warn(`⚠️ 发现原型属于不同页面，将更新第一个页面: ${targetPageId} (${targetPageName})`);
      }

      this.logger.log(`📄 目标页面: ${targetPageId} (${targetPageName})`);

      // 获取合并结果的 playground
      const mergePlaygroundId = taskItem.playgroundId;
      if (!mergePlaygroundId) {
        this.logger.warn(`⚠️ 任务项 ${taskItem.itemId} 没有 playgroundId，跳过文件获取`);


        await this.prisma.designPage.update({
          where: { id: targetPageId },
          data: {
            status: 'completed',
            playgroundId: mergePlaygroundId,
          },
        });

        return;
      }

      this.logger.log(`📁 获取合并结果 playground ${mergePlaygroundId} 的文件内容`);

      // 获取合并结果 playground 中的文件列表
      const files = await this.taskWorker.listPlaygroundFiles(mergePlaygroundId);
      this.logger.log(`📋 找到 ${files.length} 个文件:`, files.map(f => f.path || f.name));

      let resultHtml = null;
      let resultCss = null;

      // 查找并读取 HTML 文件
      const htmlFile = files.find(f =>
        f.path === 'index.html' ||
        f.path?.endsWith('.html') ||
        f.name === 'index.html' ||
        f.name?.endsWith('.html')
      );

      if (htmlFile) {
        const filePath = htmlFile.path || htmlFile.name;
        this.logger.log(`📄 读取合并后的 HTML 文件: ${filePath}`);
        const htmlData = await this.taskWorker.getPlaygroundFile(mergePlaygroundId, filePath);
        resultHtml = htmlData.content;
      }

      // 查找并读取 CSS 文件
      const cssFile = files.find(f =>
        f.path === 'styles.css' ||
        f.path?.endsWith('.css') ||
        f.name === 'styles.css' ||
        f.name?.endsWith('.css')
      );

      if (cssFile) {
        const filePath = cssFile.path || cssFile.name;
        this.logger.log(`🎨 读取合并后的 CSS 文件: ${filePath}`);
        const cssData = await this.taskWorker.getPlaygroundFile(mergePlaygroundId, filePath);
        resultCss = cssData.content;
      }

      // 更新页面记录
      this.logger.log(`💾 更新页面 ${targetPageId} 的合并结果`);

      await this.prisma.designPage.update({
        where: { id: targetPageId },
        data: {
          status: 'completed',
          playgroundId: mergePlaygroundId,
          resultHtml: resultHtml,
          resultCss: resultCss,
        },
      });

      this.logger.log(`✅ 页面 ${targetPageId} 合并结果更新成功`, {
        pageName: targetPageName,
        hasHtml: !!resultHtml,
        hasCss: !!resultCss,
        htmlLength: resultHtml?.length || 0,
        cssLength: resultCss?.length || 0,
        mergedPrototypes: prototypes.length,
      });

    } catch (error) {
      this.logger.error(`❌ 更新页面合并结果失败: ${taskItem.itemId}`, error);

      // 即使获取文件失败，也要尝试更新状态为完成
      try {
        // 先尝试找到页面
        const playgroundIds = taskItem.metadata?.playgroundIds || [];
        if (playgroundIds.length > 0) {
          const prototype = await this.prisma.designPagePrototype.findFirst({
            where: { playgroundId: { in: playgroundIds } },
            include: { designPage: true }
          });

          if (prototype) {
            await this.prisma.designPage.update({
              where: { id: prototype.designPage.id },
              data: {
                status: 'completed',
                playgroundId: taskItem.playgroundId,
              },
            });

          }
        }
      } catch (updateError) {
        this.logger.error(`❌ 更新页面状态失败: ${taskItem.itemId}`, updateError);
      }
    }
  }

  /**
   * 更新失败合并任务的页面状态
   */
  private async updateDesignPageFailure(
    taskItem: any,
    config: BackgroundTaskConfig
  ): Promise<void> {
    try {
      // 从任务项元数据中获取原型信息
      const prototypes = taskItem.metadata?.prototypes || [];
      if (!prototypes || prototypes.length === 0) {
        this.logger.warn(`⚠️ 失败任务项 ${taskItem.itemId} 没有原型信息，无法确定页面`);
        return;
      }

      this.logger.log(`🔍 从元数据获取失败任务对应的页面:`, prototypes[0]);

      // 使用第一个原型的页面信息
      const targetPageId = prototypes[0].designPageId;
      const targetPageName = prototypes[0].designPageName;

      this.logger.log(`💾 更新失败页面 ${targetPageId} 的状态`);

      await this.prisma.designPage.update({
        where: { id: targetPageId },
        data: {
          status: 'failed',
          playgroundId: taskItem.playgroundId, // 可能为空，但也要记录
        },
      });

      this.logger.log(`✅ 失败页面 ${targetPageId} 状态更新成功`);
    } catch (error) {
      this.logger.error(`❌ 更新失败页面状态出错: ${taskItem.itemId}`, error);
    }
  }
} 