// 基础任务处理器接口和类
export { BaseTaskHandler, TaskDataResult } from './base-task-handler';

// 任务处理器注册表
export { TaskHandlerRegistry } from './task-handler-registry';

// 具体的任务处理器实现
export { ImgToCodeHandler } from './img-to-code.handler';
export { ImgVisualSplitHandler } from './img-visual-split.handler';
export { ImgSplitToCodeHandler } from './img-split-to-code.handler';
export { CoordsToLayoutHandler } from './coords-to-layout.handler';
export { SpecToProdCodeHandler } from './spec-to-prod-code.handler';
export { DesignTranscodeHandler } from './design-transcode.handler';
export { DesignMergeHandler } from './design-merge.handler';
export { CustomTaskHandler } from './custom.handler';

// 任务类型定义
export { 
  TaskType, 
  TaskTypeMetadata, 
  TASK_TYPE_DEFINITIONS,
  getAllTaskTypes,
  getTaskTypeMetadata,
  getTaskTypesByCategory,
  isValidTaskType
} from '../enums/task-types.enum'; 