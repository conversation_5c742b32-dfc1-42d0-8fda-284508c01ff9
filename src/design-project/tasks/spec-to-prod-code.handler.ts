import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '@nestjs/common';
import { executeCommand } from '../../ai-coding/playground';
import { playgroundRootPath } from '../../ai-coding/playground/ctx';
import { BackgroundTaskItem, BackgroundTaskConfig, BackgroundTaskResult } from '../../background-task';
import { PostChatCommand } from '../../background-task/process-manager.service';
import { TaskWorkerService } from '../../background-task/task-worker.service';
import { PrismaService } from '../../prisma/prisma.service';
import { GitlabUtil } from '../../utils/gitlab.util';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class SpecToProdCodeHandler extends BaseTaskHandler {
  private readonly logger = new Logger(SpecToProdCodeHandler.name);

  // 配置需要排除的文件和目录模式 - 使用glob模式
  private static readonly EXCLUDE_PATTERNS = [
    'node_modules/**',      // npm依赖包及其所有子文件
    '**/node_modules/**',  // 任何嵌套的node_modules目录
  ];

  // 仓库地址缓存
  private gitUrlCache: Map<string, string> = new Map();

  constructor(
    private readonly promptManager: PromptTemplateManager,
    private readonly prisma: PrismaService,
    private readonly taskWorker: TaskWorkerService
  ) {
    super();
    // 初始化GitLab工具类
    GitlabUtil.initialize();
  }

  getSupportedTaskType(): string {
    return TaskType.SPEC_TO_PROD_CODE;
  }

  /**
   * 配置Git用户信息，使用token对应的真实用户信息
   */
  private async configureGitUser(playgroundId: string, gitUrl: string, logPrefix: string = '[GitUser]'): Promise<void> {
    try {
      this.logger.log(`👤 ${logPrefix} 开始配置Git用户信息...`);

      // 获取用户信息
      const userInfo = await GitlabUtil.getUserInfoByUrl(gitUrl);

      if (!userInfo) {
        this.logger.warn(`⚠️ ${logPrefix} 无法获取用户信息，使用默认配置`);
        // 使用默认配置
        await this.executeGitCommandWithTimeout(
          playgroundId,
          `git config user.name "gitlab-ci-token"`,
          { dir: 'src' },
          10000
        );

        await this.executeGitCommandWithTimeout(
          playgroundId,
          `git config user.email "<EMAIL>"`,
          { dir: 'src' },
          10000
        );
        return;
      }

      this.logger.log(`📋 ${logPrefix} 获取到用户信息: ${userInfo.name} (${userInfo.email})`);

      // 设置用户名
      const setUserNameResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git config user.name "${userInfo.name}"`,
        { dir: 'src' },
        10000
      );

      if (setUserNameResult.exitCode !== 0) {
        this.logger.warn(`⚠️ ${logPrefix} 设置Git用户名警告: ${setUserNameResult.stderr}`);
      }

      // 设置邮箱
      const setUserEmailResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git config user.email "${userInfo.email}"`,
        { dir: 'src' },
        10000
      );

      if (setUserEmailResult.exitCode !== 0) {
        this.logger.warn(`⚠️ ${logPrefix} 设置Git邮箱警告: ${setUserEmailResult.stderr}`);
      }

      // 验证配置
      const verifyConfigResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git config --list | grep -E "(user.name|user.email)"`,
        { dir: 'src' },
        10000
      );

      this.logger.log(`✅ ${logPrefix} Git用户配置完成: ${verifyConfigResult.stdout.trim()}`);

    } catch (error) {
      this.logger.error(`❌ ${logPrefix} 配置Git用户失败:`, error);
      // 降级到默认配置
      try {
        await this.executeGitCommandWithTimeout(
          playgroundId,
          `git config user.name "gitlab-ci-token"`,
          { dir: 'src' },
          10000
        );

        await this.executeGitCommandWithTimeout(
          playgroundId,
          `git config user.email "<EMAIL>"`,
          { dir: 'src' },
          10000
        );
        this.logger.log(`🔄 ${logPrefix} 已降级到默认Git用户配置`);
      } catch (fallbackError) {
        this.logger.error(`❌ ${logPrefix} 连默认配置都失败了:`, fallbackError);
        throw fallbackError;
      }
    }
  }

  /**
   * 获取Git地址的统一方法
   * 支持仓库转换逻辑：将用户输入的git地址转换为我们group下的fork仓库地址
   * @param originalGitUrl 用户输入的原始git地址
   * @param taskId 任务ID，用于缓存标识
   * @returns 处理后的git地址
   */
  private async getGitUrl(originalGitUrl: string, taskId?: string): Promise<string> {
    try {
      // 构建缓存键，优先使用taskId，否则使用原始URL
      const cacheKey = taskId || originalGitUrl;

      // 检查缓存
      if (this.gitUrlCache.has(cacheKey)) {
        const cachedUrl = this.gitUrlCache.get(cacheKey);
        this.logger.log(`📋 [GitUrl] 使用缓存的Git地址: ${cachedUrl}`);
        return cachedUrl;
      }

      this.logger.log(`🔄 [GitUrl] 开始处理Git地址转换: ${originalGitUrl}`);

      // 实现仓库fork逻辑：
      // 1. 检查原始仓库是否已经在我们的group下
      // 2. 如果不在，fork到我们的group下
      // 3. 返回fork后的仓库地址
      const processedGitUrl = await this.processGitUrlForFork(originalGitUrl);

      // 缓存结果
      this.gitUrlCache.set(cacheKey, processedGitUrl);

      this.logger.log(`✅ [GitUrl] Git地址处理完成: ${originalGitUrl} -> ${processedGitUrl}`);
      return processedGitUrl;

    } catch (error) {
      this.logger.error(`❌ [GitUrl] Git地址处理失败: ${originalGitUrl}`, error);
      // 出错时返回原始地址，确保不影响现有功能
      return originalGitUrl;
    }
  }

  /**
   * 处理Git仓库Fork逻辑的核心方法
   * @param originalGitUrl 原始Git地址
   * @returns Fork后的Git地址
   */
  private async processGitUrlForFork(originalGitUrl: string): Promise<string> {
    try {
      // FIXME 先直接返回原始地址方便开发测试，后续再实现具体逻辑
      this.logger.log(`🔧 [GitFork] 开始处理仓库fork逻辑: ${originalGitUrl}`);

      // 调用具体的fork实现
      const forkedUrl = await GitlabUtil.forkRepositoryToTargetGroup(originalGitUrl);

      this.logger.log(`✅ [GitFork] Fork处理完成: ${originalGitUrl} -> ${forkedUrl}`);
      return forkedUrl;

    } catch (error) {
      this.logger.error(`❌ [GitFork] Fork处理失败: ${originalGitUrl}`, error);
      // 出错时返回原始地址，确保不影响现有功能
      this.logger.log(`🔄 [GitFork] 回退到原始地址: ${originalGitUrl}`);
      return originalGitUrl;
    }
  }

  /**
   * 将仓库fork到目标group的具体实现
   * @param originalGitUrl 原始Git地址
   * @returns Fork后的Git地址
   */
  // 已移除forkRepositoryToTargetGroup方法，现在使用GitlabUtil.forkRepositoryToTargetGroup

  /**
   * 解析Git URL，提取项目信息
   */
  private parseGitUrl(gitUrl: string): {
    gitDomain: string;
    namespace: string;
    projectName: string;
    gitlabEnv: string;
  } | null {
    // 直接使用GitlabUtil的通用方法
    return GitlabUtil.parseGitUrl(gitUrl);
  }

  /**
   * 根据GitLab环境获取目标group
   */
  private getTargetGroupForEnv(gitlabEnv: string): string {
    // 直接使用GitlabUtil的通用方法
    return GitlabUtil.getTargetGroupForEnv(gitlabEnv);
  }

  /**
   * 根据GitLab环境获取对应的JIRA_ID
   */
  private getJiraIdForEnv(gitlabEnv: string): string {
    // 根据不同的GitLab环境返回对应的JIRA_ID
    const jiraIds = {
      'gitlab': process.env.GITLAB1_JIRA_ID || 'WEBDP-599',
      'gitlab2': process.env.GITLAB2_JIRA_ID || 'WEBDP-599',
      'gitlab-test': process.env.GITLAB1_TEST_JIRA_ID || 'WEBPRO-3',
      'gitlab2-test': process.env.GITLAB2_TEST_JIRA_ID || 'WEBPRO-3',
    };

    return jiraIds[gitlabEnv] || 'WEBPRO-3'; // 如果环境不匹配，返回默认值
  }

  /**
   * 检查目标group下是否已存在对应的fork
   */
  private async checkExistingFork(gitlabEnv: string, targetGroup: string, projectName: string): Promise<any> {
    // 直接使用GitlabUtil的通用方法
    return await GitlabUtil.checkExistingFork(gitlabEnv, targetGroup, projectName);
  }

  /**
   * 获取项目ID
   */
  private async getProjectId(gitlabEnv: string, namespace: string, projectName: string): Promise<number | null> {
    // 直接使用GitlabUtil的通用方法
    return await GitlabUtil.getProjectId(gitlabEnv, namespace, projectName);
  }

  /**
   * 创建fork
   */
  private async createFork(gitlabEnv: string, projectId: number, targetGroup: string): Promise<any> {
    // 直接使用GitlabUtil的通用方法
    return await GitlabUtil.createFork(gitlabEnv, projectId, targetGroup);
  }

  /**
   * 根据GitLab环境获取对应的域名
   */
  private getGitDomainForEnv(gitlabEnv: string): string {
    // 直接使用GitlabUtil的通用方法
    return GitlabUtil.getGitDomainForEnv(gitlabEnv);
  }

  /**
   * 构建Git URL
   */
  private buildGitUrl(gitDomain: string, namespace: string, projectName: string): string {
    // 直接使用GitlabUtil的通用方法
    return GitlabUtil.buildGitUrl(gitDomain, namespace, projectName);
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('生产级代码生成任务需要至少一个页面');
    }

    // 🔧 新架构：每个item对应一个页面，所以验证每个item
    for (const item of items) {
      if (!item.metadata?.gitUrl) {
        throw new Error('Git地址不能为空');
      }

      // 🔧 新架构：页面信息在item.metadata.page中
      if (!item.metadata?.page) {
        throw new Error('页面信息不能为空');
      }

      const page = item.metadata.page;
      if (!page.name || !page.htmlContent) {
        throw new Error(`页面 ${page.name || '未命名'} 缺少名称或HTML内容`);
      }
    }

    this.logger.log(`✅ 验证通过: ${items.length} 个页面任务项`);
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    // 这个方法在自定义执行逻辑中不会被调用，但必须实现以满足抽象基类
    return { initialMessage: '', files: [] };
  }

  /**
   * 自定义执行逻辑：处理单个页面任务（新架构下每个item对应一个页面）
   */
  async executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: any,
    updateProgress: (progress: any) => Promise<void>
  ): Promise<any> {
    // 🔧 新架构：从item.metadata中获取单个页面信息
    const { gitUrl: originalGitUrl, branch = 'feature/ai-coding-test', page, projectId, taskName, pageIndex, totalPages } = item.metadata;

    try {
      this.logger.log(`🚀 开始执行单页面生产级代码生成任务`);

      // 使用统一的Git地址获取方法
      const gitUrl = await this.getGitUrl(originalGitUrl, context.taskId);

      this.logger.log(`📦 Git仓库: ${gitUrl} (分支: ${branch})`);
      this.logger.log(`📄 处理页面: "${page.name}" (${pageIndex + 1}/${totalPages})`);
      this.logger.log(`🤖 接收到的模型参数: ${config.metadata?.model || '未指定'}`);
      this.logger.log(`🔄 执行模式: 单页面处理`);

      // 直接调用updateProgress，确保进度信息传递到TaskWatcherService
      await updateProgress({
        progress: 5,
        stage: `准备处理页面: ${page.name}`
      });

      // 步骤1：为页面创建独立的playground并导入Git仓库
      this.logger.log(`🔄 步骤1: 为页面 "${page.name}" 创建playground并导入Git仓库`);

      await updateProgress({
        progress: 10,
        stage: `创建页面 "${page.name}" 的playground工作区`
      });

      const pagePlaygroundId = await this.createPlaygroundFromGit(
        gitUrl,
        branch,
        config.user,
        taskName,
        page.name,
        config.metadata?.model,
        originalGitUrl
      );

      this.logger.log(`✅ 页面 "${page.name}" playground创建成功: ${pagePlaygroundId}`);

      await updateProgress({
        progress: 30,
        stage: `页面 "${page.name}" playground创建完成`
      });

      // 步骤2：处理页面
      this.logger.log(`🔄 步骤2: 开始处理页面 "${page.name}"`);

      await updateProgress({
        progress: 35,
        stage: `开始处理页面 "${page.name}"`
      });

      // 创建页面级别的进度跟踪器
      const pageProgressTracker = async (progress: number) => {
        // 页面处理占用35%-95%的总体进度
        const currentPageProgress = 35 + (progress * 60) / 100;

        this.logger.log(`📊 页面 "${page.name}" 进度: ${progress}% → 总体进度: ${currentPageProgress.toFixed(1)}%`);

        // 直接调用updateProgress，确保进度信息传递到TaskWatcherService
        await updateProgress({
          progress: Math.floor(currentPageProgress),
          stage: `处理页面 "${page.name}" (${progress}%)`
        });
      };

      // 处理单个页面
      await this.processPageInIndependentPlayground(
        page,
        pagePlaygroundId,
        config,
        null, // projectConfig
        pageProgressTracker,
        gitUrl,
        originalGitUrl,
        branch,
        context.taskId
      );

      this.logger.log(`✅ 页面 "${page.name}" 处理完成`);

      await updateProgress({
        progress: 95,
        stage: `页面 "${page.name}" 处理完成`
      });

      // 确保最终进度为100%
      await updateProgress({
        progress: 100,
        stage: `页面 "${page.name}" 任务完成`
      });

      this.logger.log(`🎉 页面 "${page.name}" 任务执行完成!`);

      // 🔧 新架构：返回单个页面的结果
      const resultData = {
        success: true,
        result: {
          message: `页面 "${page.name}" 生产级代码生成任务执行完成`,
          page: page.name,
          playgroundId: pagePlaygroundId,
          executionMode: 'single-page'
        },
        metadata: {
          taskId: context.taskId,
          gitUrl,
          branch,
          pageName: page.name,
          pageIndex,
          totalPages,
          playgroundId: pagePlaygroundId,
          executionMode: 'single-page'
        }
      };

      return resultData;

    } catch (error) {
      this.logger.error(`❌ 页面 "${page.name}" 任务执行失败:`, error);

      // 错误情况下，使用原始gitUrl或处理过的gitUrl（如果可用）
      let errorGitUrl = originalGitUrl;
      try {
        errorGitUrl = await this.getGitUrl(originalGitUrl, context.taskId);
      } catch {
        // 如果获取失败，使用原始地址
        errorGitUrl = originalGitUrl;
      }

      const errorResultData = {
        success: false,
        error: `页面 "${page.name}" 生产级代码生成任务执行失败: ${error.message}`,
        metadata: {
          taskId: context.taskId,
          gitUrl: errorGitUrl,
          branch,
          pageName: page.name,
          pageIndex,
          totalPages,
          executionMode: 'single-page'
        }
      };

      return errorResultData;
    }
  }

  /**
   * 从Git仓库创建playground
   */
  private async createPlaygroundFromGit(gitUrl: string, branch: string, userId: string, taskName: string, pageName: string, configModel?: string, originalGitUrl?: string): Promise<string> {
    try {
      this.logger.log(`🔄 开始从Git仓库创建playground: ${gitUrl} (${branch}) for page: ${pageName}`);
      this.logger.log(`🤖 使用模型: ${configModel || 'openrouter::google/gemini-2.5-pro-preview'}`);

      // 使用TaskWorkerService创建playground，确保所有字段都正确设置
      const playgroundId = await this.taskWorker.createPlayground({
        name: taskName, // taskName已经包含了页面名称
        desc: taskName, // 使用相同的名称作为描述，避免重复拼接
        user: userId,
        model: configModel || 'openrouter::google/gemini-2.5-pro-preview',
        isPublic: true,
        enableAutoIteration: false,
        enableStepByStep: false,
      });

      this.logger.log(`✅ TaskWorkerService创建playground成功: ${playgroundId} for page: ${pageName}`);

      // 使用底层API更新playground的类型、标签和预览设置（这些字段TaskWorkerService不支持）
      await this.prisma.playground.update({
        where: { id: playgroundId },
        data: {
          type: 'spec-to-prod-code', // 使用spec-to-prod-code类型标识这是生产级代码生成任务
          tags: ['git-import', 'spec-to-prod-code', 'parallel-generation', `page-${pageName}`],
          enableCustomPreview: true, // 🔧 新增：标识这种场景支持用户自定义预览地址
          // enablePreview: false, // 生产级代码生成任务不需要预览功能，主要关注代码生成
        }
      });

      // 在playground中克隆Git仓库
      await this.cloneGitRepositoryInPlayground(playgroundId, gitUrl, branch, originalGitUrl);

      this.logger.log(`🎉 从Git仓库创建playground完成: ${playgroundId} for page: ${pageName}`);
      return playgroundId;
    } catch (error) {
      this.logger.error(`❌ 从Git仓库创建playground失败 for page: ${pageName}:`, error);
      throw new Error(`从Git仓库创建playground失败 (${pageName}): ${error.message}`);
    }
  }

  /**
   * 使用Git命令同步分支 - 当API同步失败时的备用方案
   */
  private async syncBranchUsingGitCommands(
    playgroundId: string,
    forkGitUrl: string,
    originalGitUrl: string,
    branch: string
  ): Promise<boolean> {
    try {
      this.logger.log(`🔄 [GitSync] 开始使用Git命令同步分支: ${branch}`);
      this.logger.log(`   原始仓库: ${originalGitUrl}`);
      this.logger.log(`   Fork仓库: ${forkGitUrl}`);

      // 转换URL
      const forkHttpUrl = GitlabUtil.convertTohttpWithToken(forkGitUrl);
      const originalHttpUrl = GitlabUtil.convertTohttpWithToken(originalGitUrl);

      if (!forkHttpUrl || !originalHttpUrl) {
        throw new Error('无法转换Git URL');
      }

      // 步骤1: 克隆fork仓库到临时目录
      this.logger.log(`🔄 [GitSync] 克隆fork仓库...`);
      const cloneResult = await executeCommand(
        playgroundId,
        `git clone ${forkHttpUrl} src`,
        { dir: '.' }
      );

      if (cloneResult.exitCode !== 0) {
        throw new Error(`克隆fork仓库失败: ${cloneResult.stderr}`);
      }

      // 步骤2: 添加原始仓库作为远程仓库
      this.logger.log(`🔄 [GitSync] 添加原始仓库作为远程仓库...`);
      const addRemoteResult = await executeCommand(
        playgroundId,
        `git remote add upstream ${originalHttpUrl}`,
        { dir: 'src' }
      );

      if (addRemoteResult.exitCode !== 0) {
        this.logger.warn(`添加远程仓库警告: ${addRemoteResult.stderr}`);
      }

      // 步骤3: 获取原始仓库的分支
      this.logger.log(`🔄 [GitSync] 获取原始仓库的分支...`);
      const fetchResult = await executeCommand(
        playgroundId,
        `git fetch upstream ${branch}:${branch}`,
        { dir: 'src' }
      );

      if (fetchResult.exitCode !== 0) {
        throw new Error(`获取原始分支失败: ${fetchResult.stderr}`);
      }

      // 步骤4: 切换到目标分支
      this.logger.log(`🔄 [GitSync] 切换到目标分支: ${branch}`);
      const checkoutResult = await executeCommand(
        playgroundId,
        `git checkout ${branch}`,
        { dir: 'src' }
      );

      if (checkoutResult.exitCode !== 0) {
        throw new Error(`切换分支失败: ${checkoutResult.stderr}`);
      }

      this.logger.log(`✅ [GitSync] Git命令同步完成`);
      return true;

    } catch (error) {
      this.logger.error(`❌ [GitSync] Git命令同步失败:`, error);
      return false;
    }
  }

  /**
   * 在playground中克隆Git仓库 - 使用token认证
   */
  private async cloneGitRepositoryInPlayground(playgroundId: string, gitUrl: string, branch: string, originalGitUrl?: string): Promise<void> {
    try {
      this.logger.log(`🔄 在playground ${playgroundId} 中克隆Git仓库: ${gitUrl}`);

      // 步骤1: 递归清空工作区目录除src目录外的其他内容（保留ai会话需要的package.json、.env、index.html文件，其他文件都删除）
      // FIXME 做成可配置的
      this.logger.log(`🔄 清空playground工作区目录除src目录外的其他内容（保留ai会话需要的package.json、.env、index.html文件，其他文件都删除）`);
      const cleanResult = await executeCommand(
        playgroundId,
        `find . -mindepth 1 -maxdepth 1 ! -name 'src' ! -name 'package.json' ! -name '.env' ! -name 'index.html' ! -name '.git' -exec rm -rf {} +`,
        { dir: '.' }
      );

      if (cleanResult.exitCode !== 0) {
        this.logger.warn(`清空工作区警告: ${cleanResult.stderr}`);
      } else {
        this.logger.log(`✅ 工作区清理完成（保留src目录和重要配置文件）`);
      }

      // 步骤2: 转换为带token的http URL
      const httpUrl = GitlabUtil.convertTohttpWithToken(gitUrl);
      if (!httpUrl) {
        throw new Error(`无法为Git URL生成带token的http地址: ${gitUrl}`);
      }

      this.logger.log(`🔐 使用带token的http方式克隆仓库`);

      // 步骤3: 尝试克隆指定分支，如果失败则尝试同步分支后重试
      let cloneSuccess = false;
      let lastError: Error | null = null;

      // 第一次尝试：直接克隆指定分支
      this.logger.log(`🔄 [GitClone] 第一次尝试：直接克隆分支 ${branch}`);
      const firstCloneResult = await executeCommand(
        playgroundId,
        `git clone --branch ${branch} --single-branch --depth 1 ${httpUrl} src`,
        { dir: '.' }
      );

      if (firstCloneResult.exitCode === 0) {
        cloneSuccess = true;
        this.logger.log(`✅ [GitClone] 第一次尝试成功：分支 ${branch} 克隆完成`);
      } else {
        lastError = new Error(`Git clone失败: ${firstCloneResult.stderr}`);
        this.logger.warn(`⚠️ [GitClone] 第一次尝试失败: ${firstCloneResult.stderr}`);

        // 检查是否是分支不存在的错误
        const errorMessage = firstCloneResult.stderr.toLowerCase();
        const isBranchNotFoundError = errorMessage.includes('remote branch') &&
          errorMessage.includes('not found') &&
          errorMessage.includes(branch.toLowerCase());

        if (isBranchNotFoundError && originalGitUrl && originalGitUrl !== gitUrl) {
          this.logger.log(`🔄 [GitClone] 检测到分支不存在错误，尝试从原始仓库同步分支`);

          // 尝试从原始仓库同步分支到fork仓库
          const syncSuccess = await GitlabUtil.syncBranchFromUpstream(
            originalGitUrl,
            gitUrl,
            branch
          );

          if (syncSuccess) {
            this.logger.log(`🔄 [GitClone] API分支同步成功，第二次尝试克隆分支 ${branch}`);

            // 第二次尝试：同步分支后重新克隆
            const secondCloneResult = await executeCommand(
              playgroundId,
              `git clone --branch ${branch} --single-branch --depth 1 ${httpUrl} src`,
              { dir: '.' }
            );

            if (secondCloneResult.exitCode === 0) {
              cloneSuccess = true;
              this.logger.log(`✅ [GitClone] 第二次尝试成功：分支 ${branch} 克隆完成`);
            } else {
              lastError = new Error(`分支同步后仍然克隆失败: ${secondCloneResult.stderr}`);
              this.logger.error(`❌ [GitClone] 第二次尝试失败: ${secondCloneResult.stderr}`);
            }
          } else {
            this.logger.warn(`⚠️ [GitClone] API分支同步失败，尝试Git命令方式同步`);

            // 第三次尝试：使用Git命令方式进行分支同步
            const gitSyncSuccess = await this.syncBranchUsingGitCommands(
              playgroundId,
              gitUrl,
              originalGitUrl,
              branch
            );

            if (gitSyncSuccess) {
              cloneSuccess = true;
              this.logger.log(`✅ [GitClone] Git命令同步成功：分支 ${branch}`);
            } else {
              this.logger.error(`❌ [GitClone] Git命令同步也失败`);
            }
          }
        } else {
          this.logger.log(`ℹ️ [GitClone] 非分支不存在错误或无原始仓库信息，跳过分支同步尝试`);
        }
      }

      // 如果两次尝试都失败，抛出最后的错误
      if (!cloneSuccess) {
        throw lastError || new Error('Git clone失败：未知错误');
      }

      this.logger.log(`✅ Git仓库克隆到临时目录完成`);

      // 步骤4: 配置Git用户信息，使用token对应的真实用户信息
      await this.configureGitUser(playgroundId, gitUrl, '[GitClone]');

      // 步骤5: 获取当前分支和提交信息
      const branchInfoResult = await executeCommand(
        playgroundId,
        `git branch --show-current && git log --oneline -1`,
        { dir: 'src' }
      );

      if (branchInfoResult.exitCode === 0) {
        const [currentBranch, lastCommit] = branchInfoResult.stdout.trim().split('\n');
        this.logger.log(`🌿 当前分支: ${currentBranch}`);
        this.logger.log(`📝 最新提交: ${lastCommit}`);
      }

      this.logger.log(`✅ Git仓库导入完成，仓库已克隆到src目录`);
    } catch (error) {
      this.logger.error(`❌ 克隆Git仓库失败:`, error);
      throw error;
    }
  }

  /**
   * 在独立playground中处理单个页面 - 专为并行执行设计
   * 移除串行处理时的延迟和竞争条件避免逻辑
   */
  private async processPageInIndependentPlayground(
    page: { name: string; htmlContent: string; description?: string },
    playgroundId: string,
    config: BackgroundTaskConfig,
    projectConfig: any,
    updateProgress: (progress: number) => Promise<void>,
    gitUrl: string,
    originalGitUrl?: string,
    targetBranch?: string,
    taskId?: string
  ): Promise<void> {
    try {
      this.logger.log(`🚀 [ParallelProcess] ===== 开始独立处理页面: ${page.name} (playground: ${playgroundId}) =====`);
      const startTime = Date.now();

      await updateProgress(5);

      // 动态读取spec-to-prod-code.md文件内容
      const specFilePath = path.join(__dirname, `design-project/prompts/spec-to-prod-code.md`);
      let specContent = '';

      try {
        specContent = fs.readFileSync(specFilePath, 'utf-8');
        this.logger.log(`📖 [ParallelProcess] 页面 "${page.name}" 成功读取应用规范文档: ${specFilePath} (长度: ${specContent.length}字符)`);
      } catch (error) {
        this.logger.warn(`⚠️ [ParallelProcess] 页面 "${page.name}" 读取应用规范文档失败: ${error.message}`);
        specContent = '应用规范文档读取失败，请检查文件路径';
      }

      await updateProgress(15);

      // 提取UI组件规范提示词（项目配置中用户填写的UI组件规范）
      const desingProject = await this.prisma.project.findUnique({
        where: {
          id: config.metadata?.projectId
        },
        select: {
          llmstxt: true,
          gitCodeDir: true
        }
      });

      const uiPrompt = desingProject?.llmstxt;
      // 代码生成目录
      const codeDir = desingProject?.gitCodeDir?.replace(/\/+$/, '');

      if (!codeDir) {
        throw new Error("无代码生成目录，无法执行项目级代码生成任务，请先在项目配置中配置代码生成目录后再执行项目级生码任务");
      }

      // 使用新的模板系统构建包含完整规范文档的提示词
      const promptMessage = await this.buildPromptMessage(
        page,
        uiPrompt || '',
        specContent,
        codeDir,
        config.metadata?.projectId
      );

      this.logger.log(`📝 [ParallelProcess] 页面 "${page.name}" 构建的提示词长度: ${promptMessage.length} 字符`);
      this.logger.log(`📄 [ParallelProcess] 页面 "${page.name}" HTML内容长度: ${page.htmlContent.length} 字符`);

      await updateProgress(20);

      // 使用TaskWorkerService创建用户消息
      this.logger.log(`📤 [ParallelProcess] 页面 "${page.name}" 发送提示词到playground: ${playgroundId}`);
      await this.taskWorker.createUserMessage(playgroundId, promptMessage);
      this.logger.log(`✅ [ParallelProcess] 页面 "${page.name}" 提示词发送完成`);

      await updateProgress(25);

      // 在独立playground中不需要延迟避免竞争条件，因为每个页面都有独立的playground
      this.logger.log(`🚀 [ParallelProcess] 页面 "${page.name}" 开始独立AI处理流程...`);

      await updateProgress(30);
      // 🚀 与AI对话并行执行安装依赖和启动应用，不阻塞AI处理
      // FIXME 先禁用自定义预览服务，保障测试环境整体流程走通
      // 先开启预览，测试效果
      this.executePreProcessingCommandsAsync(playgroundId, page.name).catch(error => {
        this.logger.error(`❌ [AsyncPreProcess] 页面 "${page.name}" 异步前置处理失败:`, error);
        this.logger.error(`❌ [AsyncPreProcess] 错误堆栈:`, error.stack);
      });

      // 使用TaskWorkerService执行AI对话 - 不传递postChatCommands，我们将在AI完成后手动执行
      this.logger.log(`🎯 [ParallelProcess] ===== 开始页面 "${page.name}" 的独立AI对话流程 =====`);
      this.logger.log(`🔄 [ParallelProcess] 页面 "${page.name}": 启动独立AI处理... (playground: ${playgroundId})`);

      // 包装AI对话，添加页面级别的监控，并传递进度回调
      await this.executePageAIDialogWithMonitoring(
        page.name,
        playgroundId,
        {
          enableAutoIteration: config.metadata?.enableAutoIteration || false,
          enableStepByStep: config.metadata?.enableStepByStep || false,
          // 不在这里传递postChatCommands，我们将在AI完成后手动执行
        },
        // 传递进度回调，AI对话占用30%-95%的进度
        async (aiProgress) => {
          // 计算总体进度：30% + (aiProgress * 65% / 100)
          const overallProgress = 30 + (aiProgress * 65) / 100;

          // 记录实际进度值，用于调试
          this.logger.log(`📊 [ProgressUpdate] 页面 "${page.name}" AI进度: ${aiProgress}% → 总体进度: ${overallProgress.toFixed(1)}%`);

          // 确保调用外层的updateProgress函数更新总体进度
          await updateProgress(overallProgress);
        }
      );

      this.logger.log(`✅ [ParallelProcess] ===== 页面 "${page.name}" 的独立AI对话流程完成 =====`);

      await updateProgress(95);

      // 验证是否真正生成了内容
      await this.verifyAIResponse(playgroundId, page.name);

      await updateProgress(100);

      // 🔥 关键修改：AI对话完成并验证后，页面任务立即完成
      this.logger.log(`🎉 [ParallelProcess] 页面 "${page.name}" 核心生成任务完成，进度100%`);

      const endTime = Date.now();
      const processingTime = Math.round((endTime - startTime) / 1000);
      this.logger.log(`🎉 [ParallelProcess] ===== 页面 "${page.name}" 独立处理完成，总耗时: ${processingTime}秒 =====`);

      // 优化版Git分支创建，支持MR功能
      this.logger.log(`ℹ️ [GitProcess] 页面 "${page.name}" 启动优化版Git分支创建和MR流程`);

      setImmediate(() => {
        this.executeGitBranchCreationAsync(
          playgroundId,
          page.name,
          gitUrl,
          originalGitUrl,
          targetBranch,
          taskId
        ).catch(error => {
          this.logger.error(`❌ [AsyncGitProcess] 页面 "${page.name}" 异步Git分支创建失败:`, error);
        });
      });

    } catch (error) {
      // 只有在AI对话阶段失败时才抛出错误
      this.logger.error(`❌ [ParallelProcess] ===== 页面 "${page.name}" 独立处理失败 =====`);
      this.logger.error(`❌ [ParallelProcess] 错误详情:`, {
        pageName: page.name,
        playgroundId,
        errorMessage: error.message,
        errorStack: error.stack
      });
      throw error;
    }
  }

  /**
   * 异步执行前置处理命令（安装依赖和启动应用），与AI对话并行执行
   * 实现正确的预览服务生命周期管理
   * 流程：启动PM2服务 → 验证运行 → 主动终止 → 注册供后续使用
   */
  private async executePreProcessingCommandsAsync(playgroundId: string, pageName: string): Promise<void> {
    const startTime = Date.now();
    let logCount = 0;

    // 生成唯一的任务标识符
    const taskId = `${pageName}-pre-${Date.now()}`;

    const addLog = async (message: string) => {
      try {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [AsyncPreProcess-${taskId}] ${message}`;
        logCount++;
        this.logger.log(logEntry);

        try {
          const fs = await import('fs');
          const fsPromises = fs.promises;
          const logsDir = playgroundRootPath(playgroundId, 'logs');
          if (!fs.existsSync(logsDir)) {
            await fsPromises.mkdir(logsDir, { recursive: true });
          }
          await fsPromises.appendFile(
            playgroundRootPath(playgroundId, 'logs', 'log.txt'),
            `${logEntry}\n`,
            'utf8'
          );
        } catch (writeError) {
          this.logger.error(`❌ [AsyncPreProcess] 实时写入日志失败:`, writeError);
        }
      } catch (error) {
        this.logger.error(`❌ [AsyncPreProcess] 日志记录失败:`, error);
      }
    };

    try {
      await addLog(`🚀 开始异步执行页面 "${pageName}" 的前置处理命令（与AI对话并行）`);
      await addLog(`🔍 [Debug] Playground ID: ${playgroundId}`);
      await addLog(`🔍 [Debug] Page Name: ${pageName}`);
      await addLog(`🔍 [Debug] Task ID: ${taskId}`);

      // 步骤1: 执行安装依赖命令
      try {
        await addLog(`🔧 开始执行安装依赖命令...`);
        await this.executePostGenerationCommandWithLogging(
          playgroundId,
          pageName,
          'install-dependencies',
          'pnpm',
          [
            'install',
            '--prod=false',
            '--frozen-lockfile',
            '--prefer-offline',
            '--silent'
          ],
          'src',
          300000, // 5分钟超时
          false,
          addLog
        );
        await addLog(`✅ 安装依赖命令执行完成`);
      } catch (error) {
        await addLog(`❌ 安装依赖命令执行失败: ${error.message}`);

        if (error.message.includes('ENOSPC') || error.message.includes('file watchers')) {
          await addLog(`⚠️ 检测到文件监视器限制错误，跳过预览服务管理`);
          return;
        }
        // 依赖安装失败但继续尝试预览服务管理
        await addLog(`⚠️ 依赖安装失败，但继续执行预览服务管理`);
      }

      // 步骤2: 执行预览服务完整生命周期管理
      await addLog(`🔧 开始执行预览服务生命周期管理...`);

      try {
        // 获取playground信息
        const playground = await this.prisma.playground.findUnique({
          where: { id: playgroundId }
        });

        if (!playground) {
          await addLog(`❌ 无法找到playground信息，跳过预览服务管理`);
          return;
        }

        // 🔧 更新：使用PM2PreviewClientService调用独立的PM2服务
        const { PM2PreviewClientService } = await import('../../pm2-preview-client/pm2-preview-client.service');

        // 创建PM2预览客户端服务实例
        const pm2PreviewService = new PM2PreviewClientService();

        // 🚀 阶段1：强制启动PM2预览服务（忽略环境变量）
        await addLog(`🚀 [生命周期1/4] 强制启动PM2自定义预览服务...`);

        const startResult = await pm2PreviewService.startCustomPreviewWithPM2(
          playgroundId,
          pageName,
          playground.user,
          'src'
        );

        if (startResult.processId) {
          await addLog(`✅ [生命周期1/4] PM2预览服务启动成功`);
          await addLog(`📋 进程ID: ${startResult.processId}`);
          if (startResult.url) {
            await addLog(`🔗 预览URL: ${startResult.url}`);
          }
          if (startResult.port) {
            await addLog(`🔌 端口: ${startResult.port}`);
          }

          // 🔍 阶段2：验证服务运行状态（等待5秒让服务完全启动）
          await addLog(`🔍 [生命周期2/4] 等待5秒后验证服务运行状态...`);
          await new Promise(resolve => setTimeout(resolve, 5000));

          try {
            const statusResult = await pm2PreviewService.getCustomPreviewStatusWithPM2(playgroundId);
            await addLog(`📊 [生命周期2/4] 预览服务状态: ${JSON.stringify(statusResult)}`);

            if (statusResult.length > 0 && statusResult[0].status === 'online') {
              await addLog(`✅ [生命周期2/4] 预览服务验证成功，确认正常运行`);
            } else {
              await addLog(`⚠️ [生命周期2/4] 预览服务状态异常，但继续执行后续步骤`);
            }
          } catch (statusError) {
            await addLog(`⚠️ [生命周期2/4] 无法获取预览服务状态: ${statusError.message}`);
          }

          // 🛑 阶段3：主动终止预览服务
          await addLog(`🛑 [生命周期3/4] 主动终止预览服务（为后续手动启动做准备）...`);

          try {
            const stopResult = await pm2PreviewService.stopCustomPreviewWithPM2(playgroundId, pageName);
            await addLog(`✅ [生命周期3/4] 预览服务终止完成: ${JSON.stringify(stopResult)}`);
          } catch (stopError) {
            await addLog(`⚠️ [生命周期3/4] 预览服务终止时出现警告: ${stopError.message}`);
          }

          // 📝 阶段4：注册服务配置供后续手动启动
          await addLog(`📝 [生命周期4/4] 确保预览服务配置已注册，供用户后续手动启动...`);

          try {
            const registerResult = await pm2PreviewService.registerCustomPreviewServiceOnly(
              playgroundId,
              pageName,
              playground.user,
              'src'
            );

            if (registerResult.registered) {
              await addLog(`✅ [生命周期4/4] 预览服务配置注册完成`);
              await addLog(`📋 服务名称: ${registerResult.serviceName}`);
              await addLog(`💡 用户可通过"重启预览服务"功能手动启动应用`);
            } else {
              await addLog(`⚠️ [生命周期4/4] 预览服务配置注册状态未知`);
            }
          } catch (registerError) {
            await addLog(`⚠️ [生命周期4/4] 预览服务配置注册时出现警告: ${registerError.message}`);
          }

          await addLog(`🎉 预览服务生命周期管理完成：启动→验证→终止→注册`);

        } else {
          await addLog(`❌ [生命周期1/4] PM2预览服务启动失败，尝试传统方式...`);

          // 回退到传统启动方式进行验证
          await this.executeTraditionalPreviewStartup(playgroundId, pageName, addLog);
        }

        // 清理资源 - 不需要断开连接，因为使用的是HTTP客户端

      } catch (error) {
        await addLog(`❌ 预览服务生命周期管理失败: ${error.message}`);
        await addLog(`❌ 错误堆栈: ${error.stack}`);
      }

      const endTime = Date.now();
      const processingTime = Math.round((endTime - startTime) / 1000);
      await addLog(`🎉 页面 "${pageName}" 异步前置处理完成，总耗时: ${processingTime}秒`);

    } catch (error) {
      const errorMessage = `❌ 页面 "${pageName}" 异步前置处理出现严重异常: ${error.message}`;
      await addLog(errorMessage);
      await addLog(`❌ 异常堆栈: ${error.stack}`);
      this.logger.error(errorMessage, error);
    } finally {
      try {
        await addLog(`📝 [Debug] 前置处理日志记录完成，共 ${logCount} 条日志已实时写入`);
      } catch (error) {
        this.logger.error(`❌ [AsyncPreProcess] 最终日志记录失败:`, error);
      }
    }
  }

  /**
   * 🔧 新增：传统预览服务启动方式（用于PM2失败时的回退）
   */
  private async executeTraditionalPreviewStartup(
    playgroundId: string,
    pageName: string,
    addLog: (message: string) => Promise<void>
  ): Promise<void> {
    try {
      await addLog(`🔄 使用传统方式验证预览服务功能...`);

      // 检查package.json中的可用脚本
      const packageJsonPath = playgroundRootPath(playgroundId, 'src', 'package.json');
      let availableScripts: string[] = [];

      try {
        const fs = await import('fs');
        const packageJsonContent = await fs.promises.readFile(packageJsonPath, 'utf8');
        const packageJson = JSON.parse(packageJsonContent);
        availableScripts = Object.keys(packageJson.scripts || {});

        await addLog(`📋 可用脚本: ${availableScripts.join(', ')}`);
      } catch (error) {
        await addLog(`❌ 无法读取package.json: ${error.message}`);
        return;
      }

      // 确定启动脚本优先级
      const startCommands = ['start', 'dev', 'serve', 'preview'];
      let selectedCommand = null;

      for (const cmd of startCommands) {
        if (availableScripts.includes(cmd)) {
          selectedCommand = cmd;
          break;
        }
      }

      if (!selectedCommand) {
        await addLog(`⚠️ 未找到合适的启动脚本，跳过传统预览验证`);
        return;
      }

      await addLog(`🎯 选择启动脚本进行验证: ${selectedCommand}`);
      await addLog(`ℹ️ 注意：传统方式仅用于验证项目可启动性，不会持续运行`);

      // 使用传统方式尝试启动（短时间验证后自动终止）
      await this.executePostGenerationCommandWithLogging(
        playgroundId,
        pageName,
        'verify-traditional-start',
        'pnpm',
        ['run', selectedCommand],
        'src',
        15000, // 15秒超时，仅用于验证
        true,  // 后台运行
        addLog
      );

      await addLog(`✅ 传统预览方式验证完成`);

    } catch (error) {
      await addLog(`⚠️ 传统预览方式验证失败: ${error.message}`);
    }
  }

  /**
   * 🔧 新增：检查文件监视器使用情况
   */
  private async checkFileWatcherUsage(playgroundId: string, addLog: (message: string) => Promise<void>): Promise<{ canStartApp: boolean; usage: string }> {
    try {
      await addLog(`🔍 检查系统文件监视器使用情况...`);

      // 尝试获取当前文件监视器使用数量
      const result = await executeCommand(
        playgroundId,
        `find /proc/*/fd -lname anon_inode:inotify 2>/dev/null | wc -l || echo "unknown"`,
        { dir: '.' }
      );

      if (result.exitCode === 0) {
        const currentWatchers = parseInt(result.stdout.trim()) || 0;
        await addLog(`📊 当前文件监视器使用数量: ${currentWatchers}`);

        // 获取系统限制
        const limitResult = await executeCommand(
          playgroundId,
          `cat /proc/sys/fs/inotify/max_user_watches 2>/dev/null || echo "8192"`,
          { dir: '.' }
        );

        const maxWatchers = parseInt(limitResult.stdout.trim()) || 8192;
        await addLog(`📊 系统文件监视器限制: ${maxWatchers}`);

        const usagePercent = Math.round((currentWatchers / maxWatchers) * 100);
        const usage = `${currentWatchers}/${maxWatchers} (${usagePercent}%)`;

        // 如果使用率超过70%，不启动应用
        const canStartApp = usagePercent < 70;

        await addLog(`📊 文件监视器使用率: ${usage}, 可启动应用: ${canStartApp ? '是' : '否'}`);

        return { canStartApp, usage };
      } else {
        await addLog(`⚠️ 无法获取文件监视器使用情况，默认允许启动`);
        return { canStartApp: true, usage: 'unknown' };
      }

    } catch (error) {
      await addLog(`❌ 检查文件监视器失败: ${error.message}`);
      return { canStartApp: true, usage: 'error' };
    }
  }

  /**
   * 异步执行Git分支创建，不阻塞主任务流程
   * 添加超时保护，防止Git操作长时间阻塞服务
   * 支持创建feature分支、发起MR到用户仓库和fork仓库
   */
  private async executeGitBranchCreationAsync(
    playgroundId: string,
    pageName: string,
    gitUrl: string,
    originalGitUrl?: string,
    targetBranch?: string,
    taskId?: string
  ): Promise<void> {
    const startTime = Date.now();
    let logCount = 0;

    // 生成唯一的日志标识符
    const logTaskId = `${pageName}-git-${Date.now()}`;

    const addLog = async (message: string) => {
      try {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] [AsyncGitProcess-${logTaskId}] ${message}`;
        logCount++;
        this.logger.log(logEntry);

        // 非阻塞式日志写入
        setImmediate(async () => {
          try {
            const fs = await import('fs');
            const fsPromises = fs.promises;
            const logsDir = playgroundRootPath(playgroundId, 'logs');
            if (!fs.existsSync(logsDir)) {
              await fsPromises.mkdir(logsDir, { recursive: true });
            }
            await fsPromises.appendFile(
              playgroundRootPath(playgroundId, 'logs', 'log.txt'),
              `${logEntry}\n`,
              'utf8'
            );
          } catch (writeError) {
            console.error(`[AsyncGitProcess] 实时写入日志失败:`, writeError);
          }
        });
      } catch (error) {
        console.error(`[AsyncGitProcess] 日志记录失败:`, error);
      }
    };

    try {
      await addLog(`🚀 开始异步执行页面 "${pageName}" 的优化Git分支创建流程`);
      await addLog(`📋 当前工作仓库: ${gitUrl}`);
      await addLog(`📋 原始用户仓库: ${originalGitUrl || '未提供'}`);
      await addLog(`📋 目标分支: ${targetBranch || 'feature/ai-coding-test'}`);

      // 为整个Git操作添加超时保护
      const gitOperationPromise = this.createFeatureBranchAndMRWithLogging(
        pageName,
        playgroundId,
        gitUrl,
        originalGitUrl,
        targetBranch,
        addLog,
        taskId || logTaskId
      );

      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Git操作超时（10分钟）'));
        }, 10 * 60 * 1000); // 10分钟超时，给MR操作足够时间
      });

      // 执行Git操作（带超时保护）
      try {
        await addLog(`🌟 开始创建feature分支和MR流程（超时保护已启用）...`);
        const result = await Promise.race([gitOperationPromise, timeoutPromise]);
        await addLog(`✅ Git分支创建和MR发起完成: ${JSON.stringify(result)}`);
      } catch (error) {
        if (error.message.includes('超时')) {
          await addLog(`⏰ Git操作超时: ${error.message}`);
          await addLog(`ℹ️ Git操作可能仍在后台继续执行，请检查仓库和MR状态`);
        } else {
          await addLog(`❌ Git操作失败: ${error.message}`);
        }
      }

      const endTime = Date.now();
      const processingTime = Math.round((endTime - startTime) / 1000);
      await addLog(`🎉 页面 "${pageName}" 异步Git处理完成，总耗时: ${processingTime}秒`);

    } catch (error) {
      await addLog(`❌ 页面 "${pageName}" 异步Git处理出现异常: ${error.message}`);
    } finally {
      setImmediate(async () => {
        try {
          await addLog(`📝 [Debug] Git处理日志记录完成，共 ${logCount} 条日志已实时写入`);
        } catch (error) {
          console.error(`[AsyncGitProcess] 最终日志记录失败:`, error);
        }
      });
    }
  }

  /**
   * 创建feature分支并处理MR逻辑 - 带日志版本
   */
  private async createFeatureBranchAndMRWithLogging(
    pageName: string,
    playgroundId: string,
    gitUrl: string,
    originalGitUrl?: string,
    targetBranch?: string,
    addLog?: (message: string) => Promise<void>,
    taskId?: string
  ): Promise<{ featureBranch: string; userMR?: any; forkMR?: any; intermediateBranch?: string }> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🌿 开始为页面 "${pageName}" 创建feature分支和MR流程`);

      const prioritizedTaskId = await this.getPrioritizedWorkflowId(taskId, pageName, log);

      // 步骤1: 获取当前版本号
      const currentVersion = await this.getCurrentVersionFromGit(playgroundId);
      const featureBranchName = `feature/${pageName}-${currentVersion}-${prioritizedTaskId}`; // 使用taskId确保唯一性

      await log(`📋 检测到playground版本: ${currentVersion}`);
      await log(`🎯 将创建feature分支: ${featureBranchName}`);

      // 步骤2: 切换到当前版本分支（确保代码是最新的）
      await this.checkoutToVersion(playgroundId, currentVersion);
      await log(`✅ 版本分支切换完成`);

      // 步骤3: 设置远程仓库
      await this.setupGitRemote(playgroundId, gitUrl);
      await log(`✅ Git远程仓库设置完成`);

      // 步骤4: 创建并切换到feature分支
      await this.createFeatureBranch(playgroundId, featureBranchName, currentVersion, gitUrl);
      await log(`✅ Feature分支创建完成: ${featureBranchName}`);

      // 步骤5: 提交代码到feature分支
      await this.commitAndPushCode(playgroundId, pageName, featureBranchName, gitUrl);
      await log(`✅ 代码提交和推送到feature分支完成`);

      // 步骤6: 发起MR（如果提供了原始仓库信息）
      const results: { featureBranch: string; userMR?: any; forkMR?: any; intermediateBranch?: string } = {
        featureBranch: featureBranchName
      };

      if (originalGitUrl && targetBranch) {
        await log(`🔄 开始发起MR流程...`);

        try {
          // 6.1: 检查并关闭已存在的用户仓库MR，然后发起新MR
          await log(`📤 检查并处理用户原始仓库MR: ${originalGitUrl}`);
          await this.closeExistingMRAndCreateNew(
            gitUrl,
            featureBranchName,
            originalGitUrl,
            targetBranch,
            {
              title: `feat: 生成页面 ${pageName} 的生产级代码`,
              description: `## 页面代码生成完成\n\n- 页面名称: ${pageName}\n- 基于版本: ${currentVersion}\n- 生成时间: ${new Date().toLocaleString()}\n- 任务ID: ${taskId || 'N/A'}\n\n此MR包含了基于设计稿自动生成的生产级代码，请审核后合并。`,
              autoMerge: false
            },
            log
          ).then(userMR => {
            results.userMR = userMR;
            log(`✅ 用户仓库MR处理完成: ${userMR?.web_url || '未返回URL'}`);
          });
        } catch (error) {
          await log(`⚠️ 用户仓库MR处理失败: ${error.message}`);
        }

        try {
          // 6.2: 优化的fork仓库合并流程 - 创建中间分支并合并
          await log(`📤 开始优化的fork仓库合并流程`);
          const intermediateBranchResult = await this.createIntermediateBranchAndMerge(
            playgroundId,
            gitUrl,
            featureBranchName,
            targetBranch,
            taskId || Date.now().toString(),
            {
              pageName,
              currentVersion,
              taskId
            },
            log
          );

          results.intermediateBranch = intermediateBranchResult.intermediateBranch;
          results.forkMR = intermediateBranchResult.mergeResult;

          await log(`✅ Fork仓库中间分支合并完成: ${intermediateBranchResult.intermediateBranch}`);
        } catch (error) {
          await log(`⚠️ Fork仓库中间分支合并失败: ${error.message}`);
        }

      } else {
        await log(`ℹ️ 未提供原始仓库信息，跳过MR创建`);
      }

      await log(`✅ 页面 "${pageName}" feature分支创建和MR处理完成`);
      return results;

    } catch (error) {
      await log(`❌ 页面 "${pageName}" feature分支创建失败: ${error.message}`);
      throw new Error(`创建feature分支失败: ${error.message}`);
    }
  }

  /**
   * 检查并关闭已存在的MR，然后创建新的MR
   */
  private async closeExistingMRAndCreateNew(
    sourceRepoUrl: string,
    sourceBranch: string,
    targetRepoUrl: string,
    targetBranch: string,
    options: {
      title: string;
      description: string;
      autoMerge: boolean;
    },
    addLog?: (message: string) => Promise<void>
  ): Promise<any> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔍 [MR-Check] 检查是否存在已有的MR`);
      await log(`📊 [MR-Check] 源仓库: ${sourceRepoUrl}`);
      await log(`📊 [MR-Check] 目标仓库: ${targetRepoUrl}`);
      await log(`📊 [MR-Check] 源分支: ${sourceBranch}`);
      await log(`📊 [MR-Check] 目标分支: ${targetBranch}`);

      // 解析源仓库和目标仓库信息
      const sourceProjectInfo = this.parseGitUrl(sourceRepoUrl);
      const targetProjectInfo = this.parseGitUrl(targetRepoUrl);

      if (!sourceProjectInfo || !targetProjectInfo) {
        throw new Error('无法解析仓库URL信息');
      }

      await log(`📊 [MR-Check] 源项目信息: ${JSON.stringify(sourceProjectInfo)}`);
      await log(`📊 [MR-Check] 目标项目信息: ${JSON.stringify(targetProjectInfo)}`);

      // 获取项目ID
      const sourceProjectId = await this.getProjectId(
        sourceProjectInfo.gitlabEnv,
        sourceProjectInfo.namespace,
        sourceProjectInfo.projectName
      );

      const targetProjectId = await this.getProjectId(
        targetProjectInfo.gitlabEnv,
        targetProjectInfo.namespace,
        targetProjectInfo.projectName
      );

      if (!sourceProjectId || !targetProjectId) {
        throw new Error('无法获取项目ID');
      }

      await log(`📊 [MR-Check] 源项目ID: ${sourceProjectId}`);
      await log(`📊 [MR-Check] 目标项目ID: ${targetProjectId}`);

      // 构造分支前缀用于匹配相同页面的所有分支
      // 从 feature/${pageName}-${currentVersion}-${taskId} 提取前缀 feature/${pageName}-
      const branchPrefix = this.extractBranchPrefix(sourceBranch);
      await log(`🎯 [MR-Check] 使用页面前缀进行匹配: ${branchPrefix} (将关闭所有相同页面的MR)`);

      // 查找已存在的MR（使用前缀匹配）
      // 注意：我们需要同时在源项目和目标项目中查找MR，以覆盖所有可能的情况
      await log(`🔍 [MR-Check] 开始查找重复的MR...`);

      let existingMRs = [];

      // 1. 在目标项目中查找所有指向目标分支的MR
      const targetProjectMRs = await this.findExistingMergeRequestsByPrefix(
        targetProjectInfo.gitlabEnv,
        targetProjectId,
        branchPrefix,
        targetProjectId,
        targetBranch,
        log
      );
      existingMRs.push(...targetProjectMRs);

      // 2. 如果源项目和目标项目不同，也在源项目中查找
      if (sourceProjectId !== targetProjectId) {
        await log(`🔍 [MR-Check] 源项目和目标项目不同，也在源项目中查找MR...`);
        const sourceProjectMRs = await this.findExistingMergeRequestsByPrefix(
          sourceProjectInfo.gitlabEnv,
          sourceProjectId,
          branchPrefix,
          targetProjectId,
          targetBranch,
          log
        );
        existingMRs.push(...sourceProjectMRs);
      }

      // 去重（以iid + project_id为键）
      const uniqueMRs = [];
      const seenMRs = new Set();
      for (const mr of existingMRs) {
        const key = `${mr.project_id || 'unknown'}-${mr.iid}`;
        if (!seenMRs.has(key)) {
          seenMRs.add(key);
          uniqueMRs.push(mr);
        }
      }
      existingMRs = uniqueMRs;

      // 关闭已存在的MR
      if (existingMRs && existingMRs.length > 0) {
        await log(`📋 [MR-Check] 找到 ${existingMRs.length} 个已存在的MR，准备关闭`);

        for (const mr of existingMRs) {
          try {
            // 确定应该在哪个项目中关闭MR
            const closeInProjectId = mr.project_id || targetProjectId;
            const closeInEnv = targetProjectInfo.gitlabEnv; // 假设都在同一环境

            await this.closeMergeRequest(
              closeInEnv,
              closeInProjectId,
              mr.iid,
              log
            );
            await log(`✅ [MR-Close] 已关闭MR: ${mr.web_url} (源分支: ${mr.source_branch})`);
          } catch (closeError) {
            await log(`⚠️ [MR-Close] 关闭MR失败: ${mr.web_url}, 错误: ${closeError.message}`);
          }
        }
      } else {
        await log(`ℹ️ [MR-Check] 未找到已存在的MR`);
      }

      // 创建新的MR
      await log(`🚀 [MR-Create] 创建新的MR`);
      const newMR = await this.createMergeRequest(
        sourceRepoUrl,
        sourceBranch,
        targetRepoUrl,
        targetBranch,
        options,
        log
      );

      return newMR;

    } catch (error) {
      await log(`❌ [MR-Check] 处理MR时出错: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从分支名称中提取前缀，用于匹配相同页面的分支
   * 例如：feature/我的账户-version-1-ZIAv8EWPe9mblY8u -> feature/我的账户-
   * 分支命名模式：feature/${pageName}-version-${number}-${taskId}
   */
  private extractBranchPrefix(branchName: string): string {
    // 分支名格式: feature/${pageName}-version-${number}-${taskId}
    // 我们需要提取: feature/${pageName}-

    if (!branchName.startsWith('feature/')) {
      return branchName;
    }

    // 去掉 "feature/" 前缀
    const withoutFeaturePrefix = branchName.substring(8); // "feature/".length = 8

    // 查找 "-version-" 的位置，这是pageName和后续部分的分界点
    const versionIndex = withoutFeaturePrefix.indexOf('-version-');

    if (versionIndex !== -1) {
      // 找到了"-version-"，提取页面名部分
      const pageName = withoutFeaturePrefix.substring(0, versionIndex);
      return `feature/${pageName}-`;
    }

    // 如果没有找到"-version-"模式，尝试其他可能的模式
    // 按 - 分割并从后往前查找
    const parts = withoutFeaturePrefix.split('-');

    if (parts.length < 2) {
      return branchName;
    }

    // 寻找版本号模式的位置
    let versionPartIndex = -1;
    for (let i = 0; i < parts.length; i++) {
      if (/^(version|v)$/i.test(parts[i]) && i + 1 < parts.length && /^\d+$/.test(parts[i + 1])) {
        versionPartIndex = i;
        break;
      }
    }

    if (versionPartIndex > 0) {
      // 找到了版本号，页面名是前面的部分
      const pageNameParts = parts.slice(0, versionPartIndex);
      const pageName = pageNameParts.join('-');
      return `feature/${pageName}-`;
    }

    // 如果都没找到，默认只取第一部分作为页面名
    return `feature/${parts[0]}-`;
  }

  /**
   * 根据分支前缀查找已存在的合并请求
   */
  private async findExistingMergeRequestsByPrefix(
    gitlabEnv: string,
    sourceProjectId: number,
    branchPrefix: string,
    targetProjectId: number,
    targetBranch: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<any[]> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      const token = GitlabUtil.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const searchMRUrl = `http://${gitDomain}/api/v4/projects/${sourceProjectId}/merge_requests`;

      await log(`🔍 [MR-Search] 搜索分支前缀匹配的MR: ${branchPrefix}*`);
      await log(`🔍 [MR-Search] API URL: ${searchMRUrl}`);
      await log(`🔍 [MR-Search] 查找参数: target_branch=${targetBranch}, target_project_id=${targetProjectId}, state=opened`);

      const axios = await import('axios');
      const response = await axios.default.get(searchMRUrl, {
        params: {
          target_branch: targetBranch,
          target_project_id: targetProjectId,
          state: 'opened' // 只查找开放状态的MR
        },
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 30000
      });

      const allMRs = response.data || [];

      await log(`🔍 [MR-Search] API返回总计 ${allMRs.length} 个MR`);

      if (allMRs.length > 0) {
        await log(`🔍 [MR-Search] 所有MR的源分支: ${allMRs.map(mr => mr.source_branch).join(', ')}`);
      }

      // 过滤出源分支以指定前缀开头的MR
      const matchingMRs = allMRs.filter(mr => {
        if (!mr.source_branch) return false;

        // 现在前缀以连字符结尾（如 feature/我的账户-），
        // 需要匹配所有以此前缀开头的分支
        const matches = mr.source_branch.startsWith(branchPrefix);

        return matches;
      });

      await log(`📋 [MR-Search] 总共找到 ${allMRs.length} 个MR，其中 ${matchingMRs.length} 个匹配前缀 "${branchPrefix}"`);

      if (matchingMRs.length > 0) {
        await log(`📝 [MR-Search] 匹配的分支: ${matchingMRs.map(mr => mr.source_branch).join(', ')}`);
      }

      return matchingMRs;

    } catch (error) {
      await log(`❌ [MR-Search] 搜索MR失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 查找已存在的合并请求
   */
  private async findExistingMergeRequests(
    gitlabEnv: string,
    sourceProjectId: number,
    sourceBranch: string,
    targetProjectId: number,
    targetBranch: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<any[]> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      const token = GitlabUtil.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const searchMRUrl = `http://${gitDomain}/api/v4/projects/${sourceProjectId}/merge_requests`;

      await log(`🔍 [MR-Search] 搜索已存在的MR: ${searchMRUrl}`);

      const axios = await import('axios');
      const response = await axios.default.get(searchMRUrl, {
        params: {
          source_branch: sourceBranch,
          target_branch: targetBranch,
          target_project_id: targetProjectId,
          state: 'opened' // 只查找开放状态的MR
        },
        headers: {
          'PRIVATE-TOKEN': token
        },
        timeout: 30000
      });

      const existingMRs = response.data || [];
      await log(`📋 [MR-Search] 找到 ${existingMRs.length} 个匹配的MR`);

      return existingMRs;

    } catch (error) {
      await log(`❌ [MR-Search] 搜索MR失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 关闭合并请求
   */
  private async closeMergeRequest(
    gitlabEnv: string,
    projectId: number,
    mergeRequestIid: number,
    addLog?: (message: string) => Promise<void>
  ): Promise<any> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔄 [MR-Close] 关闭MR: #${mergeRequestIid}`);

      // 需要先获取项目的namespace和projectName才能使用GitlabUtil.closeMergeRequest
      // 这里直接调用GitLab API，因为我们已有projectId
      const token = GitlabUtil.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const closeMRUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests/${mergeRequestIid}`;

      const axios = await import('axios');
      const response = await axios.default.put(closeMRUrl, {
        state_event: 'close'
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      return response.data;

    } catch (error) {
      await log(`❌ [MR-Close] 关闭MR失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建中间分支并合并feature分支
   */
  private async createIntermediateBranchAndMerge(
    playgroundId: string,
    gitUrl: string,
    featureBranch: string,
    targetBranch: string,
    taskId: string,
    metadata: {
      pageName: string;
      currentVersion: string;
      taskId?: string;
    },
    addLog?: (message: string) => Promise<void>
  ): Promise<{ intermediateBranch: string; mergeResult: any }> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      // 按优先级获取工作流ID用于中间分支命名
      const prioritizedTaskId = await this.getPrioritizedWorkflowId(taskId, metadata.pageName, log);

      // 创建中间分支名称，避免Git引用冲突（不能用 dev/xxx 格式，因为dev分支已存在）
      const intermediateBranch = `temp/${targetBranch}-${prioritizedTaskId}`;

      await log(`🌿 [Intermediate] 开始创建中间分支流程`);
      await log(`📋 [Intermediate] Feature分支: ${featureBranch}`);
      await log(`📋 [Intermediate] 目标分支: ${targetBranch}`);
      await log(`📋 [Intermediate] 中间分支: ${intermediateBranch}`);
      await log(`📋 [Intermediate] 原始任务ID: ${taskId}`);
      await log(`📋 [Intermediate] 优先级任务ID: ${prioritizedTaskId}`);

      // 步骤1: 切换到目标分支并拉取最新代码
      await log(`🔄 [Intermediate] 步骤1: 切换到目标分支 ${targetBranch}`);

      const checkoutTargetResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git checkout ${targetBranch}`,
        { dir: 'src' },
        15000
      );

      if (checkoutTargetResult.exitCode !== 0) {
        await log(`⚠️ [Intermediate] 切换到目标分支警告: ${checkoutTargetResult.stderr}`);
        // 尝试从远程检出
        const checkoutRemoteResult = await this.executeGitCommandWithTimeout(
          playgroundId,
          `git checkout -b ${targetBranch} origin/${targetBranch}`,
          { dir: 'src' },
          15000
        );
        if (checkoutRemoteResult.exitCode !== 0) {
          throw new Error(`无法切换到目标分支: ${targetBranch}`);
        }
      }

      // 拉取最新代码
      const pullResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git pull origin ${targetBranch} || true`,
        { dir: 'src' },
        30000
      );

      if (pullResult.exitCode !== 0) {
        await log(`⚠️ [Intermediate] 拉取目标分支最新代码警告: ${pullResult.stderr}`);
      }

      // 步骤2: 基于目标分支创建中间分支
      await log(`🔄 [Intermediate] 步骤2: 创建中间分支 ${intermediateBranch}`);

      const createIntermediateResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git checkout -b ${intermediateBranch}`,
        { dir: 'src' },
        15000
      );

      if (createIntermediateResult.exitCode !== 0) {
        throw new Error(`创建中间分支失败: ${createIntermediateResult.stderr}`);
      }

      await log(`✅ [Intermediate] 中间分支创建成功`);

      // 步骤3: 合并feature分支到中间分支
      await log(`🔄 [Intermediate] 步骤3: 合并feature分支到中间分支`);

      const mergeResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git merge ${featureBranch} --no-ff -m "Merge feature branch ${featureBranch} into intermediate branch ${intermediateBranch}"`,
        { dir: 'src' },
        30000
      );

      if (mergeResult.exitCode !== 0) {
        await log(`❌ [Intermediate] 合并失败，尝试解决冲突...`);

        // 如果合并失败，尝试重置并使用强制策略
        await this.executeGitCommandWithTimeout(
          playgroundId,
          `git merge --abort || true`,
          { dir: 'src' },
          15000
        );

        // 使用 theirs 策略强制合并
        const forceMergeResult = await this.executeGitCommandWithTimeout(
          playgroundId,
          `git merge ${featureBranch} -X theirs --no-ff -m "Force merge feature branch ${featureBranch} into intermediate branch ${intermediateBranch}"`,
          { dir: 'src' },
          30000
        );

        if (forceMergeResult.exitCode !== 0) {
          throw new Error(`强制合并也失败: ${forceMergeResult.stderr}`);
        }

        await log(`✅ [Intermediate] 强制合并成功`);
      } else {
        await log(`✅ [Intermediate] 常规合并成功`);
      }

      // 步骤4: 智能推送中间分支到远程（检查远端分支是否存在）
      await log(`🔄 [Intermediate] 步骤4: 推送中间分支到远程`);

      await this.smartPushBranch(playgroundId, intermediateBranch, log);

      await log(`✅ [Intermediate] 中间分支推送成功`);

      // 步骤5: 创建从feature分支到中间分支的MR记录（可选，用于记录）
      await log(`📝 [Intermediate] 步骤5: 记录合并操作完成`);

      const result = {
        intermediateBranch,
        mergeResult: {
          message: `Feature分支 ${featureBranch} 已成功合并到中间分支 ${intermediateBranch}`,
          source_branch: featureBranch,
          target_branch: intermediateBranch,
          merged_at: new Date().toISOString(),
          metadata
        }
      };

      await log(`🎉 [Intermediate] 中间分支合并流程完成`);

      return result;

    } catch (error) {
      await log(`❌ [Intermediate] 中间分支合并流程失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建feature分支
   */
  private async createFeatureBranch(playgroundId: string, featureBranchName: string, baseBranch: string, gitUrl?: string): Promise<void> {
    try {
      this.logger.log(`🔄 [GitFeature] 创建feature分支: ${featureBranchName} (基于: ${baseBranch})`);

      // 确保在正确的基础分支上
      const checkoutResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git checkout ${baseBranch}`,
        { dir: 'src' },
        15000
      );

      if (checkoutResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [GitFeature] 切换到基础分支警告: ${checkoutResult.stderr}`);
      }

      // 拉取最新更改
      const pullResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git pull origin ${baseBranch} || true`,
        { dir: 'src' },
        30000
      );

      if (pullResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [GitFeature] 拉取最新更改警告: ${pullResult.stderr}`);
      }

      // 创建新的feature分支
      const createBranchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git checkout -b ${featureBranchName}`,
        { dir: 'src' },
        15000
      );

      if (createBranchResult.exitCode !== 0) {
        throw new Error(`创建feature分支失败: ${createBranchResult.stderr}`);
      }

      // 确保Git用户配置正确
      if (gitUrl) {
        await this.configureGitUser(playgroundId, gitUrl, '[GitFeature]');
      }

      this.logger.log(`✅ [GitFeature] Feature分支创建成功: ${featureBranchName}`);

    } catch (error) {
      this.logger.error(`❌ [GitFeature] 创建feature分支失败:`, error);
      throw error;
    }
  }

  /**
   * 创建合并请求 (Merge Request)
   */
  private async createMergeRequest(
    sourceRepoUrl: string,
    sourceBranch: string,
    targetRepoUrl: string,
    targetBranch: string,
    options: {
      title: string;
      description: string;
      autoMerge: boolean;
    },
    addLog?: (message: string) => Promise<void>
  ): Promise<any> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔄 [MR] 开始创建合并请求`);
      await log(`📋 [MR] 源仓库: ${sourceRepoUrl}`);
      await log(`📋 [MR] 源分支: ${sourceBranch}`);
      await log(`📋 [MR] 目标仓库: ${targetRepoUrl}`);
      await log(`📋 [MR] 目标分支: ${targetBranch}`);
      await log(`📋 [MR] 自动合并: ${options.autoMerge ? '是' : '否'}`);

      // 解析源仓库信息
      const sourceProjectInfo = this.parseGitUrl(sourceRepoUrl);
      if (!sourceProjectInfo) {
        throw new Error(`无法解析源仓库URL: ${sourceRepoUrl}`);
      }

      // 解析目标仓库信息
      const targetProjectInfo = this.parseGitUrl(targetRepoUrl);
      if (!targetProjectInfo) {
        throw new Error(`无法解析目标仓库URL: ${targetRepoUrl}`);
      }

      await log(`📋 [MR] 源项目: ${sourceProjectInfo.namespace}/${sourceProjectInfo.projectName}`);
      await log(`📋 [MR] 目标项目: ${targetProjectInfo.namespace}/${targetProjectInfo.projectName}`);

      // 获取源项目ID（用于创建MR）
      const sourceProjectId = await this.getProjectId(
        sourceProjectInfo.gitlabEnv,
        sourceProjectInfo.namespace,
        sourceProjectInfo.projectName
      );

      if (!sourceProjectId) {
        throw new Error(`无法获取源项目ID: ${sourceProjectInfo.namespace}/${sourceProjectInfo.projectName}`);
      }

      // 获取目标项目ID
      const targetProjectId = await this.getProjectId(
        targetProjectInfo.gitlabEnv,
        targetProjectInfo.namespace,
        targetProjectInfo.projectName
      );

      if (!targetProjectId) {
        throw new Error(`无法获取目标项目ID: ${targetProjectInfo.namespace}/${targetProjectInfo.projectName}`);
      }

      await log(`📋 [MR] 源项目ID: ${sourceProjectId}`);
      await log(`📋 [MR] 目标项目ID: ${targetProjectId}`);

      // 创建MR
      const token = GitlabUtil.getToken(sourceProjectInfo.gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${sourceProjectInfo.gitlabEnv} 的token`);
      }

      const gitDomain = this.getGitDomainForEnv(sourceProjectInfo.gitlabEnv);
      const createMRUrl = `http://${gitDomain}/api/v4/projects/${sourceProjectId}/merge_requests`;

      await log(`🔄 [MR] 调用GitLab API创建MR: ${createMRUrl}`);

      const axios = await import('axios');
      const mrData = {
        source_branch: sourceBranch,
        target_branch: targetBranch,
        target_project_id: targetProjectId,
        title: options.title,
        description: options.description,
        remove_source_branch: false, // 不删除源分支
        merge_when_pipeline_succeeds: options.autoMerge, // 管道成功时自动合并
      };

      await log(`📋 [MR] MR数据: ${JSON.stringify(mrData, null, 2)}`);

      const response = await axios.default.post(createMRUrl, mrData, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      const mr = response.data;
      await log(`✅ [MR] MR创建成功: ${mr.web_url}`);
      await log(`📋 [MR] MR ID: ${mr.iid}`);
      await log(`📋 [MR] MR状态: ${mr.state}`);

      // 如果需要自动合并且是同一仓库的MR，立即执行合并
      if (options.autoMerge && sourceProjectId === targetProjectId) {
        await log(`🔄 [MR] 开始自动合并MR...`);
        try {
          const mergeResult = await this.acceptMergeRequest(
            sourceProjectInfo.gitlabEnv,
            sourceProjectId,
            mr.iid,
            log
          );
          await log(`✅ [MR] 自动合并完成: ${JSON.stringify(mergeResult)}`);
        } catch (mergeError) {
          await log(`⚠️ [MR] 自动合并失败: ${mergeError.message}`);
        }
      }

      return mr;

    } catch (error) {
      await log(`❌ [MR] 创建合并请求失败: ${error.message}`);
      if (error.response) {
        await log(`❌ [MR] 响应状态: ${error.response.status}`);
        await log(`❌ [MR] 响应数据: ${JSON.stringify(error.response.data)}`);
      }
      throw error;
    }
  }

  /**
   * 接受合并请求（自动合并）
   */
  private async acceptMergeRequest(
    gitlabEnv: string,
    projectId: number,
    mergeRequestIid: number,
    addLog?: (message: string) => Promise<void>
  ): Promise<any> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      const token = GitlabUtil.getToken(gitlabEnv as any);
      if (!token) {
        throw new Error(`未找到环境 ${gitlabEnv} 的token`);
      }

      const gitDomain = this.getGitDomainForEnv(gitlabEnv);
      const acceptMRUrl = `http://${gitDomain}/api/v4/projects/${projectId}/merge_requests/${mergeRequestIid}/merge`;

      await log(`🔄 [MR-Merge] 调用GitLab API合并MR: ${acceptMRUrl}`);

      const axios = await import('axios');
      const response = await axios.default.put(acceptMRUrl, {
        merge_commit_message: `Merge branch with AI generated code`,
        should_remove_source_branch: false,
        merge_when_pipeline_succeeds: false,
      }, {
        headers: {
          'PRIVATE-TOKEN': token,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      return response.data;

    } catch (error) {
      await log(`❌ [MR-Merge] 合并MR失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行后续命令并记录详细日志
   */
  private async executePostGenerationCommandWithLogging(
    playgroundId: string,
    pageName: string,
    commandName: string,
    command: string,
    args: string[],
    cwd: string,
    timeout: number = 0,
    runInBackground: boolean = false,
    addLog: (message: string) => Promise<void>
  ): Promise<void> {
    try {
      await addLog(`🔧 页面 "${pageName}" 开始执行命令: ${commandName}`);
      await addLog(`📋 命令详情: ${command} ${args.join(' ')} (工作目录: ${cwd})`);
      const fullCommand = `${command} ${args.join(' ')}`;

      if (runInBackground) {
        // 后台运行命令（如启动应用）- 使用新的实时日志方法
        await addLog(`🔄 在后台启动命令: ${fullCommand}`);

        await this.executeCommandWithRealTimeLogging(
          playgroundId,
          fullCommand,
          { dir: cwd },
          addLog,
          {
            isBackground: true,
            checkStartupTime: 30000, // 30秒检测启动状态
            startupSuccessPatterns: ['started', 'running', 'Server is listening', 'Local:', 'localhost:', 'development server'],
            commandName
          }
        );

      } else {
        // 前台运行命令（如安装依赖）
        await addLog(`⏳ 执行前台命令: ${fullCommand}`);

        const result = await executeCommand(
          playgroundId,
          fullCommand,
          {
            dir: cwd
          }
        );

        if (result.exitCode === 0) {
          await addLog(`✅ 命令执行成功: ${commandName}`);
          // 完整输出所有日志，分段记录避免单条过长
          await this.logFullOutput(result.stdout, '📄 执行输出', addLog);
          if (result.stderr) {
            await this.logFullOutput(result.stderr, '⚠️ 执行警告', addLog);
          }
        } else {
          await addLog(`❌ 命令执行失败 (退出码: ${result.exitCode})`);
          // 失败时也要完整输出日志以便调试
          if (result.stdout) {
            await this.logFullOutput(result.stdout, '📄 失败输出', addLog);
          }
          if (result.stderr) {
            await this.logFullOutput(result.stderr, '❌ 错误信息', addLog);
          }
          throw new Error(`命令执行失败: ${result.stderr || result.stdout}`);
        }
      }

    } catch (error) {
      await addLog(`❌ 页面 "${pageName}" 命令 "${commandName}" 执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 分段输出完整日志，避免单条日志过长
   */
  private async logFullOutput(output: string, prefix: string, addLog: (message: string) => Promise<void>): Promise<void> {
    if (!output || output.trim() === '') {
      await addLog(`${prefix}: (无输出)`);
      return;
    }

    const lines = output.split('\n');
    const chunkSize = 50; // 每50行作为一个分段

    await addLog(`${prefix}: (共 ${lines.length} 行)`);

    for (let i = 0; i < lines.length; i += chunkSize) {
      const chunk = lines.slice(i, i + chunkSize);
      const chunkIndex = Math.floor(i / chunkSize) + 1;
      const totalChunks = Math.ceil(lines.length / chunkSize);

      await addLog(`${prefix} [${chunkIndex}/${totalChunks}]:`);
      await addLog(chunk.join('\n'));
    }

    await addLog(`${prefix}: 输出完成`);
  }

  /**
   * 执行命令并提供实时日志输出（使用shelljs）
   */
  private async executeCommandWithRealTimeLogging(
    playgroundId: string,
    command: string,
    options: { dir?: string; stdin?: string },
    addLog: (message: string) => Promise<void>,
    config: {
      isBackground: boolean;
      checkStartupTime?: number;
      startupSuccessPatterns?: string[];
      commandName: string;
    }
  ): Promise<void> {
    const shell = await import('shelljs');
    const { playgroundRootPath } = await import('../../ai-coding/playground/ctx');

    let cwd: string;
    if (options.dir) {
      cwd = playgroundRootPath(playgroundId, options.dir);
    } else {
      cwd = playgroundRootPath(playgroundId);
    }

    await addLog(`🚀 使用shelljs启动命令: ${command}`);
    await addLog(`📁 工作目录: ${cwd}`);

    // 设置shelljs配置
    shell.config.silent = false;
    shell.config.fatal = false;

    // 设置环境变量
    const originalEnv = { ...process.env };
    Object.assign(process.env, {
      NO_COLOR: 'true',
      NODE_ENV: 'development',
      HOST: '0.0.0.0',
      VITE_HOST: '0.0.0.0',
      SERVER_HOST: '0.0.0.0',
      BIND_HOST: '0.0.0.0',
      REACT_APP_HOST: '0.0.0.0',
      HOSTNAME: '0.0.0.0',
      EXPRESS_HOST: '0.0.0.0',
      NUXT_HOST: '0.0.0.0',
      SVELTE_HOST: '0.0.0.0',
      BROWSER: 'none',
      DISABLE_OPENCOLLECTIVE: 'true',
      CHOKIDAR_USEPOLLING: 'false',
      CHOKIDAR_INTERVAL: '1000',
      WATCHPACK_POLLING: 'false',
      CI: 'true',
      FORCE_COLOR: '0',
      NO_UPDATE_NOTIFIER: 'true',
      VITE_CJS_IGNORE_WARNING: 'true',
      VITE_CJS_TRACE: 'false',
    });

    try {
      // 切换到工作目录
      const originalDir = shell.pwd().toString();
      shell.cd(cwd);

      if (config.isBackground) {
        // 后台命令：使用shelljs异步执行
        await addLog(`🔄 使用shelljs异步执行后台命令: ${command}`);

        return new Promise((resolve, reject) => {
          const child = shell.exec(command, { async: true }, (code, stdout, stderr) => {
            // 恢复环境变量和目录
            process.env = originalEnv;
            shell.cd(originalDir);

            addLog(`🏁 shelljs命令进程结束，退出码: ${code}`).then(() => {
              if (code !== 0) {
                addLog(`❌ 后台命令启动失败，退出码: ${code}`).finally(() => {
                  reject(new Error(`后台命令启动失败: ${config.commandName}, 退出码: ${code}`));
                });
              } else {
                addLog(`ℹ️ 后台命令正常退出: ${config.commandName}`).finally(() => {
                  resolve();
                });
              }
            }).catch(error => {
              reject(error);
            });
          });

          let hasStartupSuccess = false;
          let outputBuffer = '';

          // 监听stdout输出
          child.stdout.on('data', (data) => {
            const text = data.toString();
            outputBuffer += text;

            // 实时输出日志（按行分割避免过长）
            const lines = text.split('\n').filter(line => line.trim());
            for (const line of lines) {
              if (line.trim()) {
                addLog(`📤 [shelljs-stdout] ${line.trim()}`).catch(error => {
                  console.error('日志输出失败:', error);
                });
              }
            }

            // 检查启动成功模式
            if (config.startupSuccessPatterns && !hasStartupSuccess) {
              for (const pattern of config.startupSuccessPatterns) {
                if (text.includes(pattern)) {
                  hasStartupSuccess = true;
                  addLog(`✅ 检测到启动成功标志: "${pattern}"`).catch(error => {
                    console.error('日志输出失败:', error);
                  });
                  break;
                }
              }
            }
          });

          // 监听stderr输出
          child.stderr.on('data', (data) => {
            const text = data.toString();

            const lines = text.split('\n').filter(line => line.trim());
            for (const line of lines) {
              if (line.trim()) {
                addLog(`⚠️ [shelljs-stderr] ${line.trim()}`).catch(error => {
                  console.error('日志输出失败:', error);
                });
              }
            }
          });

          // 对于后台命令，设置启动检测超时
          if (config.checkStartupTime) {
            setTimeout(() => {
              if (hasStartupSuccess) {
                addLog(`✅ 后台命令 "${config.commandName}" 启动成功，进程将继续在后台运行`).finally(() => {
                  resolve();
                });
              } else {
                addLog(`⚠️ 后台命令 "${config.commandName}" 在 ${config.checkStartupTime}ms 内未检测到启动成功标志`).then(() => {
                  return addLog(`📄 最近输出: ${outputBuffer.slice(-500)}`);
                }).then(() => {
                  return addLog(`🔄 继续将命令视为后台运行状态`);
                }).finally(() => {
                  resolve();
                });
              }
            }, config.checkStartupTime);
          }
        });

      } else {
        // 前台命令：使用shelljs同步执行
        await addLog(`⏳ 使用shelljs同步执行前台命令: ${command}`);

        const result = shell.exec(command);

        // 恢复环境变量和目录
        process.env = originalEnv;
        shell.cd(originalDir);

        if (result.code === 0) {
          await addLog(`✅ shelljs命令执行成功: ${config.commandName}`);
          if (result.stdout) {
            await addLog(`📄 [shelljs-stdout] ${result.stdout}`);
          }
          if (result.stderr) {
            await addLog(`⚠️ [shelljs-stderr] ${result.stderr}`);
          }
        } else {
          await addLog(`❌ shelljs命令执行失败 (退出码: ${result.code})`);
          if (result.stdout) {
            await addLog(`📄 [shelljs-stdout] ${result.stdout}`);
          }
          if (result.stderr) {
            await addLog(`❌ [shelljs-stderr] ${result.stderr}`);
          }
          throw new Error(`shelljs命令执行失败: ${result.stderr || result.stdout}`);
        }
      }
    } catch (error) {
      // 恢复环境变量和目录
      process.env = originalEnv;
      await addLog(`❌ shelljs命令执行异常: ${error.message}`);
      throw error;
    }
  }



  /**
   * 从playground的git中获取当前版本号
   * @param playgroundId Playground ID
   * @returns 当前版本号（如 version-1）
   */
  private async getCurrentVersionFromGit(playgroundId: string): Promise<string> {
    try {
      // 获取当前分支名
      const { executeCommand } = await import('../../ai-coding/playground');
      const result = await this.executeGitCommandWithTimeout(playgroundId, 'git branch --show-current', { dir: 'src' }, 10000);

      if (result.exitCode !== 0) {
        throw new Error(`获取当前分支失败: ${result.stderr}`);
      }

      const currentBranch = result.stdout.trim();
      this.logger.log(`📋 [GitVersion] 当前分支: ${currentBranch}`);

      // 如果当前分支就是版本分支，直接返回
      if (currentBranch.startsWith('version-')) {
        return currentBranch;
      }

      // 否则获取最新的版本分支
      const branchResult = await this.executeGitCommandWithTimeout(playgroundId, 'git branch -r --format="%(refname:short)"', { dir: 'src' }, 15000);
      if (branchResult.exitCode !== 0) {
        // 如果没有远程分支，获取本地分支
        const localResult = await this.executeGitCommandWithTimeout(playgroundId, 'git branch --format="%(refname:short)"', { dir: 'src' }, 10000);
        if (localResult.exitCode === 0) {
          const branches = localResult.stdout.split('\n').filter(b => b.trim().startsWith('version-'));
          if (branches.length > 0) {
            const latestVersion = branches.sort().pop();
            this.logger.log(`📋 [GitVersion] 从本地分支获取版本: ${latestVersion}`);
            return latestVersion;
          }
        }

        // 如果没有找到版本分支，返回默认版本
        this.logger.log(`⚠️ [GitVersion] 未找到版本分支，使用默认版本: version-1`);
        return 'version-1';
      }

      // 筛选版本分支
      const versionBranches = branchResult.stdout
        .split('\n')
        .filter(branch => branch.trim().includes('version-'))
        .map(branch => branch.replace('origin/', '').trim())
        .filter(branch => branch.startsWith('version-'));

      if (versionBranches.length === 0) {
        this.logger.log(`⚠️ [GitVersion] 未找到版本分支，使用默认版本: version-1`);
        return 'version-1';
      }

      // 获取最新版本（按版本号排序）
      const latestVersion = versionBranches.sort((a, b) => {
        const aNum = parseInt(a.replace('version-', ''));
        const bNum = parseInt(b.replace('version-', ''));
        return bNum - aNum; // 降序排列
      })[0];

      this.logger.log(`📋 [GitVersion] 检测到最新版本: ${latestVersion}`);
      return latestVersion;

    } catch (error) {
      this.logger.error(`❌ [GitVersion] 获取版本失败:`, error);
      // 返回默认版本
      return 'version-1';
    }
  }

  /**
   * 切换到指定版本分支
   * @param playgroundId Playground ID
   * @param version 版本号（如 version-1）
   */
  private async checkoutToVersion(playgroundId: string, version: string): Promise<void> {
    try {
      const { executeCommand } = await import('../../ai-coding/playground');

      // 检查分支是否存在
      const checkResult = await this.executeGitCommandWithTimeout(playgroundId, `git show-ref --verify --quiet refs/heads/${version}`, { dir: 'src' }, 10000);

      if (checkResult.exitCode === 0) {
        // 在切换分支前处理logs/log.txt文件冲突
        this.logger.log(`🔧 [GitCheckout] 准备切换到分支 ${version}，先处理logs/log.txt冲突...`);

        // 检查logs/log.txt是否被修改
        const statusResult = await this.executeGitCommandWithTimeout(playgroundId, `git status --porcelain logs/log.txt`, { dir: 'src' }, 10000);

        if (statusResult.exitCode === 0 && statusResult.stdout.trim()) {
          this.logger.log(`⚠️ [GitCheckout] 检测到logs/log.txt有未提交的修改，将先stash保存`);

          // 将logs/log.txt的修改加入git并暂存
          const addResult = await this.executeGitCommandWithTimeout(playgroundId, `git add logs/log.txt`, { dir: 'src' }, 10000);
          if (addResult.exitCode === 0) {
            const stashResult = await this.executeGitCommandWithTimeout(playgroundId, `git stash push -m "Auto-stash logs before checkout to ${version}"`, { dir: 'src' }, 15000);
            if (stashResult.exitCode === 0) {
              this.logger.log(`✅ [GitCheckout] logs/log.txt修改已暂存，可以安全切换分支`);
            } else {
              this.logger.warn(`⚠️ [GitCheckout] 暂存失败，尝试强制重置logs/log.txt: ${stashResult.stderr}`);
              // 如果stash失败，直接重置logs/log.txt文件
              await this.executeGitCommandWithTimeout(playgroundId, `git checkout -- logs/log.txt`, { dir: 'src' }, 10000);
            }
          } else {
            this.logger.warn(`⚠️ [GitCheckout] 添加logs/log.txt到git失败，尝试直接重置`);
            // 如果add失败，直接重置文件
            await this.executeGitCommandWithTimeout(playgroundId, `git checkout -- logs/log.txt`, { dir: 'src' }, 10000);
          }
        } else {
          this.logger.log(`✅ [GitCheckout] logs/log.txt无冲突，可以直接切换分支`);
        }

        // 现在安全地切换分支
        const checkoutResult = await this.executeGitCommandWithTimeout(playgroundId, `git checkout ${version}`, { dir: 'src' }, 15000);
        if (checkoutResult.exitCode !== 0) {
          // 如果还是失败，尝试强制切换
          this.logger.warn(`⚠️ [GitCheckout] 常规切换失败，尝试强制切换: ${checkoutResult.stderr}`);
          const forceCheckoutResult = await this.executeGitCommandWithTimeout(playgroundId, `git checkout -f ${version}`, { dir: 'src' }, 15000);
          if (forceCheckoutResult.exitCode !== 0) {
            throw new Error(`强制切换到分支 ${version} 也失败: ${forceCheckoutResult.stderr}`);
          }
          this.logger.log(`✅ [GitCheckout] 强制切换到版本分支成功: ${version}`);
        } else {
          this.logger.log(`✅ [GitCheckout] 已切换到版本分支: ${version}`);
        }
      } else {
        this.logger.log(`ℹ️ [GitCheckout] 分支 ${version} 不存在，保持当前分支`);
      }

    } catch (error) {
      this.logger.error(`❌ [GitCheckout] 切换版本分支失败:`, error);
      // 非关键错误，不抛出异常
    }
  }

  /**
   * 执行带超时的Git命令
   * 缩短默认超时时间，防止长时间阻塞
   */
  private async executeGitCommandWithTimeout(
    playgroundId: string,
    command: string,
    options: any,
    timeoutMs: number = 15000 // 缩短为15秒超时
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Git命令超时（${timeoutMs / 1000}秒）: ${command}`));
      }, timeoutMs);

      executeCommand(playgroundId, command, options)
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * 在playground中设置Git远程仓库 - 使用token认证
   */
  private async setupGitRemote(playgroundId: string, gitUrl: string): Promise<void> {
    try {
      this.logger.log(`🔗 [GitRemote] 设置Git远程仓库: ${gitUrl}`);

      // 转换为带token的http URL
      const httpUrl = GitlabUtil.convertTohttpWithToken(gitUrl);
      if (!httpUrl) {
        throw new Error(`无法为Git URL生成带token的http地址: ${gitUrl}`);
      }

      // 先移除现有的origin（如果存在）
      await this.executeGitCommandWithTimeout(
        playgroundId,
        `git remote remove origin || true`,
        { dir: 'src' },
        10000 // 10秒超时
      );

      // 添加新的远程仓库
      const addRemoteResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git remote add origin ${httpUrl}`,
        { dir: 'src' },
        10000
      );

      if (addRemoteResult.exitCode !== 0) {
        throw new Error(`添加Git远程仓库失败: ${addRemoteResult.stderr}`);
      }

      // 配置Git用户信息，使用token对应的真实用户信息
      await this.configureGitUser(playgroundId, gitUrl, '[GitRemote]');

      this.logger.log(`✅ [GitRemote] 远程仓库设置完成，使用真实用户身份`);

    } catch (error) {
      this.logger.error(`❌ [GitRemote] 设置Git远程仓库失败:`, error);
      throw error;
    }
  }

  /**
   * 切换到新分支
   */
  private async switchToBranch(playgroundId: string, branchName: string): Promise<void> {
    try {
      this.logger.log(`🔄 [GitCheckout] 切换到分支: ${branchName}`);

      // 先拉取最新的远程分支信息（添加超时）
      const fetchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git fetch origin`,
        { dir: 'src' },
        30000 // 30秒超时
      );

      if (fetchResult.exitCode !== 0) {
        this.logger.warn(`⚠️ [GitCheckout] Git fetch警告: ${fetchResult.stderr}`);
      }

      // 创建并切换到新分支
      const checkoutResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git checkout -b ${branchName} origin/${branchName} || git checkout -b ${branchName}`,
        { dir: 'src' },
        15000 // 15秒超时
      );

      if (checkoutResult.exitCode !== 0) {
        throw new Error(`切换分支失败: ${checkoutResult.stderr}`);
      }

      this.logger.log(`✅ [GitCheckout] 成功切换到分支: ${branchName}`);

    } catch (error) {
      this.logger.error(`❌ [GitCheckout] 切换分支失败:`, error);
      throw error;
    }
  }

  /**
   * 提交并推送代码
   */
  private async commitAndPushCode(
    playgroundId: string,
    pageName: string,
    branchName: string,
    gitUrl?: string
  ): Promise<void> {
    try {
      this.logger.log(`📝 [GitCommit] ===== 开始Git提交流程 =====`);
      this.logger.log(`📝 [GitCommit] 输入参数:`, {
        playgroundId,
        pageName,
        branchName,
        gitUrl
      });

      // 获取工作目录信息
      const { playgroundRootPath } = await import('../../ai-coding/playground/ctx');
      const workingDir = playgroundRootPath(playgroundId, 'src');
      this.logger.log(`📁 [GitCommit] 工作目录: ${workingDir}`);

      // 检查工作目录是否存在
      const fs = await import('fs');
      if (!fs.existsSync(workingDir)) {
        throw new Error(`工作目录不存在: ${workingDir}`);
      }

      // 获取当前Git状态信息
      this.logger.log(`🔍 [GitCommit] 获取当前Git状态信息...`);

      // 检查当前分支
      const currentBranchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git branch --show-current`,
        { dir: 'src' },
        10000
      );
      this.logger.log(`🌿 [GitCommit] 当前分支: ${currentBranchResult.stdout.trim()}`);

      // 检查Git配置
      const gitConfigResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git config --list | grep -E "(user.name|user.email|remote.origin.url)"`,
        { dir: 'src' },
        10000
      );
      this.logger.log(`⚙️ [GitCommit] Git配置信息:`, gitConfigResult.stdout.trim());

      // 检查提交前的文件状态
      this.logger.log(`📊 [GitCommit] 检查提交前的文件状态...`);
      const preStatusResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git status --porcelain`,
        { dir: 'src' },
        15000
      );
      this.logger.log(`📊 [GitCommit] 提交前文件状态:`, {
        hasChanges: !!preStatusResult.stdout.trim(),
        changes: preStatusResult.stdout.trim() || '无更改'
      });

      // 步骤1: 添加所有更改的文件
      this.logger.log(`🔄 [GitCommit] ===== 步骤1: 执行 git add . =====`);
      this.logger.log(`📁 [GitCommit] 执行目录: ${workingDir}`);
      this.logger.log(`🎯 [GitCommit] 命令: git add .`);

      const addResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git add .`,
        { dir: 'src' },
        60000 // 延长到60秒超时
      );

      this.logger.log(`📋 [GitCommit] git add 执行结果:`, {
        exitCode: addResult.exitCode,
        stdout: addResult.stdout || '(空)',
        stderr: addResult.stderr || '(空)'
      });

      if (addResult.exitCode !== 0) {
        this.logger.error(`❌ [GitCommit] Git add 失败:`, {
          exitCode: addResult.exitCode,
          stderr: addResult.stderr,
          stdout: addResult.stdout,
          workingDir
        });
        throw new Error(`Git add失败: ${addResult.stderr}`);
      }

      this.logger.log(`✅ [GitCommit] Git add 执行成功`);

      // 步骤2: 检查add后的文件状态
      this.logger.log(`🔄 [GitCommit] ===== 步骤2: 检查add后的文件状态 =====`);
      const postAddStatusResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git status --porcelain`,
        { dir: 'src' },
        15000
      );

      this.logger.log(`📊 [GitCommit] add后文件状态:`, {
        hasChanges: !!postAddStatusResult.stdout.trim(),
        changes: postAddStatusResult.stdout.trim() || '无更改'
      });

      // 也检查git status的详细信息
      const detailedStatusResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git status`,
        { dir: 'src' },
        15000
      );
      this.logger.log(`📊 [GitCommit] 详细状态信息:`, detailedStatusResult.stdout.trim());

      if (!postAddStatusResult.stdout.trim()) {
        this.logger.warn(`⚠️ [GitCommit] 页面 "${pageName}" 没有检测到代码更改，跳过提交`);
        return;
      }

      this.logger.log(`📋 [GitCommit] 检测到以下文件更改:`, postAddStatusResult.stdout.trim());

      // 步骤3: 获取JIRA_ID（如果提供了gitUrl）
      this.logger.log(`🔄 [GitCommit] ===== 步骤3: 获取JIRA_ID =====`);
      let jiraId = 'WEBPRO-3'; // 设置默认值
      this.logger.log(`📋 [GitCommit] 默认JIRA_ID: ${jiraId}`);

      if (gitUrl) {
        this.logger.log(`🔗 [GitCommit] 从Git URL获取JIRA_ID: ${gitUrl}`);
        try {
          const projectInfo = this.parseGitUrl(gitUrl);
          this.logger.log(`📊 [GitCommit] 解析Git URL结果:`, projectInfo);

          if (projectInfo) {
            const envJiraId = this.getJiraIdForEnv(projectInfo.gitlabEnv);
            this.logger.log(`🏷️ [GitCommit] 环境 "${projectInfo.gitlabEnv}" 对应的JIRA_ID: ${envJiraId}`);
            jiraId = envJiraId;
          }
        } catch (error) {
          this.logger.warn(`⚠️ [GitCommit] 无法从Git URL获取JIRA_ID，使用默认值: ${jiraId}`, {
            error: error.message,
            gitUrl
          });
        }
      } else {
        this.logger.log(`ℹ️ [GitCommit] 未提供Git URL，使用默认JIRA_ID`);
      }

      // 步骤4: 构建commit message并转义特殊字符
      this.logger.log(`🔄 [GitCommit] ===== 步骤4: 构建commit message =====`);
      this.logger.log(`📝 [GitCommit] 原始页面名称: "${pageName}"`);

      const safePageName = pageName.replace(/['"\\]/g, ''); // 移除可能导致问题的特殊字符
      this.logger.log(`📝 [GitCommit] 安全页面名称: "${safePageName}"`);

      const commitMessage = `feat: #${jiraId}# ${safePageName}页面AI生成`;
      this.logger.log(`📝 [GitCommit] 最终commit消息: "${commitMessage}"`);
      this.logger.log(`📝 [GitCommit] commit消息长度: ${commitMessage.length} 字符`);

      // 步骤5: 执行git commit（跳过hooks）
      this.logger.log(`🔄 [GitCommit] ===== 步骤5: 执行 git commit =====`);
      this.logger.log(`📁 [GitCommit] 执行目录: ${workingDir}`);
      this.logger.log(`🎯 [GitCommit] 完整命令: git commit -n -m "${commitMessage}"`);
      this.logger.log(`⏰ [GitCommit] 超时设置: 90秒`);

      const commitResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git commit -n -m "${commitMessage}"`,
        { dir: 'src' },
        90000 // 延长到90秒超时，因为大文件提交可能需要更长时间
      );

      this.logger.log(`📋 [GitCommit] git commit 执行结果:`, {
        exitCode: commitResult.exitCode,
        stdout: commitResult.stdout || '(空)',
        stderr: commitResult.stderr || '(空)',
        commitMessage,
        workingDir
      });

      if (commitResult.exitCode !== 0) {
        this.logger.error(`❌ [GitCommit] Git commit 失败:`, {
          exitCode: commitResult.exitCode,
          stderr: commitResult.stderr,
          stdout: commitResult.stdout,
          commitMessage,
          workingDir,
          safePageName,
          jiraId
        });
        throw new Error(`Git commit失败: ${commitResult.stderr}`);
      }

      this.logger.log(`✅ [GitCommit] 代码提交成功: ${commitMessage}`);

      // 检查提交后的状态
      this.logger.log(`🔍 [GitCommit] 检查提交后的Git状态...`);
      const postCommitStatusResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git status`,
        { dir: 'src' },
        15000
      );
      this.logger.log(`📊 [GitCommit] 提交后状态:`, postCommitStatusResult.stdout.trim());

      // 获取最新的commit信息
      const lastCommitResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git log -1 --oneline`,
        { dir: 'src' },
        10000
      );
      this.logger.log(`📝 [GitCommit] 最新提交信息:`, lastCommitResult.stdout.trim());

      // 步骤6: 智能推送到远程仓库（检查远端分支是否存在）
      this.logger.log(`🔄 [GitCommit] ===== 步骤6: 推送到远程仓库 =====`);
      this.logger.log(`🌿 [GitCommit] 目标分支: ${branchName}`);
      this.logger.log(`📁 [GitCommit] 执行目录: ${workingDir}`);

      await this.smartPushBranch(playgroundId, branchName);

      this.logger.log(`🚀 [GitCommit] 代码推送成功到分支: ${branchName}`);
      this.logger.log(`📝 [GitCommit] ===== Git提交流程完成 =====`);

    } catch (error) {
      this.logger.error(`❌ [GitCommit] 提交代码失败:`, {
        playgroundId,
        pageName,
        branchName,
        gitUrl,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * 构建提示词消息
   */
  private async buildPromptMessage(
    page: { name: string; htmlContent: string; description?: string },
    uiPrompt: string,
    specContent: string,
    codeDir: string,
    projectId?: string
  ): Promise<string> {
    try {
      // 1. 构建任务目标提示词
      let targetPrompt = '';
      try {
        const targetContent = await this.promptManager.getTargetPromptContentAsync(TaskType.SPEC_TO_PROD_CODE, projectId);
        if (targetContent) {
          // 使用nunjucks渲染目标提示词
          const nunjucks = await import('nunjucks');
          targetPrompt = nunjucks.renderString(targetContent, {
            page: { name: page.name },
            codeDir: codeDir
          });
        }
      } catch (error) {
        this.logger.warn(`⚠️ 获取目标提示词失败，使用空内容:`, error);
      }

      // 2. 获取项目规范提示词模板
      const baseTemplate = await this.promptManager.getTemplate(TaskType.SPEC_TO_PROD_CODE);
      const context = {
        taskType: TaskType.SPEC_TO_PROD_CODE,
        items: [],
        options: {},
        metadata: {}
      };
      const baseContent = baseTemplate.buildMessage(context);

      // 3. 组装完整提示词
      const promptParts = [];
      
      // 添加任务目标（如果存在）
      if (targetPrompt.trim()) {
        promptParts.push(targetPrompt.trim());
        promptParts.push(''); // 空行分隔
      }

      // 添加UI组件规范（如果存在）
      if (uiPrompt && uiPrompt.trim()) {
        promptParts.push('组件规范内容如下：');
        promptParts.push('');
        promptParts.push(uiPrompt.trim());
        promptParts.push(''); // 空行分隔
      }

      // 添加基础技术规范
      promptParts.push('应用规范内容如下：');
      promptParts.push('');
      promptParts.push(baseContent.trim());
      promptParts.push(''); // 空行分隔

      // 添加页面相关信息
      if (page && page.htmlContent) {
        promptParts.push(''); // 空行分隔
        promptParts.push('页面HTML内容如下：');
        promptParts.push('');
        promptParts.push('```html');
        promptParts.push(page.htmlContent.trim());
        promptParts.push('```');

        if (page.description) {
          promptParts.push('');
          promptParts.push(`页面描述：${page.description}`);
        }
      }

      const fullPrompt = promptParts.join('\n');

      return fullPrompt;

    } catch (error) {
      this.logger.error(`❌ 构建提示词失败:`, error);
      throw new Error(`构建提示词失败: ${error.message}`);
    }
  }

  /**
   * 执行页面AI对话并提供页面级别的监控
   * 为每个页面提供独立的进度跟踪和日志
   */
  private async executePageAIDialogWithMonitoring(
    pageName: string,
    playgroundId: string,
    options: {
      enableAutoIteration?: boolean;
      enableStepByStep?: boolean;
      postChatCommands?: PostChatCommand[];
    },
    onProgress?: (progress: number) => Promise<void>
  ): Promise<void> {
    this.logger.log(`🚀 [PageAI-${pageName}] 开始AI对话监控 (playground: ${playgroundId})`);
    const pageStartTime = Date.now();

    try {
      // 获取AI对话开始前的消息数量
      const initialMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });

      this.logger.log(`📊 [PageAI-${pageName}] AI对话前消息数量: ${initialMessageCount}`);

      // 如果有进度回调，先报告开始进度
      if (onProgress) {
        await onProgress(0);
      }

      // 调用TaskWorkerService的AI对话方法，并模拟进度更新
      const aiDialogPromise = this.taskWorker.executeAIDialog(playgroundId, {
        enableAutoIteration: options.enableAutoIteration,
        enableStepByStep: options.enableStepByStep,
        postChatCommands: options.postChatCommands,
        maxWaitTime: 300000, // 5分钟超时
        pollInterval: 2000,  // 每2秒检查一次
      });

      // 启动一个进度模拟器（因为executeAIDialog不提供实时进度）
      if (onProgress) {
        let progressInterval: NodeJS.Timeout | null = null;
        let currentProgress = 0;

        // 启动进度模拟器
        progressInterval = setInterval(async () => {
          // 每次增加5%，最多到95%，确保进度更新更频繁
          currentProgress = Math.min(currentProgress + 5, 95);
          try {
            // 调用进度回调，确保进度更新传递到外层
            await onProgress(currentProgress);
            // 添加日志，确认进度更新被调用
            this.logger.log(`📈 [ProgressSimulator-${pageName}] 更新进度: ${currentProgress}%`);
          } catch (error) {
            this.logger.warn(`⚠️ [ProgressSimulator] 进度回调失败:`, error);
          }
        }, 3000); // 每3秒更新一次，更频繁地更新进度

        try {
          await aiDialogPromise;

          // AI对话完成，清理进度模拟器
          if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
          }

          // 确保进度达到100%
          await onProgress(100);
          // 添加日志，确认最终进度更新
          this.logger.log(`✅ [ProgressSimulator-${pageName}] 完成最终进度更新: 100%`);
        } catch (error) {
          // 出错时也要清理进度模拟器
          if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
          }
          throw error;
        }
      } else {
        await aiDialogPromise;
      }

      // 获取AI对话结束后的消息数量
      const finalMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });

      const newMessageCount = finalMessageCount - initialMessageCount;
      const pageProcessingTime = Math.round((Date.now() - pageStartTime) / 1000);

      this.logger.log(`✅ [PageAI-${pageName}] AI对话完成`);
      this.logger.log(`📊 [PageAI-${pageName}] 处理统计:`);
      this.logger.log(`   - 初始消息数: ${initialMessageCount}`);
      this.logger.log(`   - 最终消息数: ${finalMessageCount}`);
      this.logger.log(`   - 新增消息数: ${newMessageCount}`);
      this.logger.log(`   - 处理耗时: ${pageProcessingTime}秒`);

      // 检查是否真的有AI回复
      if (newMessageCount === 0) {
        this.logger.warn(`⚠️ [PageAI-${pageName}] 警告: 没有检测到新消息产生`);
      } else if (newMessageCount === 1) {
        this.logger.warn(`⚠️ [PageAI-${pageName}] 警告: 只检测到1条新消息，可能没有AI回复`);
      } else {
        this.logger.log(`✅ [PageAI-${pageName}] 确认检测到 ${newMessageCount} 条新消息，包含AI回复`);
      }

    } catch (error) {
      const pageProcessingTime = Math.round((Date.now() - pageStartTime) / 1000);
      this.logger.error(`❌ [PageAI-${pageName}] AI对话失败 (耗时: ${pageProcessingTime}秒)`);
      this.logger.error(`❌ [PageAI-${pageName}] 错误详情:`, error.message);
      throw error;
    }
  }

  /**
   * 验证AI是否生成了回复
   */
  private async verifyAIResponse(playgroundId: string, pageName: string): Promise<void> {
    try {
      this.logger.log(`🔍 验证页面 "${pageName}" 是否生成了新的AI回复...`);

      const finalMessages = await this.prisma.chatMessage.findMany({
        where: { playgroundId: playgroundId },
        orderBy: { created: 'desc' },
        take: 5, // 获取最新的5条消息
      });

      this.logger.log(`📋 页面 "${pageName}" 最新5条消息:`, finalMessages.map(msg => ({
        role: msg.role,
        created: msg.created,
        contentLength: msg.content.length,
        contentPreview: msg.content.substring(0, 100)
      })));

      // 检查是否有AI回复
      const hasAssistantMessage = finalMessages.some(msg => msg.role === 'ASSISTANT');

      if (!hasAssistantMessage) {
        this.logger.warn(`⚠️ 警告：页面 "${pageName}" 没有检测到AI回复消息`);
      } else {
        this.logger.log(`✅ 确认页面 "${pageName}" 生成了AI回复消息`);
      }

    } catch (error) {
      this.logger.error(`❌ 验证AI回复失败:`, error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 任务完成回调 - 处理任务完成后的清理和状态更新
   */
  async onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void> {
    this.logger.log(`🎯 开始处理spec-to-prod-code任务完成回调，任务ID: ${taskId}`);

    try {
      // 不使用事务，改为逐个处理避免超时
      // 获取任务项
      const taskItems = await this.prisma.backgroundTaskItem.findMany({
        where: { backgroundTaskId: taskId },
      });

      this.logger.log(`📋 找到 ${taskItems.length} 个页面任务项，处理结果 ${results.length} 个`);

      // 逐个处理每个任务项的结果，避免事务超时
      for (let i = 0; i < Math.min(taskItems.length, results.length); i++) {
        const taskItem = taskItems[i];
        const result = results[i];

        try {
          if (result.success) {
            const pageName = result.metadata?.pageName || result.result?.page || taskItem.itemId;
            this.logger.log(`✅ 页面任务项成功: ${pageName}`);

            // 更新任务项状态为完成
            await this.prisma.backgroundTaskItem.update({
              where: { id: taskItem.id },
              data: {
                status: 'completed',
                progress: 100,
                stage: '页面任务完成',
                result: result.result || {},
                updated: new Date()
              }
            });
          } else {
            const pageName = result.metadata?.pageName || taskItem.itemId;
            this.logger.log(`❌ 页面任务项失败: ${pageName}, 错误: ${result.error}`);

            // 更新任务项状态为失败
            await this.prisma.backgroundTaskItem.update({
              where: { id: taskItem.id },
              data: {
                status: 'failed',
                stage: '页面任务失败',
                error: result.error || '未知错误',
                result: result.result || {},
                updated: new Date()
              }
            });
          }
        } catch (error) {
          const pageName = result.metadata?.pageName || taskItem.itemId;
          this.logger.error(`❌ 处理页面任务项结果时出错: ${pageName}`, error);
          // 继续处理其他项目，不中断整个流程
        }
      }

      // 最后更新主任务状态
      try {
        await this.prisma.backgroundTask.update({
          where: { id: taskId },
          data: {
            status: 'completed',
            updated: new Date()
          }
        });
      } catch (error) {
        this.logger.error(`❌ 更新主任务状态时出错: ${taskId}`, error);
      }

      // 统计结果
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      this.logger.log(`📊 任务完成回调处理结果: 成功 ${successCount} 个，失败 ${failureCount} 个`);

    } catch (error) {
      this.logger.error(`❌ 处理任务完成回调时出错:`, error);
      // 不重新抛出错误，避免影响任务管理器
    }
  }

  /**
   * 检查远端分支是否存在
   */
  private async checkRemoteBranchExists(
    playgroundId: string,
    branchName: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<boolean> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔍 [BranchCheck] 检查远端分支是否存在: ${branchName}`);

      // 先更新远程分支信息
      const fetchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git fetch origin --prune`,
        { dir: 'src' },
        30000
      );

      if (fetchResult.exitCode !== 0) {
        await log(`⚠️ [BranchCheck] fetch远程分支警告: ${fetchResult.stderr}`);
      }

      // 检查远端分支是否存在
      const checkResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git ls-remote --heads origin ${branchName}`,
        { dir: 'src' },
        15000
      );

      const exists = checkResult.exitCode === 0 && checkResult.stdout.trim().length > 0;

      await log(`📋 [BranchCheck] 远端分支 ${branchName} ${exists ? '已存在' : '不存在'}`);

      return exists;

    } catch (error) {
      await log(`❌ [BranchCheck] 检查远端分支失败: ${error.message}`);
      return false; // 出错时假设分支不存在，允许推送
    }
  }

  /**
   * 智能推送分支到远程仓库
   * 如果远端分支已存在，则使用force push；如果不存在，则正常推送
   */
  private async smartPushBranch(
    playgroundId: string,
    branchName: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<void> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🚀 [SmartPush] ===== 开始智能推送分支 =====`);
      await log(`📋 [SmartPush] 输入参数: playgroundId=${playgroundId}, branchName=${branchName}`);

      // 获取工作目录信息
      const { playgroundRootPath } = await import('../../ai-coding/playground/ctx');
      const workingDir = playgroundRootPath(playgroundId, 'src');
      await log(`📁 [SmartPush] 工作目录: ${workingDir}`);

      // 检查当前分支状态
      const currentBranchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git branch --show-current`,
        { dir: 'src' },
        10000
      );
      await log(`🌿 [SmartPush] 当前分支: ${currentBranchResult.stdout.trim()}`);

      // 检查本地分支是否存在
      const localBranchResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git branch --list ${branchName}`,
        { dir: 'src' },
        10000
      );
      await log(`📊 [SmartPush] 本地分支检查: ${localBranchResult.stdout.trim() || '分支不存在'}`);

      // 检查远程仓库配置
      const remoteResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        `git remote -v`,
        { dir: 'src' },
        10000
      );
      await log(`🔗 [SmartPush] 远程仓库配置: ${remoteResult.stdout.trim()}`);

      // 检查远端分支是否存在
      await log(`🔍 [SmartPush] 检查远端分支是否存在...`);
      const remoteBranchExists = await this.checkRemoteBranchExists(playgroundId, branchName, log);
      await log(`📊 [SmartPush] 远端分支存在状态: ${remoteBranchExists}`);

      let pushCommand: string;
      let pushStrategy: string;

      if (remoteBranchExists) {
        // 远端分支已存在，使用force push更新
        pushCommand = `git push origin ${branchName} --force-with-lease`;
        pushStrategy = '强制推送（远端分支已存在）';
        await log(`🔄 [SmartPush] ${pushStrategy}: ${branchName}`);
      } else {
        // 远端分支不存在，正常推送
        pushCommand = `git push origin ${branchName}`;
        pushStrategy = '正常推送（创建新分支）';
        await log(`🔄 [SmartPush] ${pushStrategy}: ${branchName}`);
      }

      await log(`🎯 [SmartPush] 执行命令: ${pushCommand}`);
      await log(`📁 [SmartPush] 执行目录: ${workingDir}`);
      await log(`⏰ [SmartPush] 超时设置: 60秒`);

      // 执行推送
      const pushResult = await this.executeGitCommandWithTimeout(
        playgroundId,
        pushCommand,
        { dir: 'src' },
        60000 // 60秒超时
      );

      await log(`📋 [SmartPush] 推送执行结果: ${JSON.stringify({
        exitCode: pushResult.exitCode,
        stdout: pushResult.stdout || '(空)',
        stderr: pushResult.stderr || '(空)',
        command: pushCommand
      })}`);

      if (pushResult.exitCode !== 0) {
        // 如果强制推送失败，尝试普通推送
        if (remoteBranchExists && pushCommand.includes('--force-with-lease')) {
          await log(`⚠️ [SmartPush] 强制推送失败，尝试fallback推送: ${pushResult.stderr}`);

          const fallbackCommand = `git push origin ${branchName} --force`;
          await log(`🎯 [SmartPush] Fallback命令: ${fallbackCommand}`);

          const fallbackPushResult = await this.executeGitCommandWithTimeout(
            playgroundId,
            fallbackCommand,
            { dir: 'src' },
            60000
          );

          await log(`📋 [SmartPush] Fallback执行结果: ${JSON.stringify({
            exitCode: fallbackPushResult.exitCode,
            stdout: fallbackPushResult.stdout || '(空)',
            stderr: fallbackPushResult.stderr || '(空)',
            command: fallbackCommand
          })}`);

          if (fallbackPushResult.exitCode !== 0) {
            throw new Error(`推送分支失败（包括fallback）: ${fallbackPushResult.stderr}`);
          }

          await log(`✅ [SmartPush] Fallback推送成功: ${branchName}`);
        } else {
          throw new Error(`推送分支失败: ${pushResult.stderr}`);
        }
      } else {
        await log(`✅ [SmartPush] ${pushStrategy}成功: ${branchName}`);
      }

      await log(`🎉 [SmartPush] ===== 智能推送完成 =====`);

    } catch (error) {
      await log(`❌ [SmartPush] 智能推送失败: ${error.message}`);
      await log(`❌ [SmartPush] 错误详情: ${JSON.stringify({
        playgroundId,
        branchName,
        error: error.message,
        stack: error.stack
      })}`);
      throw error;
    }
  }

  /**
   * 按优先级获取工作流ID，用于中间分支命名
   * 优先级：多页面工作流ID > 单页面工作流ID > 当前任务ID
   */
  private async getPrioritizedWorkflowId(
    currentTaskId: string,
    pageName: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<string> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔍 [WorkflowPriority] 开始查找优先级工作流ID`);
      await log(`📋 [WorkflowPriority] 当前任务ID: ${currentTaskId}`);
      await log(`📋 [WorkflowPriority] 页面名称: ${pageName}`);

      // 第一优先级：查找多页面工作流ID
      const multiPageWorkflowId = await this.findMultiPageWorkflowId(currentTaskId, pageName, log);
      if (multiPageWorkflowId) {
        await log(`✅ [WorkflowPriority] 找到多页面工作流ID: ${multiPageWorkflowId}`);
        return multiPageWorkflowId;
      }

      // 第二优先级：查找单页面工作流ID
      const singlePageWorkflowId = await this.findSinglePageWorkflowId(currentTaskId, pageName, log);
      if (singlePageWorkflowId) {
        await log(`✅ [WorkflowPriority] 找到单页面工作流ID: ${singlePageWorkflowId}`);
        return singlePageWorkflowId;
      }

      // 第三优先级（fallback）：使用当前任务ID
      await log(`ℹ️ [WorkflowPriority] 未找到工作流ID，使用当前任务ID: ${currentTaskId}`);
      return currentTaskId;

    } catch (error) {
      await log(`❌ [WorkflowPriority] 查找工作流ID失败: ${error.message}`);
      // 出错时返回当前任务ID作为fallback
      return currentTaskId;
    }
  }

  /**
   * 查找多页面工作流ID
   */
  private async findMultiPageWorkflowId(
    currentTaskId: string,
    pageName: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<string | null> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔍 [MultiPageWorkflow] 查找多页面工作流ID`);

      // 策略1：通过当前任务查找关联的多页面工作流
      const currentTask = await this.prisma.backgroundTask.findUnique({
        where: { id: currentTaskId },
      });

      if (currentTask && currentTask.metadata) {
        const metadata = currentTask.metadata as any;

        // 🔧 优先检查任务级别的workflowId
        if (metadata.workflowId && metadata.workflowType === 'multi-page-code-generation-workflow') {
          await log(`✅ [MultiPageWorkflow] 通过任务metadata直接找到多页面工作流: ${metadata.workflowId}`);
          return metadata.workflowId;
        }

        // 🔧 检查item级别的workflowId
        if (metadata.items && Array.isArray(metadata.items)) {
          for (const item of metadata.items) {
            if (item.metadata && item.metadata.workflowId && item.metadata.workflowType === 'multi-page-code-generation-workflow') {
              // 验证这个workflowId确实存在且为多页面工作流
              const workflowTask = await this.prisma.backgroundTask.findUnique({
                where: { id: item.metadata.workflowId },
              });

              if (workflowTask && workflowTask.taskType === 'multi-page-code-generation-workflow') {
                await log(`✅ [MultiPageWorkflow] 通过item metadata找到多页面工作流: ${item.metadata.workflowId}`);
                return item.metadata.workflowId;
              }
            }
          }
        }

        // 🔧 向后兼容：检查通用的workflowId
        if (metadata.workflowId) {
          const workflowTask = await this.prisma.backgroundTask.findUnique({
            where: { id: metadata.workflowId },
          });

          if (workflowTask && workflowTask.taskType === 'multi-page-code-generation-workflow') {
            await log(`✅ [MultiPageWorkflow] 通过通用workflowId找到多页面工作流: ${metadata.workflowId}`);
            return metadata.workflowId;
          }
        }
      }

      // 策略2：通过时间范围和页面名称查找相关的多页面工作流
      const timeRange = new Date(Date.now() - 60 * 60 * 1000); // 最近1小时
      const multiPageWorkflows = await this.prisma.backgroundTask.findMany({
        where: {
          taskType: 'multi-page-code-generation-workflow',
          created: {
            gte: timeRange,
          },
        },
        orderBy: { created: 'desc' },
        take: 10, // 限制查询数量
      });

      await log(`📋 [MultiPageWorkflow] 找到 ${multiPageWorkflows.length} 个最近的多页面工作流`);

      // 检查每个多页面工作流是否包含当前页面
      for (const workflow of multiPageWorkflows) {
        if (workflow.metadata) {
          const metadata = workflow.metadata as any;
          const workflowStatus = metadata.workflowStatus;

          if (workflowStatus && workflowStatus.pageWorkflows) {
            // 检查是否包含当前页面
            const hasCurrentPage = workflowStatus.pageWorkflows.some((pw: any) =>
              pw.pageName === pageName || pw.pageId === pageName
            );

            if (hasCurrentPage) {
              await log(`✅ [MultiPageWorkflow] 通过页面匹配找到多页面工作流: ${workflow.id}`);
              return workflow.id;
            }
          }
        }
      }

      await log(`ℹ️ [MultiPageWorkflow] 未找到相关的多页面工作流`);
      return null;

    } catch (error) {
      await log(`❌ [MultiPageWorkflow] 查找多页面工作流失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 查找单页面工作流ID
   */
  private async findSinglePageWorkflowId(
    currentTaskId: string,
    pageName: string,
    addLog?: (message: string) => Promise<void>
  ): Promise<string | null> {
    const log = addLog || (async (msg) => this.logger.log(msg));

    try {
      await log(`🔍 [SinglePageWorkflow] 查找单页面工作流ID`);

      // 策略1：通过当前任务查找关联的单页面工作流
      const currentTask = await this.prisma.backgroundTask.findUnique({
        where: { id: currentTaskId },
      });

      if (currentTask && currentTask.metadata) {
        const metadata = currentTask.metadata as any;

        // 🔧 优先检查任务级别的workflowId
        if (metadata.workflowId && metadata.workflowType === 'page-code-generation-workflow') {
          await log(`✅ [SinglePageWorkflow] 通过任务metadata直接找到单页面工作流: ${metadata.workflowId}`);
          return metadata.workflowId;
        }

        // 🔧 检查item级别的workflowId
        if (metadata.items && Array.isArray(metadata.items)) {
          for (const item of metadata.items) {
            if (item.metadata && item.metadata.workflowId && item.metadata.workflowType === 'page-code-generation-workflow') {
              // 验证这个workflowId确实存在且为单页面工作流
              const workflowTask = await this.prisma.backgroundTask.findUnique({
                where: { id: item.metadata.workflowId },
              });

              if (workflowTask && workflowTask.taskType === 'page-code-generation-workflow') {
                await log(`✅ [SinglePageWorkflow] 通过item metadata找到单页面工作流: ${item.metadata.workflowId}`);
                return item.metadata.workflowId;
              }
            }
          }
        }

        // 🔧 向后兼容：检查通用的workflowId
        if (metadata.workflowId) {
          const workflowTask = await this.prisma.backgroundTask.findUnique({
            where: { id: metadata.workflowId },
          });

          if (workflowTask && workflowTask.taskType === 'page-code-generation-workflow') {
            await log(`✅ [SinglePageWorkflow] 通过通用workflowId找到单页面工作流: ${metadata.workflowId}`);
            return metadata.workflowId;
          }
        }
      }

      // 策略2：通过时间范围和页面名称查找相关的单页面工作流
      const timeRange = new Date(Date.now() - 60 * 60 * 1000); // 最近1小时
      const singlePageWorkflows = await this.prisma.backgroundTask.findMany({
        where: {
          taskType: 'page-code-generation-workflow',
          created: {
            gte: timeRange,
          },
        },
        orderBy: { created: 'desc' },
        take: 20, // 限制查询数量
      });

      await log(`📋 [SinglePageWorkflow] 找到 ${singlePageWorkflows.length} 个最近的单页面工作流`);

      // 检查每个单页面工作流是否匹配当前页面
      for (const workflow of singlePageWorkflows) {
        if (workflow.metadata) {
          const metadata = workflow.metadata as any;

          // 检查页面名称匹配
          if (metadata.pageName === pageName ||
            (metadata.workflowStatus &&
              metadata.workflowStatus.nodes &&
              metadata.workflowStatus.nodes.some((node: any) =>
                node.subTasks &&
                node.subTasks.some((subTask: any) =>
                  subTask.metadata &&
                  subTask.metadata.pageName === pageName
                )
              )
            )
          ) {
            await log(`✅ [SinglePageWorkflow] 通过页面匹配找到单页面工作流: ${workflow.id}`);
            return workflow.id;
          }
        }
      }

      await log(`ℹ️ [SinglePageWorkflow] 未找到相关的单页面工作流`);
      return null;

    } catch (error) {
      await log(`❌ [SinglePageWorkflow] 查找单页面工作流失败: ${error.message}`);
      return null;
    }
  }

}