import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig, BackgroundTaskResult, TaskExecutionContext, BackgroundTaskProgress } from '../../background-task';
import { PromptTemplateManager } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class CustomTaskHandler extends BaseTaskHandler {
  private readonly logger = new Logger(CustomTaskHandler.name);
  constructor(
    private readonly promptManager: PromptTemplateManager,
    private readonly taskWorker?: any,  // 注入TaskWorkerService
    private readonly createPlaygroundMethod?: (item: BackgroundTaskItem, config: BackgroundTaskConfig) => Promise<string>
  ) {
    super();
  }

  getSupportedTaskType(): string {
    return 'custom';
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('自定义任务需要至少一个项目');
    }

    // 检查是否为空会话创建模式
    const isEmptySession = config.metadata?.createEmptySession === true;

    this.logger.log(`🔍 验证任务配置: isEmptySession=${isEmptySession}, createEmptySession=${config.metadata?.createEmptySession}, sessionName="${config.metadata?.sessionName}"`);

    if (!isEmptySession) {
      // 非空会话模式下，需要验证customPrompt
      if (!config.metadata?.customPrompt) {
        throw new Error('自定义任务需要提供 customPrompt');
      }

      const customPrompt = config.metadata.customPrompt.trim();
      if (customPrompt.length === 0) {
        throw new Error('自定义提示词不能为空');
      }

      if (customPrompt.length > 50000) {
        throw new Error('自定义提示词长度不能超过50000字符');
      }
    } else {
      // 空会话模式下，验证会话名称
      if (!config.metadata?.sessionName) {
        this.logger.error(`❌ 空会话验证失败: sessionName="${config.metadata?.sessionName}"`);
        throw new Error('创建空会话时需要提供会话名称');
      }

      const sessionName = config.metadata.sessionName.trim();
      if (sessionName.length === 0) {
        this.logger.error(`❌ 空会话验证失败: sessionName为空字符串`);
        throw new Error('会话名称不能为空');
      }

      if (sessionName.length > 100) {
        this.logger.error(`❌ 空会话验证失败: sessionName长度超过限制(${sessionName.length})`);
        throw new Error('会话名称长度不能超过100字符');
      }

      this.logger.log(`✅ 空会话验证通过: sessionName="${sessionName}"`);
    }
  }

  /**
   * 自定义执行逻辑：区分空会话和正常会话
   */
  async executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: TaskExecutionContext,
    updateProgress: (progress: BackgroundTaskProgress) => Promise<void>
  ): Promise<BackgroundTaskResult> {
    this.logger.log(`🎯 [CustomTaskHandler] 开始自定义执行: ${item.name} (${item.id})`);

    const isEmptySession = config.metadata?.createEmptySession === true;

    try {
      // 更新进度：创建工作区
      await updateProgress({
        stage: isEmptySession ? '创建空会话' : '创建工作区',
        progress: 20,
      });

      // 创建playground（这里需要访问父类的createPlayground方法）
      // 由于无法直接访问父类的私有方法，我们需要重新实现
      const playgroundId = await this.createCustomPlayground(item, config);

      // 更新任务状态
      await this.updateItemStatus(item, config, playgroundId, 'processing');

      if (isEmptySession) {
        // 空会话模式：只创建playground，不执行AI对话
        this.logger.log(`📝 [CustomTaskHandler] 空会话创建完成，跳过AI对话执行`);

        await updateProgress({
          stage: '空会话创建完成',
          progress: 100,
          metadata: { playgroundId },
        });

        // 更新最终状态
        await this.updateItemStatus(item, config, playgroundId, 'completed');

        return {
          success: true,
          result: { message: '空会话创建成功' },
          metadata: {
            playgroundId,
            isEmptySession: true,
            sessionName: config.metadata?.sessionName
          },
        };
      } else {
        // 正常模式：执行完整的AI对话流程
        this.logger.log(`🤖 [CustomTaskHandler] 开始执行AI对话流程`);

        await updateProgress({
          stage: '执行任务',
          progress: 50,
          metadata: { playgroundId },
        });

        // 执行AI对话（需要注入TaskWorkerService或使用其他方式）
        if (this.taskWorker) {
          await this.taskWorker.executeAIDialog(playgroundId, {
            projectId: config.metadata?.projectId,
            enableAutoIteration: config.metadata?.enableAutoIteration || false,
            enableStepByStep: config.metadata?.enableStepByStep || false,
          });
        }

        await updateProgress({
          stage: '任务完成',
          progress: 100,
          metadata: { playgroundId },
        });

        // 更新最终状态
        await this.updateItemStatus(item, config, playgroundId, 'completed');

        return {
          success: true,
          result: { message: '任务执行成功' },
          metadata: {
            playgroundId,
            isEmptySession: false
          },
        };
      }
    } catch (error) {
      this.logger.error(`❌ [CustomTaskHandler] 任务执行失败:`, error);

      // 更新失败状态
      await this.updateItemStatus(item, config, null, 'failed');

      return {
        success: false,
        error: `任务执行失败: ${error.message}`,
        metadata: { isEmptySession },
      };
    }
  }

  /**
   * 自定义创建playground的方法
   */
  private async createCustomPlayground(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<string> {
    if (!this.taskWorker) {
      throw new Error('TaskWorkerService未注入，无法创建playground');
    }

    const options = config.metadata || {};


    // 解决标题重复问题：如果 taskName 和 item.name 相同，则只使用 taskName
    // 如果启用了智能主题生成，优先使用智能生成的 sessionName
    const useSmartTheme = config.metadata?.smartTheme === 'true' || config.metadata?.smartTheme === true;
    let desc: string;


    // 生成安全的playground名称
    const safeName = `${!useSmartTheme ? config.taskType + '-' : ''}${item.name}`
      .replace(/[^a-zA-Z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .toLowerCase();

    if (useSmartTheme && config.metadata?.sessionName) {
      // 启用智能主题：使用 sessionName（这是经过智能主题生成的）
      desc = config.metadata?.intelligentTaskTopic ?? config.metadata?.sessionName;
    } else {
      // 正常模式：使用原有逻辑
      desc = config.taskName === item.name
        ? config.taskName
        : `${config.taskName} - ${item.name}`;
    }

    // 创建playground
    const playgroundId = await this.taskWorker.createPlayground({
      name: safeName,
      desc: desc,
      user: config.user,
      model: options.model || 'openrouter::moonshotai/kimi-k2',
      projectId: options.projectId,
      isPublic: true,
      enableAutoIteration: options.enableAutoIteration || false,
      enableStepByStep: options.enableStepByStep || false,
      type: options.type,
      tags: options.tags, // 任务标签
      ...(typeof options.enableCustomPreview !== 'undefined'
        ? { enableCustomPreview: options.enableCustomPreview === true || options.enableCustomPreview === 'true' }
        : {}),
    });

    // 准备初始消息和附件
    const { initialMessage, files } = await this.prepareTaskData(item, config);

    // 准备文件附件
    const attachments = files.length > 0 ? this.taskWorker.prepareFileAttachments(
      files.map(file => ({
        name: file.name,
        content: file.url.split(',')[1], // 直接使用Base64字符串
        contentType: file.contentType
      }))
    ) : undefined;

    // 创建用户消息（仅在有消息内容时）
    if (initialMessage && initialMessage.trim().length > 0) {
      await this.taskWorker.createUserMessage(playgroundId, initialMessage, attachments);
      this.logger.log(`📤 [CustomTaskHandler] 初始消息已发送到playground: ${playgroundId}`);
    } else {
      this.logger.log(`📝 [CustomTaskHandler] 跳过创建用户消息（空会话模式）`);
    }

    return playgroundId;
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];

    // 检查是否为空会话创建模式
    const isEmptySession = config.metadata?.createEmptySession === true;

    // 处理附件文件
    if (item.metadata?.files && Array.isArray(item.metadata.files)) {
      for (const file of item.metadata.files) {
        if (file.name && file.content) {
          files.push(this.createFileFromContent(
            file.content,
            file.name,
            file.contentType || 'text/plain'
          ));
        }
      }
    }

    // 处理代码内容
    if (item.metadata?.codeContent) {
      files.push(this.createFileFromContent(
        item.metadata.codeContent,
        item.metadata.fileName || 'code.txt',
        item.metadata.contentType || 'text/plain'
      ));
    }

    // 处理图片内容（Base64格式）
    if (item.metadata?.imageContent) {
      const imageType = item.metadata.imageType || 'image/png';
      const imageName = item.metadata.imageName || 'image.png';

      // 检查是否已经是Data URL格式
      if (item.metadata.imageContent.startsWith('data:')) {
        files.push({
          name: imageName,
          contentType: imageType,
          url: item.metadata.imageContent,
        });
      } else {
        // 假设是纯Base64字符串
        files.push({
          name: imageName,
          contentType: imageType,
          url: `data:${imageType};base64,${item.metadata.imageContent}`,
        });
      }
    }

    // 处理多个图片
    if (item.metadata?.images && Array.isArray(item.metadata.images)) {
      for (let i = 0; i < item.metadata.images.length; i++) {
        const image = item.metadata.images[i];
        const imageType = image.type || 'image/png';
        const imageName = image.name || `image-${i + 1}.png`;

        if (image.content.startsWith('data:')) {
          files.push({
            name: imageName,
            contentType: imageType,
            url: image.content,
          });
        } else {
          files.push({
            name: imageName,
            contentType: imageType,
            url: `data:${imageType};base64,${image.content}`,
          });
        }
      }
    }

    let initialMessage: string;

    if (isEmptySession) {
      // 空会话模式：不发送初始消息，只创建playground
      initialMessage = ''; // 空字符串表示不发送消息
      this.logger.log(`📝 创建空会话: ${config.metadata?.sessionName}`);
    } else {
      // 正常模式：使用自定义提示词作为初始消息
      const customPrompt = config.metadata?.customPrompt;

      // 可以在自定义提示词中嵌入变量
      initialMessage = customPrompt;

      // 替换常用变量
      if (initialMessage.includes('{{ITEM_NAME}}')) {
        initialMessage = initialMessage.replace(/\{\{ITEM_NAME\}\}/g, item.name);
      }

      if (initialMessage.includes('{{ITEM_ID}}')) {
        initialMessage = initialMessage.replace(/\{\{ITEM_ID\}\}/g, item.id);
      }

      if (initialMessage.includes('{{FILE_COUNT}}')) {
        initialMessage = initialMessage.replace(/\{\{FILE_COUNT\}\}/g, files.length.toString());
      }

      // 处理项目元数据变量
      if (item.metadata) {
        for (const [key, value] of Object.entries(item.metadata)) {
          const placeholder = `{{${key.toUpperCase()}}}`;
          if (initialMessage.includes(placeholder)) {
            const stringValue = typeof value === 'object'
              ? JSON.stringify(value, null, 2)
              : String(value);
            initialMessage = initialMessage.replace(new RegExp(placeholder, 'g'), stringValue);
          }
        }
      }
    }

    return { initialMessage, files };
  }

  async updateItemStatus(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    // 记录自定义任务状态
    this.logger.log(`ℹ️ 自定义任务状态更新: ${item.name} -> ${status}`);

    if (playgroundId && status === 'completed') {
      const isEmptySession = config.metadata?.createEmptySession === true;
      this.logger.log(`🎉 自定义任务完成${isEmptySession ? '（空会话模式）' : ''}，结果保存在 playground: ${playgroundId}`);
    }
  }
} 