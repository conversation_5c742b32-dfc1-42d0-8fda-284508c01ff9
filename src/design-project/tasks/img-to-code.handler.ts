import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig } from '../../background-task';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';
export class ImgToCodeHandler extends BaseTaskHandler {
  private readonly logger = new Logger(ImgToCodeHandler.name);
  constructor(private readonly promptManager: PromptTemplateManager) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.IMG_TO_CODE;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('图片转代码任务需要至少一个图片项目');
    }

    for (const item of items) {
      this.logger.log(`🔍 验证项目 ${item.name}:`, {
        hasImageContent: !!item.metadata?.imageContent,
        hasImageUrl: !!item.metadata?.imageUrl,
        imageContentLength: item.metadata?.imageContent?.length || 0,
        imageType: item.metadata?.imageType,
        imageName: item.metadata?.imageName,
      });

      if (!item.metadata?.imageContent && !item.metadata?.imageUrl) {
        throw new Error(`项目 ${item.name} 缺少图片内容或图片URL`);
      }

      // 检查图片内容是否为空字符串
      if (item.metadata?.imageContent === '') {
        throw new Error(`项目 ${item.name} 的图片内容为空，请提供有效的Base64编码图片数据`);
      }
    }
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];

    this.logger.log(`📋 准备任务数据:`, {
      itemName: item.name,
      hasImageContent: !!item.metadata?.imageContent,
      hasImageUrl: !!item.metadata?.imageUrl,
      imageContentLength: item.metadata?.imageContent?.length || 0,
      imageType: item.metadata?.imageType,
      imageName: item.metadata?.imageName,
    });

    // 处理图片数据
    if (item.metadata?.imageContent) {
      // 直接提供的图片内容（Base64）
      const imageType = item.metadata.imageType || 'png';
      const imageName = item.metadata.imageName || 'design';
      
      // 确保图片名称有正确的扩展名
      const finalImageName = imageName.includes('.') 
        ? imageName 
        : `${imageName}.${imageType}`;
      
      files.push({
        name: finalImageName,
        contentType: `image/${imageType}`,
        url: item.metadata.imageContent.startsWith('data:') 
          ? item.metadata.imageContent 
          : `data:image/${imageType};base64,${item.metadata.imageContent}`,
      });
    } else if (item.metadata?.imageUrl) {
      // 图片URL（需要下载）
      // TODO: 如果需要，可以在这里实现图片下载逻辑
      const imageType = item.metadata.imageType || 'png';
      const imageName = item.metadata.imageName || 'design';
      
      // 确保图片名称有正确的扩展名
      const finalImageName = imageName.includes('.') 
        ? imageName 
        : `${imageName}.${imageType}`;
      
      files.push({
        name: finalImageName,
        contentType: `image/${imageType}`,
        url: item.metadata.imageUrl,
      });
    }

    // 如果有其他辅助文件
    if (item.metadata?.additionalFiles) {
      for (const file of item.metadata.additionalFiles) {
        files.push({
          name: file.name,
          contentType: file.contentType,
          url: file.url,
        });
      }
    }

    // 准备提示词上下文
    const sanitizedItem = {
      ...item,
      metadata: {
        ...item.metadata,
        // 移除大文件内容，避免在提示词中传递
        imageContent: undefined,
        additionalFiles: undefined,
      }
    };

    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: [sanitizedItem],
      options: config.metadata || {},
      metadata: { 
        item: sanitizedItem,
        hasImages: files.length > 0,
        imageCount: files.length,
      },
    };

    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    return { initialMessage, files };
  }
} 