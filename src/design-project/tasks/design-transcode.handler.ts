import { Logger } from '@nestjs/common';
import { BackgroundTaskItem, BackgroundTaskConfig, BackgroundTaskResult } from '../../background-task';
import { TaskWorkerService } from '../../background-task/task-worker.service';
import { PrismaService } from '../../prisma/prisma.service';
import { TaskType } from '../enums/task-types.enum';
import { PromptTemplateManager, PromptContext } from '../prompts/template-manager';
import { HtmlCssMerger } from '../utils/html-css-merger';
import { BaseTaskHandler, TaskDataResult } from './base-task-handler';

export class DesignTranscodeHandler extends BaseTaskHandler {
  private readonly logger = new Logger(DesignTranscodeHandler.name);
  constructor(
    private readonly promptManager: PromptTemplateManager,
    private readonly prisma: PrismaService,
    private readonly taskWorker: TaskWorkerService
  ) {
    super();
  }

  getSupportedTaskType(): string {
    return TaskType.DESIGN_TRANSCODE;
  }

  async validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    if (!items || items.length === 0) {
      throw new Error('设计稿代码优化任务需要至少一个项目');
    }

    for (const item of items) {
      // 检查是否有HTML内容或者原型ID
      if (!item.metadata?.htmlContent && !item.id) {
        throw new Error(`项目 ${item.name} 缺少HTML内容或原型ID`);
      }

      // 如果不是直接代码输入模式，检查原型是否存在
      if (!config.metadata?.isDirect && !item.metadata?.isDirect && !item.metadata?.htmlContent) {
        const prototype = await this.prisma.designPagePrototype.findUnique({
          where: { id: item.id },
        });

        if (!prototype) {
          throw new Error(`原型 ${item.id} 不存在`);
        }
      }
    }
  }

  /**
   * 任务开始时的回调 - 及时更新原型的transcodeTaskItems关联关系
   */
  async onTaskStart(taskId: string, config: BackgroundTaskConfig): Promise<void> {
    this.logger.log(`🎯 任务开始回调: ${taskId}`);

    // 如果是直接代码输入模式，不需要更新原型关联
    if (config.metadata?.isDirect) {
      this.logger.log(`ℹ️ 直接输入模式，跳过原型关联更新`);
      return;
    }

    try {
      // 获取当前任务的所有任务条目
      const taskItems = await this.prisma.backgroundTaskItem.findMany({
        where: { backgroundTaskId: taskId },
      });

      this.logger.log(`📋 找到 ${taskItems.length} 个任务条目`);

      // 为每个任务条目建立与原型的关联关系
      for (const taskItem of taskItems) {
        if (taskItem.itemId) {
          // 检查原型是否存在
          const prototype = await this.prisma.designPagePrototype.findUnique({
            where: { id: taskItem.itemId },
          });

          if (prototype) {
            // 更新任务条目的designPagePrototypeId字段
            await this.prisma.backgroundTaskItem.update({
              where: { id: taskItem.id },
              data: { designPagePrototypeId: taskItem.itemId },
            });

            // 同时更新原型状态为processing
            await this.prisma.designPagePrototype.update({
              where: { id: taskItem.itemId },
              data: { status: 'processing' },
            });

            this.logger.log(`🔗 已建立原型 ${taskItem.itemId} 与任务条目 ${taskItem.id} 的关联`);
          } else {
            this.logger.warn(`⚠️ 原型 ${taskItem.itemId} 不存在，跳过关联`);
          }
        }
      }

      this.logger.log(`✅ 完成 ${taskItems.length} 个任务条目的原型关联更新`);
    } catch (error) {
      this.logger.error(`❌ 更新原型关联失败:`, error);
    }
  }

  async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<TaskDataResult> {
    const files: Array<{ name: string; contentType: string; url: string }> = [];
    let htmlContent = item.metadata?.htmlContent;
    let cssContent = item.metadata?.cssContent;
    let jsContent = item.metadata?.jsContent;

    // 如果没有直接提供HTML内容，从数据库获取原型信息
    if (!htmlContent && !config.metadata?.isDirect && !item.metadata?.isDirect) {
      const prototype = await this.prisma.designPagePrototype.findUnique({
        where: { id: item.id },
        include: { designPage: true },
      });

      if (!prototype) {
        throw new Error(`原型 ${item.id} 不存在`);
      }

      htmlContent = prototype.htmlContent;
      cssContent = prototype.cssContent;

      // 如果原型没有HTML内容，则自动使用智能切图的HTML内容
      if (!htmlContent && (prototype.slicedAssets as any)?.html?.content) {
        htmlContent = (prototype.slicedAssets as any).html.content;
      }

      // 更新item的metadata以包含原型信息
      item.metadata = {
        ...item.metadata,
        designPageName: prototype.designPage.name,
        htmlFileName: prototype.htmlFileName,
        cssFileName: prototype.cssFileName,
      };
    }

    // 处理HTML和CSS文件：优先合并为单个HTML文件以减少上下文长度
    if (htmlContent && cssContent) {
      try {
        this.logger.log(`🔗 正在合并HTML和CSS文件，压缩优化中...`);

        // 合并HTML和CSS，并进行压缩
        const mergedHtml = await HtmlCssMerger.smartMergeAndMinify(htmlContent, cssContent, {
          removeComments: true,
          collapseWhitespace: true,
          minifyCSS: true,
          minifyJS: true,
          removeRedundantAttributes: true,
        });

        // 添加合并后的HTML文件
        files.push(this.createFileFromContent(
          mergedHtml,
          item.metadata?.htmlFileName || 'index.html',
          'text/html'
        ));

        this.logger.log(`✅ HTML/CSS合并完成，原始大小: ${htmlContent.length + cssContent.length} 字符，压缩后: ${mergedHtml.length} 字符，压缩率: ${((1 - mergedHtml.length / (htmlContent.length + cssContent.length)) * 100).toFixed(1)}%`);
      } catch (error) {
        this.logger.warn(`⚠️ HTML/CSS合并失败，回退到分离文件模式: ${error.message}`);

        // 如果合并失败，回退到原来的分离模式
        files.push(this.createFileFromContent(
          htmlContent,
          item.metadata?.htmlFileName || 'index.html',
          'text/html'
        ));

        files.push(this.createFileFromContent(
          cssContent,
          item.metadata?.cssFileName || 'styles.css',
          'text/css'
        ));
      }
    } else {
      // 如果只有HTML或只有CSS，单独处理
      if (htmlContent) {
        try {
          // 即使只有HTML，也尝试压缩
          const compressedHtml = await HtmlCssMerger.smartMergeAndMinify(htmlContent);
          files.push(this.createFileFromContent(
            compressedHtml,
            item.metadata?.htmlFileName || 'index.html',
            'text/html'
          ));
          this.logger.log(`✅ HTML压缩完成，原始大小: ${htmlContent.length} 字符，压缩后: ${compressedHtml.length} 字符`);
        } catch (error) {
          this.logger.warn(`⚠️ HTML压缩失败，使用原始内容: ${error.message}`);
          files.push(this.createFileFromContent(
            htmlContent,
            item.metadata?.htmlFileName || 'index.html',
            'text/html'
          ));
        }
      }

      if (cssContent) {
        files.push(this.createFileFromContent(
          cssContent,
          item.metadata?.cssFileName || 'styles.css',
          'text/css'
        ));
      }
    }

    // // 处理JavaScript文件
    // if (jsContent) {
    //   files.push(this.createFileFromContent(
    //     jsContent,
    //     item.metadata?.jsFileName || 'script.js',
    //     'application/javascript'
    //   ));
    // }

    // // 如果有设计规范文件
    // if (item.metadata?.designSpecs) {
    //   files.push(this.createFileFromContent(
    //     JSON.stringify(item.metadata.designSpecs, null, 2),
    //     'design-specs.json',
    //     'application/json'
    //   ));
    // }

    // 准备提示词上下文
    const sanitizedItem = {
      ...item,
      metadata: {
        ...item.metadata,
        // 移除大文件内容，保留关键信息
        htmlContent: undefined,
        cssContent: undefined,
        jsContent: undefined,
        designSpecs: undefined,
        fileCount: files.length,
        optimizationOptions: {
          framework: config.metadata?.framework || 'react',
          styleFramework: config.metadata?.styleFramework || 'tailwind',
          performance: config.metadata?.performance || true,
          accessibility: config.metadata?.accessibility || true,
          responsive: config.metadata?.responsive || true,
          modernCSS: config.metadata?.modernCSS || true,
          componentization: config.metadata?.componentization || true,
        }
      }
    };

    const promptContext: PromptContext = {
      taskType: config.taskType,
      items: [sanitizedItem],
      options: config.metadata || {},
      metadata: {
        item: sanitizedItem,
        hasFiles: files.length > 0,
        isDirect: config.metadata?.isDirect || item.metadata?.isDirect,
      },
    };

    const templateId = config.metadata?.promptTemplate || config.taskType;
    const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

    return { initialMessage, files };
  }

  async updateItemStatus(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    // 如果是直接代码输入模式，不需要更新数据库状态
    if (config.metadata?.isDirect || item.metadata?.isDirect) {
      this.logger.log(`ℹ️ 设计稿优化任务（直接输入模式）状态更新: ${status}`);
      return;
    }

    // 更新原型状态（非直接输入模式）
    try {
      await this.prisma.designPagePrototype.update({
        where: { id: item.id },
        data: {
          status,
          playgroundId,
        },
      });
      this.logger.log(`ℹ️ 设计稿优化任务状态更新: ${item.id} -> ${status}`);
    } catch (error) {
      this.logger.error(`❌ 更新原型状态失败: ${error.message}`);
    }
  }

  /**
   * 任务完成时的回调 - 更新原型的结果代码
   */
  async onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void> {
    this.logger.log(`🎯 开始处理任务完成回调，任务ID: ${taskId}`);

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    this.logger.log(`📊 任务完成统计: ${successCount}/${totalCount} 成功`);

    // 获取任务的所有任务项
    const taskItems = await this.prisma.backgroundTaskItem.findMany({
      where: { backgroundTaskId: taskId },
      orderBy: { created: 'asc' },
    });

    this.logger.log(`📋 找到 ${taskItems.length} 个任务项`);

    // 逐个处理每个任务项的结果
    for (let i = 0; i < results.length && i < taskItems.length; i++) {
      const result = results[i];
      const taskItem = taskItems[i];

      try {
        if (result.success) {
          this.logger.log(`🔄 处理成功的任务项: ${taskItem.itemId}`);
          await this.updatePrototypeResults(taskItem, result, config);
        } else {
          this.logger.log(`❌ 处理失败的任务项: ${taskItem.itemId}, 错误: ${result.error}`);
          await this.updatePrototypeFailure(taskItem, config);
        }
      } catch (error) {
        this.logger.error(`❌ 处理任务项结果时出错: ${taskItem.itemId}`, error);
      }
    }

    this.logger.log(`✅ 任务完成回调处理完毕`);
  }

  /**
   * 更新成功任务的原型结果
   */
  private async updatePrototypeResults(
    taskItem: any,
    result: BackgroundTaskResult,
    config: BackgroundTaskConfig
  ): Promise<void> {
    // 如果是直接代码输入模式，不需要更新原型记录
    if (config.metadata?.isDirect) {
      this.logger.log(`ℹ️ 直接输入模式，跳过原型结果更新`);
      return;
    }

    try {
      // 检查当前任务是否为该原型的最新任务
      const latestTaskItem = await this.prisma.backgroundTaskItem.findFirst({
        where: {
          designPagePrototypeId: taskItem.itemId,
          status: { in: ['completed', 'processing'] }
        },
        orderBy: { created: 'desc' },
        include: { backgroundTask: true }
      });

      if (latestTaskItem && latestTaskItem.id !== taskItem.id) {
        this.logger.log(`⚠️ 发现更新的任务 ${latestTaskItem.id}，跳过原型 ${taskItem.itemId} 的结果更新`);
        this.logger.log(`📊 当前任务: ${taskItem.id} (${taskItem.created})`);
        this.logger.log(`📊 最新任务: ${latestTaskItem.id} (${latestTaskItem.created})`);

        // 即使跳过更新，也要更新任务条目的状态
        await this.prisma.backgroundTaskItem.update({
          where: { id: taskItem.id },
          data: {
            status: 'completed',
            stage: '任务完成（已被更新任务覆盖）'
          },
        });
        return;
      }

      const playgroundId = taskItem.playgroundId;
      if (!playgroundId) {
        this.logger.warn(`⚠️ 任务项 ${taskItem.itemId} 没有 playgroundId，跳过文件获取`);

        // 即使没有文件，也要更新状态
        await this.prisma.designPagePrototype.update({
          where: { id: taskItem.itemId },
          data: {
            status: 'completed',
          },
        });
        return;
      }

      this.logger.log(`📁 获取 playground ${playgroundId} 的文件内容`);

      // 获取 playground 中的文件列表
      const files = await this.taskWorker.listPlaygroundFiles(playgroundId);
      this.logger.log(`📋 找到 ${files.length} 个文件:`, files.map(f => f.path || f.name));

      let resultHtml = null;
      let resultCss = null;

      // 查找并读取 HTML 文件
      const htmlFile = files.find(f =>
        f.path === 'index.html' ||
        f.path?.endsWith('.html') ||
        f.name === 'index.html' ||
        f.name?.endsWith('.html')
      );

      if (htmlFile) {
        const filePath = htmlFile.path || htmlFile.name;
        this.logger.log(`📄 读取 HTML 文件: ${filePath}`);
        const htmlData = await this.taskWorker.getPlaygroundFile(playgroundId, filePath);
        resultHtml = htmlData.content;
      }

      // 查找并读取 CSS 文件
      const cssFile = files.find(f =>
        f.path === 'styles.css' ||
        f.path?.endsWith('.css') ||
        f.name === 'styles.css' ||
        f.name?.endsWith('.css')
      );

      if (cssFile) {
        const filePath = cssFile.path || cssFile.name;
        this.logger.log(`🎨 读取 CSS 文件: ${filePath}`);
        const cssData = await this.taskWorker.getPlaygroundFile(playgroundId, filePath);
        resultCss = cssData.content;
      }

      // 再次检查是否仍为最新任务（双重检查，防止并发情况）
      const finalCheck = await this.prisma.backgroundTaskItem.findFirst({
        where: {
          designPagePrototypeId: taskItem.itemId,
          status: { in: ['completed', 'processing'] }
        },
        orderBy: { created: 'desc' }
      });

      if (finalCheck && finalCheck.id !== taskItem.id) {
        this.logger.log(`⚠️ 最终检查发现更新任务，跳过原型 ${taskItem.itemId} 的结果更新`);
        return;
      }

      // 更新原型记录
      this.logger.log(`💾 更新原型 ${taskItem.itemId} 的结果`);
      await this.prisma.designPagePrototype.update({
        where: { id: taskItem.itemId },
        data: {
          status: 'completed',
          playgroundId: playgroundId,
          resultHtml: resultHtml,
          resultCss: resultCss,
        },
      });

      this.logger.log(`✅ 原型 ${taskItem.itemId} 结果更新成功`, {
        hasHtml: !!resultHtml,
        hasCss: !!resultCss,
        htmlLength: resultHtml?.length || 0,
        cssLength: resultCss?.length || 0,
      });

    } catch (error) {
      this.logger.error(`❌ 更新原型结果失败: ${taskItem.itemId}`, error);

      // 即使获取文件失败，也要更新状态为完成但标记错误
      try {
        await this.prisma.designPagePrototype.update({
          where: { id: taskItem.itemId },
          data: {
            status: 'completed',
            playgroundId: taskItem.playgroundId,
          },
        });
      } catch (updateError) {
        this.logger.error(`❌ 更新原型状态失败: ${taskItem.itemId}`, updateError);
      }
    }
  }

  /**
   * 更新失败任务的原型状态
   */
  private async updatePrototypeFailure(
    taskItem: any,
    config: BackgroundTaskConfig
  ): Promise<void> {
    // 如果是直接代码输入模式，不需要更新原型记录
    if (config.metadata?.isDirect) {
      this.logger.log(`ℹ️ 直接输入模式，跳过失败状态更新`);
      return;
    }

    try {
      // 检查当前任务是否为该原型的最新任务
      const latestTaskItem = await this.prisma.backgroundTaskItem.findFirst({
        where: {
          designPagePrototypeId: taskItem.itemId,
          status: { in: ['completed', 'processing', 'failed'] }
        },
        orderBy: { created: 'desc' }
      });

      if (latestTaskItem && latestTaskItem.id !== taskItem.id) {
        this.logger.log(`⚠️ 发现更新的任务 ${latestTaskItem.id}，跳过失败原型 ${taskItem.itemId} 的状态更新`);

        // 即使跳过更新，也要更新任务条目的状态
        await this.prisma.backgroundTaskItem.update({
          where: { id: taskItem.id },
          data: {
            status: 'failed',
            stage: '任务失败（已被更新任务覆盖）'
          },
        });
        return;
      }

      this.logger.log(`💾 更新失败原型 ${taskItem.itemId} 的状态`);
      await this.prisma.designPagePrototype.update({
        where: { id: taskItem.itemId },
        data: {
          status: 'failed',
          playgroundId: taskItem.playgroundId, // 可能为空，但也要记录
        },
      });

      this.logger.log(`✅ 失败原型 ${taskItem.itemId} 状态更新成功`);
    } catch (error) {
      this.logger.error(`❌ 更新失败原型状态出错: ${taskItem.itemId}`, error);
    }
  }
} 