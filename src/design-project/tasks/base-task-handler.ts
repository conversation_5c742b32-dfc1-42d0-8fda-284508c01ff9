import { BackgroundTaskItem, BackgroundTaskConfig, BackgroundTaskResult } from '../../background-task';

// 任务数据准备结果接口
export interface TaskDataResult {
  initialMessage: string;
  files: Array<{
    name: string;
    contentType: string;
    url: string;
  }>;
}

// 任务处理器基类
export abstract class BaseTaskHandler {
  /**
   * 获取支持的任务类型
   */
  abstract getSupportedTaskType(): string;

  /**
   * 验证任务项目
   */
  abstract validateItems(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void>;

  /**
   * 准备任务数据
   */
  abstract prepareTaskData(item: BackgroundTaskItem, config: BackgroundTaskConfig): Promise<TaskDataResult>;

  /**
   * 检查是否支持自定义执行
   */
  supportsCustomExecution(): boolean {
    return typeof (this as any).executeItem === 'function';
  }

  /**
   * 更新项目状态（可选实现）
   */
  async updateItemStatus(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    // 默认实现为空，子类可以重写
  }

  /**
   * 任务开始时的回调
   */
  async onTaskStart(taskId: string, config: BackgroundTaskConfig): Promise<void> {
    // 默认实现为空，子类可以重写
  }

  /**
   * 任务完成时的回调
   */
  async onTaskComplete(
    taskId: string, 
    results: BackgroundTaskResult[], 
    config: BackgroundTaskConfig
  ): Promise<void> {
    // 默认实现为空，子类可以重写
  }

  /**
   * 创建Base64编码的文件对象
   */
  protected createFileFromContent(
    content: string,
    fileName: string,
    contentType: string
  ): { name: string; contentType: string; url: string } {
    return {
      name: fileName,
      contentType,
      url: `data:${contentType};base64,${Buffer.from(content).toString('base64')}`,
    };
  }

  /**
   * 创建Base64编码的图片文件对象
   */
  protected createImageFileFromBuffer(
    buffer: Buffer,
    fileName: string,
    mimeType: string = 'image/png'
  ): { name: string; contentType: string; url: string } {
    return {
      name: fileName,
      contentType: mimeType,
      url: `data:${mimeType};base64,${buffer.toString('base64')}`,
    };
  }
} 