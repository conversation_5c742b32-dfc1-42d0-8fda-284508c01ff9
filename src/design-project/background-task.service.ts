import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';

// 导入通用后台任务框架
import {
  BackgroundTaskExecutor,
  BackgroundTaskConfig,
  BackgroundTaskItem,
  BackgroundTaskResult,
  BackgroundTaskProgress,
  TaskExecutionContext,
  TaskWatcherService,
  TaskWorkerService,
} from '../background-task';
import { TaskManager } from '../background-task/task-manager';
import { PrismaService } from '../prisma/prisma.service';

// 导入新的提示词模板系统
import { PromptTemplateManager, PromptContext } from './prompts/template-manager';

// 导入新的任务处理器架构
import { TaskHandlerRegistry } from './tasks';

// 通用设计项目任务执行器
@Injectable()
export class DesignProjectTaskExecutor extends BackgroundTaskExecutor {
  private readonly logger = new Logger(DesignProjectTaskExecutor.name);

  constructor(
    prisma: PrismaService,
    private readonly promptManager: PromptTemplateManager,
    private readonly taskWorker: TaskWorkerService,
    private readonly taskWatcher: TaskWatcherService,
    private readonly taskHandlerRegistry: TaskHandlerRegistry
  ) {
    super(prisma);
  }

  async validateItems(items: BackgroundTaskItem[]): Promise<void> {
    // 由于基类期望的是单参数签名，这里只做基础验证
    // 具体的任务验证逻辑会在executeItem中结合config进行
    if (!items || items.length === 0) {
      throw new Error('任务项目列表不能为空');
    }
  }

  // 新增：带配置的验证方法
  private async validateItemsWithConfig(items: BackgroundTaskItem[], config: BackgroundTaskConfig): Promise<void> {
    // 尝试使用新的任务处理器架构
    const handler = this.taskHandlerRegistry.getHandler(config.taskType);
    if (handler) {
      await handler.validateItems(items, config);
      return;
    }

    // 保持对原有任务类型的兼容性
    if (config.taskType === 'design-transcode') {
      // 原有的验证逻辑
      return;
    } else if (config.taskType === 'design-merge') {
      if (!items || items.length === 0) {
        throw new Error('设计稿合并任务需要至少一个项目');
      }
    } else if (config.taskType === 'custom') {
      if (!config.metadata?.customPrompt) {
        throw new Error('自定义任务需要提供 customPrompt');
      }
    }
  }

  async onTaskStart(taskId: string, config: BackgroundTaskConfig): Promise<void> {
    // 尝试使用新的任务处理器架构
    const handler = this.taskHandlerRegistry.getHandler(config.taskType);
    if (handler) {
      await handler.onTaskStart(taskId, config);
      return;
    }

    // 保持对原有任务类型的兼容性
    if (config.taskType === 'design-transcode') {
      const prototypeIds = config.metadata?.prototypeIds || [];

      // 1. 更新原型状态为processing
      await this.prisma.designPagePrototype.updateMany({
        where: { id: { in: prototypeIds } },
        data: { status: 'processing' },
      });
      this.logger.log(`🔄 已将 ${prototypeIds.length} 个原型状态更新为 processing`);

      // 2. 更新原型的transcodeTaskItems关联关系
      if (prototypeIds.length > 0) {
        try {
          // 获取当前任务的所有任务条目
          const taskItems = await this.prisma.backgroundTaskItem.findMany({
            where: { backgroundTaskId: taskId },
          });

          // 为每个原型更新关联的任务条目
          for (const prototypeId of prototypeIds) {
            const relatedTaskItem = taskItems.find(item => item.itemId === prototypeId);
            if (relatedTaskItem) {
              await this.prisma.backgroundTaskItem.update({
                where: { id: relatedTaskItem.id },
                data: { designPagePrototypeId: prototypeId },
              });
              this.logger.log(`🔗 已建立原型 ${prototypeId} 与任务条目 ${relatedTaskItem.id} 的关联`);
            }
          }
          this.logger.log(`✅ 已完成 ${prototypeIds.length} 个原型的transcodeTaskItems关联更新`);
        } catch (error) {
          this.logger.error(`❌ 更新原型transcodeTaskItems关联失败:`, error);
        }
      }
    }
  }

  async executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: TaskExecutionContext,
    updateProgress: (progress: BackgroundTaskProgress) => Promise<void>
  ): Promise<BackgroundTaskResult> {
    this.logger.log(`🔄 开始处理任务: ${item.name} (${item.id})`);

    try {
      // 首先进行详细的任务验证
      await this.validateItemsWithConfig([item], config);

      // 检查是否有自定义执行的任务处理器
      const handler = this.taskHandlerRegistry.getHandler(config.taskType);
      if (handler && handler.supportsCustomExecution()) {
        this.logger.log(`🎯 [TaskExecutor] 使用自定义执行逻辑: ${config.taskType}`);

        // 调用任务处理器的自定义执行方法
        const result = await (handler as any).executeItem(item, config, context, updateProgress);

        // 更新最终状态
        const finalStatus = result.success ? 'completed' : 'failed';
        await this.updateItemStatus(item, config, result.metadata?.playgroundId || null, finalStatus);

        return result;
      }

      // 使用标准执行流程
      this.logger.log(`🔧 [TaskExecutor] 使用标准执行流程: ${config.taskType}`);

      // 更新进度：创建工作区
      await updateProgress({
        stage: '创建工作区',
        progress: 20,
      });

      // 创建playground
      const playgroundId = await this.createPlayground(item, config);

      // 将playgroundId添加到item的metadata中
      await this.updateItemStatus(item, config, playgroundId, 'processing');

      // 更新进度：执行任务
      await updateProgress({
        stage: '执行任务',
        progress: 50,
        metadata: { playgroundId },
      });

      // 执行完整的AI对话流程
      await this.executeBackgroundChat(playgroundId, config);

      // 新增：执行自检查流程（仅在用户启用时）
      const enableAutoIteration = config.metadata?.enableAutoIteration === true;

      if (enableAutoIteration) {
        const templateId = config.metadata?.promptTemplate || config.taskType;
        
        // 准备context，包含projectId以支持项目自定义检查提示词
        const checkContext: PromptContext = {
          taskType: config.taskType,
          items: [],
          options: config.metadata || {},
          metadata: config.metadata || {},
        };
        
        const checkPromptContent = await this.promptManager.getCheckPromptContent(templateId, checkContext);

        if (checkPromptContent && checkPromptContent.trim().length > 0) {
          this.logger.log(`🔍 开始执行自检查流程 (playground: ${playgroundId})`);
          await this.taskWorker.createUserMessage(playgroundId, checkPromptContent);
          await this.executeBackgroundChat(playgroundId, config);
          this.logger.log(`✅ 自检查流程完成 (playground: ${playgroundId})`);
        } else {
          this.logger.log(`ℹ️ 自检查已启用，但模板 ${templateId} 未配置检查提示词`);
        }
      } else {
        this.logger.log(`ℹ️ 自检查未启用，跳过自检查流程 (enableAutoIteration: ${config.metadata?.enableAutoIteration})`);
      }

      // 更新进度：任务完成
      await updateProgress({
        stage: '任务完成',
        progress: 90,
      });

      // 根据任务类型更新相关记录
      await this.updateItemStatus(item, config, playgroundId, 'completed');

      this.logger.log(`✅ 任务 ${item.name} 完成`);

      return {
        success: true,
        result: {
          message: '任务执行完成',
        },
        metadata: {
          playgroundId,
          taskId: context.taskId,
        }
      };

    } catch (error) {
      this.logger.error(`❌ 任务 ${item.name} 失败:`, error);

      // 更新状态为失败
      await this.updateItemStatus(item, config, null, 'failed');

      return {
        success: false,
        error: error.message,
      };
    }
  }

  async onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void> {
    // 尝试使用新的任务处理器架构
    const handler = this.taskHandlerRegistry.getHandler(config.taskType);
    if (handler) {
      await handler.onTaskComplete(taskId, results, config);
      return;
    }

    // 默认处理
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    this.logger.log(`🎉 ${config.taskName} 完成: ${successCount}/${totalCount} 成功`);
  }

  // 创建Playground的私有方法
  private async createPlayground(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<string> {
    const options = config.metadata || {};

    // 修复标题重复问题：如果 taskName 和 item.name 相同，则只使用 taskName
    const desc = config.taskName === item.name
      ? config.taskName
      : `${config.taskName} - ${item.name}`;

    // 生成安全的playground名称
    const safeName = `${config.taskType}-${item.name}`
      .replace(/[^a-zA-Z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .toLowerCase();

    // 使用通用任务执行器创建playground
    const playgroundId = await this.taskWorker.createPlayground({
      name: safeName,
      desc: desc,
      user: config.user,
      model: options.model || 'openrouter::google/gemini-2.5-pro-preview',
      projectId: options.projectId,
      isPublic: true,
      enableAutoIteration: options.enableAutoIteration || false,
      enableStepByStep: options.enableStepByStep || false,
      type: options.type, // 传递从 AiCodingService 传来的 type
    });

    // 准备初始消息和附件
    const { initialMessage, files } = await this.prepareTaskData(item, config);

    // 准备文件附件
    const attachments = files.length > 0 ? this.taskWorker.prepareFileAttachments(
      files.map(file => ({
        name: file.name,
        content: file.url.split(',')[1], // 直接使用Base64字符串，不转换为普通字符串
        contentType: file.contentType
      }))
    ) : undefined;

    // 创建用户消息（仅在有消息内容时）
    if (initialMessage && initialMessage.trim().length > 0) {
      await this.taskWorker.createUserMessage(playgroundId, initialMessage, attachments);
    } else {
      this.logger.log(`📝 跳过创建用户消息（空会话模式）`);
    }

    return playgroundId;
  }

  // 准备任务数据（根据不同任务类型）
  private async prepareTaskData(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig
  ): Promise<{ initialMessage: string; files: any[] }> {
    // 首先尝试使用新的任务处理器架构
    const handler = this.taskHandlerRegistry.getHandler(config.taskType);
    if (handler) {
      this.logger.log(`📋 使用新任务处理器: ${config.taskType}`);
      return await handler.prepareTaskData(item, config);
    }

    // 保持对原有任务类型的兼容性
    this.logger.log(`📋 使用兼容性任务处理器: ${config.taskType}`);

    const templateId = config.metadata?.promptTemplate || config.taskType;

    // 优先处理直接代码输入模式，因为它不依赖数据库
    if (config.taskType === 'design-transcode' && item.metadata?.isDirect) {
      const files = [
        {
          name: item.metadata.htmlFileName || 'index.html',
          contentType: 'text/html',
          url: `data:text/html;base64,${Buffer.from(item.metadata.htmlContent).toString('base64')}`,
        },
      ];

      if (item.metadata.cssContent) {
        files.push({
          name: item.metadata.cssFileName || 'styles.css',
          contentType: 'text/css',
          url: `data:text/css;base64,${Buffer.from(item.metadata.cssContent).toString('base64')}`,
        });
      }

      // 准备用于模板的上下文，移除代码内容
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { htmlContent, cssContent, ...sanitizedMetadata } = item.metadata;
      const promptItem = { ...item, metadata: sanitizedMetadata };

      const promptContext: PromptContext = {
        taskType: config.taskType,
        items: [promptItem],
        options: {
          ...config.metadata,
          isDirect: true,
        },
        metadata: { isDirect: true },
      };

      const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);
      return { initialMessage, files };
    }

    // 修正：使用 else if 确保逻辑分支互斥
    else if (config.taskType === 'design-transcode') {
      // 转码任务：获取原型数据
      const prototype = await this.prisma.designPagePrototype.findUnique({
        where: { id: item.id },
        include: { designPage: true },
      });

      if (!prototype) {
        throw new Error(`原型 ${item.id} 不存在`);
      }

      const files = [
        {
          name: prototype.htmlFileName || 'index.html',
          contentType: 'text/html',
          url: `data:text/html;base64,${Buffer.from(prototype.htmlContent).toString('base64')}`,
        },
      ];

      if (prototype.cssContent) {
        files.push({
          name: prototype.cssFileName || 'styles.css',
          contentType: 'text/css',
          url: `data:text/css;base64,${Buffer.from(prototype.cssContent).toString('base64')}`,
        });
      }

      // 将prototype转换为BackgroundTaskItem格式
      const prototypeItem: BackgroundTaskItem = {
        id: prototype.id,
        name: prototype.prototypeName,
        metadata: {
          designPageName: prototype.designPage.name,
          htmlFileName: prototype.htmlFileName,
          cssFileName: prototype.cssFileName,
        }
      };

      // 准备用于模板的上下文，移除代码内容
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { htmlContent, cssContent, ...sanitizedPrototypeData } = prototype;

      const promptContext: PromptContext = {
        taskType: config.taskType,
        items: [prototypeItem],
        options: config.metadata || {},
        metadata: { prototype: sanitizedPrototypeData },
      };

      const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

      return { initialMessage, files };
    } else if (config.taskType === 'design-merge') {
      // 合并任务：获取多个playground结果
      const playgroundIds = config.metadata?.playgroundIds || [];
      const playgrounds = await this.prisma.playground.findMany({
        where: { id: { in: playgroundIds } },
      });

      // 将playgrounds转换为BackgroundTaskItem格式
      const playgroundItems: BackgroundTaskItem[] = playgrounds.map(playground => ({
        id: playground.id,
        name: playground.name || 'Unnamed Playground',
        metadata: {
          desc: playground.desc,
          model: playground.model,
        }
      }));

      const promptContext: PromptContext = {
        taskType: config.taskType,
        items: playgroundItems,
        options: config.metadata || {},
        metadata: { playgrounds },
      };

      const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

      return { initialMessage, files: [] };
    } else {
      // 其他任务类型
      const promptContext: PromptContext = {
        taskType: config.taskType,
        items: [item],
        options: config.metadata || {},
        metadata: { item },
      };

      const initialMessage = await this.promptManager.buildPrompt(templateId, promptContext);

      return { initialMessage, files: [] };
    }
  }

  // 更新项目状态
  private async updateItemStatus(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    // 首先尝试使用新的任务处理器架构
    const handler = this.taskHandlerRegistry.getHandler(config.taskType);
    if (handler) {
      await handler.updateItemStatus(item, config, playgroundId, status);
      // 确保同时更新 backgroundTaskItem 记录
      await this.updateBackgroundTaskItem(item, playgroundId, status);
      return;
    }

    // 通用逻辑：更新 backgroundTaskItem 的 playgroundId（所有任务类型都需要）
    await this.updateBackgroundTaskItem(item, playgroundId, status);

    // 保持对原有任务类型的兼容性 - 特定任务类型的额外处理
    if (config.taskType === 'design-transcode') {
      // 对于直接代码输入模式，已经在通用逻辑中处理了 backgroundTaskItem
      if (config.metadata?.isDirect || item.metadata?.isDirect) {
        this.logger.log(`ℹ️ 直接代码输入模式，已通过通用逻辑更新任务项 [${item.id}] 的 playgroundId`);
        return;
      }

      // 更新原型状态（design-transcode 特有逻辑）
      await this.prisma.designPagePrototype.update({
        where: { id: item.id },
        data: {
          status,
          playgroundId,
        },
      });
    }
    // 可以为其他任务类型添加相应的状态更新逻辑
  }

  // 新增：通用的更新 backgroundTaskItem 方法
  private async updateBackgroundTaskItem(
    item: BackgroundTaskItem,
    playgroundId: string | null,
    status: string
  ): Promise<void> {
    try {
      // 通过itemId查找对应的backgroundTaskItem记录
      const taskItem = await this.prisma.backgroundTaskItem.findFirst({
        where: { itemId: item.id },
        orderBy: { created: 'desc' }
      });

      if (taskItem) {
        await this.prisma.backgroundTaskItem.update({
          where: { id: taskItem.id },
          data: { 
            playgroundId: playgroundId,
            status: status,
            updated: new Date()
          },
        });
        this.logger.log(`✅ 已更新任务项 [${item.id}] 的状态: ${status}, playgroundId: ${playgroundId || 'null'}`);
      } else {
        this.logger.warn(`⚠️ 无法找到对应的BackgroundTaskItem记录，itemId: ${item.id}`);
      }
    } catch (error) {
      this.logger.error(`❌ 更新BackgroundTaskItem失败，itemId: ${item.id}`, error);
    }
  }

  // 执行后台Chat流程
  private async executeBackgroundChat(playgroundId: string, config: BackgroundTaskConfig) {
    this.logger.log(`🤖 开始执行后台Chat流程 (playground: ${playgroundId})`);

    try {
      const options = config.metadata || {};

      // 使用通用任务执行器执行AI对话
      await this.taskWorker.executeAIDialog(playgroundId, {
        projectId: options.projectId,
        enableAutoIteration: options.enableAutoIteration || false,
        enableStepByStep: options.enableStepByStep || false,
      });

      this.logger.log(`🎉 后台Chat流程完成 (playground: ${playgroundId})`);

    } catch (error) {
      this.logger.error(`❌ 后台Chat流程失败 (playground: ${playgroundId}):`, error);
      throw error;
    }
  }
}

// 兼容性服务：保持原有的接口，并扩展支持新的任务类型
@Injectable()
export class DesignProjectBackgroundTaskService {
  private readonly taskExecutor: DesignProjectTaskExecutor;
  private readonly logger = new Logger(DesignProjectBackgroundTaskService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly promptManager: PromptTemplateManager,
    private readonly taskManager: TaskManager,
    private readonly commonTaskWorker: TaskWorkerService,
    private readonly taskWatcher: TaskWatcherService,
    private readonly taskHandlerRegistry: TaskHandlerRegistry
  ) {
    this.taskExecutor = new DesignProjectTaskExecutor(
      prisma,
      promptManager,
      commonTaskWorker,
      taskWatcher,
      taskHandlerRegistry
    );
  }

  // 兼容性方法：保持原有的图转码接口
  async createAndExecuteTranscodeTask(data: {
    projectId: string;
    prototypeIds: string[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    promptTemplate?: string;
    promptOptions?: Record<string, any>;
    waitForCompletion?: boolean;
  }) {
    // 获取原型信息
    const prototypes = await this.prisma.designPagePrototype.findMany({
      where: { id: { in: data.prototypeIds } },
      include: { designPage: true },
    });

    if (prototypes.length === 0) {
      throw new Error('没有找到有效的原型');
    }

    // 转换为通用任务格式，使用新的任务处理器架构
    const taskItems: BackgroundTaskItem[] = prototypes.map(prototype => ({
      id: prototype.id,
      name: prototype.prototypeName,
      metadata: {
        designPageName: prototype.designPage.name,
        htmlFileName: prototype.htmlFileName,
        cssFileName: prototype.cssFileName,
        htmlContent: prototype.htmlContent,
        cssContent: prototype.cssContent
      },
    }));

    // 使用通用任务执行方法和design-transcode处理器
    return await this.createAndExecuteGenericTask({
      taskType: 'design-transcode',
      taskName: `设计稿转码任务 - ${prototypes.length}个原型`,
      items: taskItems,
      user: data.user,
      model: data.model,
      enableAutoIteration: data.enableAutoIteration,
      enableStepByStep: data.enableStepByStep,
      promptTemplate: data.promptTemplate,
      metadata: {
        projectId: data.projectId,
        prototypeIds: data.prototypeIds,
        ...data.promptOptions,
      },
      waitForCompletion: data.waitForCompletion,
    });
  }

  // 新增：支持直接传入HTML和CSS代码的转码任务
  async createAndExecuteDirectTranscodeTask(data: {
    projectId: string;
    htmlContent: string;
    cssContent?: string;
    name: string;
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    promptTemplate?: string;
    promptOptions?: Record<string, any>;
    waitForCompletion?: boolean;
  }) {
    // 生成临时ID作为原型ID（不依赖数据库）
    const tempId = generateId();

    // 转换为通用任务格式，使用新的任务处理器架构
    const taskItems: BackgroundTaskItem[] = [{
      id: tempId,
      name: data.name || '直接代码输入',
      metadata: {
        designPageName: data.name || '直接代码输入',
        htmlFileName: 'index.html',
        cssFileName: data.cssContent ? 'styles.css' : undefined,
        htmlContent: data.htmlContent,
        cssContent: data.cssContent || '',
        isDirect: true, // 标记为直接代码输入
      },
    }];

    // 使用通用任务执行方法和design-transcode处理器
    return await this.createAndExecuteGenericTask({
      taskType: 'design-transcode',
      taskName: `直接代码转码任务 - ${data.name}`,
      items: taskItems,
      user: data.user,
      model: data.model,
      enableAutoIteration: data.enableAutoIteration,
      enableStepByStep: data.enableStepByStep,
      promptTemplate: data.promptTemplate,
      metadata: {
        projectId: data.projectId,
        isDirect: true, // 标记为直接代码输入模式
        ...data.promptOptions,
      },
      waitForCompletion: data.waitForCompletion,
    });
  }

  // 新增：设计稿合并任务
  async createAndExecuteMergeTask(data: {
    projectId: string;
    prototypeIds: string[];
    projectName: string;
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    promptTemplate?: string;
    promptOptions?: Record<string, any>;
    waitForCompletion?: boolean;
  }) {
    // 获取原型信息并提取 playgroundIds
    const prototypes = await this.prisma.designPagePrototype.findMany({
      where: {
        id: { in: data.prototypeIds },
        playgroundId: { not: null } // 确保原型已经有转码结果
      },
      include: { designPage: true },
    });

    if (prototypes.length === 0) {
      throw new Error('没有找到有效的已转码原型');
    }

    if (prototypes.length !== data.prototypeIds.length) {
      const foundIds = prototypes.map(p => p.id);
      const missingIds = data.prototypeIds.filter(id => !foundIds.includes(id));
      throw new Error(`以下原型不存在或尚未转码: ${missingIds.join(', ')}`);
    }

    // 检查所有原型是否属于同一个页面
    const uniquePageIds = [...new Set(prototypes.map(p => p.designPageId))];
    if (uniquePageIds.length > 1) {
      this.logger.warn(`⚠️ [Service] 发现原型属于多个页面 (${uniquePageIds.length} 个页面)，将继续合并`);
    }

    // 提取 playgroundIds
    const playgroundIds = prototypes.map(p => p.playgroundId!).filter(Boolean);

    this.logger.log(`📋 [Service] 原型到 playground 映射:`, {
      prototypeCount: prototypes.length,
      playgroundCount: playgroundIds.length,
      prototypes: prototypes.map(p => ({
        id: p.id,
        name: p.prototypeName,
        playgroundId: p.playgroundId,
        pageName: p.designPage.name
      }))
    });

    // 转换为通用任务格式，使用新的任务处理器架构
    const taskItems: BackgroundTaskItem[] = [{
      id: generateId(),
      name: data.projectName,
      metadata: {
        prototypeIds: data.prototypeIds,
        playgroundIds: playgroundIds,
        prototypes: prototypes.map(p => ({
          id: p.id,
          name: p.prototypeName,
          designPageId: p.designPageId,
          designPageName: p.designPage.name,
          playgroundId: p.playgroundId,
        })),
      },
    }];

    // 使用通用任务执行方法和design-merge处理器
    return await this.createAndExecuteGenericTask({
      taskType: 'design-merge',
      taskName: `设计稿合并任务 - ${data.projectName}`,
      items: taskItems,
      user: data.user,
      model: data.model,
      enableAutoIteration: data.enableAutoIteration,
      enableStepByStep: data.enableStepByStep,
      promptTemplate: data.promptTemplate,
      metadata: {
        projectId: data.projectId,
        projectName: data.projectName,
        prototypeIds: data.prototypeIds,
        playgroundIds: playgroundIds,
        prototypes: prototypes.map(p => ({
          id: p.id,
          name: p.prototypeName,
          designPageId: p.designPageId,
          designPageName: p.designPage.name,
          playgroundId: p.playgroundId,
        })),
      },
      waitForCompletion: data.waitForCompletion,
    });
  }

  // 通用的创建和执行任务方法（使用新的任务处理器架构）
  async createAndExecuteGenericTask(data: {
    taskType: string;
    taskName: string;
    items: BackgroundTaskItem[];
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    promptTemplate?: string;
    metadata?: Record<string, any>;
    waitForCompletion?: boolean;
  }) {
    // 检查是否支持该任务类型
    if (!this.taskHandlerRegistry.supportsTaskType(data.taskType)) {
      throw new Error(`不支持的任务类型: ${data.taskType}`);
    }

    const taskConfig: BackgroundTaskConfig = {
      taskType: data.taskType,
      taskName: data.taskName,
      user: data.user,
      metadata: {
        model: data.model || 'openrouter::google/gemini-2.5-pro-preview',
        enableAutoIteration: data.enableAutoIteration || false,
        enableStepByStep: data.enableStepByStep || false,
        promptTemplate: data.promptTemplate || data.taskType,
        ...data.metadata,
      },
    };

    // 使用TaskManager执行任务
    const taskId = await this.taskManager.executeTask(taskConfig, data.items, this.taskExecutor);

    // 如果需要等待任务完成
    if (data.waitForCompletion) {
      // 对于 spec-to-prod-code 任务，跳过文件遍历
      const skipFileTraversal = data.taskType === 'spec-to-prod-code';

      // debugger
      const result = await this.taskWatcher.waitForTaskCompletionAndGetResults(taskId, undefined, {
        skipFileTraversal
      });

      // 🔍 调试日志：查看spec-to-prod-code任务的result内容
      if (data.taskType === 'spec-to-prod-code') {
        this.logger.log(`🔍 [Debug] spec-to-prod-code任务result结构:`, {
          hasAllPlaygroundInfo: !!result.allPlaygroundInfo,
          allPlaygroundInfoLength: result.allPlaygroundInfo?.length || 0,
          allPlaygroundInfoContent: result.allPlaygroundInfo,
          resultKeys: Object.keys(result),
          resultStatus: result.status,
          resultTaskId: result.taskId
        });
      }

      // spec-to-prod-code任务的特殊处理：提取所有页面的playgroundId
      if (data.taskType === 'spec-to-prod-code' && result.allPlaygroundInfo && result.allPlaygroundInfo.length > 0) {
        this.logger.log(`🎯 [Service] spec-to-prod-code任务完成，提取页面playground信息`);
        this.logger.log(`📊 [Service] 发现 ${result.allPlaygroundInfo.length} 个页面的playground信息:`,
          result.allPlaygroundInfo.map(info => ({
            pageName: info.pageName,
            playgroundId: info.playgroundId
          }))
        );

        // 提取所有页面的playgroundId
        const allPlaygroundIds = result.allPlaygroundInfo.map(info => info.playgroundId);

        // 将playground信息添加到返回结果中
        return {
          ...result,
          playgroundIds: allPlaygroundIds, // 所有页面的playgroundId数组
          playgroundId: allPlaygroundIds[0], // 第一个playgroundId作为主要ID（向后兼容）
          pageResults: result.allPlaygroundInfo.map(info => ({
            page: info.pageName,
            playgroundId: info.playgroundId,
            taskId: info.taskId
          })),
          pageCount: result.allPlaygroundInfo.length,
          executionMode: 'parallel'
        };
      }

      // 图片视觉分割任务的特殊处理：从index.json文件中提取coordinates
      if (data.taskType === 'img-visual-split' && result.files && result.files['index.json']) {
        try {
          const indexJsonContent = result.files['index.json'].content;
          let coordinates;

          // 检查内容是否已经是对象格式
          if (typeof indexJsonContent === 'string') {
            coordinates = JSON.parse(indexJsonContent);
          } else if (Array.isArray(indexJsonContent)) {
            coordinates = indexJsonContent;
          } else {
            // 如果是其他格式，尝试字符串化后再解析
            const stringContent = String(indexJsonContent);
            if (stringContent !== '[object Object]') {
              coordinates = JSON.parse(stringContent);
            }
          }

          this.logger.log(`📊 [Service] 图片视觉分割任务解析coordinates成功，数量: ${coordinates?.length || 0}`);

          return {
            ...result,
            coordinates: coordinates
          };
        } catch (error) {
          this.logger.error(`❌ [Service] 解析index.json失败:`, error);
          this.logger.error(`📄 [Service] index.json内容类型: ${typeof result.files['index.json'].content}`);
          this.logger.error(`📄 [Service] index.json内容: ${result.files['index.json'].content}`);

          // 即使解析失败也返回原始结果
          return result;
        }
      }

      return result;
    }

    // 非等待完成模式：等待第一个 playground 创建完成后返回
    this.logger.log(`⏳ [Service] 等待第一个 playground 创建完成 (taskId: ${taskId})`);
    
    try {
      // 等待第一个 playground 创建完成（最多等待30秒）
      const playgroundId = await this.waitForFirstPlaygroundCreation(taskId, 30000);
      
      this.logger.log(`✅ [Service] 获取到第一个 playgroundId: ${playgroundId} (taskId: ${taskId})`);
      
      return {
        taskId,
        success: true,
        playgroundId,
        message: '任务已创建，playground已初始化完成',
      };
    } catch (error) {
      this.logger.warn(`⚠️ [Service] 等待 playground 创建超时，返回任务ID (taskId: ${taskId}):`, error.message);
      
      // 如果等待超时，仍然返回任务ID，但 playgroundId 为 null
      return {
        taskId,
        success: true,
        playgroundId: null,
        message: '任务已创建，playground正在初始化中。可通过任务状态查询获取 playgroundId',
      };
    }
  }

  // 新增：获取任务状态信息
  async getTaskStatus(taskId: string) {
    try {
      // 使用TaskManager获取任务状态
      return await this.taskManager.getTaskStatus(taskId);
    } catch (error) {
      this.logger.error(`❌ [Service] 获取任务状态失败:`, error);
      throw new Error(`获取任务状态失败: ${error.message}`);
    }
  }

  // 查询用户任务列表
  async getUserTasks(user: string, taskType?: string) {
    return this.taskManager.getUserTasks(user, taskType);
  }

  // 获取可用的提示词模板
  async getAvailablePromptTemplates() {
    return this.promptManager.listTemplates().map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
    }));
  }

  // 获取支持的任务类型
  async getSupportedTaskTypes() {
    const supportedTypes = this.taskHandlerRegistry.getSupportedTaskTypes();
    const allTemplates = this.promptManager.listTemplates();

    return allTemplates.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      isSupported: supportedTypes.includes(template.id),
      config: this.promptManager.getTemplateConfig(template.id),
    }));
  }

  // 新增：重新加载提示词模板（用于开发时热更新）
  async reloadPromptTemplates() {
    this.promptManager.reloadTemplates();
    return { success: true, message: '提示词模板已重新加载' };
  }

  // 新增：获取提示词模板详细信息
  async getPromptTemplateInfo(templateId: string) {
    try {
      const template = this.promptManager.getTemplate(templateId);
      const config = this.promptManager.getTemplateConfig(templateId);

      return {
        id: template.id,
        name: template.name,
        description: template.description,
        supportedOptions: config.supportedOptions || [],
        filePath: config.filePath,
        isSupported: this.taskHandlerRegistry.supportsTaskType(templateId),
      };
    } catch (error) {
      throw new Error(`获取提示词模板信息失败: ${error.message}`);
    }
  }

  // 新增：获取playground工作区中的文件内容
  async getPlaygroundFile(playgroundId: string, filePath: string) {
    try {
      // 使用通用任务执行器获取文件内容
      const fileData = await this.commonTaskWorker.getPlaygroundFile(playgroundId, filePath);

      return {
        playgroundId,
        filePath,
        content: fileData.content,
        contentType: fileData.contentType
      };
    } catch (error) {
      throw new Error(`获取工作区文件内容失败: ${error.message}`);
    }
  }

  // 新增：获取playground工作区中的所有文件列表
  async listPlaygroundFiles(playgroundId: string) {
    try {
      // 使用通用任务执行器获取文件列表
      return await this.commonTaskWorker.listPlaygroundFiles(playgroundId);
    } catch (error) {
      this.logger.error(`❌ [Service] 获取工作区文件列表失败:`, error);
      throw new Error(`获取工作区文件列表失败: ${error.message}`);
    }
  }

  // 新增：添加createAndExecuteCustomTask方法
  async createAndExecuteCustomTask(data: {
    taskName: string;
    customPrompt: string;
    items: Array<{
      id: string;
      name: string;
      metadata?: Record<string, any>;
    }>;
    user: string;
    model?: string;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    metadata?: Record<string, any>;
    waitForCompletion?: boolean;
  }) {
    // 配置任务
    const taskConfig: BackgroundTaskConfig = {
      taskType: 'custom',
      taskName: data.taskName,
      user: data.user,
      metadata: {
        customPrompt: data.customPrompt,
        model: data.model || 'openrouter::google/gemini-2.5-pro-preview',
        enableAutoIteration: data.enableAutoIteration || false,
        enableStepByStep: data.enableStepByStep || false,
        ...data.metadata,
      },
    };

    // 转换任务项
    const taskItems: BackgroundTaskItem[] = data.items.map(item => ({
      id: item.id,
      name: item.name,
      metadata: item.metadata || {},
    }));

    // 使用TaskManager执行任务
    const taskId = await this.taskManager.executeTask(taskConfig, taskItems, this.taskExecutor);

    // 如果需要等待任务完成
    if (data.waitForCompletion) {
      return this.taskWatcher.waitForTaskCompletionAndGetResults(taskId);
    }

    // 返回任务ID
    return {
      taskId,
      success: true,
    };
  }

  // 新增：查询项目下的所有任务列表
  async getProjectTasks(
    projectId: string,
    taskType?: string,
    taskIds?: string,
  ) {
    this.logger.log(
      `查询项目 [${projectId}] 的任务列表，筛选条件: ${JSON.stringify({
        taskType,
        taskIds,
      })}`,
    );

    const where: any = {
      designProjectId: projectId,
    };

    if (taskType) {
      where.taskType = taskType;
    }

    if (taskIds) {
      const ids = taskIds.split(',').map(id => id.trim());
      if (ids.length > 0) {
        where.id = {
          in: ids,
        };
      }
    }

    try {
      const tasks = await this.prisma.backgroundTask.findMany({
        where,
        include: {
          items: true,
        },
        orderBy: {
          created: 'desc',
        },
      });

      // 为每个任务计算成功/失败/进行中的项目数量
      const tasksWithStats = tasks.map((task) => {
        const stats = {
          total: task.items.length,
          completed: task.items.filter((item) => item.status === 'completed')
            .length,
          failed: task.items.filter((item) => item.status === 'failed').length,
          processing: task.items.filter((item) => item.status === 'processing')
            .length,
          pending: task.items.filter((item) => item.status === 'pending')
            .length,
        };

        // 尝试从任务条目中提取playgroundId
        const playgroundId = this.extractPlaygroundId(task.items);

        return {
          id: task.id,
          taskType: task.taskType,
          status: task.status,
          progress: task.progress,
          stats, // 在这里加入统计信息
          createdAt: task.created,
          updatedAt: task.updated,
          completedAt: task.status === 'completed' ? task.updated : undefined,
          playgroundId,
          error: task.status === 'failed' ? '任务执行失败' : undefined,
          metadata: {
            projectId: task.designProjectId,
            model: task.model,
            enableAutoIteration: task.enableAutoIteration,
            enableStepByStep: task.enableStepByStep,
            taskName: task.taskName,
          },
          items: task.items.map(item => ({
            id: item.itemId,
            status: item.status,
            result: item.result || (item.playgroundId ? { playgroundId: item.playgroundId } : undefined),
            error: item.error || undefined,
            metadata: {
              playgroundId: item.playgroundId || undefined,
              progress: item.progress,
              stage: item.stage,
            },
          })),
        };
      });

      return tasksWithStats;
    } catch (error) {
      this.logger.error(
        `查询项目 [${projectId}] 的任务列表失败:`,
        error,
      );
      throw new Error(`获取项目任务列表失败: ${error.message}`);
    }
  }

  // 新增：从任务项中提取playgroundId的辅助方法
  private extractPlaygroundId(items: any[]): string | undefined {
    if (!items || items.length === 0) {
      return undefined;
    }

    // 尝试从第一个完成的项目中获取playgroundId
    const completedItem = items.find(item => item.status === 'completed' && item.playgroundId);
    if (completedItem && completedItem.playgroundId) {
      return completedItem.playgroundId;
    }

    // 如果没有完成的项目，尝试从第一个处理中的项目获取
    const processingItem = items.find(item => item.status === 'processing' && item.playgroundId);
    if (processingItem && processingItem.playgroundId) {
      return processingItem.playgroundId;
    }

    return undefined;
  }

  // 新增：等待第一个 playground 创建完成的辅助方法
  private async waitForFirstPlaygroundCreation(taskId: string, timeout: number): Promise<string> {
    const startTime = Date.now();
    let currentPlaygroundId: string | null = null;

    while (Date.now() - startTime < timeout) {
      try {
        const taskStatus = await this.taskWatcher.getTaskStatus(taskId);
        if (taskStatus && taskStatus.playgroundId) {
          currentPlaygroundId = taskStatus.playgroundId;
          break;
        }
      } catch (error) {
        this.logger.warn(`⚠️ 获取任务状态失败:`, error.message);
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // 每秒检查一次
    }

    if (!currentPlaygroundId) {
      throw new Error(`等待任务 ${taskId} 的第一个 playground 创建超时`);
    }
    return currentPlaygroundId;
  }
}
