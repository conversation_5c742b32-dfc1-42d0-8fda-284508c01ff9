import * as fs from 'fs';
import * as path from 'path';
import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TaskType } from './enums/task-types.enum';
import { PromptTemplateManager } from './prompts/template-manager';

// 使用Prisma生成的类型
export type ProjectPromptTemplate = {
  id: string;
  projectId: string;
  taskType: string;
  promptType: string;
  content: string;
  version: number;
  status: string;
  created: Date;
  updated: Date;
  createdBy: string;
  updatedBy: string | null;
}

export interface CreatePromptTemplateDto {
  projectId: string;
  taskType: string;
  promptType: 'main' | 'check' | 'target'; // 添加target类型支持
  content: string;
  createdBy: string;
}

export interface UpdatePromptTemplateDto {
  content?: string;
  updatedBy: string;
}

export interface PromptTemplateSummary {
  taskType: string;
  taskName: string;
  hasCustomMain: boolean;
  hasCustomCheck: boolean;
  hasCustomTarget?: boolean; // 仅spec-to-prod-code任务有
  mainTemplateId?: string;
  checkTemplateId?: string;
  targetTemplateId?: string; // 仅spec-to-prod-code任务有
  mainUpdated?: Date;
  checkUpdated?: Date;
  targetUpdated?: Date; // 仅spec-to-prod-code任务有
}

/**
 * 项目提示词管理服务
 * 负责管理每个项目的自定义提示词模板
 */
@Injectable()
export class PromptManagementService {
  private readonly logger = new Logger(PromptManagementService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly promptManager: PromptTemplateManager,
  ) {
    // 设置循环依赖
    this.promptManager.setPromptManagementService(this);
    this.promptManager.setPrismaService(this.prisma);
  }

  /**
   * 获取项目的所有自定义提示词模板
   */
  async getProjectPromptTemplates(projectId: string): Promise<ProjectPromptTemplate[]> {
    return this.prisma.projectPromptTemplate.findMany({
      where: {
        projectId,
        status: 'active',
      },
      orderBy: [
        { taskType: 'asc' },
        { promptType: 'asc' },
      ],
    });
  }

  /**
   * 获取项目提示词模板概览
   */
  async getProjectPromptSummary(projectId: string): Promise<PromptTemplateSummary[]> {
    // 获取所有支持的任务类型（排除自定义任务）
    const supportedTaskTypes = [
      TaskType.IMG_TO_CODE,
      TaskType.IMG_VISUAL_SPLIT,
      TaskType.IMG_SPLIT_TO_CODE,
      TaskType.COORDS_TO_LAYOUT,
      TaskType.SPEC_TO_PROD_CODE,
      TaskType.DESIGN_TRANSCODE,
      TaskType.DESIGN_MERGE,
    ];

    const customTemplates = await this.getProjectPromptTemplates(projectId);
    const templateMap = new Map<string, { main?: ProjectPromptTemplate; check?: ProjectPromptTemplate; target?: ProjectPromptTemplate }>();

    // 建立任务类型到模板的映射
    customTemplates.forEach(template => {
      if (!templateMap.has(template.taskType)) {
        templateMap.set(template.taskType, {});
      }
      const taskTemplates = templateMap.get(template.taskType)!;
      if (template.promptType === 'main') {
        taskTemplates.main = template;
      } else if (template.promptType === 'check') {
        taskTemplates.check = template;
      } else if (template.promptType === 'target') {
        taskTemplates.target = template;
      }
    });

    // 构建概览数据
    return supportedTaskTypes.map(taskType => {
      const taskTemplates = templateMap.get(taskType) || {};
      const taskName = this.getTaskTypeName(taskType);

      const result: PromptTemplateSummary = {
        taskType,
        taskName,
        hasCustomMain: !!taskTemplates.main,
        hasCustomCheck: !!taskTemplates.check,
        mainTemplateId: taskTemplates.main?.id,
        checkTemplateId: taskTemplates.check?.id,
        mainUpdated: taskTemplates.main?.updated,
        checkUpdated: taskTemplates.check?.updated,
      };

      // 只有spec-to-prod-code任务才包含target相关字段
      if (taskType === TaskType.SPEC_TO_PROD_CODE) {
        result.hasCustomTarget = !!taskTemplates.target;
        result.targetTemplateId = taskTemplates.target?.id;
        result.targetUpdated = taskTemplates.target?.updated;
      }

      return result;
    });
  }

  /**
   * 创建或更新项目自定义提示词模板
   */
  async upsertPromptTemplate(data: CreatePromptTemplateDto): Promise<ProjectPromptTemplate> {
    const { projectId, taskType, promptType, content, createdBy } = data;

    // 验证项目是否存在
    const project = await this.prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      throw new NotFoundException(`项目 ${projectId} 不存在`);
    }

    // 验证任务类型
    if (!this.isValidTaskType(taskType)) {
      throw new BadRequestException(`不支持的任务类型: ${taskType}`);
    }

    // 验证提示词类型
    if (!['main', 'check', 'target'].includes(promptType)) {
      throw new BadRequestException(`不支持的提示词类型: ${promptType}`);
    }

    // target类型只有spec-to-prod-code任务支持
    if (promptType === 'target' && taskType !== TaskType.SPEC_TO_PROD_CODE) {
      throw new BadRequestException(`任务类型 ${taskType} 不支持 target 提示词`);
    }

    // 查找现有模板
    const existingTemplate = await this.prisma.projectPromptTemplate.findUnique({
      where: {
        projectId_taskType_promptType: {
          projectId,
          taskType,
          promptType,
        },
      },
    });

    if (existingTemplate) {
      // 更新现有模板
      return this.prisma.projectPromptTemplate.update({
        where: { id: existingTemplate.id },
        data: {
          content,
          version: existingTemplate.version + 1,
          updatedBy: createdBy,
        },
      });
    } else {
      // 创建新模板
      return this.prisma.projectPromptTemplate.create({
        data: {
          projectId,
          taskType,
          promptType,
          content,
          createdBy,
        },
      });
    }
  }

  /**
   * 更新项目自定义提示词模板
   */
  async updatePromptTemplate(id: string, data: UpdatePromptTemplateDto): Promise<ProjectPromptTemplate> {
    const template = await this.prisma.projectPromptTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new NotFoundException(`提示词模板 ${id} 不存在`);
    }

    return this.prisma.projectPromptTemplate.update({
      where: { id },
      data: {
        ...data,
        version: template.version + 1,
      },
    });
  }

  /**
   * 删除项目自定义提示词模板
   */
  async deletePromptTemplate(id: string): Promise<void> {
    const template = await this.prisma.projectPromptTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new NotFoundException(`提示词模板 ${id} 不存在`);
    }

    await this.prisma.projectPromptTemplate.update({
      where: { id },
      data: { status: 'archived' },
    });
  }

  /**
   * 获取默认提示词内容
   */
  async getDefaultPromptContent(taskType: string, promptType: 'main' | 'check' | 'target'): Promise<string | null> {
    try {
      const promptsDir = path.join(__dirname, 'prompts');
      let fileName: string;

      if (promptType === 'main') {
        fileName = `${taskType}.md`;
      } else if (promptType === 'check') {
        fileName = `${taskType}-check.md`;
      } else {
        fileName = `${taskType}-target.md`;
      }

      const filePath = path.join(promptsDir, fileName);
      
      if (fs.existsSync(filePath)) {
        return fs.readFileSync(filePath, 'utf-8');
      }

      return null;
    } catch (error) {
      this.logger.error(`获取默认提示词失败: ${taskType}-${promptType}`, error);
      return null;
    }
  }

  /**
   * 重置项目提示词为默认值
   */
  async resetToDefault(projectId: string, taskType: string, promptType: 'main' | 'check' | 'target'): Promise<void> {
    const defaultContent = await this.getDefaultPromptContent(taskType, promptType);
    
    if (!defaultContent) {
      throw new NotFoundException(`找不到 ${taskType}-${promptType} 的默认提示词`);
    }

    // 删除自定义模板（通过设置为archived状态）
    await this.prisma.projectPromptTemplate.updateMany({
      where: {
        projectId,
        taskType,
        promptType,
        status: 'active',
      },
      data: { status: 'archived' },
    });
  }

  /**
   * 获取项目的有效提示词内容（优先使用自定义，否则使用默认）
   */
  async getEffectivePromptContent(
    projectId: string, 
    taskType: string, 
    promptType: 'main' | 'check' | 'target'
  ): Promise<string | null> {
    // 先查找项目自定义模板
    const customTemplate = await this.prisma.projectPromptTemplate.findUnique({
      where: {
        projectId_taskType_promptType: {
          projectId,
          taskType,
          promptType,
        },
        status: 'active',
      },
    });

    if (customTemplate) {
      this.logger.debug(`使用项目 ${projectId} 的自定义 ${taskType}-${promptType} 提示词`);
      return customTemplate.content;
    }

    // 如果没有自定义模板，使用默认模板
    const defaultContent = await this.getDefaultPromptContent(taskType, promptType);
    if (defaultContent) {
      this.logger.debug(`使用默认 ${taskType}-${promptType} 提示词`);
    }

    return defaultContent;
  }

  /**
   * 验证任务类型是否有效
   */
  private isValidTaskType(taskType: string): boolean {
    const validTaskTypes = [
      TaskType.IMG_TO_CODE,
      TaskType.IMG_VISUAL_SPLIT,
      TaskType.IMG_SPLIT_TO_CODE,
      TaskType.COORDS_TO_LAYOUT,
      TaskType.SPEC_TO_PROD_CODE,
      TaskType.DESIGN_TRANSCODE,
      TaskType.DESIGN_MERGE,
    ];

    return validTaskTypes.includes(taskType as TaskType);
  }

  /**
   * 获取任务类型的中文名称
   */
  private getTaskTypeName(taskType: string): string {
    const taskNames: Record<string, string> = {
      [TaskType.IMG_TO_CODE]: '图片转代码',
      [TaskType.IMG_VISUAL_SPLIT]: '图片视觉分割',
      [TaskType.IMG_SPLIT_TO_CODE]: '分割图转代码',
      [TaskType.COORDS_TO_LAYOUT]: '坐标转布局',
      [TaskType.SPEC_TO_PROD_CODE]: '根据应用规范生成生产级代码',
      [TaskType.DESIGN_TRANSCODE]: '设计稿转码',
      [TaskType.DESIGN_MERGE]: '设计稿合并',
    };

    return taskNames[taskType] || taskType;
  }
} 