import { Test, TestingModule } from '@nestjs/testing';
import { Ai<PERSON><PERSON>ingController } from './ai-coding.controller';
import { AiCodingService } from './ai-coding.service';

describe('AiCodingController', () => {
  let controller: AiCodingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiCodingController],
      providers: [AiCodingService],
    }).compile();

    controller = module.get<AiCodingController>(AiCodingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
