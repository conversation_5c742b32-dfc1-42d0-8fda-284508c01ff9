export class CreateAiCodingDto {
  desc: string;
  model?: string;
  projectId?: string;
  user: string;
  isPublic?: boolean;
  type?: string;
  
  // HTML Agent 执行配置
  enableAutoIteration?: boolean; // 是否启用自动迭代
  enableStepByStep?: boolean;    // 是否启用分步执行
  enablePreview?: boolean;       // 是否启用预览功能
}

export class CreateD2CAiCodingDto {
  type: string;
  filename: string;
  template: string;
  user: string;
  isPublic?: boolean;
}

/**
 * 带附件的一体化Chat任务创建DTO
 * 支持multipart/form-data格式，可以同时上传文件和发送聊天消息
 * 
 * @example
 * 前端JavaScript使用示例：
 * ```javascript
 * const formData = new FormData();
 * 
 * // 添加基本信息
 * formData.append('desc', '创建一个React图片展示组件');
 * formData.append('model', 'gpt-4');
 * formData.append('user', 'user123');
 * formData.append('type', 'react');
 * formData.append('enableAutoIteration', 'true');
 * formData.append('enableStepByStep', 'false');
 * 
 * // 添加聊天消息
 * formData.append('messageContent', '请根据这些图片创建一个图片展示组件');
 * formData.append('messageRole', 'user');
 * 
 * // 添加附件文件
 * const fileInput = document.querySelector('#fileInput');
 * for (let file of fileInput.files) {
 *   formData.append('files', file);
 * }
 * 
 * // 发送请求
 * fetch('/ai-coding/chat-task-with-attachments', {
 *   method: 'POST',
 *   body: formData
 * });
 * ```
 * 
 * @example
 * curl命令示例：
 * ```bash
 * curl -X POST "http://localhost:3000/ai-coding/create-common-chat" \
 *   -F "desc=创建React组件" \
 *   -F "model=gpt-4" \
 *   -F "user=user123" \
 *   -F "type=react" \
 *   -F "messageContent=请根据这些图片创建组件" \
 *   -F "files=@image1.png" \
 *   -F "files=@image2.jpg"
 * ```
 * 
 * @example
 * 创建空会话示例：
 * ```bash
 * curl -X POST "http://localhost:3000/ai-coding/chat/create" \
 *   -F "user=user123" \
 *   -F "createEmptySession=true" \
 *   -F "sessionName=我的空会话" \
 *   -F "tags=工作,前端开发,React"
 * ```
 */
export class CreateCommonChatDto {
  // 创建会话的基本信息（通过表单字段传递）
  desc?: string;          // 项目描述，可选，若不提供则使用 messageContent
  model?: string;         // 使用的AI模型，可选，默认使用 htsc::saas-doubao-15-pro-32k
  user: string;          // 用户标识
  projectId?: string;    // 可选：关联的项目ID
  isPublic?: string;     // 是否公开项目（字符串形式，因为form-data都是字符串）
  type?: string;         // 项目类型
  enableAutoIteration?: string;  // 是否启用自动迭代（字符串形式）
  enableStepByStep?: string;     // 是否启用分步执行（字符串形式）
  enablePreview?: string;        // 是否启用预览功能（字符串形式）

  enableCustomPreview?: string; // 是否启用自定义预览功能（字符串形式）
  
  // 聊天消息内容（通过表单字段传递）
  messageContent?: string;       // 消息文本内容，支持空值（创建空会话时）
  messageRole?: string;          // 消息角色，默认为'user'
  
  // 新增：空会话创建相关字段
  createEmptySession?: string;   // 是否创建空会话（字符串形式，'true'/'false'）
  sessionName?: string;          // 会话名称（创建空会话时使用）
  
  // 新增：智能主题生成字段
  smartTheme?: string;           // 是否启用智能主题生成（字符串形式，'true'/'false'，默认为'true'）
  
  // 新增：标签相关字段
  tags?: string;                 // 用户自定义标签，以逗号分隔的字符串形式
  
  // 文件将通过 @UploadedFiles() 装饰器单独处理
  // 支持的文件类型：图片(jpg,png,gif,webp)、文档(pdf,txt,md)等
  // 最大文件数量：10个
  // 单个文件大小限制：由服务器配置决定
}
