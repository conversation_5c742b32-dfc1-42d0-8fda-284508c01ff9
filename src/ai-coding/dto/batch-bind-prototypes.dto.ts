export interface LanhuImageItem {
  id: string;
  name: string;
  pinyinname: string;
  latest_version?: string;
  width?: number;
  height?: number;
  // 其他需要的字段
}

export class BatchBindPrototypesDto {
  projectId: string;
  pageId: string;
  selectedImages: LanhuImageItem[];
}

export interface BatchBindPrototypesResponse {
  success: boolean;
  message: string;
  results: {
    successCount: number;
    failedCount: number;
    createdPrototypes: any[];
    errors?: string[];
  };
}