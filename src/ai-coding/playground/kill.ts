import { type spawn } from 'node:child_process';
import * as treeKill from 'tree-kill';

export function treeKillAsync(
  ps: ReturnType<typeof spawn> | undefined,
  signal: string,
) {
  if (!ps) {
    return Promise.resolve();
  }
  return new Promise<void>((resolve, reject) => {
    treeKill(ps.pid!, signal, (err) => {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });
};
