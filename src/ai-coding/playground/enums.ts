export enum ConfigFileType {
  Readme = 'README',
  PackageJson = 'package.json',
  Env = 'env',
}

export enum FileType {
  Unknown = 0,
  /**
   * A regular file.
   */
  File = 1,
  /**
   * A directory.
   */
  Directory = 2,
  /**
   * A symbolic link to a file.
   */
  SymbolicLink = 64,
}

export enum ViteStartState {
  Init, // 初始化
  Sucess, // 成功
  Failed, // 🔧 新增：失败状态
}

export enum Command {
  DesignToCode, // d2c
}
