import { Controller, Get, Param, Delete } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

@Controller('api/playground')
export class PlaygroundController {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Check if there are any running background tasks for a playground
   */
  @Get(':id/running-tasks')
  async getRunningTasks(@Param('id') playgroundId: string) {
    try {
      // 首先检查是否有已完成的消息
      const completedMessages = await this.prisma.chatMessage.findMany({
        where: {
          playgroundId: playgroundId,
          role: 'ASSISTANT',
          status: {
            in: ['completed', 'failed']
          }
        },
        select: {
          id: true,
          created: true,
          status: true,
          content: true
        },
        orderBy: { created: 'desc' },
        take: 5
      });

      // 如果有已完成的消息，检查是否是最近的消息
      if (completedMessages.length > 0) {
        const latestCompletedMessage = completedMessages[0];
        
        // 检查在已完成消息之后是否还有processing消息
        const laterProcessingMessages = await this.prisma.chatMessage.findMany({
          where: {
            playgroundId: playgroundId,
            role: 'ASSISTANT',
            status: {
              in: ['processing', 'pending']
            },
            created: {
              gt: latestCompletedMessage.created
            }
          }
        });

        // 如果没有更晚的processing消息，说明任务已完成
        if (laterProcessingMessages.length === 0) {
          return {
            hasRunningTasks: false,
            processingMessages: [],
            completedMessages: completedMessages.map(msg => ({
              id: msg.id,
              status: msg.status,
              created: msg.created,
              contentLength: msg.content?.length || 0
            })),
            summary: `找到${completedMessages.length}个已完成的消息，无运行中任务`
          };
        }
      }

      // 检查是否有处理中的消息
      const processingMessages = await this.prisma.chatMessage.findMany({
        where: {
          playgroundId: playgroundId,
          role: 'ASSISTANT',
          status: {
            in: ['processing', 'pending']
          }
        },
        select: {
          id: true,
          created: true,
          status: true,
          content: true
        },
        orderBy: { created: 'desc' }
      });

      // 过滤掉空的处理中消息（可能是无效的占位符）
      const validProcessingMessages = processingMessages.filter(msg => 
        msg.content && msg.content.trim().length > 0
      );

      const hasRunningTasks = validProcessingMessages.length > 0;

      return {
        hasRunningTasks,
        processingMessages: validProcessingMessages.map(msg => ({
          id: msg.id,
          status: msg.status,
          created: msg.created,
          contentLength: msg.content?.length || 0
        })),
        completedMessages: completedMessages.map(msg => ({
          id: msg.id,
          status: msg.status,
          created: msg.created,
          contentLength: msg.content?.length || 0
        })),
        summary: hasRunningTasks 
          ? `发现${validProcessingMessages.length}个运行中任务` 
          : '无运行中任务'
      };
    } catch (error) {
      console.error('检查运行中任务时出错:', error);
      return {
        hasRunningTasks: false,
        error: error.message,
        summary: '检查运行中任务时出错'
      };
    }
  }

  /**
   * Clean up orphaned processing messages for a playground
   */
  @Delete(':id/clean-orphaned-messages')
  async cleanOrphanedMessages(@Param('id') playgroundId: string) {
    try {
      // 查找所有处理中状态的空消息
      const orphanedMessages = await this.prisma.chatMessage.findMany({
        where: {
          playgroundId: playgroundId,
          role: 'ASSISTANT',
          status: {
            in: ['processing', 'pending']
          },
          OR: [
            { content: '' },
            { content: null },
            { content: { contains: '' } }
          ]
        },
        select: {
          id: true,
          content: true,
          status: true,
          created: true
        }
      });

      // 检查是否有已完成的消息
      const completedMessages = await this.prisma.chatMessage.findMany({
        where: {
          playgroundId: playgroundId,
          role: 'ASSISTANT',
          status: {
            in: ['completed', 'failed']
          }
        },
        select: {
          id: true,
          created: true
        },
        orderBy: { created: 'desc' },
        take: 1
      });

      let deletedCount = 0;
      const deletedIds = [];

      // 如果有已完成的消息，删除所有orphaned的processing消息
      if (completedMessages.length > 0) {
        for (const orphaned of orphanedMessages) {
          if (!orphaned.content || orphaned.content.trim() === '') {
            await this.prisma.chatMessage.delete({
              where: { id: orphaned.id }
            });
            deletedIds.push(orphaned.id);
            deletedCount++;
          }
        }
      }

      return {
        success: true,
        deletedCount,
        deletedIds,
        orphanedFound: orphanedMessages.length,
        hasCompletedMessages: completedMessages.length > 0,
        summary: `清理完成：删除了${deletedCount}个orphaned消息`
      };
    } catch (error) {
      console.error('清理orphaned消息时出错:', error);
      return {
        success: false,
        error: error.message,
        summary: '清理orphaned消息时出错'
      };
    }
  }
}