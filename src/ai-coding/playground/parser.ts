import { marked } from "marked";
import { DOMParser } from 'xmldom';
import dedent from 'dedent';

export interface ToolCall {
  name: string;
  arguments?: Record<string, any>;
}

export type MessageBlock = {
  type: 'text';
  text: string;
} | {
  type: 'tool';
  name: string;
  arguments?: Record<string, string>;
} | {
  type: 'code';
  code: string;
  lang?: string;
} | {
  type: 'diff';
  diff: string;
  closed: boolean;
} | {
  type: 'files';
  list: string[];
  files?: {
    path: string;
    content: string;
  }[];
  closed: boolean;
}

function parseFileList(str: string, shouldDetectTruncation: boolean = false): {
  list: string[];
  files: { path: string; content: string }[];
} {
  const ast = marked.lexer(str);
  const list: string[] = [];
  const files: { path: string; content: string }[] = [];
  let heading: string = '';
  let currentFilePath: string = '';
  
  // 提取文件路径的函数 - 增强版
  const extractFilePath = (headingText: string): string => {
    // 去除markdown格式符号和多余的符号
    const cleanText = headingText.replace(/\*\*/g, '').replace(/[：:]+$/, '').trim();
    
    // 格式1: "文件：index.html"
    if (cleanText.startsWith('文件：')) {
      const result = cleanText.slice(3);
      return result;
    }
    
    // 格式2: "index.html 文件格式" 或 "styles.css 文件格式"
    const fileFormatMatch = cleanText.match(/^(.+?)\s*文件格式?$/);
    if (fileFormatMatch) {
      const result = fileFormatMatch[1];
      return result;
    }
    
    // 格式3: "步骤X完成 - 标题" 后面跟随的文件名
    const stepCompleteMatch = cleanText.match(/^步骤\d+完成\s*-?\s*(.+)$/);
    if (stepCompleteMatch) {
      // 这是步骤完成标题，不是文件名，返回空
      return '';
    }
    
    // 格式4: 直接的文件名（如果包含常见文件扩展名）
    if (/\.(html?|css|js|jsx|ts|tsx|vue|json|md)$/i.test(cleanText)) {
      return cleanText;
    }
    
    // 格式5: "index.html" 或 "styles.css" (简单文件名标题)
    if (/^(index\.html|styles\.css|main\.js|app\.jsx|component\.tsx)$/i.test(cleanText)) {
      return cleanText;
    }
    
    return '';
  };
  
  // 判断文本是否为CSS代码
  const isCSSCode = (text: string): boolean => {
    const trimmedText = text.trim();
    // CSS代码特征：以{开头，包含CSS属性，以}结尾
    return /^\s*\{[\s\S]*\}\s*$/.test(trimmedText) || 
           /^\s*[a-zA-Z-]+\s*:\s*[^;]+;/.test(trimmedText) ||
           /margin\s*:\s*\d+|padding\s*:\s*\d+|box-sizing\s*:\s*/.test(trimmedText);
  };
  
  // 智能推断文件名的函数
  const inferFileName = (lang: string, content: string): string => {
    if (lang === 'html' || content.includes('<!DOCTYPE') || content.includes('<html')) {
      return files.some(f => f.path === 'index.html') ? '' : 'index.html';
    }
    if (lang === 'css' || isCSSCode(content)) {
      return files.some(f => f.path === 'styles.css') ? '' : 'styles.css';
    }
    if (lang === 'javascript' || lang === 'js') {
      return files.some(f => f.path === 'main.js') ? '' : 'main.js';
    }
    return '';
  };
  
  ast.forEach((e, idx) => {
    const { type } = e;
    
    if (type === 'heading') {
      heading = e.text;
      
      // 尝试提取文件路径
      const filePath = extractFilePath(heading);
      
      if (filePath) {
        currentFilePath = filePath;
      } else {
        // 检查是否是步骤标题，如果是则保持当前文件上下文
        const isStepTitle = /^步骤\d+|第\d+步|^\d+[\.、]/.test(heading);
        
        // 检查是否是解释性标题，这些标题应该重置文件上下文
        const isExplanationTitle = /^(说明|注意|提示|重要|关键|总结|分析|优化|特点|要求|规范|约束|验证|检查|测试|效果|结果|完成|下一步|接下来|然后|最后|另外|此外|同时|同样|相比|对比|与此|因此|所以|总之|综上|整体|全面|详细|具体|明确|清楚|简单|复杂|高级|基础|核心|主要|次要|可选|必须|建议|推荐|最佳|优先|首选|备选|替代|改进|升级|更新|修改|调整|完善|增强|扩展|简化|精简|统一|标准|规范|通用|专用|定制|自定义)/.test(heading);
        
        if (!isStepTitle || isExplanationTitle) {
          currentFilePath = ''; // 重置文件上下文
        }
      }
    } else if (type === 'list') {
      list.push(...e.items.map((i: any) => i.text));
    } else if (type === 'code') {
      let targetFilePath = currentFilePath;
      
      if (!targetFilePath) {
        // 尝试根据代码语言和内容推断文件名
        targetFilePath = inferFileName(e.lang || '', e.text);
      }
      
      if (targetFilePath) {
        // 检查是否已经有这个文件，如果有则合并内容
        const existingFileIndex = files.findIndex(f => f.path === targetFilePath);
        if (existingFileIndex >= 0) {
          // 避免重复内容
          if (!files[existingFileIndex].content.includes(e.text.trim())) {
            files[existingFileIndex].content += '\n' + e.text;
          }
        } else {
          files.push({
            path: targetFilePath,
            content: e.text,
          });
        }
      }
    } else if (type === 'html') {
      let targetFilePath = currentFilePath || 'index.html';
      
      const existingFileIndex = files.findIndex(f => f.path === targetFilePath);
      if (existingFileIndex >= 0) {
        if (!files[existingFileIndex].content.includes(e.text.trim())) {
          files[existingFileIndex].content += '\n' + e.text;
        }
      } else {
        files.push({
          path: targetFilePath,
          content: e.text,
        });
      }
    } else if (type === 'paragraph') {
      // 处理paragraph类型的token，但要更加谨慎
      // 只有在非常明确的情况下才将段落文本添加到文件中
      if (currentFilePath && e.text.trim().length > 10) {
        const text = e.text.trim();
        
        // 严格排除中文解释文本和其他非代码文本
        const isChineseText = /[\u4e00-\u9fa5]/.test(text);
        const isExplanationText = /^(步骤|注意|说明|提示|这里|我们|现在|接下来|然后|最后|抱歉|由于|因为|所以|但是|不过|如果|当|的|了|在|为|是|有|可以|需要|应该|请|确认|继续|执行|完成|输出|内容|文件|代码|验证|检查|清单|要求|约束|条件|特点|效果|结果|下一步|接下来|剩余|分批|次|稍等|太大|无法|一次性|截断|被|将|会|已|将继续|完成|的|输出)/.test(text);
        const isErrorText = /极抱歉|截断|太大|无法|一次性|分批|次|稍等/.test(text);
        const isInstructionText = /请|确认|是否|可以|继续|执行|步骤|建议|记录|优化|后续|逐步/.test(text);
        
        // 更宽松的代码检查：如果当前文件路径明确，且内容看起来像代码，就添加
        const looksLikeCode = 
          !isExplanationText && 
          !isErrorText && 
          !isInstructionText &&
          (
            // CSS代码特征 - 放宽检查，只要当前文件是CSS就接受
            (currentFilePath.endsWith('.css') && (
              /^\s*[a-zA-Z][a-zA-Z0-9-]*\s*:\s*[^;]+;?\s*$/.test(text) ||
              /^\s*\/\*[\s\S]*?\*\/\s*$/.test(text) ||
              /^\s*[.#@][a-zA-Z][a-zA-Z0-9-]*\s*\{\s*$/.test(text) ||
              /^\s*\}\s*$/.test(text) ||
              /^\s*@[a-zA-Z]+/.test(text) ||
              // 新增：更宽松的CSS检测
              /[.#][a-zA-Z-_][a-zA-Z0-9-_]*\s*\{/.test(text) ||
              /[a-zA-Z-]+\s*:\s*[^;]+[;}]/.test(text) ||
              text.includes('{') || text.includes('}') ||
              text.includes('/*') || text.includes('*/')
            )) ||
            // HTML代码特征
            (currentFilePath.endsWith('.html') && (
              /^<[a-zA-Z][^>]*>.*<\/[a-zA-Z][^>]*>\s*$/.test(text) ||
              /^<[a-zA-Z][^>]*\/>\s*$/.test(text) ||
              /^<!DOCTYPE\s+html>/i.test(text) ||
              text.includes('<') && text.includes('>')
            )) ||
            // JavaScript代码特征
            ((currentFilePath.endsWith('.js') || currentFilePath.endsWith('.ts')) && (
              /^(var|let|const|function|class|import|export)\s/.test(text) ||
              /^\s*\/\/[^\u4e00-\u9fa5]*$/.test(text) ||
              /^\s*\/\*[^\u4e00-\u9fa5]*?\*\/\s*$/.test(text) ||
              text.includes('function') || text.includes('=>') || text.includes('const') || text.includes('let') || text.includes('var')
            ))
          );
        
        if (looksLikeCode) {
          const existingFileIndex = files.findIndex(f => f.path === currentFilePath);
          if (existingFileIndex >= 0) {
            if (!files[existingFileIndex].content.includes(text)) {
              files[existingFileIndex].content += '\n' + text;
            }
          } else {
            files.push({
              path: currentFilePath,
              content: text,
            });
          }
        }
      }
    }
  });
  
  // 后处理：只有在明确需要截断检测时才进行严格的内容清理
  const cleanedFiles = shouldDetectTruncation ? files.map(file => {
    let content = file.content.trim();
    
    // 检测并修复所有代码文件中的截断和中文混合问题
    const isCodeFile = /\.(html?|css|js|jsx|ts|tsx|vue|json|md|py|java|cpp|c|go|rs|php)$/i.test(file.path);
    
    if (isCodeFile) {
      // 分行处理代码文件内容
      const lines = content.split('\n');
      const cleanedLines: string[] = [];
      
      for (let line of lines) {
        const trimmedLine = line.trim();
        
        // 检测截断标识符（极抱歉、截断、太大等）
        if (/极抱歉|截断|太大|无法|一次性|分批|次|稍等|我的回答|被截断|将继续|请稍等/.test(trimmedLine)) {
          break; // 遇到截断标识就停止处理后续内容
        }
        
        // 根据文件类型进行特定检查
        if (file.path.endsWith('.css')) {
          // CSS文件：跳过包含中文字符的行（除了注释）
          if (/[\u4e00-\u9fa5]/.test(trimmedLine) && !trimmedLine.startsWith('/*')) {
            continue;
          }
          
          // 检测CSS属性值中的异常内容 - 放宽检测条件
          const cssPropertyMatch = trimmedLine.match(/^([a-zA-Z-]+)\s*:\s*(.+);?\s*$/);
          if (cssPropertyMatch) {
            const [, property, value] = cssPropertyMatch;
            // 只过滤明显的错误内容，允许正常的CSS值
            if (/极抱歉|截断|无法一次性|我的回答被截断/.test(value)) {
              continue;
            }
          }
          
          // 检测不完整的CSS规则 - 放宽条件，允许更多有效CSS
          if (trimmedLine.includes(':') && !trimmedLine.includes(';') && !trimmedLine.includes('{') && !trimmedLine.includes('}')) {
            // 只过滤明显被截断的行，保留可能的有效CSS属性（如calc()、url()等）
            if (trimmedLine.length > 50 && !/\s(url|rgba?|hsla?|calc|var|attr|linear-gradient|radial-gradient)\(/.test(trimmedLine) && /[\u4e00-\u9fa5]/.test(trimmedLine)) {
              continue;
            }
          }
        } else if (file.path.endsWith('.html')) {
          // HTML文件：检测标签中的中文混合内容
          if (/<[^>]*[\u4e00-\u9fa5][^>]*>/.test(trimmedLine) && !trimmedLine.includes('<!--')) {
            continue;
          }
          
          // 检测不完整的HTML标签
          if (trimmedLine.includes('<') && !trimmedLine.includes('>') && trimmedLine.length > 20) {
            continue;
          }
        } else if (/\.(js|jsx|ts|tsx)$/.test(file.path)) {
          // JavaScript/TypeScript文件：检测代码中的中文混合内容
          
          // 跳过包含中文的非注释行（除了字符串字面量）
          if (/[\u4e00-\u9fa5]/.test(trimmedLine) && 
              !trimmedLine.startsWith('//') && 
              !trimmedLine.startsWith('/*') && 
              !trimmedLine.startsWith('*') &&
              !/['"`][\u4e00-\u9fa5]+['"`]/.test(trimmedLine)) {
            continue;
          }
          
          // 检测不完整的语句（没有分号或大括号结束）
          if (trimmedLine.length > 30 && 
              !trimmedLine.endsWith(';') && 
              !trimmedLine.endsWith('{') && 
              !trimmedLine.endsWith('}') && 
              !trimmedLine.endsWith(',') &&
              !trimmedLine.startsWith('//') &&
              !trimmedLine.startsWith('import') &&
              !trimmedLine.startsWith('export') &&
              !/^\s*(if|for|while|function|class|const|let|var)\s/.test(trimmedLine)) {
            continue;
          }
        } else if (file.path.endsWith('.json')) {
          // JSON文件：检测格式错误
          if (/[\u4e00-\u9fa5]/.test(trimmedLine) && !/"\s*:\s*"[\u4e00-\u9fa5]+"|"[\u4e00-\u9fa5]+"\s*:/.test(trimmedLine)) {
            continue;
          }
        }
        
        // 通用检查：检测明显的错误内容
        if (/^(步骤|注意|说明|提示|这里|我们|现在|接下来|然后|最后|由于|因为|所以|但是|不过|如果|当|的|了|在|为|是|有|可以|需要|应该|请|确认|继续|执行|完成|输出|内容|文件|代码|验证|检查)/.test(trimmedLine)) {
          continue;
        }
        
        cleanedLines.push(line);
      }
      
      content = cleanedLines.join('\n').trim();
    }
    
    // 通用截断内容检测（适用于所有文件类型）
    if (/极抱歉|截断|太大|无法|一次性|分批|次|稍等|我的回答被截断|将继续完成|请稍等/.test(content)) {
      // 找到截断位置并移除之后的所有内容
      const truncationPatterns = [
        /极抱歉[\s\S]*/,
        /截断[\s\S]*/,
        /太大[\s\S]*/,
        /无法一次性[\s\S]*/,
        /我的回答被截断[\s\S]*/,
        /将继续完成[\s\S]*/
      ];
      
      for (const pattern of truncationPatterns) {
        const match = content.match(pattern);
        if (match) {
          content = content.substring(0, match.index).trim();
          break;
        }
      }
    }
    
    return {
      ...file,
      content
    };
  }).filter(file => file.content.length > 0) : files.filter(file => file.content.trim().length > 0);
  
  // 确保list包含所有解析出的文件路径
  const filePathsFromFiles = cleanedFiles.map(file => file.path);
  const uniqueList = Array.from(new Set([...list, ...filePathsFromFiles]));
  
  return { list: uniqueList, files: cleanedFiles };
}

const markupStartReg = new RegExp('^(?:\\s*)((?<code>```)(?<lang>.*)?|(?<tool><tool>)|(?<files><files>)|(?<diff><diff>))', 'gm');

export function textParser(message: string, parseMarkdown = true, shouldDetectTruncation = false) {
  let match: RegExpExecArray | null;
  let lastIndex = 0;
  const blocks: MessageBlock[] = [];

  const stack: {
    state: 'text' | 'code' | 'files' | 'diff' | 'tool';
    startMatch: RegExpExecArray | null;
  }[] = [
    {
      state: 'text',
      startMatch: null,
    },
  ];

  const getReg = (state: string, i: number) => {
    let reg: RegExp;
    if (state === 'text') {
      reg = markupStartReg;
    } else if (state === 'code') {
      reg = /^(?:\s*)(?:```|(?<tool><tool>)|(?<files><files>)|(?<diff><diff>))/gm;
    } else if (state === 'tool') {
      reg = /^(?:\s*)<\/tool>/gm;
    } else if (state === 'files') {
      reg = /^(?:\s*)<\/(?<files>files)>/gm;
    } else if (state === 'diff') {
      reg = /^(?:\s*)<\/(?<diff>diff)>/gm;
    } else {
      throw new Error(`Invalid state: ${state}`);
    }
    reg.lastIndex = i;
    return reg;
  };

  const appendPart = (text: string, match: RegExpExecArray | null = null, last = false) => {
    const trimed = text.trim();
    const topStack = stack[stack.length - 1];
    if (topStack.state === 'text') {
      if (trimed) {
        blocks.push({
          type: 'text',
          text: parseMarkdown ? marked.parse(trimed, {
            async: false,
          }) : trimed,
        } satisfies MessageBlock);
      }
      if (match?.groups?.files) {
        // start of files block
        stack.push({
          state: 'files',
          startMatch: match,
        });
      } else if (match?.groups?.diff) {
        // start of diff block
        stack.push({
          state: 'diff',
          startMatch: match,
        });
      } else if (match?.groups?.tool) {
        // start of tool block
        stack.push({
          state: 'tool',
          startMatch: match,
        });
      } else if (match?.groups?.code) {
        // start of code block
        stack.push({
          state: 'code',
          startMatch: match,
        });
      }
    } else if (topStack.state === 'code') {
      // In case xml tag appear inside triple backticks
      if (trimed) {
        blocks.push({
          lang: topStack.startMatch?.groups?.lang,
          type: 'code',
          code: trimed,
        });
      }
      if (match?.groups?.files) {
        // enter files block
        stack.push({
          state: 'files',
          startMatch: match,
        });
      } else if (match?.groups?.diff) {
        // enter diff block
        stack.push({
          state: 'diff',
          startMatch: match,
        });
      } else if (match?.groups?.tool) {
        // enter tool block
        stack.push({
          state: 'tool',
          startMatch: match,
        });
      } else {
        // exit code block
        stack.pop();
      }
    } else if (topStack.state === 'tool') {
      if (trimed) {
        // end of tool block
        const xml = parseToolArgumentsFromXml(new DOMParser().parseFromString(`<tool>${trimed}</tool>`, 'text/xml'));
        if (xml.name && xml.arguments) {
          blocks.push({
            type: 'tool',
            ...xml,
          });
        }
        stack.pop();
      }
    } else if (topStack.state === 'files') {
      // end of files block
      if (trimed) {
        const { list, files } = parseFileList(trimed, shouldDetectTruncation);
        blocks.push({
          type: 'files',
          list,
          files,
          closed: !last,
        });
      }
      stack.pop();
    } else if (topStack.state === 'diff') {
      // end of diff block
      if (trimed) {
        blocks.push({
          type: 'diff',
          diff: trimed,
          closed: !last,
        });
      }
      stack.pop();
    }
  };

  let reg: RegExp;
  while (((reg = getReg(stack[stack.length - 1].state, lastIndex)), (match = reg.exec(message)))) {
    const text = message.slice(lastIndex, match.index);
    appendPart(text, match);
    lastIndex = reg.lastIndex;
  }

  if (lastIndex < message.length) {
    const text = message.slice(lastIndex);
    appendPart(text, null, true);
  }

  return blocks;
}

export function stringifyBlocks(blocks: MessageBlock[]) {
  return blocks.map((block) => {
    if (block.type === 'text') {
      return block.text;
    } else if (block.type === 'tool') {
      return dedent`<tool>
        <name>${block.name}</name>
        <arguments>${JSON.stringify(block.arguments)}</arguments>
      </tool>`;
    } else if (block.type === 'code') {
      return `\`\`\`${block.lang}\n${block.code}\n\`\`\``;
    } else if (block.type === 'files') {
      return `<files>
# 文件列表
${block.list.map((f) => `\- ${f}`).join('\n')}
</files>`;
    } else if (block.type === 'diff') {
      return `<diff>
${block.diff}
</diff>`;
    }
  }).join('\n');
}

export type AgentMessage =
  | string
  | {
      text?: string;
      reasoningText?: string;
      files?: File[];
    };

export type Message =
  | {
      role: 'user' | 'assistant';
      parts: MessageBlock[];
    }
  | string;

export interface File {
  name: string;
  type: string;
  size: number;
  url: string;
}

interface RecordData {
  [key: string]: string | RecordData;
}

function parseToolArgumentsFromXml(doc: Document) {
  const toolNode = doc.getElementsByTagName('tool')[0];
  const parseDom = (el: Node) => {
    if (el.childNodes.length === 0) return '';
    if (el.childNodes.length === 1 && el.childNodes[0].nodeType === 3) {
      return el.textContent ?? '';
    }
    const data: RecordData = {};
    Array.from(el.childNodes).forEach((child) => {
      if (child.nodeType === 1) {
        data[child.nodeName] = parseDom(child);
      }
    });
    return data;
  };
  return parseDom(toolNode) as {
    name: string;
    arguments: Record<string, string>;
  };
}
