import { exec } from 'node:child_process';
import ctx from './ctx';

// 执行Git指令方法
export function executeGitCommand(
  command: string,
  cwd: string = process.cwd()
): Promise<{ stdout: string, stderr: string }> {
  return new Promise((resolve, reject) => {
    ctx.log(`执行Git 指令: ${command}`);
    exec(`git ${command}`, { cwd }, (error, stdout, stderr) => {
      if (error) {
        // 如果执行失败，返回错误
        ctx.log(`Git 指令执行失败: ${error.message}`);
        reject(error.message);
        return;
      }
      ctx.log(`Git output: ${stdout}`);
      resolve({ stdout, stderr });
    });
  });
}
