import { spawn } from 'node:child_process';
import * as fs from 'node:fs';
import { mkdir, rename, utimes } from 'node:fs/promises';
import * as path from 'node:path';
import async from 'async';
import axios from 'axios';
import { parse } from 'dotenv';
import * as FormData from 'form-data';
import { copy, remove, readFileSync } from 'fs-extra';
import { glob } from 'glob';
import { split, map, trim, size } from 'lodash';
import * as mime from 'mime';
import * as nunjucks from 'nunjucks';
import * as shelljs from 'shelljs';
import { createServer, ViteDevServer, build } from 'vite';
import { TEMPLATE_VUE,TEMPLATE_VUE2, TEMPLATE_REACT, TEMPLATE_LITE, TEMPLATE_HTML } from './constants';
import ctx, { playgroundRootPath } from './ctx';
import { importD2C } from './d2c';
import { Command, ConfigFileType, FileType, ViteStartState } from './enums';
import { readFile, writeFile, appendToFile, modifyJson, readDirectory, zipDirectory, unzip, exists } from './fs';
import { executeGitCommand } from './git';
import { isBinaryFile } from './utils/file';
import { runYarnCommand } from './yarn';

// FIXME: 当前基于内存的 PortManager 不适用于 PM2 集群模式等多进程环境。
// 在多进程设置下，每个工作进程都会有自己独立的管理器实例，这将导致端口分配冲突。
// 为了使其在生产环境的集群中稳定运行，应将其替换为使用共享状态管理器（如 Redis）的方案，
// 以确保所有进程之间的端口操作是原子性的且无冲突。
class PortManager {
  private availablePorts: Set<number>;
  private playgroundPortMap: Map<string, number>; // 🔧 新增：playground到端口的固定映射

  constructor(startPort: number, endPort: number) {
    this.availablePorts = new Set();
    this.playgroundPortMap = new Map(); // 🔧 初始化映射表
    for (let i = startPort; i <= endPort; i++) {
      this.availablePorts.add(i);
    }
  }

  allocatePort(playgroundId?: string): number | null {
    // 🔧 优先从已分配的端口中获取
    if (playgroundId && this.playgroundPortMap.has(playgroundId)) {
      const existingPort = this.playgroundPortMap.get(playgroundId);
      ctx.log(`🔄 [PortManager] 复用playground ${playgroundId} 的现有端口: ${existingPort}`);
      return existingPort;
    }

    if (this.availablePorts.size === 0) {
      return null; // No available ports
    }
    
    const port = this.availablePorts.values().next().value;
    this.availablePorts.delete(port);
    
    // 🔧 建立playground到端口的映射
    if (playgroundId) {
      this.playgroundPortMap.set(playgroundId, port);
      ctx.log(`📌 [PortManager] 为playground ${playgroundId} 分配固定端口: ${port}。剩余可用端口: ${this.availablePorts.size}`);
    } else {
      ctx.log(`🔢 [PortManager] 分配临时端口: ${port}。剩余可用端口: ${this.availablePorts.size}`);
    }
    
    return port;
  }

  freePort(port: number, playgroundId?: string): void {
    if (port) {
      // 🔧 只有明确释放时才删除映射关系
      if (playgroundId && this.playgroundPortMap.get(playgroundId) === port) {
        this.playgroundPortMap.delete(playgroundId);
        ctx.log(`🗑️ [PortManager] 释放playground ${playgroundId} 的端口映射: ${port}`);
      }
      
      this.availablePorts.add(port);
      ctx.log(`🔓 [PortManager] 释放端口: ${port}。剩余可用端口: ${this.availablePorts.size}`);
    }
  }

  // 🔧 新增：获取playground当前使用的端口
  getPlaygroundPort(playgroundId: string): number | null {
    return this.playgroundPortMap.get(playgroundId) || null;
  }

  // 🔧 新增：检查playground是否已分配端口
  hasPlaygroundPort(playgroundId: string): boolean {
    return this.playgroundPortMap.has(playgroundId);
  }
}

const portManager = new PortManager(10001, 10100);

type PlaygroundInfo = {
  id: string;
  type: string;
  desc: string;
  name: string;
  tags?: string[];
  after?: [Command, Record<string, any>];
};

type ViteServerContext = {
  server: ViteDevServer;
  state: ViteStartState;
  error?: string; // 🔧 新增错误字段
};

type Task = {
  type: 'runYarnCommand' | 'addCommand' | 'removeCommand';
  id: string;
  depsText?: string;
};

const queue = async.queue((task: Task, done) => {
  (async () => {
    if (task.type === 'runYarnCommand') {

      const sourceDir = playgroundRootPath(task.id);
      if (!exists(sourceDir)) {
        done('Playground not found');
        return;
      }

      let ps = runYarnCommand(task.id, 'yarn');
      ps.on('error', (err) => {
        done(err);
      });
      ps.on('exit', (code) => {
        ctx.log(`Playground(${task.id}) install exits with code ${code}`);
        done(null, code);
      });
      ps.stderr.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
        ctx.sendLogToSse(task.id, string);
      });
      ps.stdout.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
        ctx.sendLogToSse(task.id, string);
      });
    } else if (task.type === 'addCommand' || task.type === 'removeCommand') {
      const projectUri: string = playgroundRootPath(task.id);

      const cmd: string = `yarn ${task.type === 'addCommand' ? 'add' : 'remove'} ${task.depsText}`;
      ctx.log(`Running command ${cmd} in directory ${projectUri}`);

      const ps = spawn(cmd, {
        shell: true,
        cwd: projectUri,
        env: {
          ...process.env,
          NODE_ENV: 'development', // Need to be development for yarn to install devDependencies
        },
      });
      ps.on('exit', (code) => {
        ctx.log(`Playground(${task.id}) install deps exits with code ${code}`);
        done(null, code);
      });
      ps.stderr.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
        ctx.sendLogToSse(task.id, string);
      });
      ps.stdout.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
        ctx.sendLogToSse(task.id, string);
      });
    }
  })();
}, 1);

const viteServerContextMap: Record<string, ViteServerContext> = {}; // playground id -> ViteServerContext
const viteServerActiveMap: Record<string, number> = {}; // playground id -> last active time
const CHECK_INTERVAL = 1000 * 60 * 5; // 5 minutes
const INACTIVE_TIMEOUT = 1000 * 60 * 60 * 12; // 12 hours

// 修改 package.json
function modifyPackageJson(config: Record<string, any>, name: string, desc: string, type: string) {
  if (name) config.name = name;
  if (desc) config.description = desc;
  let dependencies: Record<string, string>, devDependencies: Record<string, string>;
  if (type === TEMPLATE_REACT) {
    dependencies = {
      react: '^18.3.1',
      'react-dom': '^18.3.1',
    };
    devDependencies = {
      '@types/react': '^18.3.11',
      '@types/react-dom': '^18.3.1',
    };
  } else if (type === TEMPLATE_VUE) {
    dependencies = {
      vue: '^3.5.12',
    };
  } else if (type === TEMPLATE_VUE2) {
    dependencies = {
      vue: '^2.7.16',
    };
  } else if (type === TEMPLATE_LITE) {
    dependencies = {
      lit: '^3.2.1',
    };
  } else if (type === TEMPLATE_HTML) {
    // HTML模板不需要特殊依赖
    dependencies = {};
    devDependencies = {};
  }
  config.dependencies = {
    ...config.dependencies,
    ...dependencies,
  };
  config.devDependencies = {
    ...config.devDependencies,
    ...devDependencies,
  };
}

export async function createPlayground(playgroundInfo: PlaygroundInfo): Promise<{ id: string }> {
  ctx.log(`createPlayground info: ${JSON.stringify(playgroundInfo)}`);
  const { id, type, desc, name, tags, after } = playgroundInfo;
  await mkdir(playgroundRootPath(), { recursive: true });
  const workspaceSrc = path.join(ctx.assetDir, 'templates', 'workspace-template');
  const target = playgroundRootPath(id);
  const files = await glob('**/*.tmpl', {
    cwd: workspaceSrc, 
    absolute: true,
    ignore: ['node_modules/**'],
  });
  
  try {
    await Promise.all(files.map(async tmplPath => {
      const tmplContent = await readFile(tmplPath, 'utf-8');
      const tmplConfig = {
        type, // tmpl 模板变量
      };
      
      const renderedContent = nunjucks.renderString(tmplContent, tmplConfig);
      const targetPath = tmplPath.replace(/\.tmpl$/, '');
      await mkdir(path.dirname(targetPath), { recursive: true });
      await writeFile(targetPath, renderedContent);
    }));

    // 删掉模板文件
    // await Promise.all(files.map(async tmplPath => {
    //   await unlink(tmplPath);
    // }));
  } catch (error) {
    ctx.log(`error: ${error}`);
  }

  await copy(workspaceSrc, target, { overwrite: true });

  // 写 package.json 信息
  await modifyJson<Record<string, any>>(target, 'package.json', {}, (config) => {
    // Package name must be globally unique to be used in workspace.
    // Too many node_modules will cripple NAS performance.
    modifyPackageJson(config, `${name}-${id}`, desc, type);
    return true;
  });

  // Create env file
  await writeFile(path.join(target, '.env'), `VITE_PLAYGROUND_TYPE=${type}`);

  // Copy package
  const pkgSrc = path.join(ctx.assetDir, 'templates', type + '-template', 'src');
  
  // 检查源目录是否存在，如果存在才进行复制
  if (await exists(pkgSrc)) {
    await copy(pkgSrc, path.join(target, 'src'), { overwrite: true });
  } else {
    // 如果源目录不存在，创建一个空的src目录
    await mkdir(path.join(target, 'src'), { recursive: true });
    ctx.log(`Template src directory not found for ${type}, created empty src directory`);
  }

  // 初始git
  await executeGitCommand('init', target);

  // Run after hook if any
  if (after) {
    const [command, args] = after;
    if (command === Command.DesignToCode) {
      // Only aiCoderImportD2C supported for now
      await importD2C(args.filename, id, args.type);
    }
  }
  return { id };
}

export async function startPlayground(id: string, restart?: boolean, hostname?: string): Promise<{ url: string }> {
  ctx.log(`startPlayground id: ${id} restart: ${restart} hostname: ${hostname}`);

  // 🔧 提取previewUrl函数，避免重复代码
  const previewUrl = (port: number) => {
    // 根据hostname选择不同的预览URL配置
    // v0.web.htsc 和 v0.fe.htsc都走 /preview/{port}/ 格式 的预览路径
    // FIXME，生成还有一套ai-coding.fe.htsc的配置，所以这里会这么写
    if (['v0.web.htsc', 'v0.fe.htsc'].includes(hostname)) {
      const v0PreviewUrl = process.env.V0_PREVIEW_BASE_URL;
      if (v0PreviewUrl) {
        // 确保 URL 以 /preview/{port}/ 格式，与新的 base 配置保持一致
        let url = v0PreviewUrl.replace('{port}', String(port));
        if (!url.endsWith('/')) {
          url += '/';
        }

        if(hostname === 'v0.fe.htsc'){
          url = url.replace('v0.web.htsc', 'v0.fe.htsc');
        }
        return url;
      }
      return `http://localhost:${port}/preview/`;
    }
    
    // 默认配置 - 使用新的 /preview/{port} 格式
    const defaultPreviewUrl = process.env.PREVIEW_URL;
    if (defaultPreviewUrl) {
      // 支持两种模板格式：
      // 1. 新格式：http://domain/preview/{port} 
      // 2. 兼容旧格式：http://domain/{port} (会自动转换为 /preview/{port})
      let url = defaultPreviewUrl.replace('{port}', String(port));
      
      // 如果URL不包含 /preview/，则自动添加
      if (!url.includes('/preview/')) {
        // 将 /{port} 替换为 /preview/{port}
        url = url.replace(`/${port}`, `/preview/${port}`);
      }
      
      if (!url.endsWith('/')) {
        url += '/';
      }
      return url;
    }
    return `http://localhost:${port}/preview/`;
  };

  // 🔧 第一步：检查playground类型（最优先处理）
  let playgroundType: string;
  try {
    // 从环境变量获取playground类型（如果存在）
    const envPath = playgroundRootPath(id, '.env');
    if (await exists(envPath)) {
      const envContent = await readFile(envPath);
      const envData = parse(envContent as string);
      playgroundType = envData.VITE_PLAYGROUND_TYPE;
      ctx.log(`[Playground] ${id} detected type: ${playgroundType}`);
    }
  } catch (error) {
    ctx.log(`无法读取playground类型，使用默认行为: ${error}`);
  }

  // 🔧 如果是HTML类型的playground，直接返回文件访问URL（不需要启动Vite服务）
  if (playgroundType === 'html') {
    ctx.log(`HTML类型playground，返回直接文件访问URL: ${id}`);
    
    // 构建直接文件访问的URL
    const directUrl = `/api/background-tasks/download/${id}/index.html`;
    
    return { url: directUrl };
  }

  // 🔧 第二步：检查并发状态，防止重复启动（仅对需要Vite服务的playground）
  const existingContext: ViteServerContext = viteServerContextMap[id];
  
  // vite server 在启动中，等待 server 启动，不再启动
  if (existingContext && existingContext.state === ViteStartState.Init) {
    ctx.log(`vite server ${id} is starting, wait for it to start`);
    
    // 等待服务启动完成，最多等待30秒
    let waitCount = 0;
    while (waitCount < 30) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      waitCount++;
      
      const currentContext = viteServerContextMap[id];
      if (currentContext && currentContext.state === ViteStartState.Sucess && currentContext.server) {
        const listenPort: number = currentContext.server.config.server.port;
        ctx.log(`[Playground] ${id} has successfully started. Returning URL.`);
        return { url: previewUrl(listenPort) };
      }
      
      // 如果状态变为失败，抛出错误
      if (currentContext && currentContext.state === ViteStartState.Failed) {
        throw new Error(`Vite服务启动失败: ${currentContext.error || '未知错误'}`);
      }
    }
    
    // 超时，清理状态并抛出错误
    delete viteServerContextMap[id];
    throw new Error('Vite服务启动超时');
  }
  
  // vite server 启动成功，直接返回
  if (!restart && existingContext && existingContext.state === ViteStartState.Sucess && existingContext.server) {
    const listenPort: number = existingContext.server.config.server.port;
    ctx.log(`[Playground] ${id} is already running. Returning existing URL.`);
    return { url: previewUrl(listenPort) };
  }

  // 如果是重启请求，先关闭旧的服务
  if (restart && existingContext?.server) {
    const serverToClose = existingContext.server;
    const oldPort = serverToClose.config.server.port;
    await serverToClose.close();
    ctx.log(`🔄 [Playground] ${id} 重启，关闭旧服务，保持端口: ${oldPort}`);
    // 不调用 portManager.freePort，保持端口映射
    // 不删除 viteServerContextMap[id]，因为下面会覆盖状态为Init
    // 继续执行下面的创建流程
  }

  // 🔧 第三步：立即设置状态为Init，防止并发请求
  viteServerContextMap[id] = {
    state: ViteStartState.Init,
    server: null,
    error: null,
  };
  ctx.log(`[Playground] ${id} state set to 'Init'.`);

  // 🔧 第四步：添加详细的启动日志
  ctx.log(`🚀 [Playground] 开始启动预览服务: ${id}`);

  try {
    // 第五步：从端口管理器分配一个端口（或复用已有端口）
    const listenPort = portManager.allocatePort(id);

    if (!listenPort) {
      // 🔧 失败时清理状态
      delete viteServerContextMap[id];
      ctx.log(`❌ [Playground] ${id} failed to allocate port.`);
      throw new Error("所有预览服务端口已被占用，请稍后重试。");
    }

    ctx.log(`🎯 [Playground] ${id} 使用端口: ${listenPort}`);

    // 使用获取到的端口来构建 base 路径
    // 新的URL结构：外部 /preview/{port} 映射到内部 /preview/{port}
    // 这样确保 Vite 生成的资源链接包含端口号
    const basePath = `/preview/${listenPort}/`;

    // 下载依赖
    const task: Task = {
      id,
      type: 'runYarnCommand',
    };
    ctx.log(`[Playground] ${id} queueing yarn install.`);

    const promise = new Promise<number>((resolve, reject) => {
      queue.push(task, (err, code) => {
        if (err) reject(err);
        else resolve(code);
      });
    });
    await promise;
    ctx.log(`[Playground] ${id} yarn install finished.`);

    // 更新时间戳，太老的项目会被archive
    const now = new Date();
    await utimes(playgroundRootPath(id), now, now);

    // 启动 server
    ctx.log(`[Playground] ${id} creating Vite server...`);
    const server: ViteDevServer = await createServer({
      root: playgroundRootPath(id),
      base: basePath,
      server: {
        port: listenPort,
        strictPort: true,
        host: '0.0.0.0',
        allowedHosts: ['.fe.htsc', '.saas.htsc', '.web.htsc'],
        // 🔧 移除自定义HMR配置，使用Vite默认配置
        // hmr: {
        //   path: `/hmr`,
        // }
      },
      logLevel: 'info',
      customLogger: {
        info: (msg) => {
          ctx.sendLogToSse(id, msg);
        },
        warn: (msg) => {
          ctx.sendLogToSse(id, msg);
        },
        error: (msg) => {
          ctx.sendLogToSse(id, msg);
        },
        warnOnce: (msg) => {
          ctx.sendLogToSse(id, msg);
        },
        clearScreen: () => {},
        hasErrorLogged: () => false,
        hasWarned: false,
      },
    });

    await server.listen();
    ctx.log(`[Playground] ${id} Vite server is listening.`);
    viteServerContextMap[id].server = server;
    viteServerContextMap[id].state = ViteStartState.Sucess;

    // 🔧 新增：等待WebSocket服务完全启动
    ctx.log(`⏳ [HMR] 等待WebSocket服务完全启动...`);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒确保服务完全启动
    ctx.log(`✅ [HMR] WebSocket服务启动完成`);

    // 添加中间件,更新活跃时间
    viteServerActiveMap[id] = Date.now();
    server.middlewares.use((req, res, next) => {
      viteServerActiveMap[id] = Date.now();
      next();
    });

    ctx.log(`✅ [Playground] ${id} 预览服务启动成功，端口: ${listenPort}`);
    return { url: previewUrl(listenPort) };

  } catch (error) {
    // 🔧 改进错误处理：记录详细错误信息并清理状态
    ctx.log(`❌ [Playground] ${id} 启动失败: ${error.message}`);
    
    // 更新状态为失败
    if (viteServerContextMap[id]) {
      viteServerContextMap[id].state = ViteStartState.Failed;
      viteServerContextMap[id].error = error.message;
    }
    
    // 清理端口（如果已分配）
    if (portManager.hasPlaygroundPort(id)) {
      const port = portManager.getPlaygroundPort(id);
      if (port) {
        portManager.freePort(port, id);
        ctx.log(`🔧 [Playground] ${id} 释放端口: ${port}`);
      }
    }
    
    throw error;
  }
}

export async function restartPlayground(id: string, hostname?: string) {
  ctx.log(`restartPlayground: ${id}`);
  
  // 🔧 新增：检查playground类型，为spec-to-prod-code提供特殊重启逻辑
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient();
    
    const playground = await prisma.playground.findUnique({
      where: { id }
    });
    
    if (playground && playground.type === 'spec-to-prod-code') {
      ctx.log(`🔄 [RestartPreview] 检测到spec-to-prod-code类型playground，执行自定义重启逻辑`);
      
      // 对于spec-to-prod-code类型，执行src目录中的自定义重启命令
      return await restartSpecToProdCodePreview(id);
    }
    
    await prisma.$disconnect();
  } catch (error) {
    ctx.log(`❌ [RestartPreview] 检查playground类型时出错: ${error.message}`);
    // 继续执行原有逻辑
  }
  
  // 原有的重启逻辑（针对其他类型的playground）
  return startPlayground(id, true, hostname);
}

export async function stopPlayground(id: string) {
  ctx.log(`stopPlayground: ${id}`);
  
  try {
    const context: ViteServerContext = viteServerContextMap[id];
    if (context && context.server) {
      const port = context.server.config.server.port;
      await context.server.close();
      
      // 🔧 停止时彻底释放端口映射
      portManager.freePort(port, id);
      
      // 清理上下文
      delete viteServerContextMap[id];
      delete viteServerActiveMap[id];
      
      ctx.log(`✅ [Playground] ${id} 停止成功，释放端口: ${port}`);
      return { success: true, message: `Playground ${id} 停止成功` };
    } else {
      ctx.log(`⚠️ [Playground] ${id} 未找到运行中的服务`);
      return { success: true, message: `Playground ${id} 未运行` };
    }
  } catch (error) {
    ctx.log(`❌ [Playground] ${id} 停止失败: ${error.message}`);
    throw error;
  }
}

/**
 * 🔧 新增：专门为spec-to-prod-code类型playground提供的重启预览功能
 * 这种类型的playground在src目录中运行项目代码，需要特殊的重启逻辑
 */
export async function restartSpecToProdCodePreview(id: string): Promise<{ url: string }> {
  ctx.log(`🚀 [SpecToProdCode] 开始重启预览服务: ${id}`);
  
  try {
    // 步骤1: 检查src目录中是否存在package.json
    const srcPackageJsonPath = playgroundRootPath(id, 'src', 'package.json');
    
    if (!(await exists(srcPackageJsonPath))) {
      throw new Error('src目录中未找到package.json文件');
    }
    
    // 步骤2: 读取package.json中的scripts
    const packageJsonContent = await readFile(srcPackageJsonPath);
    const packageJson = JSON.parse(packageJsonContent as string);
    const scripts = packageJson.scripts || {};
    
    ctx.log(`📋 [SpecToProdCode] 检测到的可用脚本: ${Object.keys(scripts).join(', ')}`);
    
    // 步骤3: 确定启动命令的优先级
    const startCommands = [
      'start',      // 最优先：标准的启动命令
      'dev',        // 次优先：开发模式启动
      'serve',      // 第三：服务器启动
      'preview'     // 最后：预览模式
    ];
    
    let selectedCommand = null;
    for (const cmd of startCommands) {
      if (scripts[cmd]) {
        selectedCommand = cmd;
        break;
      }
    }
    
    if (!selectedCommand) {
      throw new Error(`package.json中未找到可用的启动脚本。可用脚本: ${Object.keys(scripts).join(', ')}`);
    }
    
    ctx.log(`🎯 [SpecToProdCode] 选择启动命令: pnpm run ${selectedCommand}`);
    
    // 步骤4: 停止现有的进程（如果存在）
    await stopSpecToProdCodePreview(id);
    
    // 步骤5: 启动新的预览服务
    const result = await startSpecToProdCodePreview(id, selectedCommand);
    
    ctx.log(`✅ [SpecToProdCode] 预览服务重启完成: ${result.url}`);
    return result;
    
  } catch (error) {
    ctx.log(`❌ [SpecToProdCode] 预览服务重启失败: ${error.message}`);
    throw error;
  }
}

/**
 * 🔧 新增：停止spec-to-prod-code类型playground的预览服务
 */
async function stopSpecToProdCodePreview(id: string): Promise<void> {
  try {
    ctx.log(`🛑 [SpecToProdCode] 停止现有的预览服务进程: ${id}`);
    
    // 使用多种方式停止可能运行的进程
    const stopCommands = [
      // 停止pnpm进程
      `pkill -f "pnpm.*run.*(start|dev|serve|preview)" || true`,
      // 停止node进程（带有项目特征）
      `pkill -f "node.*vite" || true`,
      `pkill -f "node.*webpack" || true`,
      `pkill -f "node.*next" || true`,
      // 停止可能占用端口的进程
      `lsof -ti:3000,4000,5000,8000,8080,8081 | xargs -r kill -9 || true`,
    ];
    
    for (const command of stopCommands) {
      try {
        await executeCommand(id, command, { dir: 'src' });
      } catch (error) {
        // 忽略停止命令的错误，这是正常的
        ctx.log(`ℹ️ [SpecToProdCode] 停止命令执行: ${command}`);
      }
    }
    
    // 等待一段时间让进程完全停止
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    ctx.log(`✅ [SpecToProdCode] 进程清理完成`);
    
  } catch (error) {
    ctx.log(`⚠️ [SpecToProdCode] 停止进程时出现问题: ${error.message}`);
    // 不抛出错误，继续执行启动逻辑
  }
}

/**
 * 🔧 新增：启动spec-to-prod-code类型playground的预览服务
 * 这个函数会启动src目录中的自定义项目，并检测服务端口
 */
async function startSpecToProdCodePreview(id: string, startCommand: string): Promise<{ url: string }> {
  try {
    ctx.log(`🚀 [SpecToProdCode] 启动预览服务: pnpm run ${startCommand}`);
    
    // 使用spawn启动后台进程
    const { spawn } = await import('child_process');
    const srcDir = playgroundRootPath(id, 'src');
    
    const childProcess = spawn('pnpm', ['run', startCommand], {
      cwd: srcDir,
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true,
      env: {
        ...process.env,
        NODE_ENV: 'development',
        // 🔧 强制服务绑定到所有网络接口，确保可以从Docker容器外访问
        HOST: '0.0.0.0',
        VITE_HOST: '0.0.0.0',
        SERVER_HOST: '0.0.0.0',
        BIND_HOST: '0.0.0.0',
        // 常见的开发服务器环境变量
        REACT_APP_HOST: '0.0.0.0',
        VUE_CLI_SERVICE_CONFIG_PATH: '',
        // Next.js
        HOSTNAME: '0.0.0.0',
        // Express/Node服务器
        EXPRESS_HOST: '0.0.0.0',
        // 其他常见框架
        NUXT_HOST: '0.0.0.0',
        SVELTE_HOST: '0.0.0.0',
        // 禁用自动打开浏览器
        BROWSER: 'none',
        DISABLE_OPENCOLLECTIVE: 'true',
        // 文件监视器优化
        CHOKIDAR_USEPOLLING: 'false',
        CHOKIDAR_INTERVAL: '1000',
        WATCHPACK_POLLING: 'false',
        CI: 'true',
        FORCE_COLOR: '0',
        NO_UPDATE_NOTIFIER: 'true',
      }
    });
    
    // 用于捕获服务端口的正则表达式
    const portPatterns = [
      /Local:\s+https?:\/\/[^:]+:(\d+)/i,
      /listening\s+on\s+[^:]*:(\d+)/i,
      /server\s+running\s+at[^:]*:(\d+)/i,
      /development\s+server[^:]*:(\d+)/i,
      /localhost:(\d+)/i,
      /0\.0\.0\.0:(\d+)/i,
      /127\.0\.0\.1:(\d+)/i,
      /port\s+(\d+)/i,
    ];
    
    let detectedPort: number | null = null;
    let outputBuffer = '';
    
    // Promise用于等待端口检测
    const portDetectionPromise = new Promise<number>((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('启动超时：30秒内未检测到服务端口'));
      }, 30000); // 30秒超时
      
      // 监听标准输出
      childProcess.stdout.on('data', (data) => {
        const text = data.toString();
        outputBuffer += text;
        ctx.sendLogToSse(id, text);
        
        // 尝试从输出中提取端口号
        for (const pattern of portPatterns) {
          const match = text.match(pattern);
          if (match && match[1]) {
            const port = parseInt(match[1], 10);
            if (port > 1000 && port < 65536) { // 有效端口范围
              detectedPort = port;
              clearTimeout(timeout);
              ctx.log(`✅ [SpecToProdCode] 检测到服务端口: ${port}`);
              resolve(port);
              return;
            }
          }
        }
      });
      
      // 监听标准错误输出
      childProcess.stderr.on('data', (data) => {
        const text = data.toString();
        outputBuffer += text;
        ctx.sendLogToSse(id, `[stderr] ${text}`);
        
        // 也尝试从错误输出中提取端口（有些工具会输出到stderr）
        for (const pattern of portPatterns) {
          const match = text.match(pattern);
          if (match && match[1]) {
            const port = parseInt(match[1], 10);
            if (port > 1000 && port < 65536) {
              detectedPort = port;
              clearTimeout(timeout);
              ctx.log(`✅ [SpecToProdCode] 从stderr检测到服务端口: ${port}`);
              resolve(port);
              return;
            }
          }
        }
      });
      
      // 监听进程退出
      childProcess.on('exit', (code) => {
        clearTimeout(timeout);
        if (code !== 0 && !detectedPort) {
          reject(new Error(`进程异常退出，退出码: ${code}`));
        }
      });
      
      // 监听进程错误
      childProcess.on('error', (error) => {
        clearTimeout(timeout);
        reject(new Error(`进程启动失败: ${error.message}`));
      });
    });
    
    // 等待端口检测
    const port = await portDetectionPromise;
    
    // 构建预览URL
    const previewUrl = (port: number) => {
      const envPreviewUrl = process.env.PREVIEW_URL;
      return envPreviewUrl ? envPreviewUrl.replace('{port}', String(port)) : `http://localhost:${port}/`;
    };
    
    const url = previewUrl(port);
    
    ctx.log(`🎉 [SpecToProdCode] 自定义预览服务启动成功: ${url}`);
    
    return { url };
    
  } catch (error) {
    ctx.log(`❌ [SpecToProdCode] 启动预览服务失败: ${error.message}`);
    throw error;
  }
}

/**
 * 🔧 新增：从playground日志中检测预览地址
 */
async function detectPreviewUrl(id: string): Promise<string> {
  try {
    // 读取最近的日志文件
    const logPath = playgroundRootPath(id, 'logs', 'log.txt');
    let logContent = '';
    
    if (await exists(logPath)) {
      logContent = await readFile(logPath) as string;
    }
    
    // 从日志中提取localhost URL
    const urlPattern = /https?:\/\/localhost:(\d+)/g;
    const matches = logContent.match(urlPattern);
    
    if (matches && matches.length > 0) {
      // 取最后一个匹配的URL（最新的）
      const lastUrl = matches[matches.length - 1];
      ctx.log(`📡 [SpecToProdCode] 从日志中检测到URL: ${lastUrl}`);
      
      // 提取端口号并转换为可访问的预览地址
      const portMatch = lastUrl.match(/:(\d+)/);
      if (portMatch) {
        const port = parseInt(portMatch[1]);
        return generatePreviewUrl(port);
      }
    }
    
    // 如果没有从日志中找到，返回一个常见的默认地址
    ctx.log(`⚠️ [SpecToProdCode] 未从日志中检测到URL，使用默认地址`);
    return generatePreviewUrl(3000); // 默认端口
    
  } catch (error) {
    ctx.log(`❌ [SpecToProdCode] 检测预览地址失败: ${error.message}`);
    return generatePreviewUrl(3000); // 兜底方案
  }
}

/**
 * 🔧 新增：生成预览URL的统一方法
 */
function generatePreviewUrl(port: number): string {
  const previewUrl = process.env.PREVIEW_URL;
  if (previewUrl) {
    return previewUrl.replace('{port}', String(port));
  }
  
  // 兜底：返回localhost地址
  return `http://localhost:${port}/`;
}

export async function buildPlayground(id: string, mode?: 'preview'): Promise<number> {
  ctx.log(`buildPlayground id: ${id}`);
  try {
    // 下载依赖
    const promsie = new Promise<number>((resolve) => {
      const ps = runYarnCommand(id, 'yarn');
      ps.on('exit', (code) => {
        ctx.log(`Playground(${id}) install exits with code ${code}`);
        resolve(code);
      });
      ps.stderr.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
      });
      ps.stdout.on('data', (data) => {
        const string = data.toString().trimEnd();
        ctx.log(string);
      });
    });
    const code = await promsie;
    if (code !== 0) return code;

    // build
    await build({
      root: playgroundRootPath(id),
      build: {
        emptyOutDir: true,
      },
      mode,
    });

    return 0;
  } catch (error) {
    console.error(error);
    return 1;
  }
}

export async function deletePlayground(id: string) {
  ctx.log(`deletePlayground id: ${id}`);
  const server: ViteDevServer = viteServerContextMap[id]?.server;
  if (server) {
    const port = server.config.server.port;
    await server.close();
    portManager.freePort(port, id); // 归还端口
    delete viteServerContextMap[id];
  }

  const target = playgroundRootPath(id);
  await remove(target);
}

export async function cleanPlayground() {
  // 定时清理长时间未活跃的 playground, 关闭 server
  setInterval(async () => {
    for (const [id, active] of Object.entries(viteServerActiveMap)) {
      if (Date.now() - active > INACTIVE_TIMEOUT) {
        ctx.log(`delete playground id: ${id}`);
        const server: ViteDevServer = viteServerContextMap[id].server;
        if (server) {
          const port = server.config.server.port;
          await server.close();
          portManager.freePort(port, id); // 归还端口
          delete viteServerActiveMap[id];
        }
      }
    }
  }, CHECK_INTERVAL);
}

// 添加文件列表缓存和防抖机制
const fileListCache = new Map<string, { 
  data: { path: string; fileType: FileType }[]; 
  timestamp: number; 
}>();
const FILE_LIST_CACHE_DURATION = 30000; // 30秒缓存
const listFilesInProgress = new Set<string>();

export async function listFiles(id: string): Promise<
  {
    path: string;
    fileType: FileType;
  }[]
> {
  console.log(`⚡ [Playground] Shell极速扫描工作区文件 (id: ${id})`);
  
  // 检查缓存
  const cacheKey = `file-list-${id}`;
  const cached = fileListCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < FILE_LIST_CACHE_DURATION) {
    console.log(`💾 [Playground] 使用缓存的文件列表 (${cached.data.length} 个项目)`);
    return cached.data;
  }
  
  // 防止重复调用
  if (listFilesInProgress.has(id)) {
    console.log(`⏳ [Playground] 文件列表扫描进行中，等待结果...`);
    // 等待正在进行的扫描完成
    return new Promise<{ path: string; fileType: FileType }[]>((resolve) => {
      const checkInterval = setInterval(() => {
        const result = fileListCache.get(cacheKey);
        if (result && !listFilesInProgress.has(id)) {
          clearInterval(checkInterval);
          resolve(result.data);
        }
      }, 100);
      
      // 10秒超时
      setTimeout(() => {
        clearInterval(checkInterval);
        listFilesInProgress.delete(id);
        resolve([]);
      }, 10000);
    });
  }
  
  listFilesInProgress.add(id);
  
  try {
    const srcUri = playgroundRootPath(id, 'src');
    console.log(`📂 [Playground] 扫描目录: ${srcUri}`);
    
    // 检查src目录是否存在
    if (!await exists(srcUri)) {
      console.warn(`⚠️ [Playground] src目录不存在: ${srcUri}`);
      const emptyResult: { path: string; fileType: FileType }[] = [];
      fileListCache.set(cacheKey, {
        data: emptyResult,
        timestamp: Date.now()
      });
      return emptyResult;
    }
    
    console.log(`🚀 [Playground] 开始Shell极速扫描...`);
    
    // 🚀 使用 shelljs 执行统一的 find 命令
    const result: { path: string; fileType: FileType }[] = [];
    
    // 使用 shelljs 执行 find 命令，跨平台兼容
    console.log(`🔧 [Playground] 使用 shelljs 执行 find 命令...`);
    
    // 切换到 src 目录执行 find 命令
    const originalCwd = shelljs.pwd().toString();
    shelljs.cd(srcUri);
    
    console.log('开始执行find命令，时间：', new Date())
    // 🚀 优化：根据平台选择不同的命令，避免在macOS上因-printf选项报错
    const isMac = process.platform === 'darwin';
    const findExcludes = '-not -path "*/node_modules/*" -not -path "*/.git/*" -not -path "*/.vscode/*" -not -path "*/dist/*" -not -path "*/build/*" -not -name "node_modules" -not -name ".git" -not -name ".vscode" -not -name "dist" -not -name "build"';
    
    // macOS的find不支持-printf, 但支持-exec。使用ls -dF来区分文件和目录，性能比stat好。
    const findCommand = isMac
      ? `find . -maxdepth 8 ${findExcludes} -exec ls -dF {} +`
      : `find . -maxdepth 8 ${findExcludes} -printf "%y:%p\\n"`;
      
    const findResult = shelljs.exec(findCommand, { silent: true });
 
    console.log('执行find命令完成，时间：', new Date())
     
    // 恢复原目录
    shelljs.cd(originalCwd);
     
    console.log('开始解析find结果，时间：', new Date())
     
    if (findResult.code === 0 && findResult.stdout) {
      // 成功执行，快速解析结果
      const lines = findResult.stdout.trim().split('\n').filter(line => line.trim());
      
      if (isMac) {
        // 解析 `ls -dF` 的输出
        for (const line of lines) {
          let cleanPath = line.replace(/^\.\//, ''); // 移除 ./ 前缀
          if (!cleanPath || cleanPath === '.') continue;

          const isDir = cleanPath.endsWith('/');
          const fileType = isDir ? FileType.Directory : FileType.File;
          
          if (isDir) {
            // 移除末尾的 '/'
            cleanPath = cleanPath.slice(0, -1);
          } else {
            // ls -F 可能会在可执行文件后加 '*', 符号链接后加 '@' 等, 这里简单移除最后一个字符
            const lastChar = cleanPath.slice(-1);
            if (['*', '@', '=', '|'].includes(lastChar)) {
              cleanPath = cleanPath.slice(0, -1);
            }
          }
          
          if (!cleanPath) continue; // 移除修饰符后可能为空，例如路径就是 `.`

          result.push({
            path: cleanPath,
            fileType: fileType
          });
        }
      } else {
        // 解析 `find -printf` 的输出 (Linux, etc.)
        for (const line of lines) {
          const [type, fullPath] = line.split(':');
          let cleanPath = fullPath?.replace(/^\.\//, ''); // 移除 ./ 前缀
          
          if (!cleanPath || cleanPath === '.') continue; // 跳过空路径和根目录
          
          // 直接根据 find 的输出判断类型，无需额外 stat 调用
          const fileType = type === 'd' ? FileType.Directory : FileType.File;
          
          result.push({
            path: cleanPath,
            fileType: fileType
          });
        }
      }
      
      console.log('解析find结果完成，时间：', new Date())
      console.log(`⚡ [Playground] shelljs find 扫描完成，返回 ${result.length} 个项目`);
    } else {
      // find 命令失败，可能是不支持 -printf，尝试备用方案
      console.log(`🔄 [Playground] find 命令失败 (code: ${findResult.code})，尝试备用方案...`);
      
      shelljs.cd(srcUri); // 备用方案需要再次进入目录
      // 备用方案1：使用不带 -printf 的 find 命令
      const fallbackResult = shelljs.exec(
        'find . -maxdepth 5 -not -path "*/node_modules/*" -not -path "*/.git/*" -not -path "*/.vscode/*" -not -path "*/dist/*" -not -path "*/build/*" -not -name "node_modules" -not -name ".git" -not -name ".vscode" -not -name "dist" -not -name "build"',
        { silent: true }
      );
      shelljs.cd(originalCwd); // 恢复原目录
      
      if (fallbackResult.code === 0 && fallbackResult.stdout) {
        console.log('备用find命令成功，快速处理结果...');
        const lines = fallbackResult.stdout.trim().split('\n').filter(line => line.trim());
        
        // 批量处理：先收集所有路径，然后批量检查类型
        const pathsToCheck: string[] = [];
        for (const line of lines) {
          let cleanPath = line.replace(/^\.\//, '');
          if (!cleanPath || cleanPath === '.') continue;
          pathsToCheck.push(cleanPath);
        }
        
        console.log(`需要检查 ${pathsToCheck.length} 个路径的类型...`);
        
        // 🚀 优化：批量异步检查文件类型，限制并发数避免文件句柄耗尽
        const batchSize = 50; // 每批处理50个文件
        for (let i = 0; i < pathsToCheck.length; i += batchSize) {
          const batch = pathsToCheck.slice(i, i + batchSize);
          const promises = batch.map(async (cleanPath) => {
            try {
              const fullPath = path.join(srcUri, cleanPath);
              const stats = await fs.promises.stat(fullPath);
              return {
                path: cleanPath,
                fileType: stats.isDirectory() ? FileType.Directory : FileType.File
              };
            } catch (error) {
              return null; // 文件可能被删除，返回null
            }
          });
          
          const batchResults = await Promise.all(promises);
          batchResults.forEach(item => {
            if (item) result.push(item);
          });
        }
        
        console.log(`⚡ [Playground] 备用方案扫描完成，返回 ${result.length} 个项目`);
      } else {
        // 备用方案2：最简单的2层扫描
        console.log(`🔄 [Playground] 所有find方案都失败，使用最简单的回退方案...`);
        
        try {
          // 第1层：扫描src根目录
          const level1Entries = await readDirectory(srcUri);
          
          for (const [name1, type1] of level1Entries) {
            if (type1 === FileType.Directory && ['node_modules', '.git'].includes(name1)) {
              continue;
            }
            
            result.push({ path: name1, fileType: type1 });
            
            // 第2层：如果是目录，扫描子目录
            if (type1 === FileType.Directory) {
              try {
                const level2Entries = await readDirectory(path.join(srcUri, name1));
                
                for (const [name2, type2] of level2Entries) {
                  if (type2 === FileType.Directory && ['node_modules', '.git'].includes(name2)) {
                    continue;
                  }
                  
                  result.push({ path: `${name1}/${name2}`, fileType: type2 });
                }
              } catch (error) {
                console.warn(`⚠️ [Playground] 扫描子目录失败 ${name1}:`, error.message);
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ [Playground] 回退方案失败:`, error.message);
        }
      }
    }
    
    // 缓存结果
    fileListCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
    
    return result;
  } finally {
    listFilesInProgress.delete(id);
  }
}



export async function getFile(id: string, path: string): Promise<string> {
  console.log(`🔍 [Playground] 开始读取文件 (id: ${id}, path: ${path})`);
  // 文件树现在只显示src目录内容，所以需要在路径前加上src/前缀
  const fileUri = playgroundRootPath(id, 'src', path);
  
  try{
    // 直接读取文件数据
    const fileData = await readFileSync(fileUri);
    
    // 快速路径：直接检查文件类型并返回对应格式
    if (isBinaryFile(path)) {
      return fileData.toString('base64');
    }
    console.log(`✅ [Playground] 文件读取成功 (id: ${id}, path: ${path})`);
    // 对于文本文件，直接使用 utf-8 解码，性能最优
    return fileData.toString('utf-8');
  }catch(error){
    console.error(`❌ [getFile] 读取文件失败 ${path}:`, error);
    throw error;
  }
}

export async function playgroundInfo(id: string, type: ConfigFileType): Promise<string | null> {
  let fileUri: string;
  if (type === ConfigFileType.Readme) {
    fileUri = playgroundRootPath(id, 'README.md');
  } else if (type === ConfigFileType.PackageJson) {
    fileUri = playgroundRootPath(id, 'package.json');
  } else if (type === ConfigFileType.Env) {
    fileUri = playgroundRootPath(id, '.env');
  } else {
    throw new Error('不支持此类型工作区信息');
  }
  return readFile(fileUri);
}

export async function saveFile({ id, path, content }: { id: string; path: string; content: string }) {
  // 文件树现在只显示src目录内容，所以需要在路径前加上src/前缀
  const fileUri = playgroundRootPath(id, 'src', path);
  await writeFile(fileUri, content);
}

export async function createBranch({ id, tagName }: { id: string, tagName: string }) {
  const target = playgroundRootPath(id);
  
  // 清除相关缓存，因为创建分支会改变版本状态
  clearVersionCache(id);
  
  // 保存代码
  try {
    const { current, list } = await branchList(id);
    if (current !== tagName) {
      if (!list.includes(tagName)) {
    await executeGitCommand(`checkout -b ${tagName}`, target);
      } else {
        await executeGitCommand(`checkout ${tagName}`, target);
      }
    }
  } catch (error) {
    ctx.log(`创建分支失败：${error}`);
  }
}

export async function deleteBranch({ id, tagName }: { id: string, tagName: string }) {
  const target = playgroundRootPath(id);
  // 删除分支
  try {
    await executeGitCommand(`branch -D ${tagName}`, target);
  } catch (error) {
    ctx.log(`删除分支失败：${error}`);
  }
}

export async function commitFiles({ id }: { id: string }) {
  const target = playgroundRootPath(id);
  let result;
  try {
    // 查看整个项目是否有代码变化，而不仅仅是src目录
    result = await executeGitCommand(`status --porcelain`, target);
  } catch (err) {
    // 代码没差别会进入这里
  }
  // 保存代码
  try {
    // 有变化先保存
    const diff = trim(result?.stdout) || '';
    if (size(diff)) {
      // 添加所有变更的文件，而不仅仅是src目录
      await executeGitCommand(`add .`, target);
      await executeGitCommand(`commit -m "更新文件"`, target);
      
      // 清除相关缓存，因为提交文件后版本状态可能发生变化
      clearVersionCache(id);
    }
  } catch (error) {
    ctx.log(`保存文件失败：${error}`);
  }
}

async function branchList(id: string) {
  const cacheKey = `branch-list-${id}`;
  
  // 防止重复调用
  if (inProgress.has(cacheKey)) {
    return { list: [], current: '' };
  }
  
  // 检查缓存
  const cached = versionCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  inProgress.add(cacheKey);
  
  const target = playgroundRootPath(id);
  try {
    const result = await executeGitCommand(`branch -a`, target);
    const output = trim(result?.stdout || '');
    const lines = split(output, '\n');
    let current = '';
    const list = map(lines, (line) => {
      if (line.startsWith('*')) {
        const name = line.substring(1).trim();
        current = name;
        return name;
      }
      return line.trim();
    });
    
    const branchData = { list, current };
    
    // 更新缓存
    versionCache.set(cacheKey, {
      data: branchData,
      timestamp: Date.now()
    });
    
    return branchData;
  } catch (error) {
    ctx.log(`获取分支失败：${error}`);
    return { list: [], current: '' };
  } finally {
    inProgress.delete(cacheKey);
  }
}

export async function getAllVersion({ id }: { id: string }) {
  const cacheKey = `all-versions-${id}`;
  
  // 防止重复调用
  if (inProgress.has(cacheKey)) {
    return [];
  }
  
  // 检查缓存
  const cached = versionCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  inProgress.add(cacheKey);
  
  try {
    const { list } = await branchList(id);
    const versions = list.filter(e => e.startsWith('version-'));
    
    // 更新缓存
    versionCache.set(cacheKey, {
      data: versions,
      timestamp: Date.now()
    });
    
    return versions;
  } catch (error) {
    ctx.log(`获取所有版本失败：${error}`);
    return [];
  } finally {
    inProgress.delete(cacheKey);
  }
}

// 添加缓存和防抖机制
const versionCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5000; // 5秒缓存
const inProgress = new Set<string>();

// 导出清除版本缓存的函数
export function clearVersionCache(id: string) {
  versionCache.delete(`current-version-${id}`);
  versionCache.delete(`version-status-${id}`);
  versionCache.delete(`branch-list-${id}`);
  versionCache.delete(`all-versions-${id}`);
  console.log(`🗑️ 已清除项目 ${id} 的版本缓存`);
}

export async function getCurrentVersion({ id }: { id: string }) {
  const cacheKey = `current-version-${id}`;
  
  // 防止重复调用
  if (inProgress.has(cacheKey)) {
    return '';
  }
  
  // 检查缓存
  const cached = versionCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  inProgress.add(cacheKey);
  
  const target = playgroundRootPath(id);
  try {
    const result = await executeGitCommand(`branch --show-current`, target);
    const branchName = trim(result?.stdout) || '';
    
    // 只返回version开头的分支
    const versionBranch = branchName.startsWith('version-') ? branchName : '';
    
    // 更新缓存
    versionCache.set(cacheKey, {
      data: versionBranch,
      timestamp: Date.now()
    });
    
    return versionBranch;
  } catch (error) {
    ctx.log(`获取当前分支失败：${error}`);
    return '';
  } finally {
    inProgress.delete(cacheKey);
  }
}

export async function versionStatus({ id }: { id: string }) {
  const cacheKey = `version-status-${id}`;
  
  // 防止重复调用
  if (inProgress.has(cacheKey)) {
    return { list: [], current: '' };
  }
  
  // 检查缓存
  const cached = versionCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  inProgress.add(cacheKey);
  
  try {
    const list = await getAllVersion({ id });
    const current = await getCurrentVersion({ id });
    
    const result = { list, current };
    
    // 更新缓存
    versionCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
    
    return result;
  } catch (error) {
    ctx.log(`获取版本状态失败：${error}`);
    return { list: [], current: '' };
  } finally {
    inProgress.delete(cacheKey);
  }
}

export async function createDirectory({ id, path }: { id: string; path: string }) {
  // 在src目录下创建目录
  const fileUri = playgroundRootPath(id, 'src', path);
  await mkdir(fileUri, { recursive: true });
}

export async function appendFile({ id, path, content }: { id: string; path: string; content: string }) {
  // 在src目录下追加文件内容
  const fileUri = playgroundRootPath(id, 'src', path);
  await appendToFile(fileUri, content);
}

export async function moveFile({ id, oldPath, newPath }: { id: string; oldPath: string; newPath: string }) {
  // 在src目录下移动文件
  const oldUri = playgroundRootPath(id, 'src', oldPath);
  const newUri = playgroundRootPath(id, 'src', newPath);
  await rename(oldUri, newUri);
}

export async function deleteFile({ id, path }: { id: string; path: string }) {
  // 在src目录下删除文件
  const fileUri = playgroundRootPath(id, 'src', path);
  await remove(fileUri);
}

export async function setEnv(id: string, env: Record<string, string>) {
  const fileUri = playgroundRootPath(id, '.env');
  const fileData = await readFile(fileUri);
  const data = parse(fileData as string);
  Object.assign(data, env);
  await writeFile(
    fileUri,
    Object.entries(data)
      .map(([k, v]) => `${k}=${v}`)
      .join('\n'),
  );
}

export async function installDeps(id: string, deps: string): Promise<number | null> {
  const lines = deps.split('\n').map((e) => e.trim());
  const depsText = lines.join(' ');
  ctx.log(`Installing playground(${id}) deps: ${depsText}`);

  const task: Task = {
    id,
    type: 'addCommand',
    depsText,
  };
  const promise = new Promise<number>((resolve, reject) => {
    queue.push(task, (err, code) => {
      if (err) reject(err);
      else resolve(code);
    });
  });
  const code = await promise;

  // Looks like server.restart() is not working, need to mannually restart vite
  startPlayground(id, true);
  return code;
}

export async function uninstallDeps(id: string, deps: string): Promise<number | null> {
  const depsText = deps
    .split('\n')
    .map((e) => e.trim())
    .join(' ');
  ctx.log(`Uninstalling playground(${id}) deps: ${depsText}`);

  const task: Task = {
    id,
    type: 'removeCommand',
    depsText,
  };
  const promise = new Promise<number>((resolve, reject) => {
    queue.push(task, (err, code) => {
      if (err) reject(err);
      else resolve(code);
    });
  });
  const code = await promise;

  // Looks like server.restart() is not working, need to mannually restart vite
  startPlayground(id, true);
  return code;
}

export function zipPlayground(id: string) {
  const fileUri = playgroundRootPath(id, 'src');
  return zipDirectory(fileUri);
}

const AI_CODING_UPLOAD_TENANT_ID = 'd1c7a1f3-9e8b-4a2d-b54f-1c3a8e0d7b92';

export async function deployPlayground(id: string, mode?: 'preview') {
  await buildPlayground(id, mode);
  const dirname = mode === 'preview' ? 'dist-preview' : 'dist';
  const distDir = playgroundRootPath(id, dirname);

  const fd = new FormData();
  fd.append('tenantId', AI_CODING_UPLOAD_TENANT_ID);
  fd.append('path', `${id}/${dirname}`);

  const files = await readDirectory(distDir, {
    recursive: true,
  });
  const fileNames = files.filter(e => e[1] === FileType.File).map(e => e[0]);
  fileNames.forEach(e => {
    const filename = path.basename(e);
    fd.append('file', fs.createReadStream(path.join(distDir, e)), {
      filename,
      filepath: e,
      contentType: mime.getType(e),
    });
  });
  // Upload zip file
  // const zip = await zipDirectory(distDir);
  // fd.append('file', zip, {
  //   filename: 'archive.zip',
  //   filepath: 'archive.zip',
  //   contentType: 'application/zip',
  // });
  ctx.log(`Deploying playground(${id}) artifact in ${mode || 'library'} mode`);
  try {
    const res = await axios({
      method: 'POST',
      url: process.env.DEPLOY_URL,
      headers: fd.getHeaders(),
      data: fd,
    });
    ctx.log(`Deploy response: ${JSON.stringify(res.data)}, status: ${res.status}`);
    return res.data;
  } catch (e) {
    ctx.log(`部署失败：${e}`, 'error');
    throw e;
  }
}

export async function uploadImageToMinIO(fileBuffer: Buffer, fileName: string): Promise<string> {
  const fd = new FormData();
  fd.append('tenantId', AI_CODING_UPLOAD_TENANT_ID);
  fd.append('path', `design-images/${Date.now()}-${fileName}`);
  fd.append('file', fileBuffer, {
    filename: fileName,
    contentType: mime.getType(fileName) || 'application/octet-stream',
  });

  ctx.log(`Uploading image ${fileName} to MinIO`);
  try {
    const res = await axios({
      method: 'POST',
      url: process.env.DEPLOY_URL,
      headers: fd.getHeaders(),
      data: fd,
    });
    ctx.log(`Image upload response: ${JSON.stringify(res.data)}, status: ${res.status}`);
    return res.data;
  } catch (e) {
    ctx.log(`图片上传失败：${e}`, 'error');
    throw e;
  }
}

export async function unarchivePlayground(id: string) {
  const zipDir = playgroundRootPath(`${id}.zip`);
  const dest = playgroundRootPath(id);
  await unzip(zipDir, dest);

  // 更新时间戳，太老的项目会被archive
  const now = new Date();
  await utimes(dest, now, now);

  const ps = runYarnCommand(id, 'yarn');
  await new Promise<void>((resolve, reject) => {
    ps.on('exit', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(`yarn command failed: ${code}`);
      }
    });
  });
}

export function isArchived(id: string) {
  return !exists(playgroundRootPath(id)) && exists(playgroundRootPath(`${id}.zip`));
}

export async function executeCommand(
  id: string,
  cmd: string,
  opts?: {
    dir?: string;
    stdin?: string;
  },
): Promise<{
  stdout: string;
  stderr: string;
  exitCode: number;
}> {
  let cwd
  const dir = opts?.dir;
  if (dir) {
    cwd = playgroundRootPath(id, dir);
  } else {
    cwd = playgroundRootPath(id);
  }

  return new Promise((resolve, reject) => {
    let stdout = '', stderr = '';
    
    try {
      const ps = spawn(cmd, {
        shell: true,
        cwd,
      });

      // 处理spawn错误事件
      ps.on('error', (error) => {
        reject(new Error(`Command execution failed: ${error.message}`));
      });

      ps.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      ps.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      ps.on('close', (exitCode) => {
        resolve({
          stderr,
          stdout,
          exitCode,
        });
      });
      
      const stdin = opts?.stdin;
      if (stdin) {
        ps.stdin.write(stdin);
        ps.stdin.end();
      }
    } catch (error) {
      // 捕获spawn同步异常
      reject(new Error(`Failed to spawn command: ${error.message}`));
    }
  });
}

export { chat, extactPlaygroundInfo, enhance, checkIncompleteOutput } from './ai';

export async function changeBranch({ id, version }: { id: string, version: string }) {
  const target = playgroundRootPath(id);
  // 清除相关缓存
  clearVersionCache(id);
  
  try {
    const { current, list } = await branchList(id);
    if (current !== version) {
      if (!list.includes(version)) {
        await executeGitCommand(`checkout -b ${version}`, target);
      } else {
        await executeGitCommand(`checkout ${version}`, target);
      }
    }
  } catch (error) {
    ctx.log(`切换分支失败：${error}`);
  }
}

/** Remove some branches */
export async function removeVersions({ id, versions }: { id: string, versions: number[] }) {
  if (!versions.length) return;
  // 清除相关缓存
  clearVersionCache(id);
  
  // Remove some branches
  const latestVersion = versions[0] - 1;
  const nextBranch = latestVersion ? `version-${latestVersion}` : 'main';
  await changeBranch({ id, version: nextBranch });
  await deleteBranch({ id, tagName: versions.map(e => `version-${e}`).join(' ') });
}
