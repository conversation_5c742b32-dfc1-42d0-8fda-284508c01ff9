import {
  StateGraph,
  MemorySaver,
  END,
  START,
  Annotation,
} from "@langchain/langgraph";
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
  HumanMessage,
} from "@langchain/core/messages";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { createModel } from "../../model/index";
import { writeFileTool } from "../../../../tools/writeFiles";
import {
  getHtmlToCodeSystemPrompt,
  getImgToCodeSystemPrompt,
  getRoundControlPrompt,
  getHtmlRefactorPrompt,
  getReviewCodeSystemPrompt,
} from "./prompt/index";
import { isHtml, isImg, printLog } from "./utils";
import { parseModelOutput, hasToolCalls } from "../../utils";
import { extractHtmlFromToolResult } from "./utils";
import { DesignItem, RefactoredDesignItem } from "../../types";

// 常量定义
const MAX_REVIEW_ROUNDS = 1; // 最大审查轮次

const tools = [writeFileTool];
const checkpointer = new MemorySaver();

// ===================================
// 1. 定义状态注解
// ===================================
const DesignToHtmlStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  }),
  reviewRound: Annotation<number>({
    reducer: (x: number, y: number) => y,
    default: () => 0,
  }),
  input: Annotation<DesignItem>({
    reducer: (x: DesignItem, y: DesignItem) => y,
    default: () => ({ name: "", content: "", type: "html" as const }),
  }),
  output: Annotation<RefactoredDesignItem>({
    reducer: (x: RefactoredDesignItem, y: RefactoredDesignItem) => y,
    default: () => ({ name: "", refactoredHtml: "" }),
  }),
  error: Annotation<string | undefined>({
    reducer: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  }),
  status: Annotation<string>({
    reducer: (x: string, y: string) => y,
    default: () => "running",
  }),
});

// 定义状态类型
type DesignToHtmlState = typeof DesignToHtmlStateAnnotation.State;

// ===================================
// 2. 节点函数
// ===================================

/**
 * 更新state.output的通用函数
 */
function updateStateOutput(state: DesignToHtmlState, htmlContent: string) {
  state.output.name = state.input.name;
  state.output.refactoredHtml = htmlContent;
  console.log(`已更新state.output，HTML内容长度: ${htmlContent.length}`);
}

/**
 * 路由函数：根据输入内容类型决定下一步
 * * 关键改动：不再抛出异常，而是设置 `state.error` 并返回 "finalCheck" 路由。
 */
function routeByContentType(
  state: DesignToHtmlState
): "htmlToCode" | "imgToCode" | "finalCheck" {
  try {
    if (state.input.type) {
      switch (state.input.type) {
        case "html":
          console.log("根据 type 字段路由到 htmlToCode");
          return "htmlToCode";
        case "img":
          console.log("根据 type 字段路由到 imgToCode");
          return "imgToCode";
        default:
          console.log("未知的 type 类型，尝试内容检测");
          break;
      }
    }
    state.output.name = state.input.name;

    const content = state.input.content || "";
    if (!content) {
      state.error = "输入验证失败：没有提供有效的内容";
      return "finalCheck";
    }

    if (isHtml(content)) {
      console.log("检测到HTML内容，路由到htmlToCode");
      return "htmlToCode";
    }

    if (isImg(content)) {
      console.log("检测到图片内容，路由到imgToCode");
      return "imgToCode";
    }

    state.error = "输入验证失败：无法识别的内容类型";
    return "finalCheck";
  } catch (err) {
    state.error = `路由函数发生意外错误：${(err as Error).message}`;
    return "finalCheck";
  }
}

// ... 你的其他节点函数保持不变（如果它们本身没有try...catch） ...
// 为了容错性，你也可以为其他节点添加类似的 try...catch 逻辑
async function htmlToCode(state: DesignToHtmlState) {
  try {
    const model = createModel().bindTools(tools);
    const systemMessage = new SystemMessage(
      getHtmlToCodeSystemPrompt(state.input.name, state.input.content)
    );
    const userMessage = new HumanMessage(getHtmlRefactorPrompt());
    const messages = [systemMessage, userMessage];
    // 发送给模型的消息
    printLog(messages);
    const response = await model.invoke(messages);
    // 注意：这里不更新state.output，因为工具调用后的结果会在incrementRoundAfterTools中处理
    return {
      messages: [...messages, response],
    };
  } catch (err) {
    state.error = `htmlToCode 节点执行失败：${(err as Error).message}`;
    return {}; // 返回一个空状态，让流程继续到错误路由
  }
}

async function imgToCode(state: DesignToHtmlState) {
  try {
    const model = createModel().bindTools(tools);
    const systemMessage = new SystemMessage(getImgToCodeSystemPrompt());
    const userMessage = new HumanMessage(
      state.input.content || state.input.name || ""
    );
    const messages = [systemMessage, userMessage];
    printLog(messages);
    const response = await model.invoke(messages);
    // 注意：这里不更新state.output，因为工具调用后的结果会在incrementRoundAfterTools中处理
    return {
      messages: [response],
    };
  } catch (err) {
    state.error = `imgToCode 节点执行失败：${(err as Error).message}`;
    return {};
  }
}

// 对于 htmlToCode 和 imgToCode 节点：决定是否调用工具
function shouldCallToolsForToCodeNodes(
  state: DesignToHtmlState
): "refactorTools" | "reviewCode" {
  const lastMessage = state.messages[state.messages.length - 1];
  if (!lastMessage) {
    console.log(`没有消息，直接进入代码审查`);
    return "reviewCode";
  }
  if (hasToolCalls(lastMessage)) {
    console.log(`需要调用工具，准备执行工具`);
    return "refactorTools";
  }
  console.log(`无需调用工具，直接进入代码审查`);
  return "reviewCode";
}

// 对于 reviewCode 节点：决定下一步
function shouldReview(
  state: DesignToHtmlState
): "reviewTools" | "reviewCode" | "finalCheck" {
  // 如果状态已完成，直接结束
  if (state.status === "completed") {
    console.log("审查已完成，准备结束");
    return "finalCheck";
  }

  const lastMessage = state.messages[state.messages.length - 1];
  if (lastMessage && hasToolCalls(lastMessage)) {
    console.log(`第 ${state.reviewRound} 轮需要调用工具，准备执行工具`);
    return "reviewTools";
  }
  console.log(`第 ${state.reviewRound} 轮无需调用工具，继续审查`);
  return "reviewCode";
}

// 代码审查节点
async function reviewCode(state: DesignToHtmlState) {
  try {
    // 检查review次数
    if (state.reviewRound >= MAX_REVIEW_ROUNDS) {
      console.log(`已完成 ${MAX_REVIEW_ROUNDS} 轮审查，达到最大轮次，准备结束`);
      return {
        messages: [],
        status: "completed",
      };
    }

    const model = createModel().bindTools(tools);
    const displayRound = state.reviewRound + 1;
    const reviewMessage = [
      new HumanMessage(getReviewCodeSystemPrompt()),
      new HumanMessage(
        `请进行第${displayRound}轮代码审查。${getRoundControlPrompt(displayRound, MAX_REVIEW_ROUNDS)}`
      ),
    ];
    const messages = [...state.messages, ...reviewMessage];
    printLog(messages);
    const response = await model.invoke(messages);

    return {
      messages: [...reviewMessage, response],
      reviewRound: state.reviewRound + 1,
    };
  } catch (err) {
    state.error = `reviewCode 节点执行失败：${(err as Error).message}`;
    return {};
  }
}

const toolNode = new ToolNode(tools as any);

/**
 * 最终检查节点：作为流程的“门卫”
 * * 关键：检查状态，如果发现错误则抛出异常，否则正常结束。
 */
async function finalCheck(state: DesignToHtmlState) {
  console.log("执行 finalCheck 节点...");
  if (state.error) {
    console.error("检测到状态中的错误信息，准备抛出异常。");
    state.status = "failed";
    throw new Error(`Graph execution failed: ${state.error}`);
  }

  // 如何获取最终的HTML代码
  let finalHtml = "";
  const lastMessage = state.messages[state.messages.length - 1];
  if (lastMessage) {
    // 首先尝试从工具调用结果中提取
    const htmlFromTool = extractHtmlFromToolResult(lastMessage);
    if (htmlFromTool) {
      finalHtml = htmlFromTool;
      console.log("从最终工具调用结果中提取HTML代码");
    } else {
      // 如果没有工具调用，尝试从消息内容中提取
      const htmlFromContent = parseModelOutput(lastMessage);
      if (htmlFromContent) {
        finalHtml = htmlFromContent;
        console.log("从最终消息内容中提取HTML代码");
      }
    }
  }

  // 设置输出结果
  const output: RefactoredDesignItem = {
    name: state.input.name,
    refactoredHtml: finalHtml,
  };

  console.log(`最终输出HTML代码长度: ${finalHtml?.length || 0}`);
  console.log("状态正常，流程可以成功结束。");
  state.status = "completed";
  return { output }; // 返回包含output的状态更新
}

// ===================================
// 3. 定义并编译图
// ===================================
const workflow = new StateGraph(DesignToHtmlStateAnnotation)
  .addNode("htmlToCode", htmlToCode)
  .addNode("imgToCode", imgToCode)
  .addNode("reviewCode", reviewCode)
  .addNode("refactorTools", toolNode)
  .addNode("reviewTools", toolNode)
  .addNode("finalCheck", finalCheck) // 1. 新增 finalCheck 节点
  .addConditionalEdges(START, routeByContentType, {
    htmlToCode: "htmlToCode",
    imgToCode: "imgToCode",
    finalCheck: "finalCheck", // 2. 错误分支路由到 finalCheck
  })
  .addConditionalEdges("htmlToCode", shouldCallToolsForToCodeNodes, {
    refactorTools: "refactorTools",
    reviewCode: "reviewCode",
  })
  .addConditionalEdges("imgToCode", shouldCallToolsForToCodeNodes, {
    refactorTools: "refactorTools",
    reviewCode: "reviewCode",
  })
  .addEdge("refactorTools", "reviewCode")
  .addConditionalEdges("reviewCode", shouldReview, {
    reviewTools: "reviewTools",
    reviewCode: "reviewCode",
    finalCheck: "finalCheck",
  })
  .addEdge("reviewTools", "reviewCode")
  .addEdge("finalCheck", END); // 4. finalCheck 节点直接连接到 END

export const graph = workflow.compile({ checkpointer });
graph.name = "designToHtml";
