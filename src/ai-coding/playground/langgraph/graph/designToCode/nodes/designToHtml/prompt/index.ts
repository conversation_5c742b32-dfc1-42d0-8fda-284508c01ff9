import { PromptTemplate } from "@langchain/core/prompts";
import { readFileSync } from "fs";
import path from "path";

export function getSystemPrompt() {
  return PromptTemplate.fromTemplate(`
    你是一个经验丰富的网页设计师，擅长将设计稿转换为HTML代码。你的任务是根据设计稿，生成符合设计要求的HTML代码。
`);
}

export function getPicToHtmlPrompt(systemPrompt: string) {
  return PromptTemplate.fromTemplate(`
${systemPrompt}

用户输入: {input}

请仔细分析用户的输入，生成符合设计要求的HTML代码。

输出要求：
1. 生成符合设计要求的HTML代码
2. 保持HTML代码的逻辑性和条理性
3. 保持HTML代码的准确性和完整性

HTML代码：
`);
}

// HtmlToCode节点的系统提示
export function getHtmlToCodeSystemPrompt(
  designPageName: string,
  designPageContent: string
) {
  const refactorGuide = readFileSync(
    path.resolve(
      __dirname,
      "../../../../../../static/prompt/designCodeRefactor.md"
    ),
    "utf-8"
  );

  return `你是一个经验丰富的网页设计师，擅长将设计稿转换为HTML代码。你的任务是根据设计稿的html代码，生成符合以下重构要求的HTML代码：

设计稿名称: ${designPageName}
设计稿代码: ${designPageContent}
重构指南: ${refactorGuide}

请严格按照重构指南要求，生成符合要求的HTML代码，并使用工具将重构后的代码写入到本地文件。`;
}

export function getHtmlRefactorPrompt() {
  return `请严格按照要求重构设计稿代码，并使用工具将重构后的代码写入到本地文件。`;
}

// imgToCode节点的系统提示
export function getImgToCodeSystemPrompt() {
  return `你是一个经验丰富的网页设计师，擅长将图片设计稿转换为HTML代码。你的任务是根据图片设计稿，生成符合设计要求的HTML代码。

请仔细分析图片内容，生成符合设计要求的HTML代码，并使用工具将生成的代码写入到本地文件。`;
}

// reviewCode节点的系统提示
export function getReviewCodeSystemPrompt() {
  const checkList = readFileSync(
    path.resolve(
      __dirname,
      "../../../../../../static/prompt/designCodeChecklist.md"
    ),
    "utf-8"
  );
  return `现在，你将扮演一个极其严格、以发现瑕疵为荣的代码质量审计员（Code Quality Auditor）。你的唯一职责就是根据检查清单中的每一条规则，对你之前生成的代码进行最苛刻的审查。你的职业声-誉建立在找出最微小的、不符合规范的瑕疵之上。任何一点疏忽都是不可接受的。
请在心中仔细完成这份审计，找出所有不合规之处。
在你完成这次彻底的、无懈可击的内部审计后，请修复你在审计过程中发现的 所有 问题，包括但不限于：
任何不符合数据与视图分离原则的地方（如静态标题在 data 中）。
任何违反组件化拆分原则的地方（如 NavHeader 未与资产摘要分离）。
任何被遗漏的、模拟系统 UI 的元素（如底部的 Home Indicator）。
任何存在的内联样式。
任何缺失的、或格式不正确的业务组件注释。

检查清单: ${checkList}

请直接输出修复后的代码并使用工具把修复后的代码写入到本地文件。
`;
}

// 获取轮次控制提示
export function getRoundControlPrompt(currentRound: number, maxRounds: number) {
  return `当前是第${currentRound}轮审查，最大轮次为${maxRounds}轮。请在第${maxRounds}轮时确保代码质量达到标准，避免无限循环。`;
}

// failure节点的错误消息
export function getFailureMessage() {
  return "输入内容既不是HTML文件也不是图片，无法处理。请提供有效的HTML内容或图片。";
}
