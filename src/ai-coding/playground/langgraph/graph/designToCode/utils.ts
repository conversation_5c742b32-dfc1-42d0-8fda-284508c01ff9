import { AIMessage } from "@langchain/core/messages";

/**
 * 解析模型输出并提取文本内容的通用函数
 * 支持多种输出格式：字符串、数组、对象等
 */
export function parseModelOutput(response: AIMessage): string {
  if (!response) {
    return "";
  }

  // 如果响应内容是字符串，直接返回
  if (typeof response.content === "string") {
    return response.content;
  }

  // 如果响应内容是数组（多模态），尝试提取文本内容
  if (Array.isArray(response.content)) {
    const textParts = response.content
      .filter((part) => part.type === "text")
      .map((part) => (part as any).text)
      .join("");
    return textParts;
  }

  // 如果是对象，转换为JSON字符串
  if (typeof response.content === "object") {
    return JSON.stringify(response.content);
  }

  return "";
}

/**
 * 从工具调用结果中提取指定类型文件的内容
 * @param message AI消息
 * @param fileExtension 文件扩展名，如 '.html', '.css', '.js'
 * @returns 文件内容
 */
export function extractFileContentFromToolResult(
  message: AIMessage,
  fileExtension: string
): string {
  if (!message.tool_calls || message.tool_calls.length === 0) {
    return "";
  }

  // 查找文件写入工具的结果
  for (const toolCall of message.tool_calls) {
    if (toolCall.name === "writeFile" && toolCall.args) {
      try {
        const args =
          typeof toolCall.args === "string"
            ? JSON.parse(toolCall.args)
            : toolCall.args;

        // 如果写入的是指定类型的文件，返回其内容
        if (args.fileName && args.fileName.endsWith(fileExtension)) {
          return args.content || "";
        }
      } catch (error) {
        console.warn("解析工具调用参数失败:", error);
      }
    }
  }

  return "";
}

/**
 * 从工具调用结果中提取所有文件内容
 * @param message AI消息
 * @returns 文件名到内容的映射
 */
export function extractAllFileContentsFromToolResult(
  message: AIMessage
): Record<string, string> {
  const fileContents: Record<string, string> = {};

  if (!message.tool_calls || message.tool_calls.length === 0) {
    return fileContents;
  }

  // 查找所有文件写入工具的结果
  for (const toolCall of message.tool_calls) {
    if (toolCall.name === "writeFile" && toolCall.args) {
      try {
        const args =
          typeof toolCall.args === "string"
            ? JSON.parse(toolCall.args)
            : toolCall.args;

        if (args.fileName && args.content) {
          fileContents[args.fileName] = args.content;
        }
      } catch (error) {
        console.warn("解析工具调用参数失败:", error);
      }
    }
  }

  return fileContents;
}

/**
 * 检查模型输出是否包含工具调用
 */
export function hasToolCalls(message: AIMessage | undefined): boolean {
  return !!(message?.tool_calls && message.tool_calls.length > 0);
}

/**
 * 获取工具调用的名称列表
 */
export function getToolCallNames(message: AIMessage): string[] {
  if (!message.tool_calls || message.tool_calls.length === 0) {
    return [];
  }

  return message.tool_calls.map((toolCall) => toolCall.name);
}

/**
 * 检查是否调用了特定的工具
 */
export function hasSpecificToolCall(
  message: AIMessage,
  toolName: string
): boolean {
  return getToolCallNames(message).includes(toolName);
}
