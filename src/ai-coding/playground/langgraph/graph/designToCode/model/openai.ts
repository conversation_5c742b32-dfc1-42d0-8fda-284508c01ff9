import { ChatOpenAI } from "@langchain/openai";

type OpenaiProviderType = 'htsc' | 'openrouter' | 'default';

// 创建模型实例的函数
export function createOpenaiModel(providerType: OpenaiProviderType = 'htsc') {
  if (providerType === 'htsc') {
    console.log('提示: use ht::saas-deepseek-v3 model');
    return new ChatOpenAI({
      modelName: "ht::saas-deepseek-v3",
      temperature: 0,
      configuration: {
        baseURL: "http://*************/web/unauth/LLM_api_proxy/v1",
        apiKey: "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",
      },
    });
  } else if (providerType === 'openrouter') {
    console.log('提示: use openrouter model');
    return new ChatOpenAI({
      modelName: "google/gemini-2.5-flash",
      temperature: 0,
      configuration: {
        baseURL: "https://openrouter.ai/api/v1",
        apiKey:
          "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",
      },
    });
  } else {
    console.log('提示: use default model');
    return new ChatOpenAI({
      modelName: "gemini-2.5-flash",
      temperature: 0,
      configuration: {
        baseURL: process.env.LLM_BASE_URL || "http://localhost:8080/v1",
        apiKey: process.env.LLM_API_KEY || "xxx",
      },
    });
  }
}
