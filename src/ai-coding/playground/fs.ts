import * as fs from 'node:fs';
import * as stream from 'node:stream';
import * as fsPromises from 'node:fs/promises';
import * as nodePath from 'node:path';
import * as yazl from 'yazl';
import { open } from 'yauzl';
import { FileType } from './enums';

/**
 * 判断文件是否存在
 * @param path 文件路径
 * @returns 是否存在
 */
export function exists(path: string) {
  const exists: boolean = fs.existsSync(path);
  return exists;
}

/**
 * 判断文件类型
 * @param fullpath 文件路径
 * @param type 文件类型
 * @returns 是否匹配
 */
export function match(fullpath: string, type: FileType) {
  const s: fs.Stats = fs.statSync(fullpath);
  if (type === FileType.Directory && s.isDirectory()) return true;
  if (type === FileType.File && s.isFile()) return true;
  if (type === FileType.SymbolicLink && s.isSymbolicLink()) return true;
  return false;
}

/**
 * 同步读取文件
 * @param path 文件路径
 * @param encoding 编码
 * @returns 文件内容
 */
export function readFileSync(path: string, encoding: BufferEncoding = 'utf-8') {
  const text = fs.readFileSync(path, { encoding: encoding });
  return text;
}

/**
 * 读取文件
 * @param path 文件路径
 * @param encoding 编码
 * @returns 文件内容
 */
export async function readFile(path: string, encoding: BufferEncoding = 'utf-8'): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    fs.readFile(path, { encoding: encoding }, (err, data) => {
      if (err) reject(err);
      resolve(data);
    });
  });
}

/**
 * 同步写入文件
 * @param path 文件路径
 * @param content 文件内容
 */
export function writeFileSync(path: string, content: string) {
  fs.writeFileSync(path, content);
}

/**
 * 异步写入文件
 * @param path 文件路径
 * @param content 文件内容
 */
export async function writeFile(path: string, content: string): Promise<void> {
  await fsPromises.mkdir(nodePath.dirname(path), { recursive: true });
  return new Promise<void>((resolve, reject) => {
    fs.writeFile(path, content, (err) => {
      if (err) reject(err);
      resolve();
    });
  });
}

/**
 * 同步追加文件
 * @param path 文件路径
 * @param content 文件内容
 */
export function appendToFileSync(path: string, content: string) {
  fs.appendFileSync(path, content);
}

/**
 * 异步追加文件
 * @param path 文件路径
 * @param content 文件内容
 */
export async function appendToFile(path: string, content: string): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    fs.appendFile(path, content, (err) => {
      if (err) reject(err);
      resolve();
    });
  });
}

/**
 * 异步修改 json 文件
 * @param directory 目录
 * @param filename 文件名
 * @param defaultValue 默认值
 * @param mod 修改函数
 */
export async function modifyJson<T>(
  directory: string,
  filename: string,
  defaultValue: T,
  mod: (json: T) => boolean,
): Promise<T> {
  const config: T = await readJson<T>(directory, filename, defaultValue);
  const modified: boolean = mod(config);
  if (modified) {
    writeJson(JSON.stringify(config, null, 2), directory, filename);
  }
  return config;
}

/**
 * 异步读取 json 文件
 * @param directory 目录
 * @param filename 文件名
 * @param defaultValue 默认值
 * @returns 文件内容
 */
export async function readJson<T>(directory: string, filename: string, defaultValue: T): Promise<T> {
  const fullpath: string = nodePath.join(directory, filename);
  if (exists(fullpath) && match(fullpath, FileType.File)) {
    const content: string = await readFile(fullpath);
    return JSON.parse(content) as T;
  }
  return defaultValue;
}

/**
 * 异步写 json 文件
 * @param content 内容
 * @param directory 目录
 * @param filename 文件名
 */
export async function writeJson(content: string, directory: string, filename: string): Promise<void> {
  const filepath: string = nodePath.join(directory, filename);
  await writeFile(filepath, content);
}

export async function unzip(zip: string, dir: string) {
  return new Promise<void>((resolve) => {
    open(zip, { lazyEntries: true, autoClose: true }, (err, zipfile) => {
      if (err) {
        throw err;
      }
      zipfile.readEntry();
      zipfile.on('entry', function (entry) {
        if (/\/$/.test(entry.fileName)) {
          // Directory file names end with '/'.
          // Note that entries for directories themselves are optional.
          // An entry's fileName implicitly requires its parent directories to exist.
          zipfile.readEntry();
        } else {
          // file entry
          zipfile.openReadStream(entry, async function (err, readStream) {
            if (err) {
              throw err;
            }
            const dest = nodePath.join(dir, entry.fileName);
            await fsPromises.mkdir(nodePath.dirname(dest), { recursive: true });
            readStream.on('end', function () {
              zipfile.readEntry();
            });
            readStream.pipe(fs.createWriteStream(dest));
          });
        }
      });
      zipfile.on('close', () => {
        resolve();
      });
    });
  });
}

export async function readDirectory(path: string, options?: { recursive?: boolean }): Promise<[string, FileType][]> {
  const files = await fsPromises.readdir(path, {
    withFileTypes: true,
    recursive: options?.recursive ?? false,
  });
  return files.map((file) => {
    // 构建相对路径并统一使用正斜杠分隔符
    const relativePath = nodePath.join(nodePath.relative(path, file.parentPath ?? file.path), file.name);
    const normalizedPath = relativePath.replace(/\\/g, '/');
    
    return [
      normalizedPath,
      file.isDirectory() ? FileType.Directory : file.isSymbolicLink() ? FileType.SymbolicLink : FileType.File,
    ];
  });
}

/**
 * 压缩文件夹
 * @param path 输入文件夹
 */
export async function zipDirectory(path: string): Promise<stream.Readable> {
  const zip = new yazl.ZipFile();
  const files = await readDirectory(path, { recursive: true });
  for (const [name, type] of files) {
    if (type === FileType.File) {
      zip.addFile(nodePath.join(path, name), name);
    }
  }
  zip.end();
  return new stream.Readable().wrap(zip.outputStream);
}
