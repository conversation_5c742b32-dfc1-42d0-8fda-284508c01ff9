import { exec, spawn } from 'node:child_process';
import { playgroundRootPath } from './ctx';

// Return a valid yarn command depending on the yarn version
export async function yarnCommand(
  cmd: string,
  args: string,
  opts: {
    cwd: string;
  },
): Promise<string> {
  let ver = '';
  try {
    ver = await new Promise<string>((resolve, reject) => {
      exec('yarn -v', opts, (err, stdout, stderr) => {
        if (err) {
          reject(err);
        } else {
          resolve(stdout.trim());
        }
      });
    });
  } catch (e) {
    console.error(e);
  }
  if (ver.startsWith('1.')) {
    return `yarn ${cmd} ${args} -W`;
  }
  return `yarn ${cmd} ${args}`;
}

export function runYarnCommand(id: string, cmd: string) {
  const sourceDir = playgroundRootPath(id);
  return spawn(cmd, {
    shell: true,
    cwd: sourceDir,
    env: {
      ...process.env, // When env is provided, we need to merge it with the current process.env
      NO_COLOR: 'true',
      NODE_ENV: 'development', // Need to be development for yarn to install devDependencies
    },
  });
}
