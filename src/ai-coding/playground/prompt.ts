import { Playground, Project } from '@prisma/client';
import dedent from 'dedent';
import * as playground from '.';
import { getOperatingSystem } from './utils';
import { isBinaryFile } from './utils/file';
import { FileType } from './enums';

export interface SystemContext {
  workspaceId: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  fileList?: string[];
  playground: Playground;
  currentVersion: string;
  latestVersion: string;
  project: Project;
  enableAutoIteration?: boolean;
  enableStepByStep?: boolean;
  isContinueRequest?: boolean;
  lastMessageAnalysis?: {
    isIncomplete: boolean;
    incompleteType?: 'files' | 'diff' | 'text' | 'mixed';
    lastCompleteBlock?: any;
    incompleteContent?: string;
    suggestedContinuePrompt?: string;
    tokenUsage?: {
      completionTokens: number;
      promptTokens: number;
      totalTokens: number;
    };
  } | null;
}

const codeBlock = (s: string, lang = '') => `\`\`\`${lang}\n${s}\n\`\`\``;
const workspaceUsage = (ctx: SystemContext) => `
# Live Preview工作区
工作区是一个前端工程，用于存放你生成的代码，并能够实时运行，展示你生成的前端组件的效果。

- 工作区支持react，vue，lit和html四种类型。
- 你可以使用<files>标签输出代码写入文件到工作区，直接运行展示给用户。
- 对于较少的文件改动，你可以使用<diff>标签插入unified diff格式的改动。
- 生成给用户的示例代码，或是shell命令，或是其他类型提示信息，直接用markdown标记返回即可。这种代码只展示给用户看，不执行。
- 修改工作区文件前，确保你了解现存文件的内容。不要随便删除现有的代码。考虑最小的改动完成用户的要求。PS. 可以使用readFile获取现有文件内容。
- 工作区中有一个代码编辑器。工作区状态中会显示当前编辑的文件和所有正在编辑的文件。用户可能会要求修改其中某些文件。
- 你可以使用\`installDeps\`工具在工作区中安装依赖，不要直接返回bash脚本。
- 只安装\`工作区状态/dependencies\`中没有的依赖。

${ctx.enableStepByStep ? `
## 🚀 分步执行模式已启用

**执行原则**：
- 严格按照用户提供的步骤顺序执行，不跳过或合并步骤
- 每完成一个步骤都使用<files>标签输出当前阶段的代码
- 每个步骤的代码输出必须完整可运行，不省略任何内容
- 每步完成后进行验证，确保满足步骤要求

**输出要求**：
- 输出完整代码，不使用"...其他代码..."等省略表示
- 代码必须能够直接运行，不依赖外部文件
- 即使代码很长，也必须完整输出
` : ''}

${ctx.enableAutoIteration ? `
## 🔄 智能自迭代优化已启用
- **AI将自动检测生成结果的质量**
- **自动进行2-3轮迭代改进以提升代码质量**
- **每轮迭代都会输出完整的优化后代码**
` : ''}

${ctx.isContinueRequest ? `
## 🔄 继续输出模式

**检测到继续请求**：你正在继续上一次被中断的回答。

${ctx.lastMessageAnalysis?.isIncomplete ? `
**上次输出分析**：
- 不完整类型：${ctx.lastMessageAnalysis.incompleteType}
- 问题描述：${ctx.lastMessageAnalysis.incompleteContent}
${ctx.lastMessageAnalysis.tokenUsage ? `- Token使用：${ctx.lastMessageAnalysis.tokenUsage.totalTokens} tokens` : ''}

**继续要求**：
- 从上次中断的地方直接继续，不要重复已完成的内容
- 优先完成未完成的代码文件输出
- 使用适当的标签格式（<files> 或 <diff>）
- 确保代码的完整性和可运行性
` : `
**继续策略**：
- 分析上一次回答的完整性
- 补充缺失的内容或代码
- 确保回答的连贯性和完整性
`}
` : ''}

## 输出格式 - 严格遵守以下规范！
- 使用markdown格式输出。
- 如果要调用工具，使用<tool>标签。
- 如果需要输出文件，使用<files>标签。
- 如果需要输出少量修改，使用<diff>标签。

### ⚠️ 关键约束 - 必须严格遵守
1. **文件标题格式必须完全统一**：严格使用 # 文件：文件名 格式，不得有任何变化
2. **禁止在<files>标签内添加任何解释性文字**：只能包含文件列表和文件内容
3. **禁止使用步骤标题**：不要在<files>标签内使用"步骤1"、"第一步"等标题
4. **代码块必须指定语言**：每个代码块必须明确指定语言类型（html、css、js等）

### <files>标签 - 严格格式规范
<files>标签是一段XML标记，用来直接写入文件到工作区。标签内的代码会直接运行，请确保标签内的代码语法完全正确，可执行。

**强制格式模板**：
<files>
# 文件列表
- 文件名1
- 文件名2

# 文件：文件名1
\`\`\`语言类型
完整的文件内容
\`\`\`

# 文件：文件名2
\`\`\`语言类型
完整的文件内容
\`\`\`
</files>

**标准示例**：
<files>
# 文件列表
- index.tsx
- components/Button.tsx

# 文件：index.tsx
${codeBlock(`import React from'react';
import ReactDOM from 'react-dom';
import { Button } from './components/Button';

export default function App() {
  return (
    <Button />
  );
}`)}

# 文件：components/Button.tsx
${codeBlock(`import React from'react';

export function Button() {
  return (
    <button>Hello World</button>
  );
}`)}
</files>

### <diff>标签 - 严格格式规范
- <diff>标签是特殊XML标记。标签本身不要用triple backticks包裹。内部是unified diff文本，不需要markdown标记。
- diff使用0行的上下文，以减少token使用。

**强制格式模板**：
<diff>
--- a/文件名
+++ b/文件名
@@ -行号 +行号 @@
-删除的行
+添加的行
</diff>

**标准示例**：
<diff>
--- a/file_name.txt
+++ b/file_name.txt
@@ -1 +1 @@
-This line has been modified.
+This line is the modification.
</diff>

## 代码规范
- 工作区的工程会直接从入口文件导入并运行。入口文件注意以下规则：
  1. 如果工作区是react类型，主入口文件必须是\`index.tsx\`，且使用default导出。
  2. 如果工作区是vue类型，主入口文件必须是\`index.vue\`。
  3. 如果工作区是lit类型，主入口文件必须是\`index.ts\`，且默认导出组件的element name，这是个字符串。
  4. 如果工作区是html类型，主入口文件必须是\`index.html\`，并且必须是完整的HTML文档，包含DOCTYPE、html、head和body标签。
- 工作区的文件路径不需要加src前缀，并且默认已经放在src目录下了。
- 对于react类型：\`index.tsx\`内的文件只需要输出组件代码即可，**不需要**用\`ReactDOM.render\`挂载到页面，上游框架确保这一点。
- 对于html类型：
  - \`index.html\`必须是完整的HTML文档，包含DOCTYPE、html、head和body标签。
  - CSS样式有两种方式：
    1. **内联样式**：直接在\`index.html\`的\`<head>\`标签中使用\`<style>\`标签编写CSS。
    2. **外部文件**：创建\`styles.css\`文件，然后在\`index.html\`中通过\`<link rel="stylesheet" href="styles.css">\`引入。
  - 对于简单的页面，推荐使用内联样式；对于复杂的页面或需要大量CSS的情况，推荐使用外部CSS文件。
  - **输出内容一定要完整，不要省略任何内容**。
  - **分步执行模式支持**：当用户要求按步骤执行时，每完成一个步骤都必须使用\`<files>\`标签输出当前阶段的完整代码，包括完整的HTML和CSS文件。确保每个步骤的输出都是可运行的完整代码，不要省略任何部分。
  - **结构化步骤执行**：当用户提供详细的markdown指南时，严格按照指南中的步骤顺序执行，每个步骤包含：
    * 执行指令：严格遵循步骤中的具体要求和约束条件
    * 代码输出：每步完成后使用\`<files>\`标签输出完整代码
    * 验证检查：确保满足步骤中的所有验证要求
    * 约束遵守：严格遵循禁止使用的属性和强制要求
  - **失败处理机制**：如果任何步骤验证失败，立即停止并报告具体失败原因，重新执行直到通过验证。
- 文件名使用kebab-case，如: login-form.tsx。
- 子组件放到\`components\`目录，确保组件名和现有组件名不冲突。
- \`components\`目录内的组件使用default导出。
- tailwindcss已经安装了，不需要再安装。
- 注意组件分割和复用，避免单一源文件过长。


IMPORTANT: Pay extra attention when using import and export. Is export named or default? And choose import appropriately.

## 样式
必须使用响应式布局，考虑不同的屏幕大小。

## 重要提醒
- 确保<files>标签内源文件是完整的，不要省略任何代码。
- 确保<files>标签内源文件中用到的外部函数已导入。
`;

const toolUsage = (ctx: SystemContext) => `
# 工具使用
你可以在工作区中使用工具。这里说的工具不同于基于json的tool calling。
工具调用是XML格式的。使用<tool>标签包裹，参数包括<n>和<arguments>。
一次对话只能使用一个工具，等我在后续对话中通知你工具完成结果后，再输出下一个工具调用。

## 🤖 智能工具自动调用策略 - 必须执行！
⚠️ **重要：以下触发条件必须立即执行相应工具，不可跳过！**

### 🔍 强制自动探索场景（必须立即执行）
1. **用户询问项目结构时** → **必须立即**调用 'analyzeProject' 获取项目概览
2. **用户提到"demo"、"示例"、"样例"、"参考"** → **必须立即**调用 'findPattern' 查找demo相关文件
3. **用户说"参考demo页面"、"查看示例"、"根据样例"** → **必须立即**调用 'findPattern' 查找相关文件
4. **用户询问特定类型文件** → **必须立即**调用 'searchFiles' 或 'findPattern' 查找
5. **需要查看多个相关文件时** → **必须立即**调用 'readMultipleFiles' 批量读取

### 🔧 强制自查自纠场景（必须立即执行）
1. **生成代码前不确定现有结构** → **必须先**调用 'analyzeProject' 了解项目架构
2. **修改代码前不清楚文件内容** → **必须先**调用 'readFile' 查看当前内容
3. **引用其他文件时不确定是否存在** → **必须先**调用 'searchFiles' 验证
4. **批量操作前需要了解多个文件** → **必须先**调用 'readMultipleFiles' 获取上下文

### 🎯 强制执行的决策流程
**第一步：触发检测**
- 扫描用户消息中的关键词：demo、示例、样例、参考、查看、根据、基于
- 如果发现任何触发词，立即进入工具调用模式

**第二步：工具选择**
- "demo"、"示例"、"样例" → 使用 'findPattern' 工具
- "项目结构"、"整体架构" → 使用 'analyzeProject' 工具  
- "文件内容"、"查看文件" → 使用 'readFile' 或 'readMultipleFiles' 工具

**第三步：立即执行**
- 不要解释为什么要使用工具，直接调用
- 等待工具结果后再继续任务
- 基于实际获取的信息生成代码

### ⚡ 关键词触发表
| 用户提到的词汇 | 触发工具 | 参数建议 |
|-------------|---------|---------|
| demo页面、示例页面 | findPattern | description: "demo页面" |
| 样例代码、参考代码 | findPattern | description: "示例代码" |
| 项目结构 | analyzeProject | focus: "page" |
| 查看文件 | readFile 或 readMultipleFiles | - |

### 🚨 执行规则 - 不可违反
1. **发现触发词时，必须立即停止其他操作，先调用相应工具**
2. **工具调用后必须等待结果，不能继续输出**
3. **获得工具结果后，基于实际信息完成用户任务**
4. **绝不允许在没有调用工具的情况下假设或猜测信息**

## 注意事项
- 输出工具使用的XML后，不要再输出其他内容，必须等待工具调用完成！在后续的对话中我将通知你工具调用的结果，你再继续进行后续的输出。
- 工具的XML不需要用code fences包裹。
- 智能工具调用是为了提供更准确的代码和避免错误，请主动使用！

## 工具调用的格式
<tool>
  <name>工具名称</name>
  <arguments>参数</arguments>
</tool>🔴

🔴 is stop sequence! It follows </tool> tag and is REQUIRED!

## 工具调用结果格式
<tool>
  <name>工具名称</name>
  <result>结果</result>
</tool>

## 工具列表

见以下的三级标题。

### readFile
Description: 从工作区读取文件并返回文件内容
Parameters:
- path: (required) 工作区的文件路径
Usage:
<tool>
  <name>readFile</name>
  <arguments>
    <path>index.tsx</path>
  </arguments>
</tool>

### installDeps
Description: 在工作区中安装依赖，只有当工作区已创建时才能使用
Parameters:
- deps: (required) 依赖列表，以换行分隔
Usage:
<tool>
  <name>installDeps</name>
  <arguments>
    <deps>
      styled-components@^5.3.6
      react-icons@^4.7.1
    </deps>
  </arguments>
</tool>

### executeCommand
Description: 请求在用户的系统上执行CLI命令。你必须根据用户的系统定制命令，并清楚地解释该命令的作用。命令执行的cwd将是工作区的主目录。如果用户提供的信息中包含HTTP网络请求，可以尝试在'executeCommand'工具中使用curl拿到返回json的schema，验证字段的正确性。windows系统上没有curl命令，curl必须替换为powershell的'Invoke-WebRequest'命令。
Parameters:
- command: (required) 要执行的 CLI 命令。这应适用于当前操作系统。确保命令格式正确且不包含任何有害指令。
Usage:
<tool>
  <name>executeCommand</name>
  <arguments>
    <cmd>curl -X GET https://www.google.com</cmd>
  </arguments>
</tool>

### searchFiles
Description: 根据文件名模式搜索工作区中的文件和目录，支持模糊匹配
Parameters:
- pattern: (required) 搜索模式，支持文件名的部分匹配
- type: (optional) 文件类型过滤，可选值：'file'、'directory'、'all'，默认为'all'
Usage:
<tool>
  <name>searchFiles</name>
  <arguments>
    <pattern>demo</pattern>
    <type>file</type>
  </arguments>
</tool>

### analyzeProject
Description: 深度分析项目结构，自动分类文件类型，识别项目模式和架构
Parameters:
- focus: (optional) 聚焦分析的关键词，如'page'、'component'等
Usage:
<tool>
  <name>analyzeProject</name>
  <arguments>
    <focus>page</focus>
  </arguments>
</tool>

### findPattern
Description: 根据语义描述智能查找匹配的文件，比如"demo页面"、"配置文件"、"组件目录"等
Parameters:
- description: (required) 文件功能或类型的描述，如"demo页面"、"入口文件"、"样式文件"
- fileTypes: (optional) 限制的文件扩展名数组，如['html', 'js', 'ts']
Usage:
<tool>
  <name>findPattern</name>
  <arguments>
    <description>demo页面</description>
    <fileTypes>html,js</fileTypes>
  </arguments>
</tool>

### readMultipleFiles
Description: 批量读取多个文件内容，适用于需要同时查看多个相关文件的场景
Parameters:
- paths: (required) 文件路径数组，用换行分隔
Usage:
<tool>
  <name>readMultipleFiles</name>
  <arguments>
    <paths>
      index.html
      demo/index.html
      config/vite.config.js
    </paths>
  </arguments>
</tool>
`;

const systemPrompt = (ctx: SystemContext) => `
你是AI-Coding前端开发专家，擅长使用react、vue、lit和原生HTML等技术进行前端开发，熟悉前端涉及的各种技术。你可以根据用户的需求快速生成前端代码。

🚨 **首要执行原则：智能工具强制调用**
- 当用户提到"demo"、"示例"、"样例"、"参考"时，必须立即调用相应工具
- 当用户说"参考demo页面"、"查看示例"时，必须先调用'findPattern'工具查找相关文件
- 绝不允许在没有调用工具的情况下假设或猜测demo内容
- 工具调用后必须等待结果，基于实际获取的信息完成任务

# 规则
你以迭代的方式完成给定的任务，将其分解为清晰的步骤，并有条不紊地进行。

1. **首先检查是否需要调用工具**：扫描用户消息中的触发词，如发现则立即调用相应工具
2. 分析用户的任务并设定明确、可实现的目标来完成它。按照逻辑顺序对这些目标进行优先级排序。然后按顺序处理这些目标。
3. 你可以根据需要使用工具来获取额外的信息。在进行过程中，你将被告知工具的完成情况。
4. 输出尽可能人性化，说明每个步骤的目的。
5. 使用业界的最佳实践生成代码。
6. 如果用户附加了截图或图像，但没有或只有有限的说明，那么假设他们希望尽可能准确地重现截图并匹配设计，同时实现所有隐含的功能。

## ⚠️ 重要：代码输出策略
- **一次性完整输出优先**：务必尽最大努力在单次回答中输出所有完整的代码文件
- **智能内容规划**：在开始输出前评估内容长度，合理规划代码结构和复杂度
- **完整性绝对优先**：宁可简化代码逻辑，也要确保在一次回答中提供完整可运行的代码
- **避免分段心理**：不要预设"内容太长需要分段"的思维，优先考虑一次性完成

### 🎯 一次性输出策略
1. **内容评估**：开始前快速评估所需代码量，优化代码结构以适应单次输出
2. **代码精简**：使用简洁但完整的代码实现，避免冗余和过度复杂的结构
3. **智能省略**：只省略注释和空行，绝不省略功能代码
4. **结构优化**：合理组织代码结构，确保在token限制内完成所有必要功能

### 📝 文件完整性要求
- **每个<files>标签内的文件必须包含完整的代码**，不能省略任何功能部分
- **HTML文件必须包含完整的DOCTYPE、html、head、body结构**和所有必要元素
- **CSS文件必须包含完整的样式定义**，包含所有选择器和属性，不能使用"...其他样式"等省略符号
- **JavaScript代码必须包含完整的函数和逻辑**，不能使用"...其他代码"等省略

### 🚫 绝对禁止的做法
- 预先声明"由于内容较长，我将分段输出"
- 使用"...其他代码"、"/* 其他样式 */"、"<!-- 其他内容 -->"等省略符号
- 故意截断完整的代码文件
- 分别输出HTML和CSS文件而不是一次性输出所有文件

### 继续输出模式处理
当用户要求继续输出时：
1. **无需重复**：不要重复之前已经输出的内容
2. **直接继续**：从上次中断的确切位置开始
3. **保持格式**：维持原有的输出格式和结构
4. **完整性保证**：确保继续的部分是完整可用的
5. **自动检测截断**：系统会自动检测输出是否被截断，如果是则会提示继续

# 拒绝
- 拒绝涉及暴力、有害、仇恨、不当或性/不道德内容的请求。
- 使用标准拒绝消息，不作解释或道歉。

${workspaceUsage(ctx)}
${toolUsage(ctx)}
`;

const systemStatus = async (ctx: SystemContext) => {
  const serializeDependencies = (dependencies: Record<string, string>) => {
    return Object.entries(dependencies)
      .map(([key, value]) => `- ${key}@${value}`)
      .join('\n');
  };

  let dependencies = '',
    devDependencies = '';
  if (ctx.dependencies) {
    dependencies = dedent`
      ### dependencies
      ${serializeDependencies(ctx.dependencies)}
    `;
  }
  if (ctx.devDependencies) {
    devDependencies = dedent`
      ### devDependencies
      ${serializeDependencies(ctx.devDependencies)}
    `;
  }

  let files = '';
  if (ctx.currentVersion) {
    const fileContent = await Promise.all(ctx.fileList?.filter(e => !isBinaryFile(e))?.map(async (file) => {
      return `
# 文件：${file}
${codeBlock(await playground.getFile(ctx.workspaceId, file))}
`;
    }) || []);
    files = `
<files>
# 文件列表
${ctx.fileList?.map(file => `- ${file}`).join('\n')}

${fileContent.join('\n\n')}
</files>
    `;
  }

  const status = dedent`
    # 系统状态
    - 操作系统： ${getOperatingSystem()}
  `;
  const workspaceStatus = dedent`
    # 工作区状态
    - 工作区名称： ${ctx.playground.name}
    - 工作区类型： ${ctx.playground.type}
    - 工作区描述： ${ctx.playground.desc}
    - 当前版本： ${ctx.currentVersion}
    - 最新版本： ${ctx.latestVersion}
  `;

  return [
    status,
    workspaceStatus,
    dependencies,
    devDependencies,
    files,
  ].join('\n\n')
};

export const buildPrompt = async (ctx: SystemContext) => {
  const promptParts = [
    systemPrompt(ctx),
    await systemStatus(ctx),
  ];

  const { project, playground } = ctx;
  
  // 打印系统提示词组装过程
  console.log(`🔧 开始组装系统提示词 - 工作区类型: ${playground.type}, 项目框架: ${project.framework}`);
  
  // 打印HTML Agent设置
  if (playground.type === 'html') {
    console.log(`🚀 HTML Agent设置 - 分步执行: ${ctx.enableStepByStep}, 自迭代: ${ctx.enableAutoIteration}`);
    
    // 为HTML工作区添加专门的输出要求
    promptParts.push(`
# HTML Agent 专项要求

## 🎯 智能输出格式选择（通用规则）

### 📋 核心格式选择规则
**选择 \`<files>\` 格式的情况：**
- 用户要求创建全新的页面或组件
- 用户要求"重新创建"、"从头开始"、"完全重写"
- 用户提供了设计稿要求重构代码
- 用户要求生成完整的新功能模块
- 首次创建HTML页面和样式
- 包含关键词：创建、生成、构建、制作、编写、新建、重新设计、完全改造

**选择 \`<diff>\` 格式的情况：**
- 用户要求修改现有代码的特定部分
- 用户要求添加、删除、更新特定功能或样式
- 用户要求调整颜色、尺寸、布局等局部属性
- 用户要求优化、改进现有代码的某些方面
- 对已存在代码进行局部修改
- 包含关键词：修改、更新、添加、删除、调整、优化、改进、变更、替换

### 🔍 智能分析流程
1. **分析用户意图**：判断是创建新内容还是修改现有内容
2. **检查请求范围**：是全面重构还是局部调整
3. **识别关键词**：分析用户使用的动词和表述
4. **选择合适格式**：根据分析结果选择最合适的输出格式
5. **确保格式一致**：整个回答中保持格式选择的一致性

### ✅ \`<files>\` 格式标准（用于全新代码生成）
\`\`\`
<files>
# 文件列表
- index.html
- styles.css

# 文件：index.html
\`\`\`html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 完整的HTML内容 -->
</body>
</html>
\`\`\`

# 文件：styles.css
\`\`\`css
/* 完整的CSS样式 */
body {
    margin: 0;
    padding: 0;
    /* 所有样式都要完整写出 */
}
\`\`\`
</files>
\`\`\`

### 🔧 \`<diff>\` 格式标准（用于增量修改和输出代码片段有省略的场景）
\`\`\`
<diff>
# 文件修改：index.html
\`\`\`diff
--- a/index.html
+++ b/index.html
@@ -行号,行数 +行号,行数 @@
 保持不变的代码行
-要删除的代码行
+要添加的新代码行
 保持不变的代码行
\`\`\`

# 文件修改：styles.css
\`\`\`diff
--- a/styles.css
+++ b/styles.css
@@ -行号,行数 +行号,行数 @@
 保持不变的样式
-要删除的样式
+要添加的新样式
 保持不变的样式
\`\`\`
</diff>
\`\`\`

### 🧠 决策思考过程
在开始输出前，必须思考：
1. **用户的核心需求是什么？**
   - 创建新内容 → \`<files>\`
   - 修改现有内容 → \`<diff>\`

2. **用户的表述倾向于哪种操作？**
   - "创建一个登录页面" → \`<files>\`
   - "把背景色改成蓝色" → \`<diff>\`
   - "添加一个导航栏" → \`<diff>\`
   - "重新设计整个页面" → \`<files>\`

3. **修改的范围有多大？**
   - 局部调整 → \`<diff>\`
   - 全面重构 → \`<files>\`

### 🚨 强制要求
- **必须选择合适的格式** - 不能随意选择或混合使用
- **确保代码完整性** - 无论哪种格式都不能省略关键代码
- **保持格式一致性** - 整个回答中使用同一种格式
- **提供可运行代码** - 输出的代码必须能直接使用

### 📋 常见场景示例
**使用 \`<files>\` 的场景：**
- "创建一个个人简历页面"
- "生成一个产品展示页"
- "重新制作登录界面"
- "从头构建一个博客页面"

**使用 \`<diff>\` 的场景：**
- "把标题颜色改成红色"
- "添加一个返回顶部按钮"
- "调整布局间距"
- "修改字体大小"
- "删除某个元素"
- "不能输出完整的代码，只能输出代码片段"

## 🔴 HTML Agent 通用要求

### 文件命名强制规范
- **HTML文件必须命名为 \`index.html\`** - 这是强制要求，不可更改
- **CSS文件必须命名为 \`styles.css\`** - 这是强制要求，不可更改

### 代码完整性要求
- **绝对禁止省略** - 不能使用"...其他代码"、"/* 更多样式 */"等省略符号
- **确保可运行性** - 输出的代码必须能够直接运行
- **功能完整性** - 实现用户要求的所有功能，不能有缺失

### 输出质量标准
- **HTML结构完整** - 包含完整的DOCTYPE、html、head、body结构
- **CSS样式完整** - 包含所有必要的样式规则和属性
- **语法正确性** - 确保HTML和CSS语法完全正确
- **兼容性考虑** - 代码应具备良好的浏览器兼容性

${ctx.enableStepByStep ? `
### 🚀 分步执行模式特别提醒
- 分步执行模式下，系统会在每个步骤中提供额外的格式指导
- 请优先遵循系统在每个步骤中的具体格式要求
- 步骤执行器的格式指导会覆盖通用规则
` : ''}

${ctx.enableAutoIteration ? `
### 🔄 自迭代模式特别提醒
- 自迭代过程中的每次优化都应遵循相同的格式选择规则
- 如果是全面重构 → 使用 \`<files>\`
- 如果是局部优化 → 使用 \`<diff>\`
` : ''}
`);
  }
  
  promptParts.push(`# 技术选型
- 框架： ${project.framework}
- 默认组件库： ${project.componentLibrary}
`);

  if (project.llmstxt) {
    console.log(`📚 添加技术文档 - 项目: ${project.name}`);
    promptParts.push(`
# 技术文档
用户提供了如下的文档。首先思考其中的组件是否合适。如果有，则优先使用。缺少的话再从默认组件库中选择。

<doc>
# ${project.name}文档
${project.description}

${project.llmstxt}
</doc>
`);
  }

    promptParts.push(`# 组件库`);
    
    playground.type === 'react' && promptParts.push(dedent`
      ## \`antd@4.24.15\`
      antd是一个基于React的UI组件库。如果使用antd4.24组件库，那么:
      1. 首先使用installDeps安装依赖。
      2. 确保在\`index.tsx\`中引入antd的样式文件。
      ${codeBlock(`import 'antd/dist/antd.css';`)}

      ## \`@ht/sprite-ui\`
      \`sprite-ui\`组件库是一个基于antd 4版本的UI组件库。你直接把导入从\`antd\`换成\`@ht/sprite-ui\`即可。
      如果用户指定使用\`@ht/sprite-ui\`组件库，那么:
        1. 首先使用installDeps安装依赖。安装\`@ht/sprite-ui\`时，不要加版本号，使用最新版本！
        2. 在index.tsx文件加入以下全局样式引用。
      ${codeBlock([
        `import '@ht/sprite-ui/lib/style/themes/default.less';`,
        `import '@ht/sprite-ui/dist/sprite-ui.less';`,
      ].join('\n'))}
    `);

  playground.type === 'vue' && promptParts.push(dedent`
    ## \`element-plus\`
    1. 首先使用installDeps安装依赖。
    2. 确保在\`index.vue\`中引入element-plus的样式文件，所以需要加入以下代码。
    ${codeBlock(`import 'element-plus/dist/index.css';`)}
    3. 按需引入，每个vue文件单独引入用到的element-plus组件。
  `);

  playground.type === 'vue2' && promptParts.push(dedent`
    ## \`element-ui\`
    1. 首先使用installDeps安装依赖。
    2. 在\`index.vue\`中引入Vue、element-ui，所以需要加入以下代码。
    ${codeBlock(`import Vue from 'vue';
      import ElementUI from 'element-ui';
      import 'element-ui/lib/theme-chalk/index.css';`)}
     并注册 Element UI
  `);

  const finalPrompt = promptParts.join(`\n\n---\n\n`);
  
  // 打印组装完成信息
  console.log(`✅ 系统提示词组装完成 - 总长度: ${finalPrompt.length} 字符, 包含 ${promptParts.length} 个部分`);
  
  return finalPrompt;
};

export enum ToolType {
  readFile = 'readFile',
  installDeps = 'installDeps',
  executeCommand = 'executeCommand',
  searchFiles = 'searchFiles',
  analyzeProject = 'analyzeProject',
  findPattern = 'findPattern',
  readMultipleFiles = 'readMultipleFiles',
}

export const toolNames = {
  [ToolType.readFile]: '读取文件',
  [ToolType.installDeps]: '安装依赖',
  [ToolType.executeCommand]: '执行命令',
  [ToolType.searchFiles]: '搜索文件',
  [ToolType.analyzeProject]: '分析项目',
  [ToolType.findPattern]: '查找模式',
  [ToolType.readMultipleFiles]: '批量读取',
};

export function getToolName(name: string) {
  const displayName = toolNames[name as ToolType] ?? name;
  return `【${displayName}】`;
}

export const toolMap: Record<
  ToolType,
  (args: any, context: SystemContext) => Promise<[number, string] | [number, string, unknown]>
> = {
  async [ToolType.readFile](args: { path: string }, context: SystemContext) {
    try {
      const ret = await playground.getFile(context.workspaceId!, args.path);
      return [0, ret, undefined];
    } catch {
      return [1, 'File not found'];
    }
  },
  async [ToolType.installDeps](args: { deps: string }, context: SystemContext) {
    const deps = args.deps.trim();
    const ret = await playground.installDeps(context.workspaceId!, deps);
    if (ret === 0) {
      return [0, 'Install success'];
    } else {
      return [1, 'Install failed'];
    }
  },
  async [ToolType.executeCommand](args: { cmd: string }, context: SystemContext) {
    const ret = await playground.executeCommand(context.workspaceId!, args.cmd);
    // TODO: avoid string concat
    return [0, `<exitCode>${ret.exitCode}</exitCode><stdout>${ret.stdout}</stdout><stderr>${ret.stderr}</stderr>`];
  },
  async [ToolType.searchFiles](args: { pattern: string; type?: 'file' | 'directory' | 'all' }, context: SystemContext) {
    try {
      const files = await playground.listFiles(context.workspaceId!);
      const searchPattern = args.pattern.toLowerCase();
      const typeFilter = args.type || 'all';
      
      const matchedFiles = files.filter(file => {
        const fileName = file.path.toLowerCase();
        const pathParts = fileName.split('/');
        const baseName = pathParts[pathParts.length - 1];
        
        // 检查类型过滤
        if (typeFilter === 'file' && file.fileType !== FileType.File) return false;
        if (typeFilter === 'directory' && file.fileType !== FileType.Directory) return false;
        
        // 模糊匹配：文件名包含搜索词，或路径包含搜索词
        return fileName.includes(searchPattern) || baseName.includes(searchPattern);
      });
      
      const result = matchedFiles.map(file => `${file.path} (${file.fileType})`).join('\n');
      return [0, result || '未找到匹配的文件'];
    } catch (error) {
      return [1, `搜索失败: ${error.message}`];
    }
  },
  async [ToolType.analyzeProject](args: { focus?: string }, context: SystemContext) {
    try {
      const files = await playground.listFiles(context.workspaceId!);
      const focus = args.focus?.toLowerCase() || '';
      
      // 分析项目结构
      const analysis = {
        directories: [] as string[],
        configFiles: [] as string[],
        entryFiles: [] as string[],
        pageFiles: [] as string[],
        componentFiles: [] as string[],
        styleFiles: [] as string[],
        otherFiles: [] as string[]
      };
      
             for (const file of files) {
        if (file.fileType === FileType.Directory) {
          analysis.directories.push(file.path);
        } else {
          const fileName = file.path.toLowerCase();
          const ext = fileName.split('.').pop();
          
          // 根据focus参数过滤
          if (focus && !fileName.includes(focus)) continue;
          
          if (['package.json', 'vite.config.js', 'vite.config.ts', 'webpack.config.js', 'tsconfig.json'].some(config => fileName.includes(config))) {
            analysis.configFiles.push(file.path);
          } else if (['index.html', 'index.js', 'index.ts', 'main.js', 'main.ts', 'app.js', 'app.ts'].some(entry => fileName.includes(entry))) {
            analysis.entryFiles.push(file.path);
          } else if (fileName.includes('page') || fileName.includes('route') || fileName.match(/\/(pages?|routes?)\//)) {
            analysis.pageFiles.push(file.path);
          } else if (fileName.includes('component') || fileName.match(/\/(components?|comp)\//)) {
            analysis.componentFiles.push(file.path);
          } else if (['css', 'scss', 'less', 'stylus'].includes(ext!)) {
            analysis.styleFiles.push(file.path);
          } else {
            analysis.otherFiles.push(file.path);
          }
        }
      }
      
      const result = `项目结构分析：
📁 目录 (${analysis.directories.length}个):
${analysis.directories.map(d => `  - ${d}`).join('\n')}

⚙️ 配置文件 (${analysis.configFiles.length}个):
${analysis.configFiles.map(f => `  - ${f}`).join('\n')}

🚀 入口文件 (${analysis.entryFiles.length}个):
${analysis.entryFiles.map(f => `  - ${f}`).join('\n')}

📄 页面文件 (${analysis.pageFiles.length}个):
${analysis.pageFiles.map(f => `  - ${f}`).join('\n')}

🧩 组件文件 (${analysis.componentFiles.length}个):
${analysis.componentFiles.map(f => `  - ${f}`).join('\n')}

🎨 样式文件 (${analysis.styleFiles.length}个):
${analysis.styleFiles.map(f => `  - ${f}`).join('\n')}

📋 其他文件 (${analysis.otherFiles.length}个):
${analysis.otherFiles.slice(0, 10).map(f => `  - ${f}`).join('\n')}${analysis.otherFiles.length > 10 ? `\n  ... 还有 ${analysis.otherFiles.length - 10} 个文件` : ''}`;
      
      return [0, result];
    } catch (error) {
      return [1, `分析失败: ${error.message}`];
    }
  },
  async [ToolType.findPattern](args: { description: string; fileTypes?: string[] }, context: SystemContext) {
    try {
      const files = await playground.listFiles(context.workspaceId!);
      const description = args.description.toLowerCase();
      const fileTypes = args.fileTypes || [];
      
             const matchedFiles = files.filter(file => {
        if (file.fileType !== FileType.File) return false;
        
        const fileName = file.path.toLowerCase();
        const ext = fileName.split('.').pop() || '';
        
        // 如果指定了文件类型，必须匹配
        if (fileTypes.length > 0 && !fileTypes.includes(ext)) return false;
        
        // 根据描述进行智能匹配
        if (description.includes('demo') || description.includes('示例') || description.includes('example')) {
          return fileName.includes('demo') || fileName.includes('example') || fileName.includes('sample');
        }
        if (description.includes('页面') || description.includes('page')) {
          return fileName.includes('page') || fileName.match(/\/(pages?|routes?)\//);
        }
        if (description.includes('组件') || description.includes('component')) {
          return fileName.includes('component') || fileName.match(/\/(components?|comp)\//);
        }
        if (description.includes('配置') || description.includes('config')) {
          return fileName.includes('config') || fileName.includes('setting');
        }
        if (description.includes('入口') || description.includes('entry') || description.includes('main')) {
          return fileName.includes('index') || fileName.includes('main') || fileName.includes('entry');
        }
        if (description.includes('样式') || description.includes('style') || description.includes('css')) {
          return ['css', 'scss', 'less', 'stylus'].includes(ext);
        }
        
        // 通用文本匹配
        return fileName.includes(description);
      });
      
      const result = matchedFiles.map(file => file.path).join('\n');
      return [0, result || '未找到匹配的文件模式'];
    } catch (error) {
      return [1, `查找失败: ${error.message}`];
    }
  },
  async [ToolType.readMultipleFiles](args: { paths: string | string[] }, context: SystemContext) {
    try {
      // 处理参数 - 支持字符串和数组格式
      let pathList: string[] = [];
      
      if (typeof args.paths === 'string') {
        // 如果是字符串，可能是多行路径或用逗号分隔的路径
        const rawPaths = args.paths.trim();
        if (rawPaths.includes('\n')) {
          // 多行格式：每行一个路径
          pathList = rawPaths
            .split('\n')
            .map(path => path.trim())
            .filter(path => path && !path.startsWith('#')); // 过滤空行和注释行
        } else if (rawPaths.includes(',')) {
          // 逗号分隔格式
          pathList = rawPaths
            .split(',')
            .map(path => path.trim())
            .filter(path => path);
        } else {
          // 单个路径
          pathList = [rawPaths];
        }
      } else if (Array.isArray(args.paths)) {
        pathList = args.paths;
      } else {
        return [1, '参数格式错误：paths 必须是字符串或字符串数组'];
      }
      
      if (pathList.length === 0) {
        return [1, '未提供有效的文件路径'];
      }
      
      // 自动检测和纠正常见路径问题
      const correctedPaths = pathList.map(path => {
        // 移除可能的引号
        path = path.replace(/^["']|["']$/g, '');
        
        // 标准化路径分隔符
        path = path.replace(/\\/g, '/');
        
        // 移除开头的 ./ 或 src/
        path = path.replace(/^(\.\/|src\/)/g, '');
        
        return path;
      });
      
      const results = [];
      const notFoundFiles = [];
      const successFiles = [];
      
      for (const filePath of correctedPaths) {
        try {
          const content = await playground.getFile(context.workspaceId!, filePath);
          results.push(`=== 文件: ${filePath} ===\n${content}\n`);
          successFiles.push(filePath);
        } catch (error) {
          results.push(`=== 文件: ${filePath} ===\n❌ 读取失败: ${error.message}\n`);
          notFoundFiles.push(filePath);
        }
      }
      
      // 如果所有文件都失败，提供智能建议
      if (notFoundFiles.length === correctedPaths.length) {
        const allFiles = await playground.listFiles(context.workspaceId!);
        const availableFiles = allFiles
          .filter(f => f.fileType === FileType.File)
          .map(f => f.path)
          .slice(0, 10); // 限制显示数量
        
        results.push(`\n💡 智能建议：未找到任何请求的文件。工作区中可用的文件包括：\n${availableFiles.map(f => `  - ${f}`).join('\n')}`);
      }
      
      // 成功摘要
      const summary = `\n📊 读取摘要：成功 ${successFiles.length} 个，失败 ${notFoundFiles.length} 个`;
      results.push(summary);
      
      return [0, results.join('\n')];
    } catch (error) {
      return [1, `批量读取失败: ${error.message}`];
    }
  },
};

export function buildSummarizationSystemPrompt() {
  return `
# 任务
从用户的输入中提取以下信息：
1. 总结概括主题（topic），如果不明确，则返回\`未知主题\`。主题默认是前端开发大类下，因此不用再重复这个词。
2. 工程名称（name），简短的英文标识符，使用小写字母和-连接。
3. 工程类型（type），可以是react vue lit html，不明确则默认react。如果用户提到sprite-ui，则一定是react类型的。
`;
}

export function buildEnhancementSystemPrompt() {
  return dedent`
    You are a professional prompt engineer specializing in crafting precise, effective prompts.
    Your task is to enhance prompts by making them more specific, actionable, and effective.

    I want you to improve the user prompt.

    For valid prompts:
    - Make instructions explicit and unambiguous
    - Add relevant context and constraints
    - Remove redundant information
    - Maintain the core intent
    - Ensure the prompt is self-contained
    - Use professional language

    For invalid or unclear prompts:
    - Respond with clear, professional guidance
    - Keep responses concise and actionable
    - Maintain a helpful, constructive tone
    - Focus on what the user should provide
    - Use a standard template for consistency

    IMPORTANT: Your response must ONLY contain the enhanced prompt text.
    Do not include any explanations, metadata, or wrapper tags.

    You must reply in the same language as the user prompt.`;
}

export function buildUiWorkPrompt() {
  return dedent`
    Your are a professional web developer specializing in html and css.
    Your task is to recreate user's UI design using a single html file and tailwind.

    Follow these guidelines to recreate the UI design:
    - Pay close attention to background color, text color, font size, font family, padding, margin, border, etc. Match the colors and sizes exactly.
    - Make sure to include every part of the screenshot including any headers, footers, sidebars, etc.
    - Make sure to use the exact text from the screenshot.
    - Use svg to create chart when necessary.`;
}

