export function isBinaryFile(path: string) {
  return /\.(png|jpg|jpeg|gif|ttf|eot|woff|woff2|pdf|mp3|mp4|webm|ogg|flac|wav|zip|tar|gz|bz2|7z|rar|exe|dll|so|bin|DS_Store)$/.test(path);
}

export function isImage(path: string) {
  return /\.(png|jpg|jpeg|gif)$/.test(path);
}

export function dataUrl(content: string, mimeType: string = 'text/html') {
 // It's good practice to URI encode the HTML to handle special characters correctly.
  const encodedHtml = encodeURIComponent(content);
  // Then, use btoa for Base64 encoding.
  // Note: btoa expects a string where each character represents an 8-bit byte.
  // For Unicode strings, a common workaround is:
  const base64Html = btoa(unescape(encodedHtml));
  return `data:${mimeType};charset=utf-8;base64,${base64Html}`;
}
