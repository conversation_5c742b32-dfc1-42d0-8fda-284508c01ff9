import { ChatMessage, UserRole } from '@prisma/client';
import { omit, lowerCase } from 'lodash';
import { textParser, stringifyBlocks } from '../parser';

export const convertToUIMessage = (msg: ChatMessage, stripBlocks?: string[]) => {
  const parts = msg.parts;
  let content = msg.content;
  if (parts.length === 0) {
    parts.push({
      type: 'text',
      text: msg.content,
    });
  }
  let filteredParts = parts;
  if (stripBlocks && msg.role === UserRole.ASSISTANT) {
    filteredParts = parts.map((e: Record<string, any>) => {
      if (typeof e === 'object' && e.type === 'text') {
        const blocks = textParser(e.text, false, false);
        const text = stringifyBlocks(blocks.filter((b) => !stripBlocks?.includes(b.type)));
        return {
          type: 'text',
          text,
        };
      }
      return e;
    });
    content = filteredParts.map((e: any) => e.text).join('\n');
  }
  const uiMsg = omit(msg, ['created', 'attachments']);
  Object.assign(uiMsg, {
    content,
    createdAt: msg.created,
    experimental_attachments: msg.attachments,
    role: lowerCase(msg.role),
    parts: filteredParts,
  });
  return uiMsg;
};