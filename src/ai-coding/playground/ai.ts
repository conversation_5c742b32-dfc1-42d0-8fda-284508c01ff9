import type { IncomingMessage, ServerResponse } from 'node:http';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { createDeepSeek } from '@ai-sdk/deepseek';
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { generateId } from '@ai-sdk/ui-utils';
import { Message } from '@ai-sdk/ui-utils';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { Playground, Project, UserRole } from '@prisma/client';
import { generateObject, streamText, pipeDataStreamToResponse, appendResponseMessages, generateText, Attachment } from 'ai';

// 新增：上下文超限检测和继续输出相关接口
interface IncompleteOutputInfo {
  isIncomplete: boolean;
  incompleteType?: 'files' | 'diff' | 'text' | 'mixed';
  lastCompleteBlock?: any;
  incompleteContent?: string;
  suggestedContinuePrompt?: string;
  tokenUsage?: {
    completionTokens: number;
    promptTokens: number;
    totalTokens: number;
  };
}

// 检测输出是否因上下文超限而不完整
function detectIncompleteOutput(text: string, usage?: any): IncompleteOutputInfo {
  // 这里不启用截断检测，因为这个函数本身就是用来检测截断的
  const blocks = textParser(text, true, false);
  let isIncomplete = false;
  let incompleteType: 'files' | 'diff' | 'text' | 'mixed' | undefined;
  let lastCompleteBlock: any;
  let incompleteContent = '';

  // 1. 检查最后一个files或diff标签是否完整
  const filesBlocks = blocks.filter(block => block.type === 'files');
  const diffBlocks = blocks.filter(block => block.type === 'diff');

  // 检查最后一个files标签是否未关闭
  if (filesBlocks.length > 0) {
    const lastFilesBlock = filesBlocks[filesBlocks.length - 1];
    // 如果files标签未闭合，直接标记为不完整
    if (!lastFilesBlock.closed) {
      isIncomplete = true;
      incompleteType = 'files';
      incompleteContent = `检测到未闭合的<files>标签`;
    }
  }

  // 检查最后一个diff标签是否未关闭
  if (!isIncomplete && diffBlocks.length > 0) {
    const lastDiffBlock = diffBlocks[diffBlocks.length - 1];
    if (!lastDiffBlock.closed) {
      isIncomplete = true;
      incompleteType = 'diff';
      incompleteContent = `检测到未闭合的<diff>标签`;
    }
  }

  // 2. 检测明确的标签截断（文本以开始标签结尾）
  if (!isIncomplete) {
    const hasClearTagTruncation = text.trim().endsWith('<files>') ||
      text.trim().endsWith('<diff>') ||
      text.trim().endsWith('<tool>') ||
      text.trim().endsWith('```');

    if (hasClearTagTruncation) {
      isIncomplete = true;
      incompleteType = 'text';
      incompleteContent = '检测到明确的标签截断';
    }
  }

  // 3. 检测明确的"内容被截断"提示（非常严格）
  if (!isIncomplete) {
    const hasExplicitTruncationMessage =
      text.includes('内容被截断') ||
      text.includes('由于长度限制') ||
      text.includes('超出限制') ||
      text.includes('内容过长，分批输出') ||
      text.includes('分多次输出') ||
      (text.includes('抱歉') && text.includes('截断')) ||
      // 英文提示
      text.includes('content was truncated') ||
      text.includes('response was cut off') ||
      text.includes('content exceeded');

    if (hasExplicitTruncationMessage) {
      isIncomplete = true;
      incompleteType = 'text';
      incompleteContent = '检测到明确的截断提示消息';
    }
  }

  // 4. 只有在finishReason为length且有明确截断迹象时才考虑token限制
  if (!isIncomplete && usage && usage.finishReason === 'length') {
    // 非常保守的截断检测：只有在文本明确在关键位置被截断时才判断
    const hasDefinitiveTruncation =
      text.trim().endsWith('<files>') ||
      text.trim().endsWith('<diff>') ||
      text.trim().endsWith('```') ||
      text.trim().endsWith('<tool>') ||
      // 在文件头部分被截断
      (text.includes('# 文件：') && !text.trim().endsWith('```') && !text.trim().endsWith('</files>'));

    if (hasDefinitiveTruncation) {
      isIncomplete = true;
      incompleteType = 'text';
      incompleteContent = '因模型长度限制在关键位置被截断';
    }
  }

  // 生成继续提示
  let suggestedContinuePrompt = '';
  if (isIncomplete) {
    switch (incompleteType) {
      case 'files':
        suggestedContinuePrompt = '请继续输出未完成的文件内容，从上次中断的地方继续。确保使用<files>标签包裹所有代码，并输出完整的文件内容。';
        break;
      case 'diff':
        suggestedContinuePrompt = '请继续输出未完成的diff内容，从上次中断的地方继续。';
        break;
      case 'text':
        suggestedContinuePrompt = '你的回答似乎被截断了，请继续完成你刚才的回答。';
        break;
      case 'mixed':
        suggestedContinuePrompt = '你的回答包含文件代码和文本说明，但似乎没有完成。请继续输出未完成的内容，特别是确保所有代码文件都完整输出。';
        break;
      default:
        suggestedContinuePrompt = '请继续完成你的回答。';
    }
  }

  return {
    isIncomplete,
    incompleteType,
    lastCompleteBlock: blocks[blocks.length - 1],
    incompleteContent,
    suggestedContinuePrompt,
    tokenUsage: usage ? {
      completionTokens: usage.completionTokens || 0,
      promptTokens: usage.promptTokens || 0,
      totalTokens: usage.totalTokens || 0,
    } : undefined,
  };
}

// 从历史消息中分析最后一条AI消息的完整性
async function analyzeLastAssistantMessage(workspaceId: string): Promise<IncompleteOutputInfo | null> {
  try {
    // 获取最后一条助手消息
    const lastAssistantMessage = await context.prisma.chatMessage.findFirst({
      where: {
        playgroundId: workspaceId,
        role: UserRole.ASSISTANT,
      },
      orderBy: {
        created: 'desc',
      },
    });

    if (!lastAssistantMessage) {
      return null;
    }

    // 分析最后一条助手消息的完整性
    const incompleteInfo = detectIncompleteOutput(lastAssistantMessage.content);

    // 如果检测到不完整但没有建议的继续提示，添加一个通用的
    if (incompleteInfo.isIncomplete && !incompleteInfo.suggestedContinuePrompt) {
      incompleteInfo.suggestedContinuePrompt = '请继续完成你刚才的回答。';
    }

    return incompleteInfo;
  } catch (error) {
    console.error('分析最后一条AI消息时出错:', error);
    return null;
  }
}

// 生成继续输出的提示消息
function generateContinuePrompt(incompleteInfo: IncompleteOutputInfo, context: SystemContext): string {
  const { incompleteType, incompleteContent, tokenUsage } = incompleteInfo;

  let prompt = `## 🔄 继续输出请求

你刚才的回答似乎没有完成。`;

  if (incompleteContent) {
    prompt += `检测到：${incompleteContent}。`;
  }

  if (tokenUsage && tokenUsage.totalTokens > 15000) {
    prompt += `\n\n⚠️ **Token使用情况**：已使用 ${tokenUsage.totalTokens} tokens，接近模型限制。`;
  }

  prompt += `\n\n**请从上次中断的地方继续输出**，特别注意：

### 📋 继续输出要求

`;

  switch (incompleteType) {
    case 'files':
      prompt += `- **文件输出优先**：必须使用 \`<files>\` 标签完整输出所有代码文件
- **内容完整性**：确保每个文件的代码都是完整的，不要省略任何部分
- **格式规范**：严格按照 \`<files>\` 标签格式输出
- **文件列表**：先列出所有文件名，然后逐个输出完整文件内容
- **追加模式**：如果是向现有文件追加内容，可以使用 \`<diff>\` 标签进行增量更新`;
      break;

    case 'diff':
      prompt += `- **Diff完整性**：使用 \`<diff>\` 标签输出完整的unified diff内容
- **格式正确**：确保diff格式符合标准，包含正确的文件头和行号信息
- **追加新内容**：diff可以用于向现有文件追加新内容，格式如下：
  \`\`\`
  --- a/文件名.ext
  +++ b/文件名.ext
  @@ -最后一行行号,0 +最后一行行号,新增行数 @@
  +新增的内容行1
  +新增的内容行2
  \`\`\`
- **修改现有内容**：也可以用于修改现有文件的特定部分
- **最小改动原则**：只修改需要变更的部分，保持其他代码不变`;
      break;

    case 'text':
      prompt += `- **内容连贯**：从上次中断的地方自然继续
- **信息完整**：确保所有重要信息都传达完整
- **逻辑清晰**：保持回答的逻辑结构和条理性`;
      break;

    case 'mixed':
      prompt += `- **代码优先**：优先完成未输出完整的代码文件
- **灵活选择标签**：
  * 使用 \`<files>\` 标签输出完整的新文件或重写整个文件
  * 使用 \`<diff>\` 标签向现有文件追加内容或进行局部修改
- **内容完整**：确保代码和说明都完整输出
- **结构清晰**：保持代码和文档的清晰结构
- **追加策略**：对于需要在现有文件基础上添加内容的情况，优先使用diff追加模式`;
      break;

    default:
      prompt += `- **完整输出**：确保回答内容完整
- **格式正确**：使用正确的markdown和标签格式
- **逻辑连贯**：保持回答的逻辑性和连贯性
- **标签选择**：根据内容类型选择合适的标签（\`<files>\` 或 \`<diff>\`）`;
  }

  // 根据工作区类型添加特定要求
  if (context.playground.type === 'html') {
    prompt += `

### 🌐 HTML工作区特殊要求
- **文件命名**：HTML文件必须命名为 \`index.html\`，CSS文件必须命名为 \`styles.css\`
- **完整代码**：HTML必须包含完整的文档结构（DOCTYPE、html、head、body）
- **样式处理**：简单页面使用内联样式，复杂页面使用外部CSS文件
- **视觉一致**：确保输出的代码与原始设计视觉效果一致
- **严格格式约束**：必须严格按照以下格式输出，不得有任何偏差
- **通用追加模式**：向现有文件追加内容时，使用diff格式：
  \`\`\`
  <diff>
  --- a/文件名.ext
  +++ b/文件名.ext
  @@ -最后一行,0 +最后一行,新增行数 @@
  +
  +<!-- 追加的HTML内容 -->
  +<div class="new-section">
  +    <h2>新增内容</h2>
  +</div>
  
  或者追加CSS样式：
  +
  +/* 新增的CSS样式 */
  +.new-class {
  +    property: value;
  +}
  
  或者追加JavaScript代码：
  +
  +// 新增的JavaScript代码
  +function newFunction() {
  +    // 功能实现
  +}
  </diff>
  \`\`\`
- **多文件协调**：确保HTML、CSS、JavaScript文件之间的引用关系正确`;
  }

  prompt += `

### 🔧 输出格式选择指南
- **完整重写文件**：使用 \`<files>\` 标签，适用于：
  * 创建全新文件
  * 需要大幅修改现有文件（超过50%内容变更）
  * 文件结构需要重新组织
- **追加新内容**：使用 \`<diff>\` 标签，适用于：
  * 向现有文件末尾添加新的HTML元素、CSS规则、JavaScript函数
  * 在现有结构基础上扩展功能
  * 添加新的样式类或交互逻辑
- **局部修改**：使用 \`<diff>\` 标签，适用于：
  * 修改现有元素的属性或样式
  * 更新特定函数或代码块
  * 调整现有内容的细节
- **多文件操作**：可以在一个回答中混合使用 \`<files>\` 和 \`<diff>\` 标签
- **效率优先**：优先选择diff格式，避免重复输出大量未变更的代码

### 🚨 强制格式规范 - 继续输出时必须严格遵守
**文件标题格式强制要求**：
- ✅ 正确格式：\`# 文件：index.html\`
- ❌ 错误格式：\`## index.html文件\`、\`# 步骤1：创建index.html\`、\`# index.html代码\`

**<files>标签强制模板**：
<files>
# 文件列表
- index.html
- styles.css

# 文件：index.html
\\\`\\\`\\\`html
完整的HTML代码，不能省略任何部分
\\\`\\\`\\\`

# 文件：styles.css
\\\`\\\`\\\`css
完整的CSS代码，不能省略任何部分
\\\`\\\`\\\`
</files>

**<diff>标签强制模板**：
<diff>
--- a/文件名
+++ b/文件名
@@ -行号 +行号 @@
-删除的行
+添加的行
</diff>

**严格禁止事项**：
1. 在<files>标签内添加任何解释性文字或步骤说明
2. 使用非标准的文件标题格式
3. 代码块不指定语言类型
4. 在标签外输出代码

**❌ 严格禁止的错误格式：**
- 在XML标签外输出任何代码块
- 混合使用markdown代码块和XML标签
- 不完整的标签格式
- 省略文件列表或文件标题

### ⚡ 立即行动
请立即继续输出，不需要重复之前已经完成的部分。直接从中断的地方开始。

**优先考虑使用diff格式进行增量更新，这样可以更高效地追加新内容到现有文件中。**

---

**现在开始继续输出：**`;

  return prompt;
}
import { isVisionEnabled } from 'frontend/src/config/models';
import { concat, findLast, some, filter, size } from 'lodash';
import { marked, Tokens } from "marked";
import { fetch as nodeFetch, ProxyAgent } from 'undici';
import { z } from 'zod';
import { ChatMsgDto } from '../dto/chat-msg.dto';
import context from './ctx';
import { ConfigFileType, FileType } from './enums';
import { executeGitCommand } from './git';
import { textParser } from './parser';
import { buildEnhancementSystemPrompt, buildSummarizationSystemPrompt, buildPrompt, SystemContext, toolMap, buildUiWorkPrompt } from './prompt';
import { dataUrl } from './utils/file';
import { convertToUIMessage } from './utils/message';
import { playgroundInfo, saveFile, createBranch, commitFiles, versionStatus, listFiles, executeCommand } from '.';

async function writeLatestUserMessage(chatMsgDto: ChatMsgDto) {
  const userMessage = chatMsgDto.message;
  const { experimental_attachments } = userMessage;

  const sameMessage = await context.prisma.chatMessage.findUnique({
    where: {
      id: userMessage.id,
    },
  });

  // Clear previous messages if message is reloaded
  if (sameMessage) {
    await context.prisma.chatMessage.deleteMany({
      where: {
        playgroundId: chatMsgDto.id,
        created: {
          gt: sameMessage.created,
        },
      },
    });
    return;
  }

  return context.prisma.chatMessage.create({
    data: {
      id: `${chatMsgDto.id}::${generateId()}`,
      playgroundId: chatMsgDto.id,
      role: UserRole.USER,
      content: userMessage.content,
      attachments: experimental_attachments as any,
    },
  });
}

async function writeAssistantMessage(playgroundId: string, messageId: string, messages: Message[]) {
  let allContent = '';
  const allParts: any[] = [];
  for (const msg of messages) {
    allContent += msg.content;
    allParts.push(...msg.parts);
  }

  const messageRecord = await context.prisma.chatMessage.upsert({
    where: {
      id: `${playgroundId}::${messageId}`,
      playgroundId,
    },
    create: {
      id: `${playgroundId}::${messageId}`,
      playgroundId,
      role: UserRole.ASSISTANT,
      content: allContent,
      parts: allParts,
      status: 'completed', // 设置状态为已完成
    },
    update: {
      content: allContent,
      parts: allParts,
      status: 'completed', // 更新状态为已完成
    },
  });

  // 🎯 新增：清理空的processing状态占位符消息
  try {
    const placeholderMessages = await context.prisma.chatMessage.findMany({
      where: {
        playgroundId: playgroundId,
        role: 'ASSISTANT',
        status: 'processing',
        NOT: {
          id: `${playgroundId}::${messageId}` // 排除当前消息
        }
      }
    });

    // 删除空的占位符消息
    for (const placeholder of placeholderMessages) {
      if (!placeholder.content || placeholder.content.trim() === '') {
        await context.prisma.chatMessage.delete({
          where: { id: placeholder.id }
        });
        context.log(`🗑️ 清理AI占位符消息: ${placeholder.id}`);
      }
    }
  } catch (error) {
    context.log(`⚠️ 清理占位符消息时出错: ${error.message}`);
  }

  return messageRecord;
}

async function writeAssistantAnnotation(playgroundId: string, messageId: string, annotation: Record<string, any>) {
  // Append annotation to a message
  return context.prisma.chatMessage.update({
    where: {
      id: `${playgroundId}::${messageId}`,
      playgroundId,
    },
    data: {
      annotations: {
        push: annotation,
      },
    },
  });
}

// 检测是否是明确的继续请求（避免误判正常消息）
function isExplicitContinueRequest(content: string): boolean {
  const trimmedContent = content.trim().toLowerCase();

  // 1. 单独的继续指令
  if (trimmedContent === '继续' || trimmedContent === 'continue') {
    return true;
  }

  // 2. 明确的继续上次回答的请求
  const continuePhrases = [
    '继续上面的回答',
    '继续上一个回答',
    '继续刚才的回答',
    '请继续',
    '继续输出',
    '继续完成',
    'continue the previous response',
    'continue above response',
    'continue last response',
    'please continue',
    'continue output'
  ];

  // 检查是否是明确的继续短语，且没有其他具体要求
  for (const phrase of continuePhrases) {
    if (trimmedContent.includes(phrase) && trimmedContent.length < phrase.length + 10) {
      return true;
    }
  }

  // 3. 检查是否是对截断消息的继续请求
  const truncationContinuePatterns = [
    /继续.*(?:被截断|未完|没完)/,
    /continue.*(?:truncated|incomplete|cut off)/i,
    /完成.*(?:剩余|未完)/
  ];

  for (const pattern of truncationContinuePatterns) {
    if (pattern.test(content)) {
      return true;
    }
  }

  return false;
}

/**
 * Chat within a playground
 */
export async function chat(
  chatMsgDto: ChatMsgDto,
  playground: Playground,
  project: Project,
  res: ServerResponse<IncomingMessage>,
) {
  const { model } = playground;
  context.log(`Chatting with LLM(${model}): ${JSON.stringify(chatMsgDto)}`);

  // 调试日志：打印接收到的HTML Agent设置
  if (playground.type === 'html') {
    console.log(`🚀 后端接收HTML Agent设置 - 分步执行: ${chatMsgDto.enableStepByStep}, 自迭代: ${chatMsgDto.enableAutoIteration}`);
  }

  const { message } = chatMsgDto;
  const workspaceId = chatMsgDto.id;

  pipeDataStreamToResponse(res, {
    execute: async (dataStreamWriter) => {
      if (
        !isVisionEnabled(playground.model)
        && isUiWork(message.experimental_attachments)
      ) {
        context.log(`Converting attachments to html`);
        const { experimental_attachments: attachments, content } = chatMsgDto.message;
        dataStreamWriter.writeData({
          type: 'status',
          status: 'convert-attachments',
        });
        const [newContent, newAttachments] = await convertAttachmentToHtml(content, attachments);
        chatMsgDto.message.content = newContent;
        chatMsgDto.message.experimental_attachments = newAttachments;
      }

      const pkgJson = await playgroundInfo(workspaceId, ConfigFileType.PackageJson);
      const { current, list } = await versionStatus({ id: workspaceId });
      const info = JSON.parse(pkgJson);
      const fileList = (await listFiles(workspaceId))
        .filter(e => e.fileType === FileType.File)
        .map(e => e.path);

      // 检查是否是继续请求，如果是则分析历史消息
      // 只有在非常明确的继续请求场景下才触发，避免误判正常消息
      const isContinueRequest = (chatMsgDto as any).isContinueRequest ||
        isExplicitContinueRequest(chatMsgDto.message.content);

      let lastMessageAnalysis: IncompleteOutputInfo | null = null;
      if (isContinueRequest) {
        console.log('🔍 检测到明确的继续请求，分析最后一条AI消息...');
        lastMessageAnalysis = await analyzeLastAssistantMessage(workspaceId);
        if (lastMessageAnalysis?.isIncomplete) {
          console.log(`📋 分析结果：检测到不完整输出 - ${lastMessageAnalysis.incompleteType}: ${lastMessageAnalysis.incompleteContent}`);
        }
      }

      const workspaceContext: SystemContext = {
        workspaceId: chatMsgDto.id,
        dependencies: info.dependencies,
        devDependencies: info.devDependencies,
        fileList,
        playground,
        project,
        currentVersion: current,
        latestVersion: list[list.length - 1] ?? '',
        // 传递HTML Agent设置
        enableAutoIteration: chatMsgDto.enableAutoIteration,
        enableStepByStep: chatMsgDto.enableStepByStep,
        // 传递继续请求的上下文信息
        isContinueRequest,
        lastMessageAnalysis,
      };
      // const messages = [chatMsgDto.messages];
      await writeLatestUserMessage(chatMsgDto);

      // 🎯 新增：在用户消息保存后创建AI占位符消息，确保页面刷新后能显示loading效果
      const placeholderMessageId = generateId();
      const aiPlaceholderId = `${workspaceId}::${placeholderMessageId}`;

      try {
        await context.prisma.chatMessage.create({
          data: {
            id: aiPlaceholderId,
            playgroundId: workspaceId,
            role: 'ASSISTANT',
            content: '', // 初始为空内容，前端会显示loading
            status: 'processing', // 设置为处理中状态，触发前端轮询
          },
        });
        context.log(`📝 创建AI占位符消息: ${aiPlaceholderId} (在用户消息后显示loading效果)`);
      } catch (error) {
        // 如果创建失败（比如ID冲突），记录日志但继续执行
        context.log(`⚠️ 创建AI占位符消息失败: ${error.message}，继续正常流程`);
      }

      const systemPrompt = await buildPrompt(workspaceContext)
      const historyMessages = await context.prisma.chatMessage.findMany({
        where: {
          playgroundId: chatMsgDto.id,
          created: {
            lt: chatMsgDto.message.createdAt,
          },
        },
      });
      const messageId = generateId();
      const messages = [...historyMessages.map(msg => convertToUIMessage(msg, ['files', 'diff'])), chatMsgDto.message];
      const responseUIMessages: Message[] = []; // UIMessages are persisted
      let completionTokens = 0, promptTokens = 0, totalTokens = 0;
      dataStreamWriter.writeData({
        type: 'status',
        status: 'start-stream',
        workspaceId: workspaceId,
      });
      const startStream = (i = 0) => {
        context.log(`Streaming from LLM(${model}), step ${i}`);

        // 获取模型配置参数
        const modelConfig = getModelConfig(model);

        const streamOptions: any = {
          model: getModel(model),
          system: systemPrompt,
          messages,
          stopSequences: ['🔴'],
          temperature: 0,
          topP: 1,
          ...modelConfig, // 应用模型特定配置
          experimental_generateMessageId() {
            return `${workspaceId}::${generateId()}`;
          },
          experimental_transform: () => {
            return new TransformStream({
              transform(chunk, controller) {
                // This is necessary for doubao only, since doubao failed to remove stop sequence
                if (chunk.type === 'text-delta' && chunk.textDelta.includes('🔴')) {
                  const textDelta = chunk.textDelta.replaceAll('🔴', '');
                  if (textDelta) {
                    controller.enqueue({
                      type: 'text-delta',
                      textDelta,
                    });
                  }
                } else {
                  controller.enqueue(chunk);
                }
              },
            });
          },
          onChunk: (chunk) => {
            context.log(JSON.stringify(chunk), 'debug');
          },
          async onFinish(evt) {
            const { text, response, usage } = evt;
            if (typeof usage.completionTokens === 'number' && typeof usage.promptTokens === 'number' && typeof usage.totalTokens === 'number') {
              completionTokens += usage.completionTokens;
              promptTokens += usage.promptTokens;
              totalTokens += usage.totalTokens;
            }

            const uiMessages = appendResponseMessages({
              messages: [
                {
                  id: 'xxx',
                  role: 'user',
                  content: 'xxx',
                },
              ],
              responseMessages: response.messages,
            });
            responseUIMessages.push(...uiMessages.slice(1));

            // 检查是否因为长度限制被截断，决定是否启用严格的截断检测
            const isLengthTruncated = response.finishReason === 'length';
            const shouldDetectTruncation = isLengthTruncated;

            const blocks = textParser(text, true, shouldDetectTruncation);
            const toolCalls = blocks.filter((e) => e.type === 'tool');
            const files = blocks.filter((e) => e.type === 'files');
            const diffs = blocks.filter((e) => e.type === 'diff');

            // 使用智能检测函数检测输出完整性
            const incompleteInfo = detectIncompleteOutput(text, usage);

            let lastStep = true, toolResults = '';
            if (toolCalls.length) {
              lastStep = false;
              context.log(`Invoke tools for ${workspaceId}: ${JSON.stringify(toolCalls)}`);
              const results = await callTools(toolCalls, workspaceContext, context.log);
              context.log(`Tools results for ${workspaceId}: ${JSON.stringify(results)}`);
              toolResults = results
                .map(({ name, content }) => `<tool><name>${name}</name><result>${content}</result></tool>`)
                .join('\n');
            }
            await writeAssistantMessage(workspaceId, messageId, responseUIMessages);

            // Save AI generated files
            if (files.length || diffs.length) {
              // 筛选有注解信息
              const messagesWithAnnotations = filter(messages,
                (e: any) => typeof e === 'object' && size(e?.annotations) > 0
              );
              // 筛选注解，找到当前版本号
              const lastMessage = findLast(messagesWithAnnotations, (entry: any) => {
                const { annotations } = entry;
                return some(annotations, item => item?.type === 'all-files-saved' && item?.versionNumber > 0);
              });
              const currentAnnotation = findLast(lastMessage?.annotations, (item: any) => item?.type === 'all-files-saved' && item?.versionNumber > 0);

              // 生成新版本号
              const version = (currentAnnotation?.versionNumber || 0) + 1;
              const tagName = `version-${version}`;
              await createBranch({ id: workspaceId, tagName });

              // Write files
              if (files.length) {
                const fileBlocks = concat(...files.map((fileBlock) => fileBlock.files));
                context.log(`Saving ${fileBlocks.length} files: ${fileBlocks.map((f) => f.path).join(', ')}`);
                await Promise.all(
                  fileBlocks.map((fileBlock) =>
                    saveFile({
                      id: workspaceId,
                      path: fileBlock.path,
                      content: fileBlock.content,
                    }).then(
                      () => {
                        const saveAnnotation = {
                          type: 'file-saved',
                          path: fileBlock.path,
                        };
                        dataStreamWriter.writeMessageAnnotation(saveAnnotation);
                        writeAssistantAnnotation(workspaceId, messageId, saveAnnotation);
                      },
                      (err) => {
                        const saveAnnotation = {
                          type: 'file-saved',
                          path: fileBlock.path,
                          error: err,
                        };
                        dataStreamWriter.writeMessageAnnotation(saveAnnotation);
                        writeAssistantAnnotation(workspaceId, messageId, saveAnnotation);
                      },
                    ),
                  ),
                );
              } else if (diffs.length) {
                for (const diffBlock of diffs) {
                  const ret = await executeCommand(workspaceId, 'patch -p1', { dir: 'src', stdin: diffBlock.diff });
                  if (ret.exitCode !== 0) {
                    const errorAnnotation = {
                      type: 'apply-diff-error',
                      error: ret.stderr,
                    };
                    dataStreamWriter.writeMessageAnnotation(errorAnnotation);
                    writeAssistantAnnotation(workspaceId, messageId, errorAnnotation);
                    await executeGitCommand('reset');
                    break;
                  }
                }
              }

              await commitFiles({ id: workspaceId });

              // 再次清除版本相关缓存，确保前端能获取到最新版本信息
              const { clearVersionCache } = await import('./index');
              clearVersionCache(workspaceId);

              const finishAnnotation = {
                type: 'all-files-saved',
                versionNumber: version
              };
              dataStreamWriter.writeMessageAnnotation(finishAnnotation);
              writeAssistantAnnotation(workspaceId, messageId, finishAnnotation);
            }

            if (!lastStep) {
              messages.push({
                role: 'assistant',
                content: text,
              });

              messages.push({
                role: 'user',
                content: toolResults,
              });
              // Step into the next round
              dataStreamWriter.write('0:"\\n"\n');
              startStream(i++);
            } else {
              // 使用智能检测和继续提示逻辑
              if (incompleteInfo.isIncomplete) {
                // 记录检测到的不完整信息
                context.log(`🔄 检测到不完整输出，自动继续生成...`);
                context.log(`- 不完整类型: ${incompleteInfo.incompleteType}`);
                context.log(`- 不完整内容: ${incompleteInfo.incompleteContent}`);
                context.log(`- finishReason: ${response.finishReason}`);

                // 添加不完整输出的注解
                const incompleteAnnotation = {
                  type: 'incomplete-output-detected',
                  incompleteType: incompleteInfo.incompleteType,
                  incompleteContent: incompleteInfo.incompleteContent,
                  finishReason: response.finishReason,
                  tokenUsage: incompleteInfo.tokenUsage,
                };
                dataStreamWriter.writeMessageAnnotation(incompleteAnnotation);
                writeAssistantAnnotation(workspaceId, messageId, incompleteAnnotation);

                messages.push({
                  role: 'assistant',
                  content: text,
                });

                // 使用智能继续提示生成器
                const intelligentContinuePrompt = generateContinuePrompt(incompleteInfo, workspaceContext);

                messages.push({
                  role: 'user',
                  content: intelligentContinuePrompt,
                });

                // 继续下一轮生成
                dataStreamWriter.writeData('\n');
                startStream(i++);
              } else {
                // 正常结束
                if (totalTokens) {
                  const annotation = {
                    type: 'token-count',
                    completionTokens,
                    promptTokens,
                    totalTokens,
                    finishReason: response.finishReason || 'completed',
                    isContinued: incompleteInfo.isIncomplete ? true : false,
                  };
                  dataStreamWriter.writeMessageAnnotation(annotation);
                  writeAssistantAnnotation(workspaceId, messageId, annotation);
                }
              }
            }
          },
        };

        // 记录模型调用参数
        console.log(`🔍 记录第${i}轮AI模型调用参数...`);
        // 仅本地调试用
        // logModelRequestParams(model, systemPrompt, messages, streamOptions, workspaceContext);

        const result = streamText(streamOptions);
        result.mergeIntoDataStream(dataStreamWriter);
      };

      startStream();
    },
    onError: (error) => {
      // Error messages are masked by default for security reasons.
      // If you want to expose the error message to the client, you can do so here:
      // console.log('🔴 AI错误:', error); 
      // 将错误事件传播到 response 对象，让 TaskWorkerService 能够正确捕获
      // 对 (TaskWorkerService) 进行错误传播
      try {
        if (res && typeof res.emit === 'function') {
          // 🛡️ 安全检查：只有当 response 对象有 error 事件监听器时才触发错误事件
          if (res.listenerCount && res.listenerCount('error') > 0) {
            console.log('🚨 触发 mock response 的 error 事件 (检测到监听器)');
            // 使用 nextTick 确保错误事件在下一个事件循环中触发，避免同步执行问题
            process.nextTick(() => {
              try {
                res.emit('error', error);
              } catch (emitError) {
                console.error('❌ 触发 mock response error 事件时出错:', emitError);
              }
            });
          } else {
            console.log('⚠️ response 对象没有 error 事件监听器，跳过错误事件传播');

          }
        } else {
          console.log('⚠️ response 对象不支持 emit 方法，无法传播错误事件');
        }
      } catch (propagationError) {
        console.error('❌ 错误传播过程中出错:', propagationError);
      }

      return error instanceof Error ? error.message : String(error);
    },
  });
}

// 新增：处理继续输出请求的函数
export function handleContinueRequest(chatMsgDto: ChatMsgDto, playground: Playground, project: Project, res: ServerResponse<IncomingMessage>) {
  // 检查是否是继续输出请求
  const isContinueRequest = chatMsgDto.message.content.includes('继续') ||
    chatMsgDto.message.content.includes('continue') ||
    chatMsgDto.message.content.toLowerCase().includes('continue');

  if (isContinueRequest) {
    console.log('🔄 检测到用户手动继续输出请求，使用优化的继续模式');

    // 对于手动继续请求，我们可以分析上一条AI消息的完整性
    // 并在必要时增强用户的继续请求
    const enhancedChatMsgDto = {
      ...chatMsgDto,
      // 标记这是一个继续请求，可以在系统提示中利用这个信息
      isContinueRequest: true,
    };

    return chat(enhancedChatMsgDto as ChatMsgDto, playground, project, res);
  }

  return chat(chatMsgDto, playground, project, res);
}

export async function extactPlaygroundInfo(desc: string) {
  // FIXME 默认使用saas-doubao-1.5-vl-pro-32k模型，公司的deepseek-v3模型返回的json字符串有点问题，返回的是有```json开头的代码块形式的json字符串
  const model = 'htsc::saas-doubao-1.5-vl-pro-32k'; 
  const systemPrompt = buildSummarizationSystemPrompt();

  const generateObjectOptions = {
    schema: z.object({
      topic: z.string(),
      name: z.string(),
      type: z.string(),
    }),
    mode: 'json' as const,
    model: getModel(model),
    system: systemPrompt,
    messages: [
      {
        role: 'user' as const,
        content: desc,
      },
    ],
  };

  // 记录extractPlaygroundInfo函数的AI调用参数
  // console.log('🔍 记录extractPlaygroundInfo函数AI调用参数...');
  // 仅本地调试用
  // logExtractPlaygroundInfoRequestParams(model, systemPrompt, desc, generateObjectOptions);

  return generateObject(generateObjectOptions);
}

function getFetch(url: string, opts: any) {
  if (process.env.USE_AGENT === '1') {
    return nodeFetch(url, {
      ...opts,
      dispatcher: new ProxyAgent('http://168.64.5.83:8080/'),
    }) as unknown as Promise<Response>;
  }
  return fetch(url, opts);
}

function getModel(modelConfig = 'htsc::saas-doubao-15-pro-32k') {
  const [provider, model] = modelConfig.split('::');
  if (provider === 'siliconflow') {
    const sdk = createOpenAICompatible({
      name: 'siliconflow',
      apiKey: process.env.SILICONFLOW_API_KEY,
      baseURL: 'https://api.siliconflow.cn/v1',
      fetch: getFetch,
    });
    return sdk(model);
  } else if (provider === 'openrouter') {
    console.log('🔍 模型名称:', model);
    if(model.startsWith('google/gemini-2.5-pro')) {
      const baseURL = 'http://ai-coding-v2.sit.saas.htsc/v1';
      const sdk = createOpenAICompatible({
        name: 'google vertex',
        apiKey: 'google vertex',
        baseURL,
      });
      console.log('🔍 调用google vertex模型...');
      return sdk(model);
    } else {
      const openrouter = createOpenRouter({
        apiKey: process.env.OPENROUTER_API_KEY,
        fetch: getFetch,
      });
      return openrouter.chat(model);
    }
    
  } else if (provider === 'deepseek') {
    const deepseek = createDeepSeek({
      apiKey: process.env.DEEPSEEK_API_KEY,
    });
    return deepseek(model === 'deepseek-r1' ? 'deepseek-reasoner' : model);
  } else if (provider === 'htsc') {
    if (model === 'deepseek-v3' || model === 'deepseek-r1') {
      const url = 'http://168.61.127.80/api/deepseek';
      const sdk = createOpenAICompatible({
        name: 'deepseek',
        apiKey: 'deepseek',
        baseURL: url,
        fetch(_, opts) {
          return fetch(url, opts);
        },
      });
      return sdk(model === 'deepseek-v3' ? 'deepseek-chat' : 'deepseek-reasoner');
    }

    // 默认的 htsc 配置
    let baseURL = 'http://168.64.26.85/web/unauth/LLM_api_proxy/v1';
    let modelName = model;

    if (!modelName.startsWith('ht::')) {
      modelName = `ht::${modelName}`;
    }

    // 针对特定模型的配置
    if (model === 'saas-doubao-1.5-vl-pro-32k') {
      baseURL = 'http://168.64.26.85/web/unauth/LLM_api_proxy/v1';
      modelName = 'ht::saas-doubao-1.5-vl-pro-32k';
    }

    const sdk = createOpenAICompatible({
      name: 'htsc',
      apiKey: 'htsc',
      baseURL,
    });
    return sdk(modelName);
  } else if (provider === 'ht') {
    // 处理 ht:: 前缀的模型
    if (model === 'saas-doubao-1.5-vl-pro-32k') {
      const sdk = createOpenAICompatible({
        name: 'htsc',
        apiKey: 'htsc',
        baseURL: 'http://168.64.26.85/web/unauth/LLM_api_proxy/v1',
      });
      return sdk('ht::saas-doubao-1.5-vl-pro-32k');
    }

    // 其他 ht:: 模型使用默认配置
    const sdk = createOpenAICompatible({
      name: 'htsc',
      apiKey: 'htsc',
      baseURL: 'http://proxyllm.sit.saas.htsc/v1',
    });
    return sdk(`ht::${model}`);
  }

  throw new Error(`Unsupported model provider: ${provider}`);
}

/**
 * 获取模型的配置参数
 */
function getModelConfig(modelConfig: string) {
  const config: any = {};

  // 为特定模型设置参数
  if (modelConfig === 'htsc::saas-deepseek-v3') {
    config['max_tokens'] = 16384;
    config['maxTokens'] = 16384;
  }

  return config;
}

export interface ToolCall {
  name: string;
  arguments?: Record<string, any>;
}

export interface ToolResult extends Record<string, any> {
  name: string;
  content: string;
}

async function callTools(calls: ToolCall[], context: SystemContext, log: (line: string) => void): Promise<ToolResult[]> {
  const ret: ToolResult[] = [];
  for (const call of calls) {
    const { name, arguments: args = {} } = call;
    const tool = toolMap[name as keyof typeof toolMap];
    log(`Calling tool ${name} with arguments ${JSON.stringify(args)}`);
    if (tool) {
      const [code, content, data] = await tool(args, context);
      log(`Tool ${name} got result: ${code}`);
      ret.push({
        name,
        content,
      });
    } else {
      ret.push({
        name,
        content: 'Tool does not exist',
      });
    }
  }
  return ret;
}

export async function enhance(desc: string) {
  const model = 'htsc::saas-deepseek-v3'; // 使用默认模型
  const modelConfig = getModelConfig(model);

  const systemPrompt = buildEnhancementSystemPrompt();
  const generateOptions = {
    model: getModel(model),
    system: systemPrompt,
    prompt: desc,
    ...modelConfig, // 应用模型特定配置
  };

  // 记录enhance函数的AI调用参数
  // console.log('🔍 记录enhance函数AI调用参数...');
  // 仅调试用
  // logEnhanceRequestParams(model, systemPrompt, desc, generateOptions);

  const { text } = await generateText(generateOptions);
  return text;
}

// 新增：处理检测不完整输出的API请求
export function checkIncompleteOutput(content: string, messageId?: string) {
  console.log('🔍 开始检测输出完整性...');

  const incompleteInfo = detectIncompleteOutput(content);

  if (incompleteInfo.isIncomplete) {
    console.log(`⚠️ 检测到不完整输出 - 类型: ${incompleteInfo.incompleteType}, 内容: ${incompleteInfo.incompleteContent}`);
  } else {
    console.log('✅ 输出完整，无需继续');
  }

  return {
    isIncomplete: incompleteInfo.isIncomplete,
    incompleteType: incompleteInfo.incompleteType,
    incompleteContent: incompleteInfo.incompleteContent,
    suggestedContinuePrompt: incompleteInfo.suggestedContinuePrompt,
    canContinue: incompleteInfo.isIncomplete,
  };
}

// 新增：智能继续输出处理器
export async function processIntelligentContinue(
  workspaceId: string,
  systemContext: SystemContext
): Promise<{
  shouldContinue: boolean;
  continuePrompt?: string;
  analysisInfo?: IncompleteOutputInfo;
}> {
  console.log('🤖 启动智能继续输出处理器...');

  try {
    // 分析最后一条AI消息
    const analysisInfo = await analyzeLastAssistantMessage(workspaceId);

    if (!analysisInfo) {
      console.log('📝 未找到最后一条AI消息，无法继续');
      return { shouldContinue: false };
    }

    if (!analysisInfo.isIncomplete) {
      console.log('✅ 最后一条消息完整，无需继续');
      return { shouldContinue: false, analysisInfo };
    }

    // 生成智能继续提示
    const continuePrompt = generateContinuePrompt(analysisInfo, systemContext);

    console.log(`🎯 生成智能继续提示成功 - 类型: ${analysisInfo.incompleteType}`);

    return {
      shouldContinue: true,
      continuePrompt,
      analysisInfo,
    };
  } catch (error) {
    console.error('❌ 智能继续输出处理器出错:', error);
    return { shouldContinue: false };
  }
}
export function isUiWork(attachments?: Attachment[]) {
  if (attachments?.some(e => e.contentType.startsWith('image/'))) {
    return true;
  }
  return false;
}

export async function convertAttachmentToHtml(content: string, attachments: Attachment[]): Promise<[string, Attachment[]]> {
  const convertOne = async (attachment: Attachment): Promise<Attachment> => {
    const model = 'ht::saas-doubao-1.5-vl-pro-32k';
    const systemPrompt = buildUiWorkPrompt();
    const messages = [
      {
        role: 'user' as const,
        content: '设计图还原',
        experimental_attachments: attachments,
      },
    ];

    const generateOptions = {
      model: getModel(model),
      system: systemPrompt,
      messages,
    };

    // 记录convertAttachmentToHtml函数的AI调用参数
    // console.log('🔍 记录convertAttachmentToHtml函数AI调用参数...');
    // logConvertAttachmentRequestParams(model, systemPrompt, messages, generateOptions, attachment);

    const { text } = await generateText(generateOptions);
    const ast = marked.lexer(text);
    const codeNode = ast.find(e => e.type === 'code') as Tokens.Code;
    if (codeNode) {
      return {
        name: attachment.name?.replace(/\.\w+$/, '.html') ?? 'file.html',
        contentType: 'text/html',
        url: dataUrl(codeNode.text, 'text/html'),
      };
    }
    return attachment;
  }

  return [
    [content, '设计稿已转为如下HTML'].join('\n\n'),
    await Promise.all(attachments.map(convertOne)),
  ];
}

// 新增：记录AI模型调用参数的函数
function logModelRequestParams(
  modelConfig: string,
  systemPrompt: string,
  messages: any[],
  streamOptions: any,
  context: SystemContext
) {
  try {
    // 确保日志目录存在
    const logDir = join(process.cwd(), 'logs', 'ai-requests');
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }

    // 创建时间戳
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `ai-request-${timestamp}.json`;
    const filePath = join(logDir, filename);

    // 提取模型提供商和模型名称
    const [provider, model] = modelConfig.split('::');

    // 构造完整的请求参数对象
    const requestParams = {
      timestamp: new Date().toISOString(),
      modelConfig: {
        provider,
        model,
        fullConfig: modelConfig
      },
      apiConfig: getApiConfigForProvider(provider, model),
      systemPrompt,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        id: msg.id || 'generated',
        attachments: msg.experimental_attachments || [],
        createdAt: msg.createdAt || new Date().toISOString()
      })),
      requestOptions: {
        temperature: streamOptions.temperature,
        topP: streamOptions.topP,
        stopSequences: streamOptions.stopSequences,
        maxTokens: streamOptions.maxTokens || streamOptions.max_tokens,
        ...streamOptions
      },
      context: {
        workspaceId: context.workspaceId,
        playgroundType: context.playground.type,
        projectFramework: context.project.framework,
        enableStepByStep: context.enableStepByStep,
        enableAutoIteration: context.enableAutoIteration,
        isContinueRequest: context.isContinueRequest,
        fileList: context.fileList?.slice(0, 50), // 限制文件列表长度
        dependencies: context.dependencies,
        devDependencies: context.devDependencies
      }
    };

    // 生成Postman可用的请求格式
    const postmanRequest = generatePostmanRequest(requestParams);

    // 写入详细参数文件
    writeFileSync(filePath, JSON.stringify(requestParams, null, 2), 'utf8');

    // 写入Postman专用格式
    const postmanFilePath = join(logDir, `postman-${timestamp}.json`);
    writeFileSync(postmanFilePath, JSON.stringify(postmanRequest, null, 2), 'utf8');

    console.log(`📝 AI请求参数已记录到: ${filePath}`);
    console.log(`🚀 Postman格式已生成: ${postmanFilePath}`);

    // 同时写入最新请求的快捷文件（覆盖模式）
    const latestPath = join(logDir, 'latest-request.json');
    const latestPostmanPath = join(logDir, 'latest-postman.json');
    writeFileSync(latestPath, JSON.stringify(requestParams, null, 2), 'utf8');
    writeFileSync(latestPostmanPath, JSON.stringify(postmanRequest, null, 2), 'utf8');

    console.log(`📌 最新请求参数: ${latestPath}`);
    console.log(`📌 最新Postman格式: ${latestPostmanPath}`);

  } catch (error) {
    console.error('记录AI请求参数时出错:', error);
  }
}

// 新增：根据提供商获取API配置信息
function getApiConfigForProvider(provider: string, model: string) {
  const configs = {
    'htsc': {
      baseURL: model === 'saas-doubao-1.5-vl-pro-32k'
        ? 'http://168.64.26.85/web/unauth/LLM_api_proxy/v1'
        : 'http://proxyllm.sit.saas.htsc/v1',
      apiKey: 'htsc',
      headers: {
        'Authorization': 'Bearer htsc',
        'Content-Type': 'application/json'
      },
      modelName: model === 'saas-doubao-1.5-vl-pro-32k' ? 'ht::saas-doubao-1.5-vl-pro-32k' : model
    },
    'ht': {
      baseURL: model === 'saas-doubao-1.5-vl-pro-32k'
        ? 'http://168.64.26.85/web/unauth/LLM_api_proxy/v1'
        : 'http://proxyllm.sit.saas.htsc/v1',
      apiKey: 'htsc',
      headers: {
        'Authorization': 'Bearer htsc',
        'Content-Type': 'application/json'
      },
      modelName: `ht::${model}`
    },
    'siliconflow': {
      baseURL: 'https://api.siliconflow.cn/v1',
      apiKey: process.env.SILICONFLOW_API_KEY || 'YOUR_SILICONFLOW_API_KEY',
      headers: {
        'Authorization': `Bearer ${process.env.SILICONFLOW_API_KEY || 'YOUR_SILICONFLOW_API_KEY'}`,
        'Content-Type': 'application/json'
      },
      modelName: model
    },
    'openrouter': {
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: process.env.OPENROUTER_API_KEY || 'YOUR_OPENROUTER_API_KEY',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY || 'YOUR_OPENROUTER_API_KEY'}`,
        'Content-Type': 'application/json'
      },
      modelName: model
    },
    'deepseek': {
      baseURL: 'https://api.deepseek.com/v1',
      apiKey: process.env.DEEPSEEK_API_KEY || 'YOUR_DEEPSEEK_API_KEY',
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY || 'YOUR_DEEPSEEK_API_KEY'}`,
        'Content-Type': 'application/json'
      },
      modelName: model === 'deepseek-r1' ? 'deepseek-reasoner' : model
    }
  };

  return configs[provider] || {
    baseURL: 'UNKNOWN_PROVIDER',
    apiKey: 'UNKNOWN_API_KEY',
    headers: {},
    modelName: model
  };
}

// 新增：生成Postman可用的请求格式
function generatePostmanRequest(requestParams: any) {
  const { apiConfig, messages, systemPrompt, requestOptions } = requestParams;

  return {
    url: `${apiConfig.baseURL}/chat/completions`,
    method: 'POST',
    headers: apiConfig.headers,
    body: {
      model: apiConfig.modelName,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        ...messages.map(msg => ({
          role: msg.role,
          content: msg.content
        }))
      ],
      stream: false, // Postman测试时建议关闭流式
      temperature: requestOptions.temperature || 0,
      top_p: requestOptions.topP || 1,
      max_tokens: requestOptions.maxTokens || 4096,
      stop: requestOptions.stopSequences || []
    },
    description: `AI模型请求 - ${requestParams.modelConfig.provider}::${requestParams.modelConfig.model}`,
    timestamp: requestParams.timestamp,
    workspaceInfo: {
      workspaceId: requestParams.context.workspaceId,
      playgroundType: requestParams.context.playgroundType,
      projectFramework: requestParams.context.projectFramework
    }
  };
}

// 新增：记录enhance函数的AI调用参数
function logEnhanceRequestParams(
  modelConfig: string,
  systemPrompt: string,
  userPrompt: string,
  generateOptions: any
) {
  try {
    // 确保日志目录存在
    const logDir = join(process.cwd(), 'logs', 'ai-requests');
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }

    // 创建时间戳
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `enhance-request-${timestamp}.json`;
    const filePath = join(logDir, filename);

    // 提取模型提供商和模型名称
    const [provider, model] = modelConfig.split('::');

    // 构造完整的请求参数对象
    const requestParams = {
      timestamp: new Date().toISOString(),
      functionType: 'enhance',
      modelConfig: {
        provider,
        model,
        fullConfig: modelConfig
      },
      apiConfig: getApiConfigForProvider(provider, model),
      systemPrompt,
      userPrompt,
      requestOptions: {
        temperature: generateOptions.temperature,
        maxTokens: generateOptions.maxTokens || generateOptions.max_tokens,
        ...generateOptions
      }
    };

    // 生成Postman可用的请求格式（enhance使用generateText而非chat completions）
    const postmanRequest = generateEnhancePostmanRequest(requestParams);

    // 写入详细参数文件
    writeFileSync(filePath, JSON.stringify(requestParams, null, 2), 'utf8');

    // 写入Postman专用格式
    const postmanFilePath = join(logDir, `enhance-postman-${timestamp}.json`);
    writeFileSync(postmanFilePath, JSON.stringify(postmanRequest, null, 2), 'utf8');

    console.log(`📝 Enhance请求参数已记录到: ${filePath}`);
    console.log(`🚀 Enhance Postman格式已生成: ${postmanFilePath}`);

    // 同时写入最新请求的快捷文件
    const latestPath = join(logDir, 'latest-enhance-request.json');
    const latestPostmanPath = join(logDir, 'latest-enhance-postman.json');
    writeFileSync(latestPath, JSON.stringify(requestParams, null, 2), 'utf8');
    writeFileSync(latestPostmanPath, JSON.stringify(postmanRequest, null, 2), 'utf8');

  } catch (error) {
    console.error('记录enhance AI请求参数时出错:', error);
  }
}

// 新增：生成enhance函数的Postman可用请求格式
function generateEnhancePostmanRequest(requestParams: any) {
  const { apiConfig, systemPrompt, userPrompt, requestOptions } = requestParams;

  return {
    url: `${apiConfig.baseURL}/chat/completions`,
    method: 'POST',
    headers: apiConfig.headers,
    body: {
      model: apiConfig.modelName,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      stream: false,
      temperature: requestOptions.temperature || 0,
      max_tokens: requestOptions.maxTokens || 4096
    },
    description: `Enhance功能AI请求 - ${requestParams.modelConfig.provider}::${requestParams.modelConfig.model}`,
    timestamp: requestParams.timestamp,
    functionType: 'enhance'
  };
}

// 新增：记录extractPlaygroundInfo函数的AI调用参数
function logExtractPlaygroundInfoRequestParams(
  modelConfig: string,
  systemPrompt: string,
  userContent: string,
  generateObjectOptions: any
) {
  try {
    // 确保日志目录存在
    const logDir = join(process.cwd(), 'logs', 'ai-requests');
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }

    // 创建时间戳
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `extract-playground-info-${timestamp}.json`;
    const filePath = join(logDir, filename);

    // 提取模型提供商和模型名称
    const [provider, model] = modelConfig.split('::');

    // 构造完整的请求参数对象
    const requestParams = {
      timestamp: new Date().toISOString(),
      functionType: 'extractPlaygroundInfo',
      modelConfig: {
        provider,
        model,
        fullConfig: modelConfig
      },
      apiConfig: getApiConfigForProvider(provider, model),
      systemPrompt,
      userContent,
      requestOptions: {
        mode: generateObjectOptions.mode,
        schema: 'zod_schema_object', // 简化schema显示
        ...generateObjectOptions
      }
    };

    // 生成Postman可用的请求格式
    const postmanRequest = generateExtractPlaygroundInfoPostmanRequest(requestParams);

    // 写入详细参数文件
    writeFileSync(filePath, JSON.stringify(requestParams, null, 2), 'utf8');

    // 写入Postman专用格式
    const postmanFilePath = join(logDir, `extract-playground-info-postman-${timestamp}.json`);
    writeFileSync(postmanFilePath, JSON.stringify(postmanRequest, null, 2), 'utf8');

    console.log(`📝 ExtractPlaygroundInfo请求参数已记录到: ${filePath}`);
    console.log(`🚀 ExtractPlaygroundInfo Postman格式已生成: ${postmanFilePath}`);

  } catch (error) {
    console.error('记录extractPlaygroundInfo AI请求参数时出错:', error);
  }
}

// 新增：生成extractPlaygroundInfo函数的Postman可用请求格式
function generateExtractPlaygroundInfoPostmanRequest(requestParams: any) {
  const { apiConfig, systemPrompt, userContent } = requestParams;

  return {
    url: `${apiConfig.baseURL}/chat/completions`,
    method: 'POST',
    headers: apiConfig.headers,
    body: {
      model: apiConfig.modelName,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userContent
        }
      ],
      stream: false,
      temperature: 0,
      max_tokens: 1024,
      response_format: {
        type: 'json_object'
      }
    },
    description: `ExtractPlaygroundInfo功能AI请求 - ${requestParams.modelConfig.provider}::${requestParams.modelConfig.model}`,
    timestamp: requestParams.timestamp,
    functionType: 'extractPlaygroundInfo'
  };
}

// 新增：记录convertAttachmentToHtml函数的AI调用参数
function logConvertAttachmentRequestParams(
  modelConfig: string,
  systemPrompt: string,
  messages: any[],
  generateOptions: any,
  attachment: Attachment
) {
  try {
    // 确保日志目录存在
    const logDir = join(process.cwd(), 'logs', 'ai-requests');
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }

    // 创建时间戳
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `convert-attachment-${timestamp}.json`;
    const filePath = join(logDir, filename);

    // 提取模型提供商和模型名称
    const [provider, model] = modelConfig.split('::');

    // 构造完整的请求参数对象
    const requestParams = {
      timestamp: new Date().toISOString(),
      functionType: 'convertAttachmentToHtml',
      modelConfig: {
        provider,
        model,
        fullConfig: modelConfig
      },
      apiConfig: getApiConfigForProvider(provider, model),
      systemPrompt,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        experimental_attachments: msg.experimental_attachments ? 'CONTAINS_ATTACHMENTS' : undefined
      })),
      attachmentInfo: {
        name: attachment.name,
        contentType: attachment.contentType,
        hasUrl: !!attachment.url
      },
      requestOptions: generateOptions
    };

    // 生成Postman可用的请求格式
    const postmanRequest = generateConvertAttachmentPostmanRequest(requestParams);

    // 写入详细参数文件
    writeFileSync(filePath, JSON.stringify(requestParams, null, 2), 'utf8');

    // 写入Postman专用格式
    const postmanFilePath = join(logDir, `convert-attachment-postman-${timestamp}.json`);
    writeFileSync(postmanFilePath, JSON.stringify(postmanRequest, null, 2), 'utf8');

    console.log(`📝 ConvertAttachment请求参数已记录到: ${filePath}`);
    console.log(`🚀 ConvertAttachment Postman格式已生成: ${postmanFilePath}`);

  } catch (error) {
    console.error('记录convertAttachment AI请求参数时出错:', error);
  }
}

// 新增：生成convertAttachmentToHtml函数的Postman可用请求格式
function generateConvertAttachmentPostmanRequest(requestParams: any) {
  const { apiConfig, systemPrompt, messages } = requestParams;

  return {
    url: `${apiConfig.baseURL}/chat/completions`,
    method: 'POST',
    headers: apiConfig.headers,
    body: {
      model: apiConfig.modelName,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        ...messages.map((msg: any) => ({
          role: msg.role,
          content: msg.content
          // 注意：实际的图片attachments在Postman中需要单独处理
        }))
      ],
      stream: false,
      temperature: 0,
      max_tokens: 4096
    },
    description: `ConvertAttachmentToHtml功能AI请求 - ${requestParams.modelConfig.provider}::${requestParams.modelConfig.model}`,
    timestamp: requestParams.timestamp,
    functionType: 'convertAttachmentToHtml',
    note: '此请求包含图片附件，在Postman中测试时需要适当处理multipart/form-data或base64编码'
  };
}
