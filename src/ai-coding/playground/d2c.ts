import { copy, remove } from 'fs-extra';
import * as path from 'node:path';
import * as fsPromises from 'node:fs/promises';
import { JSD<PERSON> } from 'jsdom';
import { tempPath, playgroundRootPath } from './ctx';
import { exists, match, unzip, readFile, writeFile, modifyJson, readDirectory, readJson } from './fs';
import { FileType } from './enums';

async function prepareWorkspace(src: string, dest: string, unzipDir: string): Promise<void> {
  // copy zip from src to dest
  await copy(src, dest, { overwrite: true });
  // unzip to directory
  await unzip(dest, unzipDir);
}

async function moveImages(src: string, desc: string) {
  if (exists(src) && match(src, FileType.Directory)) {
    await fsPromises.rename(src, desc);
  }
}

async function moveRestFiles(src: string, desc: string) {
  const files = await readDirectory(src);
  await Promise.all(files.map((e) => fsPromises.rename(path.join(src, e[0]), path.join(desc, 'src', e[0]))));
}

export async function importD2C(filename: string, workspaceId: string, type: 'react' | 'vue' | 'html') {
  const srcZip: string = tempPath(filename);
  const destZip: string = playgroundRootPath(workspaceId, 'src', filename);
  const unzipDirectory: string = playgroundRootPath(workspaceId, 'src', 'zip');
  await prepareWorkspace(srcZip, destZip, unzipDirectory);

  if (type === 'html') {
    await htmlHandle(unzipDirectory, playgroundRootPath(workspaceId));
  } else if (type === 'react') {
    reactHandle(unzipDirectory, playgroundRootPath(workspaceId));
  } else if (type === 'vue') {
    vueHandle(unzipDirectory, playgroundRootPath(workspaceId));
  } else {
    throw new Error('Not implemented');
  }

  // clean directory
  await remove(destZip);
  await remove(unzipDirectory);
}

function writeCss(name: string, css: string) {
  const template: string = `
import { css } from 'lit';

export default css\`${css}\`;
`;

  return writeFile(name, template);
}

async function appendDepsInPackageJson(directory: string, filename: string, deps: string) {
  const lines = deps.split('\n').map((e) => e.trim());
  const depsMap = Object.fromEntries(
    lines.map((e) => {
      const idx = e.lastIndexOf('@');
      const name = e.slice(0, idx);
      const version = e.slice(idx + 1);
      return [name, version];
    }),
  );

  await modifyJson<{ dependencies: Record<string, string> }>(
    directory,
    filename,
    {} as { dependencies: Record<string, string> },
    (packageJson) => {
      packageJson.dependencies = {
        ...packageJson.dependencies,
        ...depsMap,
      };
      return true;
    },
  );
}

async function htmlHandle(unzipDir: string, workspaceDir: string) {
  const files = {
    html: path.join(unzipDir, 'index.html'),
    reset: path.join(unzipDir, 'reset.css'),
    style: path.join(unzipDir, 'index.css'),
  };

  const entries = await Promise.all(
    Object.entries(files).map(async ([name, uri]) => {
      if (!exists(uri) || !match(uri, FileType.File)) {
        return [name, null];
      }
      const content = await readFile(uri);
      return [name, content];
    }),
  );
  const fileData = Object.fromEntries(entries);

  if (fileData.reset) {
    const resetFilePath: string = path.join(workspaceDir, 'src', 'reset.ts');
    await writeCss(resetFilePath, fileData.reset);
  }
  if (fileData.style) {
    const styleFilePath: string = path.join(workspaceDir, 'src', 'style.ts');
    await writeCss(styleFilePath, fileData.style);
  }
  if (fileData.html) {
    const html = fileData.html;
    const dom = new JSDOM(html);
    const bodyNode = dom.window.document.querySelector('body');
    const body = bodyNode!.innerHTML;
    const indexFilePath: string = path.join(workspaceDir, 'src', 'index.ts');
    // Generate code from html and css
    await writeFile(
      indexFilePath,
      `
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import resetStyle from './reset.ts';
import style from './style.ts';
      
const elementName = 'playground-component';
      
@customElement(elementName)
class PlaygroundComponentElement extends LitElement {
render() {
return html\`${body}\`;
}
      
static styles = [resetStyle, style];
}
      
export default elementName;
`,
    );
  }

  const oldPath: string = path.join(unzipDir, 'imgs');
  const newPath: string = path.join(workspaceDir, 'src', 'public', 'imgs');
  await moveImages(oldPath, newPath);
}

async function reactHandle(unzipDir: string, workspaceDir: string) {
  await appendDepsInPackageJson(workspaceDir, 'package.json', '@ht/sprite-ui@^1.3.0');

  const indexJsFile: string = path.join(unzipDir, 'index.js');
  const indexTsxFile: string = path.join(workspaceDir, 'src', 'index.tsx');
  await fsPromises.rename(indexJsFile, indexTsxFile);

  const oldPath: string = path.join(unzipDir, 'imgs');
  const newPath: string = path.join(workspaceDir, 'src', 'public', 'imgs');
  await moveImages(oldPath, newPath);
  await moveRestFiles(unzipDir, workspaceDir);
}

async function vueHandle(unzipDir: string, workspaceDir: string) {
  await appendDepsInPackageJson(workspaceDir, 'package.json', 'ant-design-vue@^4.2.6');

  const oldPath: string = path.join(unzipDir, 'imgs');
  const newPath: string = path.join(workspaceDir, 'src', 'imgs');
  await moveImages(oldPath, newPath);
  await moveRestFiles(unzipDir, workspaceDir);
}

export async function listD2C(): Promise<string[]> {
  return readJson(tempPath(), 'list.json', []);
}

export async function deleteD2C(id: string) {
  await remove(tempPath(id + '.zip'));
  await modifyJson<any[]>(tempPath(), 'list.json', [], (list) => {
    const idx = list.findIndex((e) => e.id === id);
    list.splice(idx, 1);
    return true;
  });
}
