import * as path from 'node:path';
import { PrismaService } from 'src/prisma/prisma.service';
import { omit } from 'lodash';

export function init(ctx: Partial<Context>) {
  Object.assign(context, ctx);
  ctx.log(`SDK init with config: ${JSON.stringify(omit(ctx, ['prisma']), null, 2)}`);
}

interface Context {
  log(line: string, level?: 'error' | 'log' | 'warn' | 'debug' | 'verbose' | 'fatal'): void;
  sendLogToSse(id: string, line: string): void;
  emit(msg: { type: string; channel: string; data: any }): void;
  assetDir: string;
  playgroundDir: string;
  tempDir: string;
  prisma: PrismaService;
}

const context: Context = {
  log() {},
  sendLogToSse() {},
  emit() {},
  assetDir: '',
  playgroundDir: '',
  tempDir: '',
  prisma: null,
};

export default context;

export const playgroundRootPath = (...segs: string[]) => path.join(context.playgroundDir, ...segs);

export const tempPath = (...segs: string[]) => path.join(context.tempDir, ...segs);
