import { appendFile, mkdirSync, existsSync } from 'node:fs';
import { join } from 'node:path';
import { <PERSON><PERSON><PERSON>, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { playgroundRootPath } from 'src/ai-coding/playground/ctx';
import { PrismaService } from 'src/prisma/prisma.service';
import { BackgroundTaskModule } from '../background-task/background-task.module';
import { DesignProjectModule } from '../design-project/design-project.module';
import { AiCodingController } from './ai-coding.controller';
import { AiCodingService } from './ai-coding.service';
import { init } from './playground/ctx';
import { PlaygroundController } from './playground/playground.controller';

@Module({
  imports: [
    DesignProjectModule,
    forwardRef(() => BackgroundTaskModule)
  ],
  controllers: [AiCodingController, PlaygroundController],
  providers: [AiCodingService, PrismaService, ConfigService],
  exports: [AiCodingService],
})
export class AiCodingModule {
  logger = new Logger(AiCodingModule.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  onModuleInit() {
    const { logger } = this;
    
    init({
      log(line, level = 'log') {
        logger[level](line);
      },
      sendLogToSse(id, line) {
        if (!existsSync(playgroundRootPath(id))) return '';
        if (!existsSync(playgroundRootPath(id, 'logs'))) {
          mkdirSync(playgroundRootPath(id, 'logs'), { recursive: true });
        }
        appendFile(`${playgroundRootPath(id, 'logs', `log.txt`)}`, `${line}\n`, (err) => {
          if (err) {
            console.error(err);
          }
        });
      },
      emit(msg) {
        logger.log(JSON.stringify(msg));
      },
      playgroundDir: join(this.configService.get<string>('HOME_DIR')),
      tempDir: join(this.configService.get<string>('TEMP_DIR')),
      assetDir: join(__dirname, 'assets'),
      prisma: this.prisma,
    });
  }
}
