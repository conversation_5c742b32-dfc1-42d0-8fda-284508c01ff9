import { createReadStream, ReadStream, statSync } from 'node:fs';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Res, StreamableFile, Sse, Put, UseInterceptors, UploadedFile, UploadedFiles, Logger, Req } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import chokidar, { FSWatcher } from 'chokidar';
import { Response, Request } from 'express';
import { map, Observable } from 'rxjs';
import { BackgroundTaskService } from '../background-task/background-task.service';
import { AiCodingService } from './ai-coding.service';
import { BatchBindPrototypesDto } from './dto/batch-bind-prototypes.dto';
import { ChatMsgDto } from './dto/chat-msg.dto';
import { CreateAiCodingDto, CreateD2CAiCodingDto, CreateCommonChatDto } from './dto/create-ai-coding.dto';
import { ImageToCodeDto } from './dto/image-to-code.dto';
import { UpdateAiCodingDto } from './dto/update-ai-coding.dto';
import { playgroundRootPath } from './playground/ctx';
import { ConfigFileType } from './playground/enums';

@Controller('ai-coding')
export class AiCodingController {
  private readonly logger = new Logger(AiCodingController.name);

  constructor(
    private readonly aiCodingService: AiCodingService,
    private readonly config: ConfigService,
    private readonly backgroundTaskService: BackgroundTaskService, // 新增：注入BackgroundTaskService
  ) {}

  @Post()
  create(@Body() createAiCodingDto: CreateAiCodingDto) {
    return this.aiCodingService.create(createAiCodingDto);
  }

  @Post('d2c')
  createFromD2C(@Body() createD2CAiCodingDto: CreateD2CAiCodingDto) {
    return this.aiCodingService.createFromD2C(createD2CAiCodingDto);
  }

  @Post('chat/create')
  @UseInterceptors(FilesInterceptor('files', 10)) // 最多支持10个文件
  async createCommonChat(
    @Body() createChatTaskDto: CreateCommonChatDto,
    @UploadedFiles() files: Express.Multer.File[]
  ) {
    return this.aiCodingService.createCommonChat(createChatTaskDto, files);
  }

  /**
   * 向指定的chat（playground）发送消息
   * 通过playgroundId查找关联的后台任务，然后发送消息
   * @param playgroundId 聊天页面ID（playground ID）
   * @param body 请求体
   */
  @Post('chat/:playgroundId/send-message')
  @UseInterceptors(FilesInterceptor('files', 5))
  async sendMessageToChat(
    @Param('playgroundId') playgroundId: string,
    @Body() body: {
      messageContent: string;
      waitForCompletion?: string; // 'true'/'false'
      maxWaitTime?: string; // 毫秒数字符串
      messageRole?: string;
      userId?: string;
      // 兼容额外字段
      user?: string;
      type?: string;
      model?: string;
      [key: string]: any; // 允许额外字段
    },
    @UploadedFiles() files?: Express.Multer.File[] // 修改参数名为files
  ) {
    try {
      // 1. 根据playgroundId查找关联的后台任务
      const taskInfo = await this.backgroundTaskService.getTaskByPlaygroundId(playgroundId);
      
      if (!taskInfo) {
        return {
          success: false,
          error: `未找到与chat ${playgroundId} 关联的后台任务`,
          playgroundId: playgroundId,
          suggestion: '请确认该chat是通过后台任务创建的，或者该任务仍然存在'
        };
      }

      // 2. 解析请求参数
      const options: any = {
        waitForCompletion: body.waitForCompletion !== 'false', // 默认为true，除非明确设置为false
        messageRole: body.messageRole || 'user',
      };

      // 解析等待时间
      if (body.maxWaitTime) {
        const waitTime = parseInt(body.maxWaitTime, 10);
        if (!isNaN(waitTime) && waitTime > 0) {
          options.maxWaitTime = waitTime;
        }
      }

      // 添加用户ID（支持userId和user两种字段名）
      const userId = body.userId || body.user;
      if (userId) {
        options.userId = userId;
      }

      // 处理附件（修改为使用files参数）
      if (files && files.length > 0) {
        this.logger.log(`📎 处理 ${files.length} 个上传文件`);
        
        options.attachments = files.map(file => {
          this.logger.log(`📄 处理文件: ${file.originalname} (${file.mimetype}, ${file.size} bytes)`);
          
          return {
            name: file.originalname,
            content: file.buffer.toString('base64'),
            contentType: file.mimetype
          };
        });
        
        this.logger.log(`✅ 所有文件处理完成`);
      }

      // 3. 调用BackgroundTaskService发送消息到对应的任务
      const result = await this.backgroundTaskService.sendMessageToTaskChat(
        taskInfo.taskId,
        body.messageContent,
        options
      );

      // 4. 返回成功结果，包含任务信息
      return {
        ...result,
        // 添加chat相关的信息
        chatInfo: {
          playgroundId: playgroundId,
          taskId: taskInfo.taskId,
          taskName: taskInfo.taskName,
          taskType: taskInfo.taskType,
          taskStatus: taskInfo.status
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `向chat发送消息失败: ${error.message}`,
        playgroundId: playgroundId,
        details: error.message
      };
    }
  }

  /**
   * 获取指定chat关联的后台任务信息
   * @param playgroundId 聊天页面ID
   */
  @Get('chat/:playgroundId/task-info')
  async getChatTaskInfo(@Param('playgroundId') playgroundId: string) {
    try {
      const taskInfo = await this.backgroundTaskService.getTaskByPlaygroundId(playgroundId);
      
      if (!taskInfo) {
        return {
          success: false,
          message: `未找到与chat ${playgroundId} 关联的后台任务`,
          playgroundId: playgroundId,
          hasTask: false
        };
      }

      return {
        success: true,
        message: '查找成功',
        playgroundId: playgroundId,
        hasTask: true,
        taskInfo: {
          taskId: taskInfo.taskId,
          taskName: taskInfo.taskName,
          taskType: taskInfo.taskType,
          status: taskInfo.status,
          progress: taskInfo.progress,
          user: taskInfo.user,
          tags: taskInfo.tags,
          created: taskInfo.created,
          updated: taskInfo.updated,
          taskItem: taskInfo.taskItem,
          metadata: taskInfo.metadata
        }
      };

    } catch (error) {
      return {
        success: false,
        error: `查询chat任务信息失败: ${error.message}`,
        playgroundId: playgroundId,
        hasTask: false
      };
    }
  }

  @Get('credits')
  getCredits() {
    return this.aiCodingService.getCredits();
  }

  @Get()
  findAll(
    @Query('tag') tag?: string, 
    @Query('sort') sort?: 'asc' | 'desc', 
    @Query('user') user?: string,
    @Query('take') take?: string,
    @Query('skip') skip?: string,
    @Query('skipAllType') skipAllType?: string,
    @Query('projectId') projectId?: string
  ) {
    const takeNum = take ? parseInt(take, 10) : undefined;
    const skipNum = skip ? parseInt(skip, 10) : undefined;
    const skipAllTypeFlag = skipAllType === 'true';
    return this.aiCodingService.findAll(tag, sort, user, takeNum, skipNum, skipAllTypeFlag, projectId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.aiCodingService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateAiCodingDto: UpdateAiCodingDto) {
    return this.aiCodingService.update(id, updateAiCodingDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.aiCodingService.remove(id);
  }

  @Post(':id/@start')
  start(@Param('id') id: string, @Req() req: Request) {
    const hostname = req.get('host') || req.hostname;
    return this.aiCodingService.start(id, hostname);
  }

  @Post(':id/@restart')
  restart(@Param('id') id: string, @Req() req: Request) {
    const hostname = req.get('host') || req.hostname;
    return this.aiCodingService.restart(id, hostname);
  }

  @Post(':id/@stop')
  stop(@Param('id') id: string) {
    return this.aiCodingService.stop(id);
  }

  @Post(':id/@build')
  build(@Param('id') id: string) {
    return this.aiCodingService.build(id);
  }

  @Get(':id/@info')
  info(@Param('id') id: string, @Query('type') type: ConfigFileType) {
    return this.aiCodingService.info(id, type);
  }

  @Post(':id/@install')
  install(@Param('id') id: string, @Body() installDto: { deps: string }) {
    return this.aiCodingService.install(id, installDto.deps);
  }

  @Post(':id/@uninstall')
  uninstall(@Param('id') id: string, @Body() uninstallDto: { deps: string }) {
    return this.aiCodingService.uninstall(id, uninstallDto.deps);
  }

  @Post(':id/@unarchive')
  unarchive(@Param('id') id: string) {
    return this.aiCodingService.unarchive(id);
  }

  @Get(':id/@chat')
  getChat(
    @Param('id') id: string,
    @Query('sort') sort?: 'asc' | 'desc',
    @Query('take') take?: string,
    @Query('skip') skip?: string,
  ) {
    let takeOpt, skipOpt;
    if (take) {
      takeOpt = parseInt(take);
    }
    if (skip) {
      skipOpt = parseInt(skip);
    }
    return this.aiCodingService.findAllChat(id, { sort, take: takeOpt, skip: skipOpt });
  }

  @Post(':id/@chat')
  chat(@Body() chatMsgDto: ChatMsgDto, @Res() res: Response) {
    this.aiCodingService.chat(chatMsgDto, res);
  }

  @Post(':id/@mkdir')
  createDirectory(@Param('id') id: string, @Body() createDirectoryDto: { path: string }) {
    return this.aiCodingService.createDirectory(id, createDirectoryDto.path);
  }

  @Get(':id/@files')
  files(@Param('id') id: string) {
    return this.aiCodingService.listFiles(id);
  }

  @Get(':id/@files/*')
  file(@Param('id') id: string, @Param() param: string[]) {
    return this.aiCodingService.getFile(id, param[0]);
  }

  @Post(':id/@save')
  saveFile(@Param('id') id: string, @Body() saveFileDto: { path: string; content: string }) {
    return this.aiCodingService.saveFile(id, saveFileDto.path, saveFileDto.content);
  }

  @Post(':id/@move')
  moveFile(@Param('id') id: string, @Body() moveFileDto: { oldPath: string; newPath: string }) {
    return this.aiCodingService.moveFile(id, moveFileDto.oldPath, moveFileDto.newPath);
  }

  @Delete(':id/@delete')
  deleteFile(@Param('id') id: string, @Body() deleteFileDto: { path: string }) {
    return this.aiCodingService.deleteFile(id, deleteFileDto.path);
  }

  @Get(':id/@download')
  async download(@Param('id') id: string) {
    const file = await this.aiCodingService.download(id);
    return new StreamableFile(file, {
      type: 'application/zip',
      disposition: 'attachment; filename="source.zip"',
    });
  }

  @Get(':id/@webide')
  async webide(@Param('id') id: string, @Res() res: Response) {
    // Sync data to webide directory
    await this.aiCodingService.syncWebide(id);
    const webIdeUrl = this.config.get('WEBIDE_URL');
    const tenantId = 'ai-coding';
    const projectId = 'ai-coding';
    const unitId = 'ai-coding';
    const taskId = id;
    const repository = 'taskId'; // no use
    const branch = 'master'; // no use
    const userId = '019270'; // TODO: 从当前用户获取
    return res.redirect(`${webIdeUrl}?tenantId=${tenantId}&projectId=${projectId}&unitId=${unitId}&taskId=${taskId}&repository=${repository}&branch=${branch}&userId=${userId}`);
  }

  @Get(':id/@versionStatus')
  gitStatus(@Param('id') id: string) {
    return this.aiCodingService.versionStatus(id);
  }

  @Post(':id/@changeVersion')
  changeVersion(@Param('id') id: string, @Body() changeVersionDto: { version: string }) {
    return this.aiCodingService.changeVersion(id, changeVersionDto.version);
  }

  @Post(':id/@commitFiles')
  commitFiles(@Param('id') id: string) {
    return this.aiCodingService.commitFiles(id);
  }

  @Delete(':id/@deleteMessage')
  deleteMessage(@Param('id') id: string, @Body() deleteMessageDto: { messageId: string }) {
    return this.aiCodingService.deleteMessage(id, deleteMessageDto.messageId);
  }

  @Put(':id/@editMessage')
  editMessage(@Param('id') id: string, @Body() editMessageDto: { messageId: string; content: string }) {
    return this.aiCodingService.editMessage(id, editMessageDto.messageId, editMessageDto.content);
  }

  @Sse(':id/@logs')
  getLogs(@Param('id') id: string): Observable<MessageEvent> {
    let messageId: number = 0;
    return new Observable((observer) => {
      const file: string = playgroundRootPath(id, 'logs', `log.txt`);
      let start: number = 0;
      
      // 优化：只监听特定的日志文件，而不是整个目录
      // 排除不必要的文件和目录，特别是.git目录
      const usePolling = process.env.CHOKIDAR_USEPOLLING === 'true';
      const pollingInterval = parseInt(process.env.CHOKIDAR_INTERVAL || '2000', 10);
      
      const watcher: FSWatcher = chokidar.watch(file, { 
        persistent: true,
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/.next/**',
          '**/dist/**',
          '**/build/**',
          '**/.cache/**',
          '**/coverage/**',
          '**/*.log',
          '**/tmp/**',
          '**/temp/**'
        ],
        ignoreInitial: false,
        usePolling: usePolling, // 根据环境变量决定是否使用polling
        interval: usePolling ? pollingInterval : undefined,
        binaryInterval: usePolling ? pollingInterval + 1000 : undefined,
        awaitWriteFinish: {
          stabilityThreshold: usePolling ? 300 : 100,
          pollInterval: usePolling ? 100 : 50
        },
        // 减少错误日志
        followSymlinks: false,
        ignorePermissionErrors: true
      });
      
      watcher.on('add', (path) => {
        if (path === file) {
          const stream: ReadStream = createReadStream(file, { start: 0 });
          stream.on('data', (chunk) => observer.next(chunk));
          stream.on('error', (err) => observer.error(err));
          start = statSync(file).size;
        }
      });
      watcher.on('change', (path) => {
        if (path === file) {
          const stream: ReadStream = createReadStream(file, { start: start });
          stream.on('data', (chunk) => observer.next(chunk));
          stream.on('error', (err) => observer.error(err));
          start = statSync(file).size;
        }
      });

      // Clean up resources when the observable is unsubscribed
      return () => {
        watcher?.close();
      };
    }).pipe(map((chunk) => ({ id: (messageId++).toString(), data: { message: chunk.toString() } }) as MessageEvent));
  }

  @Post('@enhance')
  enhance(@Body() enhanceDto: { desc: string }) {
    return this.aiCodingService.enhance(enhanceDto.desc);
  }

  @Post(':id/@deploy')
  deploy(@Param('id') id: string, @Body() deployDto?: { mode?: 'preview' }) {
    return this.aiCodingService.deploy(id, deployDto?.mode);
  }

  @Post('@uploadImage')
  @UseInterceptors(FileInterceptor('file'))
  uploadImage(@UploadedFile() file: Express.Multer.File) {
    return this.aiCodingService.uploadImage(file.buffer, file.originalname);
  }

  @Post('image2code')
  image2code(@Body() imageDto: ImageToCodeDto, @Res() res: Response) {
    this.aiCodingService.image2code(imageDto, res);
  }

  @Post(':id/@checkIncompleteOutput')
  checkIncompleteOutput(@Param('id') id: string, @Body() checkDto: { content: string; messageId?: string }) {
    return this.aiCodingService.checkIncompleteOutput(id, checkDto.content, checkDto.messageId);
  }

  @Get('lanhu/image/version/code')
  getLanhuImageVersionCode(
    @Query('version_id') versionId: string,
    @Query('lanhu_token') lanhuToken?: string
  ) {
    return this.aiCodingService.getLanhuImageVersionCode(versionId, lanhuToken);
  }

  @Get('lanhu/project/images')
  getLanhuProjectImages(
    @Query('project_id') projectId: string,
    @Query('lanhu_token') lanhuToken?: string
  ) {
    return this.aiCodingService.getLanhuProjectImages(projectId, lanhuToken);
  }

  @Get('lanhu/project/sectors')
  getLanhuProjectSectors(
    @Query('project_id') projectId: string,
    @Query('lanhu_token') lanhuToken?: string
  ) {
    return this.aiCodingService.getLanhuProjectSectors(projectId, lanhuToken);
  }

  @Post('lanhu/login')
  lanhuLogin() {
    return this.aiCodingService.lanhuLogin();
  }

  @Put(':id/step-by-step-data')
  updateStepByStepData(@Param('id') id: string, @Body() updateStepByStepDataDto: { stepByStepData: any }) {
    return this.aiCodingService.updateStepByStepData(id, updateStepByStepDataDto.stepByStepData);
  }

  @Patch(':id/custom-preview-url')
  async updateCustomPreviewUrl(
    @Param('id') id: string,
    @Body() body: { customPreviewUrl: string }
  ) {
    return this.aiCodingService.updateCustomPreviewUrl(id, body.customPreviewUrl);
  }

  @Post(':id/@log')
  async log(@Param('id') id: string, @Body() body: { level: string; message: string }) {
  }

  /**
   * 获取系统全局配置
   */
  @Get('system/config')
  getSystemConfig() {
    return this.aiCodingService.getSystemConfig();
  }

  /**
   * 更新蓝湖授权令牌
   */
  @Put('system/lanhu-authorization')
  updateLanhuAuthorization(@Body() body: { lanhuAuthorization: string }) {
    if (!body.lanhuAuthorization || body.lanhuAuthorization.trim() === '') {
      return {
        success: false,
        error: '蓝湖授权令牌不能为空'
      };
    }
    return this.aiCodingService.updateLanhuAuthorization(body.lanhuAuthorization);
  }

  /**
   * 获取当前生效的蓝湖授权令牌状态（不返回具体令牌内容，只返回是否已设置）
   */
  @Get('system/lanhu-authorization/status')
  async getLanhuAuthorizationStatus() {
    try {
      const systemConfig = await this.aiCodingService.getSystemConfig();
      if (!systemConfig.success) {
        return {
          success: false,
          error: systemConfig.error
        };
      }

      return {
        success: true,
        data: {
          hasCustomToken: !!systemConfig.data.lanhuAuthorization,
          isUsingDefault: !systemConfig.data.lanhuAuthorization,
          lastUpdated: systemConfig.data.updated
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  @Post('batch-bind-prototypes')
  async batchBindPrototypes(@Body() batchBindDto: BatchBindPrototypesDto) {
    return this.aiCodingService.batchBindPrototypes(batchBindDto);
  }

}

export interface MessageEvent {
  data: string | object;
  id?: string;
  type?: string;
  retry?: number;
}

