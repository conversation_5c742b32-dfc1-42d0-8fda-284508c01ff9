import { EventEmitter } from 'events'; // 添加EventEmitter导入
import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatMessage, Project } from '@prisma/client';
import axios from 'axios';
import { type Response } from 'express';
import { flatten, uniq } from 'lodash';
import { fetch as nodeFetch, ProxyAgent } from 'undici';
import { $ } from 'zx'
import { BackgroundTaskService } from '../background-task/background-task.service';
import { BackgroundTaskItem, BackgroundTaskConfig } from '../background-task/interfaces';
import { DesignProjectBackgroundTaskService } from '../design-project/background-task.service';
import { DesignProjectService } from '../design-project/design-project.service';
import { PrismaService } from '../prisma/prisma.service';
import { BatchBindPrototypesDto, BatchBindPrototypesResponse } from './dto/batch-bind-prototypes.dto';
import { ChatMsgDto } from './dto/chat-msg.dto';
import { CreateAiCodingDto, CreateD2CAiCodingDto, CreateCommonChatDto } from './dto/create-ai-coding.dto';
import { ImageToCodeDto } from './dto/image-to-code.dto';
import { UpdateAiCodingDto } from './dto/update-ai-coding.dto';
import {
  createPlayground,
  deletePlayground,
  startPlayground,
  buildPlayground,
  cleanPlayground,
  chat,
  extactPlaygroundInfo,
  listFiles,
  getFile,
  createDirectory,
  moveFile,
  saveFile,
  deleteFile,
  installDeps,
  uninstallDeps,
  playgroundInfo,
  zipPlayground,
  changeBranch,
  commitFiles,
  versionStatus,
  removeVersions,
  deployPlayground,
  unarchivePlayground,
  isArchived,
  checkIncompleteOutput,
  uploadImageToMinIO,
  stopPlayground,
} from './playground';
import { enhance } from './playground/ai';
import { playgroundRootPath } from './playground/ctx';
import { Command } from './playground/enums';
import { ConfigFileType } from './playground/enums';
import { convertToUIMessage } from './playground/utils/message';

@Injectable()
export class AiCodingService {

  constructor(
    private prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly designProjectTaskService: DesignProjectBackgroundTaskService,
    private readonly backgroundTaskService: BackgroundTaskService,
    private readonly designProjectService: DesignProjectService
  ) {
    // 定时清理过期不用的 vite server
    cleanPlayground();
  }

  logger = new Logger(AiCodingService.name);

  async create(createAiCodingDto: CreateAiCodingDto) {
    const { desc, projectId, type: explicitType, enableAutoIteration, enableStepByStep, enablePreview } = createAiCodingDto;
    const info = await extactPlaygroundInfo(createAiCodingDto.desc);
    this.logger.log('Extract playground info from desc:', desc, info.object);
    let project: Project;
    if (projectId) {
      project = await this.prisma.project.findUnique({
        where: {
          id: projectId,
        },
      });
    }
    const params = {
      desc: info.object.topic,
      type: explicitType ?? project?.framework ?? info.object.type,
      name: info.object.name,
      projectId,
    };
    const rec = await this.prisma.playground.create({
      data: {
        user: createAiCodingDto.user,
        model: createAiCodingDto.model || 'htsc::saas-doubao-15-pro-32k', // 默认模型
        isPublic: !!createAiCodingDto.isPublic,
        enableAutoIteration,
        enableStepByStep,
        enablePreview,
        ...params,
      },
    });
    return createPlayground({
      ...params,
      id: rec.id,
    });
  }

  async createFromD2C(createD2CAiCodingDto: CreateD2CAiCodingDto) {
    const description: string = 'figma 插件页面'; // 默认描述
    const name: string = 'figma'; // 默认工程名
    const model: string = 'htsc::saas-doubao-15-pro-32k'; // 默认使用公司内部豆包模型
    const after: [Command, Record<string, any>] = [
      Command.DesignToCode,
      {
        filename: createD2CAiCodingDto.filename,
        type: createD2CAiCodingDto.type,
      },
    ];
    const params = {
      desc: description,
      type: createD2CAiCodingDto.template,
      name: name,
      after: after,
    };
    const rec = await this.prisma.playground.create({
      data: {
        user: createD2CAiCodingDto.user,
        model: model,
        type: createD2CAiCodingDto.template,
        desc: description,
        name: name,
      },
    });
    return createPlayground({
      ...params,
      id: rec.id,
    });
  }

  findAll(tag?: string, sort?: 'asc' | 'desc', user?, take?: number, skip?: number, skipAllType?: boolean, projectId?: string) {
    // 构建查询条件
    const whereConditions: any = {};

    // 如果指定了用户，只查询该用户的项目
    if (user) {
      whereConditions.user = user;
      // 如果指定了类型标签，添加类型过滤
      if (tag) {
        whereConditions.type = tag;
      }
    } else {
      // 如果没有指定用户，查询所有项目（包括公开和私有）
      if (tag) {
        whereConditions.type = tag;
      }
    }

    // 如果指定了项目ID，添加项目过滤
    if (projectId) {
      whereConditions.projectId = projectId;
    }

    // 默认过滤掉html类型和spec-to-prod-code类型的chat会话，除非skipAllType为true
    if (!skipAllType) {
      whereConditions.type = {
        notIn: ['html', 'spec-to-prod-code']
      };
    }

    return this.prisma.playground.findMany({
      where: whereConditions,
      orderBy: {
        created: sort ?? 'desc',
      },
      take,
      skip,
    });
  }

  async findOne(id: string) {
    const entry = await this.prisma.playground.findUnique({
      where: {
        id,
      },
    });
    const archived = isArchived(id);
    return { ...entry, archived };
  }

  update(id: string, updateAiCodingDto: UpdateAiCodingDto) {
    return this.prisma.playground.update({
      where: {
        id,
      },
      data: updateAiCodingDto,
    });
  }

  async remove(id: string) {
    await deletePlayground(id);
    await this.prisma.playground.delete({
      where: {
        id,
      },
    });
  }

  start(id: string, hostname?: string) {
    return startPlayground(id, false, hostname);
  }

  restart(id: string, hostname?: string) {
    return startPlayground(id, true, hostname);
  }

  stop(id: string) {
    return stopPlayground(id);
  }

  build(id: string) {
    return buildPlayground(id);
  }

  info(id: string, type: ConfigFileType) {
    return playgroundInfo(id, type);
  }

  install(id: string, deps: string) {
    return installDeps(id, deps);
  }

  uninstall(id: string, deps: string) {
    return uninstallDeps(id, deps);
  }

  unarchive(id: string) {
    return unarchivePlayground(id);
  }

  async findAllChat(id: string, opts: { sort?: 'asc' | 'desc', take?: number, skip?: number }) {
    const messages = await this.prisma.chatMessage.findMany({
      where: {
        playgroundId: id,
      },
      take: opts.take ?? 100,
      skip: opts.skip ?? 0,
      orderBy: {
        created: opts.sort ?? 'desc',
      },
    });
    return messages.map(msg => convertToUIMessage(msg));
  }

  async chat(chatMsgDto: ChatMsgDto, res?: Response) {
    const pg = await this.findOne(chatMsgDto.id);
    let project: Project | null = null;
    if (pg.projectId) {
      project = await this.prisma.project.findUnique({
        where: {
          id: pg.projectId,
        },
      });
    }

    // 生成唯一的对话标识符，用于独立进度跟踪
    const dialogId = `chat-dialog-${chatMsgDto.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const chatProcess = () => chat(chatMsgDto, pg, project ?? {
      id: '',
      name: '',
      description: '',
      framework: 'react',
      componentLibrary: 'antd',
      llmstxt: '',
      created: new Date(),
      user: pg.user,
      projectId: null,
      updated: new Date(),
      config: null,
    } as Project, res);

    if (res) {
      // 如果有res对象，表示是直接的HTTP请求，需要流式返回
      this.logger.log(`🚀 [Chat] 开始流式聊天对话 (playground: ${chatMsgDto.id})`);
      return chatProcess();
    } else {
      // 如果没有res对象，表示是内部调用，参考TaskWorkerService的后台执行逻辑
      this.logger.log(`🤖 [Chat] 开始后台聊天对话 (playground: ${chatMsgDto.id}, dialogId: ${dialogId})`);
      
      // 后台执行时，创建占位消息和管理会话状态
      this.executeBackgroundChatWithPlaceholder(chatMsgDto, pg, project, dialogId).catch(err => {
        this.logger.error(`❌ [Chat] 后台聊天对话失败 (session ${chatMsgDto.id}, dialogId: ${dialogId}):`, err);
      });
    }
  }

  /**
   * 执行后台聊天并管理占位消息和会话状态
   * 参考 TaskWorkerService.executeAIDialog 的实现逻辑
   */
  private async executeBackgroundChatWithPlaceholder(
    chatMsgDto: ChatMsgDto,
    playground: any,
    project: Project | null,
    dialogId: string
  ): Promise<void> {
    try {
      // 1. 记录开始时的消息数量
      const initialMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: chatMsgDto.id }
      });

      this.logger.log(`📊 [Chat] AI会话开始时消息数量: ${initialMessageCount} (dialogId: ${dialogId})`);

      // 2. 创建AI消息占位符，状态为processing（参考TaskWorkerService）
      const aiMessageId = `${chatMsgDto.id}::${generateId()}`;
      await this.prisma.chatMessage.create({
        data: {
          id: aiMessageId,
          playgroundId: chatMsgDto.id,
          role: 'ASSISTANT',
          content: '', // 初始为空内容，前端会显示loading效果
          status: 'processing', // 设置为处理中状态，前端会轮询检测
          taskId: dialogId, // 关联对话ID作为任务ID
        },
      });

      this.logger.log(`📝 [Chat] 创建AI消息占位符: ${aiMessageId} (dialogId: ${dialogId})`);

      // 3. 执行AI对话请求（不等待回复，后台异步执行）
      this.logger.log(`🚀 [Chat] 开始执行AI对话请求... (dialogId: ${dialogId})`);
      
      const chatPromise = this.executeBackgroundChatRequest(chatMsgDto, playground, project);
      
      this.logger.log(`✅ [Chat] AI对话请求已启动，开始等待AI回复... (dialogId: ${dialogId})`);

      // 4. 等待AI完成回复（参考TaskWorkerService.waitForAIResponse）
      await this.waitForBackgroundChatResponse(chatMsgDto.id, {
        initialMessageCount,
        maxWaitTime: 300000, // 默认5分钟
        pollInterval: 3000, // 默认3秒
        dialogId: dialogId,
        chatPromise: chatPromise
      });

      this.logger.log(`🎉 [Chat] AI对话执行完成 (playground: ${chatMsgDto.id}, dialogId: ${dialogId})`);

    } catch (error) {
      this.logger.error(`❌ [Chat] 后台AI对话执行失败 (playground: ${chatMsgDto.id}, dialogId: ${dialogId}):`, error);

      // 失败时更新AI消息状态为失败
      try {
        const processingMessage = await this.prisma.chatMessage.findFirst({
          where: {
            playgroundId: chatMsgDto.id,
            role: 'ASSISTANT',
            taskId: dialogId
          }
        });

        if (processingMessage) {
          await this.prisma.chatMessage.update({
            where: { id: processingMessage.id },
            data: {
              status: 'failed',
              content: `AI对话执行失败: ${error.message}`
            }
          });
          this.logger.log(`❌ [Chat] 更新AI消息状态为失败: ${processingMessage.id}`);
        }
      } catch (updateError) {
        this.logger.error(`❌ [Chat] 更新失败消息状态时出错:`, updateError);
      }

      throw error;
    }
  }

  /**
   * 执行后台AI对话请求
   * 参考 TaskWorkerService.executeAIDialogRequest 的实现
   */
  private async executeBackgroundChatRequest(
    chatMsgDto: ChatMsgDto,
    playground: any,
    project: Project | null
  ): Promise<void> {
    const finalProject = project ?? {
      id: '',
      name: '',
      description: '',
      framework: 'react',
      componentLibrary: 'antd',
      llmstxt: '',
      created: new Date(),
      user: playground.user,
      projectId: null,
      updated: new Date(),
      config: null,
    } as Project;

    // 创建模拟的Server Response对象，用于后台AI对话
    const mockResponse = this.createMockServerResponseForBackground();

    this.logger.log(`🚀 [Chat] 调用AI工具类chat方法 (playground: ${chatMsgDto.id})`);

    // 启动AI对话
    const chatPromise = new Promise<void>((resolve, reject) => {
      let isResolved = false;

      const finishHandler = () => {
        if (!isResolved) {
          isResolved = true;
          this.logger.log(`✅ [Chat] AI工具类chat请求发送完成 (playground: ${chatMsgDto.id})`);
          resolve();
        }
      };

      const errorHandler = (error) => {
        if (!isResolved) {
          isResolved = true;
          this.logger.error(`❌ [Chat] AI工具类chat请求失败 (playground: ${chatMsgDto.id}):`, error);
          reject(error);
        }
      };

      try {
        mockResponse.once('finish', finishHandler);
        mockResponse.once('error', errorHandler);

        // 启动chat方法
        chat(chatMsgDto, playground, finalProject, mockResponse as any);
      } catch (error) {
        if (!isResolved) {
          isResolved = true;
          reject(error);
        }
      }
    });

    // 等待chat方法完成（成功或失败）
    await chatPromise;

    this.logger.log(`🎯 [Chat] AI对话请求已启动，开始等待AI回复... (playground: ${chatMsgDto.id})`);
  }

  /**
   * 等待后台AI完成回复
   * 参考 TaskWorkerService.waitForAIResponse 的实现
   */
  private async waitForBackgroundChatResponse(
    playgroundId: string,
    options: {
      initialMessageCount: number;
      maxWaitTime: number;
      pollInterval: number;
      dialogId: string;
      chatPromise: Promise<void>;
    }
  ): Promise<void> {
    this.logger.log(`⏳ [Chat] 开始等待AI回复 (playground: ${playgroundId}, dialogId: ${options.dialogId})`);
    this.logger.log(`📊 [Chat] 等待参数: 初始消息数=${options.initialMessageCount}, 最大等待时间=${options.maxWaitTime}ms, 轮询间隔=${options.pollInterval}ms`);

    const startTime = Date.now();
    const maxAttempts = Math.ceil(options.maxWaitTime / options.pollInterval);
    let attempts = 0;

    this.logger.log(`🎯 [Chat] 最大尝试次数: ${maxAttempts}`);

    try {
      while (true) {
        attempts++;

        // 检查是否超过最大尝试次数
        if (attempts > maxAttempts) {
          throw new Error(`AI会话超时 (尝试次数: ${attempts}/${maxAttempts}, dialogId: ${options.dialogId})`);
        }

        // 检查是否有新的AI消息
        const currentMessageCount = await this.prisma.chatMessage.count({
          where: { playgroundId: playgroundId }
        });

        this.logger.log(`🔍 [Chat] 第${attempts}次检查: 当前消息数=${currentMessageCount}, 初始消息数=${options.initialMessageCount} (dialogId: ${options.dialogId})`);

        if (currentMessageCount > options.initialMessageCount) {
          // 检查最新的消息是否是AI回复
          const latestMessage = await this.prisma.chatMessage.findFirst({
            where: { playgroundId: playgroundId },
            orderBy: { created: 'desc' }
          });

          this.logger.log(`📝 [Chat] 最新消息信息: role=${latestMessage?.role}, contentLength=${latestMessage?.content?.length || 0}, created=${latestMessage?.created} (dialogId: ${options.dialogId})`);

          if (latestMessage && latestMessage.role === 'ASSISTANT') {
            const waitTime = Date.now() - startTime;
            this.logger.log(`✅ [Chat] AI回复完成 (playground: ${playgroundId}, dialogId: ${options.dialogId})`);
            this.logger.log(`📊 [Chat] 会话统计: 尝试次数=${attempts}, 等待时间=${waitTime}ms`);
            this.logger.log(`📝 [Chat] AI回复内容长度: ${latestMessage.content.length} 字符`);
            this.logger.log(`🎉 [Chat] AI回复内容预览: ${latestMessage.content.substring(0, 200)}...`);

            // 清理旧的占位符消息（processing状态且content为空的消息）
            const placeholderMessages = await this.prisma.chatMessage.findMany({
              where: {
                playgroundId: playgroundId,
                role: 'ASSISTANT',
                status: 'processing',
                taskId: options.dialogId,
                NOT: {
                  id: latestMessage.id // 排除当前最新消息
                }
              }
            });

            // 删除占位符消息
            for (const placeholder of placeholderMessages) {
              if (!placeholder.content || placeholder.content.trim() === '') {
                await this.prisma.chatMessage.delete({
                  where: { id: placeholder.id }
                });
                this.logger.log(`🗑️ [Chat] 删除占位符消息: ${placeholder.id}`);
              }
            }

            // 更新AI消息状态为完成
            if (latestMessage.status === 'processing') {
              await this.prisma.chatMessage.update({
                where: { id: latestMessage.id },
                data: { status: 'completed' }
              });
              this.logger.log(`✅ [Chat] 更新AI消息状态为完成: ${latestMessage.id}`);
            }

            // AI回复完成后，给AI工具一些时间来生成和保存文件
            this.logger.log(`⏳ [Chat] AI回复完成，等待文件生成处理...`);
            await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
            this.logger.log(`✅ [Chat] 文件生成等待完成`);

            return;
          } else if (latestMessage) {
            this.logger.log(`⚠️ [Chat] 检测到新消息但不是AI回复: role=${latestMessage.role} (dialogId: ${options.dialogId})`);
          }
        } else {
          this.logger.log(`ℹ️ [Chat] 消息数量未增加，继续等待... (dialogId: ${options.dialogId})`);
        }

        // 每10次检查输出一次进度
        if (attempts % 10 === 0) {
          const waitTime = Date.now() - startTime;
          this.logger.log(`⏳ [Chat] 等待AI回复中... (${attempts}/${maxAttempts}, 已等待: ${waitTime}ms, dialogId: ${options.dialogId})`);
        }

        // 等待下一次检查
        await new Promise(resolve => setTimeout(resolve, options.pollInterval));
      }
    } catch (error) {
      const totalWaitTime = Date.now() - startTime;
      this.logger.warn(`⚠️ [Chat] 等待AI回复失败 (playground: ${playgroundId}, dialogId: ${options.dialogId}, 总等待时间: ${totalWaitTime}ms)`);

      // 检查是否至少有消息增加
      const finalMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });

      this.logger.log(`📊 [Chat] 失败时的消息统计: 最终消息数=${finalMessageCount}, 初始消息数=${options.initialMessageCount}, 新增消息=${finalMessageCount - options.initialMessageCount}`);

      if (finalMessageCount > options.initialMessageCount) {
        this.logger.log(`ℹ️ [Chat] 虽然失败，但检测到有新消息产生 (${finalMessageCount - options.initialMessageCount} 条)`);

        // 获取所有新消息的详细信息
        const newMessages = await this.prisma.chatMessage.findMany({
          where: { playgroundId: playgroundId },
          orderBy: { created: 'desc' },
          take: finalMessageCount - options.initialMessageCount
        });

        this.logger.log(`📋 [Chat] 新消息详情:`, newMessages.map(msg => ({
          role: msg.role,
          contentLength: msg.content?.length || 0,
          created: msg.created
        })));
      }

      throw error;
    }
  }

  /**
   * 创建用于后台AI对话的模拟Server Response对象
   * 参考 TaskWorkerService.createMockServerResponse 的实现
   */
  private createMockServerResponseForBackground(): any {
    const mockResponse = new EventEmitter();
    let responseContent = '';
    const startTime = Date.now(); // 记录开始时间
    const ERROR_DETECTION_WINDOW = 3000; // 错误检测窗口：前3秒

    (mockResponse as any).writeHead = () => {
      return mockResponse;
    };

    (mockResponse as any).write = (chunk: any) => {
      // 正确解码chunk数据
      let chunkStr = '';
      if (chunk) {
        if (Buffer.isBuffer(chunk)) {
          chunkStr = chunk.toString('utf8');
        } else if (chunk instanceof Uint8Array) {
          chunkStr = Buffer.from(chunk).toString('utf8');
        } else if (typeof chunk === 'string') {
          chunkStr = chunk;
        } else {
          chunkStr = String(chunk);
        }
      }

      if (chunkStr) {
        responseContent += chunkStr;

        // 只在前3秒内检测API错误，避免误判正常AI回复
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime <= ERROR_DETECTION_WINDOW) {
          this.logger.log(`⏱️ [Chat] 在错误检测窗口内 (${elapsedTime}ms/${ERROR_DETECTION_WINDOW}ms)`);

          // 检测明显的API错误 - 同时检查原始内容和小写内容
          const content = responseContent.toLowerCase();

          if (content.includes('bad request') ||
            content.includes('unauthorized') ||
            content.includes('api key') ||
            content.includes('invalid api key') ||
            content.includes('authentication failed') ||
            content.includes('403 forbidden') ||
            content.includes('401 unauthorized') ||
            (content.includes('error') && content.includes('key'))) {

            this.logger.log('🚨 [Chat] 检测到API错误，触发error事件');
            this.logger.log('📋 [Chat] 错误内容:', responseContent.substring(0, 500));
            // 立即触发错误事件
            process.nextTick(() => {
              mockResponse.emit('error', new Error(`AI API调用失败: 检测到API错误响应 - ${chunkStr.includes('Unauthorized') || chunkStr.includes('unauthorized') ? 'Unauthorized' : '其他错误'}`));
            });
            return false;
          } else if (content.includes('404') || content.includes('not found')) {
            this.logger.log('🚨 [Chat] 检测到404错误，触发error事件');
            this.logger.log('📋 [Chat] 错误内容:', responseContent.substring(0, 500));
            // 立即触发错误事件
            process.nextTick(() => {
              mockResponse.emit('error', new Error(`AI API调用失败: 检测到404错误响应 - ${chunkStr.includes('Unauthorized') || chunkStr.includes('unauthorized') ? 'Unauthorized' : '其他错误'}`));
            });
            return false;
          }
        }
      }

      return true;
    };

    (mockResponse as any).end = (chunk?: any, encoding?: any, cb?: any) => {
      this.logger.log('🏁 [Chat] MockResponse end called, chunk:', chunk ? chunk.toString().substring(0, 200) : 'null');
      if (typeof chunk === 'function') {
        cb = chunk;
        chunk = undefined;
      } else if (typeof encoding === 'function') {
        cb = encoding;
        encoding = undefined;
      }

      if (chunk) {
        responseContent += chunk.toString();
        this.logger.log('🔍 [Chat] end中检测内容，总长度:', responseContent.length);
      }

      process.nextTick(() => {
        this.logger.log('✅ [Chat] 触发finish事件');
        mockResponse.emit('finish');
        if (cb) cb();
      });

      return mockResponse;
    };

    (mockResponse as any).setHeader = () => mockResponse;
    (mockResponse as any).getHeader = () => undefined;
    (mockResponse as any).removeHeader = () => mockResponse;
    (mockResponse as any).statusCode = 200;
    (mockResponse as any).statusMessage = 'OK';
    (mockResponse as any).headersSent = false;

    return mockResponse;
  }

  listFiles(id: string) {
    return listFiles(id);
  }

  getFile(id: string, path: string) {
    return getFile(id, path);
  }

  createDirectory(id: string, path: string) {
    return createDirectory({ id, path });
  }

  moveFile(id: string, oldPath: string, newPath: string) {
    return moveFile({ id, oldPath, newPath });
  }

  saveFile(id: string, path: string, content: string) {
    return saveFile({ id, path, content });
  }

  deleteFile(id: string, path: string) {
    return deleteFile({ id, path });
  }

  download(id: string) {
    return zipPlayground(id);
  }

  async versionStatus(id: string) {
    return versionStatus({ id });
  }

  changeVersion(id: string, version: string) {
    return changeBranch({ id, version });
  }

  commitFiles(id: string) {
    return commitFiles({ id });
  }

  async deleteMessage(id: string, messageId: string) {
    const ref = await this.prisma.chatMessage.findUnique({
      where: {
        id: messageId,
        playgroundId: id,
      },
    });
    if (!ref) {
      return null;
    }
    const toRemove = await this.prisma.chatMessage.findMany({
      where: {
        playgroundId: id,
        created: {
          gte: ref.created,
        },
      },
    });

    // Find versions to remove
    const versions = findVersionsFromMessages(toRemove);
    this.logger.log(`Deleting branches: ${versions.map(e => `version-${e}`).join(' ')}`);
    await removeVersions({ id, versions });

    // Remove messages
    this.logger.log(`Deleting messages: ${toRemove.map(e => e.id).join(' ')}`);
    await this.prisma.chatMessage.deleteMany({
      where: {
        playgroundId: id,
        id: {
          in: toRemove.map(e => e.id),
        }
      },
    });
  }

  async editMessage(id: string, messageId: string, content: string) {
    const ref = await this.prisma.chatMessage.findUnique({
      where: {
        id: messageId,
        playgroundId: id,
      },
    });
    if (!ref) {
      return null;
    }

    const updated = await this.prisma.chatMessage.update({
      where: {
        id: messageId,
        playgroundId: id,
      },
      data: {
        content,
      },
    });

    const toRemove = await this.prisma.chatMessage.findMany({
      where: {
        playgroundId: id,
        created: {
          gt: ref.created,
        },
      },
    });

    const versions = findVersionsFromMessages(toRemove);
    this.logger.log(`Deleting branches: ${versions.map(e => `version-${e}`).join(' ')}`);
    await removeVersions({ id, versions });

    // Remove messages
    this.logger.log(`Deleting messages: ${toRemove.map(e => e.id).join(' ')}`);
    await this.prisma.chatMessage.deleteMany({
      where: {
        playgroundId: id,
        id: {
          in: toRemove.map(e => e.id),
        }
      },
    });
  }

  async syncWebide(id: string) {
    // Use rsync to sync files to webide directory
    const src = playgroundRootPath(id);
    const webideDir = this.config.get('WEBIDE_DIR');
    this.logger.log(`Syncing files from ${src} to ${webideDir}`);
    await $`rsync -avzc --exclude 'node_modules/' ${src} ${webideDir}`;
  }

  async enhance(desc: string) {
    return enhance(desc);
  }

  async deploy(id: string, mode?: 'preview') {
    return deployPlayground(id, mode);
  }

  async image2code({ message, requestData }: ImageToCodeDto, res: Response) {
    const { id } = await this.create({
      desc: message.content,
      model: requestData?.model ?? 'htsc::saas-deepseek-v3',
      user: requestData?.user ?? 'admin',
      isPublic: requestData?.isPublic ?? true,
    });

    this.chat({
      id,
      message,
    }, res);
  }

  private getFetch(url: string, opts: any) {
    if (process.env.USE_AGENT === '1') {
      return nodeFetch(url, {
        ...opts,
        dispatcher: new ProxyAgent('http://168.64.5.83:8080/'),
      });
    }
    return fetch(url, opts);
  }

  async getLanhuImageVersionCode(versionId: string, lanhuToken?: string) {
    try {
      const authToken = lanhuToken || await this.getEffectiveLanhuToken();

      const response = await axios({
        method: 'GET',
        url: 'http://************:8089/api/dds/image/version/code',
        params: {
          // version_id: '9af8b098-4300-427a-83ba-f669c0855584'
          version_id: versionId
        },
        headers: {
          'authorization': authToken
        }
      });

      return response.data;
    } catch (error) {
      this.logger.error('蓝湖API请求失败:', error.message);
      throw error;
    }
  }

  async getLanhuProjectImages(projectId: string, lanhuToken?: string) {
    try {
      const authToken = lanhuToken || await this.getEffectiveLanhuToken();
      
      

      const response = await axios({
        method: 'GET',
        url: 'http://************:8089/api/project/images',
        params: {
          // project_id: '0dc9e8ae-544e-4696-9a1e-929b3a00dc09',
          project_id: projectId,
          dds_status: 1
        },
        headers: {
          'authorization': authToken,
          'Cookie': 'BIGipServerPool_LH_ToWAF=*********.19488.0000; lh-ent-session=eyJfcGVybWFuZW50Ijp0cnVlfQ.G1ofug.TQFWWTfj0BcDMtVCZZwKkxIBdhw'
        },
        timeout: 10000 // 10 seconds timeout
      });

      return response.data;
    } catch (error) {
      this.logger.error('蓝湖项目图片API请求失败:', error);
      throw error;
    }
  }

  /**
   * 生成蓝湖授权令牌
   * @param token 蓝湖登录返回的 token
   * @returns 生成的 Basic Auth 格式的授权令牌
   */
  private generateLanhuAuthorization(token: string): string {
    const A = 'unsavoryapidoc';
    const credentials = token + ":" + A;
    const encoded = Buffer.from(credentials).toString('base64');
    return "Basic " + encoded;
  }

  async lanhuLogin() {
    // 写死的蓝湖登录凭证
    const email = '<EMAIL>';
    const password = '123Abc__';
    
    try {
      const response = await axios({
        method: 'POST',
        url: 'http://************:8089/api/account/login',
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Origin': 'http://************:8089',
          'Pragma': 'no-cache',
          'Referer': 'http://************:8089/web/',
          'Request-From': 'web',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'X-Requested-With': 'XMLHttpRequest',
          'Cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%********%22%2C%22first_id%22%3A%22197345545f1e1d-0923a75dc3f378-********-2359296-197345545f1e1d%22%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22197345545f1e1d-0923a75dc3f378-********-2359296-197345545f1e1d%22%7D; BIGipServerPool_LH_ToWAF=*********.19488.0000; user_token=******************************************************************.eyJpZCI6IjQ5NTMwYjUwLTJmMTQtNGQxNy1iNTYxLTA2NGE1MmZkNGJjNSIsIl9uZXdfdG9rZW4iOiJ0cnVlIn0.j153WFm0UsyZE6_n2HHz5OQcuMTWFhsxWFMTzOagUT8; lh-ent-session=.eJwNzDsOwjAMANC7ZGZw4m97GWTHjsRAhaoyIe4O7wDv0-6PbHvDId2Runm5WQ9HK8EyjFxAk4MoJneObVBfCCk-QQoLFMFCbWMS1LV6KGgybKTKokiQmPifYLiacA5FpyLm0Z3MU-acQe3W7q86n37UcbX9Ot_1_QHCUysl.G3RRxQ.0i6OsTU_7rdXrucNhMUEE5JMi0k'
        },
        data: new URLSearchParams({
          email: email,
          password: password
        }).toString(),
        timeout: 10000 // 10秒超时
      });

      const lanhuToken = response.data.token;
      let lanhuAuthorization = null;

      // 如果获取到了 token，生成授权令牌并存储到数据库
      if (lanhuToken) {
        try {
          lanhuAuthorization = this.generateLanhuAuthorization(lanhuToken);
          
          // 存储到全局配置
          const updateResult = await this.updateLanhuAuthorization(lanhuAuthorization);
          
          if (updateResult.success) {
            this.logger.log('蓝湖授权令牌已更新到全局配置');
          } else {
            this.logger.warn('蓝湖授权令牌更新失败:', updateResult.error);
          }
        } catch (authError) {
          this.logger.error('生成或存储蓝湖授权令牌失败:', authError);
        }
      }

      return {
        success: true,
        data: response.data,
        token: lanhuToken,
        authorization: lanhuAuthorization,
        message: lanhuToken ? '蓝湖登录成功，授权令牌已更新' : '蓝湖登录成功，但未获取到 token'
      };
    } catch (error) {
      this.logger.error('蓝湖登录失败:', error.message);
      return {
        success: false,
        error: error.message,
        details: error.response?.data || null
      };
    }
  }

  async getLanhuProjectSectors(projectId: string, lanhuToken?: string) {
    try {
      const authToken = lanhuToken || await this.getEffectiveLanhuToken();

      const response = await axios({
        method: 'GET',
        url: 'http://************:8089/api/project/project_sectors',
        params: {
          project_id: projectId
        },
        headers: {
          'Authorization': authToken,
          'Cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%********%22%2C%22first_id%22%3A%22197345545f1e1d-0923a75dc3f378-********-2359296-197345545f2304a%22%2C%22props%22%3A%7B%7D%2C%22device_id%22%3A%22197345545f1e1d-0923a75dc3f378-********-2359296-197345545f2304a%22%7D; BIGipServerPool_LH_ToWAF=*********.19488.0000; lh-ent-session=.eJyN0M1OwzAMwPF36ZlKTvyRZC9T2bHDpkGHuk4cEO9OJ8Sdqw9__-yvaRlb3M_Tad8e8TItF59OE2ZJipSqhtaaTLGGYFQ0H0Cdjcg6J7aWKQ0EF-0ggQEFoVqpjUmwjJGsQHGGRqWwFCRwdDxKkLVUYc8FlYKYc1Kq6tJ7N5oOyBqfy367xnp4nrbn7CO2d11j3f-4b7fXy7r0c_TrTI0RjGHOI9FMnspsLGkGIeU8_Ek-Wj1Vg0FN_ThCehNxrSPXZuFogceexz2230f8q_n9A5bzW00.G2jIZQ._JVuFJmrM7V3CrDjxRivbvfSzxU; user_token=eyJhbGciOiJIUzI1NiIsImlhdCI6MTc1MzY5MTg3NywiZXhwIjoxNzU2MjgzODc3fQ.eyJpZCI6IjQ5NTMwYjUwLTJmMTQtNGQxNy1iNTYxLTA2NGE1MmZkNGJjNSIsIl9uZXdfdG9rZW4iOiJ0cnVlIn0.zaJAh7ObylnQQ6jbDh7kNu12BMt4aPZyBuf7-lND1fM',
        },
        timeout: 10000 // 10 seconds timeout
      });
      return response.data;
    } catch (error) {
      this.logger.error('蓝湖项目分区API请求失败:', error);
      throw error;
    }
  }

  async getCredits() {
    const credits = [];

    // 获取OpenRouter余额
    try {
      if (process.env.OPENROUTER_API_KEY) {
        const response = await this.getFetch('https://openrouter.ai/api/v1/credits', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
          },
        });
        if (response.ok) {
          const data = await response.json();
          const totalCredits = data.data?.total_credits || 0;
          const totalUsage = data.data?.total_usage || 0;
          const remainingCredits = totalCredits - totalUsage;

          credits.push({
            provider: 'openrouter',
            label: 'OpenRouter',
            credit: remainingCredits,
            totalCredits: totalCredits,
            totalUsage: totalUsage,
            currency: 'USD',
            status: 'active',
          });
        } else {
          credits.push({
            provider: 'openrouter',
            label: 'OpenRouter',
            credit: 0,
            currency: 'USD',
            status: 'error',
            error: 'API调用失败',
          });
        }
      }
    } catch (error) {
      credits.push({
        provider: 'openrouter',
        label: 'OpenRouter',
        credit: 0,
        currency: 'USD',
        status: 'error',
        error: error.message,
      });
    }

    // 获取SiliconFlow余额
    try {
      if (process.env.SILICONFLOW_API_KEY) {
        const response = await this.getFetch('https://api.siliconflow.cn/v1/user/info', {
          headers: {
            'Authorization': `Bearer ${process.env.SILICONFLOW_API_KEY}`,
            'Content-Type': 'application/json',
          },
        });
        if (response.ok) {
          const data = await response.json();
          const balance = parseFloat(data.data?.balance || '0');           // 余额(剩余)
          const totalBalance = parseFloat(data.data?.totalBalance || '0'); // 总额
          const chargeBalance = parseFloat(data.data?.chargeBalance || '0'); // 已使用

          credits.push({
            provider: 'siliconflow',
            label: 'SiliconFlow',
            credit: balance,                // 剩余余额
            totalCredits: totalBalance,     // 总额
            totalUsage: chargeBalance,      // 已使用
            currency: 'CNY',
            status: 'active',
          });
        } else {
          credits.push({
            provider: 'siliconflow',
            label: 'SiliconFlow',
            credit: 0,
            currency: 'CNY',
            status: 'error',
            error: 'API调用失败',
          });
        }
      }
    } catch (error) {
      credits.push({
        provider: 'siliconflow',
        label: 'SiliconFlow',
        credit: 0,
        currency: 'CNY',
        status: 'error',
        error: error.message,
      });
    }

    return credits;
  }

  checkIncompleteOutput(id: string, content: string, messageId?: string) {
    return checkIncompleteOutput(content, messageId);
  }

  async updateStepByStepData(id: string, stepByStepData: any) {
    return this.prisma.playground.update({
      where: { id },
      data: { stepByStepData },
    });
  }

  updateCustomPreviewUrl(id: string, customPreviewUrl: string) {
    return this.prisma.playground.update({
      where: { id },
      data: { customPreviewUrl },
    });
  }

  async uploadImage(fileBuffer: Buffer, fileName: string) {
    return uploadImageToMinIO(fileBuffer, fileName);
  }

  /**
   * 创建通用聊天任务（支持附件上传）
   * 使用 custom task handler 通过后台任务系统处理聊天请求
   * 
   * @param createChatTaskDto 聊天任务创建参数
   * @param files 上传的附件文件列表
   * @returns 创建的聊天任务信息
   */
  async createCommonChat(
    createChatTaskDto: CreateCommonChatDto,
    files?: Express.Multer.File[]
  ) {
    const { messageContent, messageRole, createEmptySession, sessionName, tags, smartTheme, ...createSessionData } = createChatTaskDto;

    // 检查是否为空会话创建模式
    const isEmptySession = createEmptySession === 'true';
    
    // 检查是否启用智能主题生成（默认为true）
    const enableSmartTheme = smartTheme !== 'false'; // 默认启用，只有明确设置为'false'才禁用
    
    this.logger.log(`🔍 收到请求参数: createEmptySession="${createEmptySession}", sessionName="${sessionName}", smartTheme="${smartTheme}", enableSmartTheme=${enableSmartTheme}, isEmptySession=${isEmptySession}`);

    // 1. 处理消息内容
    let formattedMessageContent = '';
    if (messageContent) {
      // 修复换行符问题：将文字 \n 替换为真实的换行符
      formattedMessageContent = messageContent.replace(/\\n/g, '\n');
    } else if (!isEmptySession) {
      throw new Error('非空会话模式下必须提供消息内容');
    }

    try {
      // 2. 处理表单数据，设置默认值
      const convertedData = {
        ...createSessionData,
        desc: createSessionData.desc || (isEmptySession ? sessionName : formattedMessageContent), // 空会话使用sessionName，否则使用messageContent
        model: createSessionData.model || 'openrouter::google/gemini-2.5-pro-preview', // 如果model为空，则使用默认模型
        // model: createSessionData.model || 'htsc::saas-doubao-15-pro-32k', // 本次测试使用
        isPublic: createSessionData.isPublic === 'true',
        enableAutoIteration: createSessionData.enableAutoIteration === 'true',
        enableStepByStep: createSessionData.enableStepByStep === 'true',
        enablePreview: createSessionData.enablePreview === 'true',
        enableCustomPreview: createSessionData.enableCustomPreview === 'true', // 是否支持自定义预览
      };

      // 3. 处理标签数据
      const taskTags: string[] = [];
      if (tags && tags.trim().length > 0) {
        // 解析逗号分隔的标签字符串
        taskTags.push(...tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0));
      }

      this.logger.log(`Creating ${isEmptySession ? '空会话' : '通用会话'}任务 for user: ${convertedData.user}, tags: ${taskTags.join(', ')}`);

      // 3. 处理附件文件
      const attachmentFiles: any[] = [];
      if (files && files.length > 0) {
        this.logger.log(`Processing ${files.length} attachment files`);

        for (const file of files) {
          try {
            // 对于不同类型的文件，使用不同的处理方式
            if (file.mimetype.startsWith('image/')) {
              // 图片文件：转换为 base64
              const base64Content = file.buffer.toString('base64');
              attachmentFiles.push({
                name: file.originalname,
                contentType: file.mimetype,
                imageContent: base64Content,
                imageType: file.mimetype,
                imageName: file.originalname,
              });
            } else {
              // 其他文件：作为文本内容处理
              const textContent = file.buffer.toString('utf-8');
              attachmentFiles.push({
                name: file.originalname,
                contentType: file.mimetype,
                content: textContent,
              });
            }

            this.logger.log(`Successfully processed attachment: ${file.originalname}`);
          } catch (processError) {
            this.logger.error(`Failed to process attachment ${file.originalname}:`, processError);
          }
        }
      }

      // 4. 处理任务主题和名称
      let taskInfo: any = null;
      let intelligentTaskTopic: string;
      let intelligentTaskName: string;
      let finalSessionName = sessionName; // 保存最终的会话名称

      if (isEmptySession) {
        // 空会话模式：根据 smartTheme 参数决定是否智能生成主题
        if (enableSmartTheme && sessionName && sessionName.trim()) {
          // 启用智能主题生成：使用 AI 从会话名称中提取更好的主题
          try {
            this.logger.log(`Smart theme enabled - extracting theme from session name: ${sessionName}`);
            taskInfo = await extactPlaygroundInfo(sessionName);
            intelligentTaskTopic = (taskInfo?.object?.topic || sessionName).trim();
            intelligentTaskName = taskInfo?.object?.name ?? intelligentTaskTopic;
            finalSessionName = intelligentTaskTopic; // 使用智能生成的主题作为最终会话名称
            this.logger.log(`Smart theme generated: ${intelligentTaskTopic}`);
          } catch (error) {
            // 如果智能主题生成失败，回退到原始会话名称
            this.logger.warn(`Smart theme generation failed, fallback to original session name:`, error);
            finalSessionName = sessionName;
            intelligentTaskTopic = sessionName;
            intelligentTaskName = intelligentTaskTopic;
          }
        } else {
          // 禁用智能主题生成或没有会话名称：使用提供的会话名称，如果没有则生成默认名称
          finalSessionName = sessionName || `空会话-${Date.now()}`;
          intelligentTaskTopic = finalSessionName;
          intelligentTaskName = intelligentTaskTopic;
        }
        this.logger.log(`Empty session mode - final session name: ${intelligentTaskTopic} (smartTheme: ${enableSmartTheme})`);
      } else {
        // 正常模式：使用 AI 从用户消息中提取任务信息（主题等）
        this.logger.log(`Extracting task info from user message: ${formattedMessageContent.substring(0, 100)}...`);
        taskInfo = await extactPlaygroundInfo(formattedMessageContent);
        this.logger.log(`Extracted task info:`, taskInfo.object);

        // 生成智能的任务名称和描述
        // 使用提取的 topic 作为任务的主要名称，避免了 "设计稿还原" -> "design-to-html" 的转换
        intelligentTaskTopic = (taskInfo?.object?.topic || formattedMessageContent.substring(0, 50)).trim();
        intelligentTaskName = intelligentTaskTopic || `Chat-${Date.now()}`; // 使用主题，如果主题为空则回退
      }

      // 5. 创建后台任务项
      const taskItem: BackgroundTaskItem = {
        id: generateId(),
        name: intelligentTaskName, // 使用从用户消息中提取的主题或会话名称
        metadata: {
          // 将附件信息添加到 metadata 中，供 CustomTaskHandler 使用
          files: attachmentFiles,
          // 其他必要的元数据
          messageContent: formattedMessageContent,
          messageRole: messageRole || 'user',
          originalDesc: convertedData.desc,
          // 添加提取的任务信息（仅在非空会话模式下）
          extractedInfo: taskInfo?.object || null,
          // 空会话相关元数据
          isEmptySession: isEmptySession,
          sessionName: finalSessionName,
        },
      };

      // 6. 创建后台任务配置
      const taskConfig: BackgroundTaskConfig = {
        taskType: 'custom',
        taskName: intelligentTaskName, // 使用智能提取的主题或会话名称
        user: convertedData.user,
        metadata: {
          projectId: convertedData.projectId,
          isPublic: convertedData.isPublic,
          type: convertedData.type,
          customPrompt: formattedMessageContent,
          extractedTaskInfo: taskInfo?.object || null, // 传递提取的任务信息（空会话时为null）
          createEmptySession: isEmptySession,
          sessionName: finalSessionName,
          intelligentTaskTopic, // 传递智能生成的主题
          smartTheme: enableSmartTheme, // 传递智能主题生成标志
          tags: taskTags, // 传递标签信息
          enableCustomPreview: convertedData.enableCustomPreview, // 是否支持自定义预览
        },
      };
      
      this.logger.log(`🔍 创建任务配置: createEmptySession=${isEmptySession}, sessionName="${finalSessionName}", taskConfig.metadata.sessionName="${taskConfig.metadata.sessionName}"`);

      this.logger.log(`Starting background task: "${intelligentTaskTopic}" with item: "${intelligentTaskName}" (${isEmptySession ? '空会话模式' : '正常模式'})`);
      // debugger
      // 7. 使用 DesignProjectBackgroundTaskService 创建和执行任务
      const result = await this.designProjectTaskService.createAndExecuteGenericTask({
        ...taskConfig, // 传递任务配置
        items: [taskItem],
        model: convertedData.model,
        enableAutoIteration: convertedData.enableAutoIteration,
        enableStepByStep: convertedData.enableStepByStep,
   
        waitForCompletion: false, // 不等待完成，立即返回
      });

      this.logger.log(`Background ${isEmptySession ? '空会话' : '聊天'}任务创建成功 with taskId: ${result.taskId}`);
      // 8. 返回Playground ID作为聊天ID
      return {
        chatId: result.playgroundId || result.taskId,
        message: (result as any).message || `${isEmptySession ? '空会话' : '任务'}创建成功`,
        isEmptySession: isEmptySession,
        sessionName: finalSessionName,
        tags: taskTags
      };
    } catch (error) {
      this.logger.error('Failed to create chat task with custom handler:', error);
      throw error;
    }
  }

  /**
   * 代理方法：向任务chat发送消息
   * 为了保持向后兼容性，在ai-coding服务中提供一个代理方法
   */
  async sendMessageToTaskChat(taskId: string, messageContent: string, options?: any) {
    return this.backgroundTaskService.sendMessageToTaskChat(taskId, messageContent, options);
  }

  /**
   * 获取系统全局配置
   * @returns 系统全局配置信息
   */
  async getSystemConfig() {
    try {
      // 尝试获取系统配置
      let systemConfig = await this.prisma.systemConfig.findUnique({
        where: { id: 'system' }
      });

      // 如果配置不存在，创建默认配置
      if (!systemConfig) {
        systemConfig = await this.prisma.systemConfig.create({
          data: {
            id: 'system',
            lanhuAuthorization: null, // 初始为空
          }
        });
        this.logger.log('创建默认系统配置');
      }

      return {
        success: true,
        data: systemConfig
      };
    } catch (error) {
      this.logger.error('获取系统配置失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 更新蓝湖授权令牌
   * @param lanhuAuthorization 新的蓝湖授权令牌
   * @returns 更新结果
   */
  async updateLanhuAuthorization(lanhuAuthorization: string) {
    try {
      // 使用 upsert 确保配置记录存在
      const systemConfig = await this.prisma.systemConfig.upsert({
        where: { id: 'system' },
        update: { 
          lanhuAuthorization: lanhuAuthorization 
        },
        create: { 
          id: 'system',
          lanhuAuthorization: lanhuAuthorization 
        }
      });

      this.logger.log('蓝湖授权令牌更新成功');
      
      return {
        success: true,
        message: '蓝湖授权令牌更新成功',
        data: {
          id: systemConfig.id,
          lanhuAuthorization: systemConfig.lanhuAuthorization ? '已设置' : '未设置',
          updated: systemConfig.updated
        }
      };
    } catch (error) {
      this.logger.error('更新蓝湖授权令牌失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取当前生效的蓝湖授权令牌
   * 从全局配置中获取蓝湖授权令牌
   * @returns 当前生效的蓝湖授权令牌
   * @throws 如果没有配置授权令牌则抛出错误
   */
  async getEffectiveLanhuToken(): Promise<string> {
    try {
      const systemConfig = await this.prisma.systemConfig.findUnique({
        where: { id: 'system' }
      });

      if (systemConfig?.lanhuAuthorization) {
        return systemConfig.lanhuAuthorization;
      } else {
        throw new Error('未找到蓝湖授权令牌，请先进行蓝湖登录');
      }
    } catch (error) {
      this.logger.error('获取蓝湖授权令牌失败:', error.message);
      throw error;
    }
  }

  /**
   * 批量绑定原型到页面
   * 先获取蓝湖代码数据，然后创建页面原型
   * @param batchBindDto 批量绑定参数
   * @returns 批量绑定结果
   */
  async batchBindPrototypes(batchBindDto: BatchBindPrototypesDto): Promise<BatchBindPrototypesResponse> {
    const { projectId, pageId, selectedImages } = batchBindDto;
    
    this.logger.log(`开始批量绑定原型：项目 ${projectId}，页面 ${pageId}，图片数量 ${selectedImages.length}`);

    const createdPrototypes = [];
    const errors = [];
    let successCount = 0;
    let failedCount = 0;

    // 并发处理
    const processingPromises = selectedImages.map(async (item, index) => {
      const prototypeName = item.name;
      
      try {
        this.logger.log(`开始处理原型 ${index + 1}/${selectedImages.length}: ${prototypeName}`);

        // 1. 获取蓝湖代码数据
        const lanhuData = await this.getLanhuCodeData(item, prototypeName);

        // 2. 创建页面原型
        const newPrototype = await this.designProjectService.createPagePrototype(pageId, {
          prototypeName,
          englishName: lanhuData.englishName,
          htmlContent: lanhuData.htmlContent,
          cssContent: lanhuData.cssContent,
          htmlFileName: lanhuData.htmlFileName,
          cssFileName: lanhuData.cssFileName,
          imgFileLink: lanhuData.imgFileLink,
          imgFileName: item.name,
          imgWidth: item.width?.toString(),
          imgHeight: item.height?.toString(),
          lanhuVersionId: lanhuData.lanhuVersionId,
        });

        this.logger.log(`成功创建原型: ${prototypeName}`);
        return { success: true, prototype: newPrototype, prototypeName };

      } catch (error) {
        const errorMessage = `创建原型 ${prototypeName} 失败: ${error.message}`;
        this.logger.error(errorMessage, error);
        return { success: false, error: errorMessage, prototypeName };
      }
    });

    // 等待所有任务完成
    const results = await Promise.allSettled(processingPromises);

    // 统计结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const taskResult = result.value;
        if (taskResult.success) {
          createdPrototypes.push(taskResult.prototype);
          successCount++;
        } else {
          errors.push(taskResult.error);
          failedCount++;
        }
      } else {
        const prototypeName = selectedImages[index]?.name || `原型${index + 1}`;
        const errorMessage = `创建原型 ${prototypeName} 失败: ${result.reason}`;
        errors.push(errorMessage);
        failedCount++;
        this.logger.error(errorMessage);
      }
    });

    const response: BatchBindPrototypesResponse = {
      success: successCount > 0,
      message: successCount > 0 
        ? `成功创建 ${successCount} 个原型${failedCount > 0 ? `，${failedCount} 个失败` : ''}`
        : '所有原型创建都失败了',
      results: {
        successCount,
        failedCount,
        createdPrototypes,
        errors: errors.length > 0 ? errors : undefined
      }
    };

    this.logger.log(`批量绑定完成：成功 ${successCount}，失败 ${failedCount}`);
    return response;
  }

  /**
   * 域名替换工具：将蓝湖图片域名替换为 IP
   */
  private replaceLanhuDomain(url?: string): string | undefined {
    if (!url) return '';
    return url.replace('http://lanhu.htsc.com.cn:8089', 'http://************:8089');
  }

  /**
   * 获取蓝湖代码数据的内部方法
   * @param item 图片项
   * @param prototypeName 原型名称
   * @param lanhuToken 蓝湖令牌
   * @returns 解析后的蓝湖代码数据
   */
  private async getLanhuCodeData(item: any, prototypeName: string) {
    let htmlContent, cssContent, htmlFileName, cssFileName;

    if (item.latest_version) {
      try {
        const response = await this.getLanhuImageVersionCode(item.latest_version);

        // 解析蓝湖返回的数据
        const h5File = response.codes?.find((codeItem: any) => codeItem.framework_type === 'h5');
        const htmlFileData = h5File?.files?.find((file: any) => file.file_name === 'index.html');
        const cssFileData = h5File?.files?.find((file: any) => file.file_name === 'index.css');

        // 设置解析后的内容
        htmlContent = htmlFileData?.file_content;
        cssContent = cssFileData?.file_content;
        
        // 将所有 https:// 转换为 http://
        if (htmlContent) {
          htmlContent = htmlContent.replace(/https:\/\//g, 'http://');
        }
        if (cssContent) {
          cssContent = cssContent.replace(/https:\/\//g, 'http://');
        }
        htmlFileName = htmlFileData?.file_name;
        cssFileName = cssFileData?.file_name;
      } catch (lanhuError) {
        this.logger.error(`获取原型 ${prototypeName} 蓝湖版本代码失败:`, lanhuError);
        // 如果获取蓝湖代码失败，继续使用原始图片链接
      }
    }

    return {
      englishName: item.pinyinname,
      htmlContent,
      cssContent,
      htmlFileName,
      cssFileName,
      imgFileLink: this.replaceLanhuDomain(item?.url),
      lanhuVersionId: item.latest_version,
    };
  }
}


function findVersionsFromMessages(messages: ChatMessage[]) {
  return uniq(flatten(messages.map(e => e.annotations)).filter(e => e.type === 'all-files-saved').map(e => e.versionNumber) as number[]);
}

