import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { Injectable, Logger } from '@nestjs/common';
import { treeKillAsync } from '../ai-coding/playground/kill';
import { PrismaService } from '../prisma/prisma.service';

export interface ProcessInfo {
  id: string;
  playgroundId: string;
  taskId: string;
  command: string;
  args: string[];
  cwd: string;
  pid?: number;
  status: 'running' | 'completed' | 'failed' | 'killed';
  startTime: Date;
  endTime?: Date;
  exitCode?: number;
  error?: string;
  output?: string;
  user: string;
  metadata?: Record<string, any>;
}

export interface PostChatCommand {
  name: string;
  command: string;
  args?: string[];
  cwd?: string;
  timeout?: number; // 超时时间（毫秒）
  runInBackground?: boolean; // 是否在后台运行
  killOnUserDisconnect?: boolean; // 用户断开连接时是否杀死进程
  metadata?: Record<string, any>;
}

@Injectable()
export class ProcessManagerService extends EventEmitter {
  private readonly logger = new Logger(ProcessManagerService.name);
  private processes = new Map<string, ProcessInfo>();
  private childProcesses = new Map<string, ChildProcess>();
  
  // 用户活动跟踪
  private userActivity = new Map<string, Date>();
  private readonly USER_TIMEOUT = 30 * 60 * 1000; // 30分钟无活动超时

  constructor(private readonly prisma: PrismaService) {
    super();
    
    // 启动清理定时器
    this.startCleanupTimer();
    
    // 监听进程事件
    this.setupEventListeners();
  }

  /**
   * 执行chat后的自定义命令
   * @param playgroundId Playground ID
   * @param taskId 任务ID
   * @param commands 要执行的命令列表
   * @param user 用户ID
   * @returns 进程ID列表
   */
  async executePostChatCommands(
    playgroundId: string,
    taskId: string,
    commands: PostChatCommand[],
    user: string
  ): Promise<string[]> {
    this.logger.log(`🚀 开始执行 ${commands.length} 个post-chat命令 (playground: ${playgroundId})`);
    
    const processIds: string[] = [];
    
    for (const cmd of commands) {
      try {
        const processId = await this.executeCommand(
          playgroundId,
          taskId,
          cmd,
          user
        );
        processIds.push(processId);
        
        this.logger.log(`✅ 命令 "${cmd.name}" 启动成功 (进程ID: ${processId})`);
      } catch (error) {
        this.logger.error(`❌ 命令 "${cmd.name}" 启动失败:`, error);
        // 继续执行其他命令
      }
    }
    
    // 更新用户活动时间
    this.updateUserActivity(user);
    
    this.logger.log(`🎉 Post-chat命令执行完成，启动了 ${processIds.length}/${commands.length} 个进程`);
    return processIds;
  }

  /**
   * 执行单个命令
   * @param playgroundId Playground ID
   * @param taskId 任务ID
   * @param cmd 命令配置
   * @param user 用户ID
   * @returns 进程ID
   */
  private async executeCommand(
    playgroundId: string,
    taskId: string,
    cmd: PostChatCommand,
    user: string
  ): Promise<string> {
    const processId = `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 构建工作目录
    const cwd = cmd.cwd || this.getPlaygroundPath(playgroundId);
    
    // 创建进程信息
    const processInfo: ProcessInfo = {
      id: processId,
      playgroundId,
      taskId,
      command: cmd.command,
      args: cmd.args || [],
      cwd,
      status: 'running',
      startTime: new Date(),
      user,
      metadata: {
        ...cmd.metadata,
        name: cmd.name,
        timeout: cmd.timeout,
        runInBackground: cmd.runInBackground,
        killOnUserDisconnect: cmd.killOnUserDisconnect,
      }
    };
    
    this.logger.log(`🔄 启动进程: ${cmd.command} ${cmd.args?.join(' ') || ''} (工作目录: ${cwd})`);
    
    try {
      // 启动子进程
      const childProcess = spawn(cmd.command, cmd.args || [], {
        cwd,
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PLAYGROUND_ID: playgroundId,
          TASK_ID: taskId,
          USER_ID: user,
        }
      });
      
      processInfo.pid = childProcess.pid;
      
      // 存储进程信息
      this.processes.set(processId, processInfo);
      this.childProcesses.set(processId, childProcess);
      
      // 设置进程事件监听
      this.setupProcessListeners(processId, childProcess, cmd);
      
      // 如果设置了超时，启动超时定时器
      if (cmd.timeout) {
        this.setupTimeout(processId, cmd.timeout);
      }
      
      // 保存到数据库
      await this.saveProcessToDatabase(processInfo);
      
      this.logger.log(`✅ 进程启动成功 (ID: ${processId}, PID: ${childProcess.pid})`);
      
      // 发送进程启动事件
      this.emit('processStarted', processInfo);
      
      return processId;
      
    } catch (error) {
      // 更新进程状态为失败
      processInfo.status = 'failed';
      processInfo.error = error.message;
      processInfo.endTime = new Date();
      
      this.processes.set(processId, processInfo);
      
      this.logger.error(`❌ 进程启动失败 (ID: ${processId}):`, error);
      
      // 发送进程失败事件
      this.emit('processFailed', processInfo);
      
      throw error;
    }
  }

  /**
   * 设置进程监听器
   */
  private setupProcessListeners(
    processId: string,
    childProcess: ChildProcess,
    cmd: PostChatCommand
  ): void {
    let output = '';
    let errorOutput = '';
    
    // 监听标准输出
    childProcess.stdout?.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      this.logger.log(`📤 [${processId}] stdout: ${text.trim()}`);
      
      // 发送输出事件
      this.emit('processOutput', {
        processId,
        type: 'stdout',
        data: text
      });
    });
    
    // 监听标准错误
    childProcess.stderr?.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      
      this.logger.warn(`📤 [${processId}] stderr: ${text.trim()}`);
      
      // 发送错误输出事件
      this.emit('processOutput', {
        processId,
        type: 'stderr',
        data: text
      });
    });
    
    // 监听进程退出
    childProcess.on('exit', async (code, signal) => {
      const processInfo = this.processes.get(processId);
      if (processInfo) {
        processInfo.status = code === 0 ? 'completed' : 'failed';
        processInfo.exitCode = code;
        processInfo.endTime = new Date();
        processInfo.output = output;
        if (errorOutput) {
          processInfo.error = errorOutput;
        }
        
        this.processes.set(processId, processInfo);
        
        // 更新数据库
        await this.updateProcessInDatabase(processInfo);
        
        this.logger.log(`🏁 进程结束 (ID: ${processId}, 退出码: ${code}, 信号: ${signal})`);
        
        // 发送进程结束事件
        this.emit('processEnded', processInfo);
      }
      
      // 清理子进程引用
      this.childProcesses.delete(processId);
    });
    
    // 监听进程错误
    childProcess.on('error', async (error) => {
      const processInfo = this.processes.get(processId);
      if (processInfo) {
        processInfo.status = 'failed';
        processInfo.error = error.message;
        processInfo.endTime = new Date();
        
        this.processes.set(processId, processInfo);
        
        // 更新数据库
        await this.updateProcessInDatabase(processInfo);
        
        this.logger.error(`❌ 进程错误 (ID: ${processId}):`, error);
        
        // 发送进程错误事件
        this.emit('processError', processInfo);
      }
      
      // 清理子进程引用
      this.childProcesses.delete(processId);
    });
  }

  /**
   * 设置超时定时器
   */
  private setupTimeout(processId: string, timeout: number): void {
    setTimeout(async () => {
      const processInfo = this.processes.get(processId);
      if (processInfo && processInfo.status === 'running') {
        this.logger.warn(`⏰ 进程超时，正在杀死进程 (ID: ${processId})`);
        await this.killProcess(processId, 'timeout');
      }
    }, timeout);
  }

  /**
   * 杀死进程
   * @param processId 进程ID
   * @param reason 杀死原因
   */
  async killProcess(processId: string, reason: string = 'manual'): Promise<void> {
    const processInfo = this.processes.get(processId);
    const childProcess = this.childProcesses.get(processId);
    
    if (!processInfo) {
      throw new Error(`进程 ${processId} 不存在`);
    }
    
    if (processInfo.status !== 'running') {
      this.logger.warn(`⚠️ 进程 ${processId} 已经结束，无需杀死`);
      return;
    }
    
    this.logger.log(`🔪 正在杀死进程 (ID: ${processId}, 原因: ${reason})`);
    
    try {
      if (childProcess) {
        // 使用tree-kill杀死进程树
        await treeKillAsync(childProcess, 'SIGTERM');
        
        // 等待一段时间，如果进程还没结束，使用SIGKILL
        setTimeout(async () => {
          if (this.childProcesses.has(processId)) {
            await treeKillAsync(childProcess, 'SIGKILL');
          }
        }, 5000);
      }
      
      // 更新进程状态
      processInfo.status = 'killed';
      processInfo.endTime = new Date();
      processInfo.error = `进程被杀死: ${reason}`;
      
      this.processes.set(processId, processInfo);
      
      // 更新数据库
      await this.updateProcessInDatabase(processInfo);
      
      this.logger.log(`✅ 进程杀死成功 (ID: ${processId})`);
      
      // 发送进程被杀死事件
      this.emit('processKilled', processInfo);
      
    } catch (error) {
      this.logger.error(`❌ 杀死进程失败 (ID: ${processId}):`, error);
      throw error;
    }
  }

  /**
   * 杀死用户的所有进程
   * @param userId 用户ID
   */
  async killUserProcesses(userId: string): Promise<void> {
    const userProcesses = Array.from(this.processes.values())
      .filter(p => p.user === userId && p.status === 'running');
    
    this.logger.log(`🔪 正在杀死用户 ${userId} 的 ${userProcesses.length} 个进程`);
    
    const killPromises = userProcesses.map(p => 
      this.killProcess(p.id, 'user_cleanup')
    );
    
    await Promise.all(killPromises);
    
    this.logger.log(`✅ 用户 ${userId} 的所有进程已被杀死`);
  }

  /**
   * 杀死playground的所有进程
   * @param playgroundId Playground ID
   */
  async killPlaygroundProcesses(playgroundId: string): Promise<void> {
    const playgroundProcesses = Array.from(this.processes.values())
      .filter(p => p.playgroundId === playgroundId && p.status === 'running');
    
    this.logger.log(`🔪 正在杀死playground ${playgroundId} 的 ${playgroundProcesses.length} 个进程`);
    
    const killPromises = playgroundProcesses.map(p => 
      this.killProcess(p.id, 'playground_cleanup')
    );
    
    await Promise.all(killPromises);
    
    this.logger.log(`✅ Playground ${playgroundId} 的所有进程已被杀死`);
  }

  /**
   * 获取进程信息
   * @param processId 进程ID
   * @returns 进程信息
   */
  getProcessInfo(processId: string): ProcessInfo | undefined {
    return this.processes.get(processId);
  }

  /**
   * 获取用户的所有进程
   * @param userId 用户ID
   * @returns 进程信息列表
   */
  getUserProcesses(userId: string): ProcessInfo[] {
    return Array.from(this.processes.values())
      .filter(p => p.user === userId);
  }

  /**
   * 获取playground的所有进程
   * @param playgroundId Playground ID
   * @returns 进程信息列表
   */
  getPlaygroundProcesses(playgroundId: string): ProcessInfo[] {
    return Array.from(this.processes.values())
      .filter(p => p.playgroundId === playgroundId);
  }

  /**
   * 获取运行中的进程
   * @returns 运行中的进程信息列表
   */
  getRunningProcesses(): ProcessInfo[] {
    return Array.from(this.processes.values())
      .filter(p => p.status === 'running');
  }

  /**
   * 更新用户活动时间
   * @param userId 用户ID
   */
  updateUserActivity(userId: string): void {
    this.userActivity.set(userId, new Date());
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 每5分钟检查一次
    setInterval(() => {
      this.cleanupInactiveUserProcesses();
      this.cleanupOldProcesses();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理不活跃用户的进程
   */
  private async cleanupInactiveUserProcesses(): Promise<void> {
    const now = new Date();
    const inactiveUsers: string[] = [];
    
    for (const [userId, lastActivity] of this.userActivity.entries()) {
      if (now.getTime() - lastActivity.getTime() > this.USER_TIMEOUT) {
        inactiveUsers.push(userId);
      }
    }
    
    if (inactiveUsers.length > 0) {
      this.logger.log(`🧹 发现 ${inactiveUsers.length} 个不活跃用户，正在清理进程`);
      
      for (const userId of inactiveUsers) {
        try {
          await this.killUserProcesses(userId);
          this.userActivity.delete(userId);
        } catch (error) {
          this.logger.error(`❌ 清理用户 ${userId} 进程失败:`, error);
        }
      }
    }
  }

  /**
   * 清理旧进程记录
   */
  private cleanupOldProcesses(): void {
    const now = new Date();
    const oldProcesses: string[] = [];
    
    for (const [processId, processInfo] of this.processes.entries()) {
      // 清理24小时前结束的进程
      if (processInfo.status !== 'running' && processInfo.endTime) {
        const ageHours = (now.getTime() - processInfo.endTime.getTime()) / (1000 * 60 * 60);
        if (ageHours > 24) {
          oldProcesses.push(processId);
        }
      }
    }
    
    if (oldProcesses.length > 0) {
      this.logger.log(`🧹 清理 ${oldProcesses.length} 个旧进程记录`);
      
      for (const processId of oldProcesses) {
        this.processes.delete(processId);
        this.childProcesses.delete(processId);
      }
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听进程启动事件
    this.on('processStarted', (processInfo: ProcessInfo) => {
      this.logger.log(`🎉 进程启动事件: ${processInfo.id}`);
    });
    
    // 监听进程结束事件
    this.on('processEnded', (processInfo: ProcessInfo) => {
      this.logger.log(`🏁 进程结束事件: ${processInfo.id} (状态: ${processInfo.status})`);
    });
    
    // 监听进程被杀死事件
    this.on('processKilled', (processInfo: ProcessInfo) => {
      this.logger.log(`🔪 进程被杀死事件: ${processInfo.id}`);
    });
  }

  /**
   * 获取playground路径
   * @param playgroundId Playground ID
   * @returns 路径
   */
  private getPlaygroundPath(playgroundId: string): string {
    // 这里需要根据实际的playground路径逻辑来实现
    return `/tmp/playground/${playgroundId}`;
  }

  /**
   * 保存进程到数据库
   * @param processInfo 进程信息
   */
  private async saveProcessToDatabase(processInfo: ProcessInfo): Promise<void> {
    try {
      // 这里可以扩展数据库模型来存储进程信息
      // 暂时使用日志记录
      this.logger.log(`💾 保存进程到数据库: ${processInfo.id}`);
    } catch (error) {
      this.logger.error(`❌ 保存进程到数据库失败:`, error);
    }
  }

  /**
   * 更新数据库中的进程信息
   * @param processInfo 进程信息
   */
  private async updateProcessInDatabase(processInfo: ProcessInfo): Promise<void> {
    try {
      // 这里可以扩展数据库模型来更新进程信息
      // 暂时使用日志记录
      this.logger.log(`💾 更新数据库中的进程: ${processInfo.id} (状态: ${processInfo.status})`);
    } catch (error) {
      this.logger.error(`❌ 更新数据库中的进程失败:`, error);
    }
  }
} 