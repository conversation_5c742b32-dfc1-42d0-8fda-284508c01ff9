import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TaskWatcherService } from './task-watcher.service';
import { TaskWorkerService } from './task-worker.service';

/**
 * 后台任务服务
 * 负责后台任务的管理和操作，包括消息发送、状态查询等
 */
@Injectable()
export class BackgroundTaskService {
  private readonly logger = new Logger(BackgroundTaskService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly taskWatcher: TaskWatcherService,
    private readonly taskWorker: TaskWorkerService,
  ) { }

  /**
   * 通用方法：根据后台任务ID向对应的chat发送消息
   * @param taskId 后台任务ID
   * @param messageContent 要发送的消息内容
   * @param options 可选配置
   * @returns 发送结果
   */
  async sendMessageToTaskChat(
    taskId: string,
    messageContent: string,
    options: {
      waitForCompletion?: boolean; // 是否等待AI回复，默认true
      maxWaitTime?: number; // 最大等待时间（毫秒），默认300000（5分钟）
      attachments?: Array<{ // 可选的附件
        name: string;
        content: string; // base64编码的内容或文本内容
        contentType: string;
      }>;
      messageRole?: string; // 消息角色，默认'user'
      userId?: string; // 用户ID，用于权限验证（可选）
    } = {}
  ) {
    const {
      waitForCompletion = true,
      maxWaitTime = 300000,
      attachments = [],
      messageRole = 'user',
      userId
    } = options;

    try {
      this.logger.log(`📨 开始向任务chat发送消息 (taskId: ${taskId})`);
      this.logger.log(`📝 消息内容: ${messageContent.substring(0, 100)}...`);
      this.logger.log(`⚙️ 配置: waitForCompletion=${waitForCompletion}, maxWaitTime=${maxWaitTime}ms, attachments=${attachments.length}个`);

      // 1. 获取任务状态，查找对应的playgroundId
      const taskStatus = await this.taskWatcher.getTaskStatus(taskId);

      if (!taskStatus) {
        throw new Error(`任务不存在: ${taskId}`);
      }

      // 2. 验证用户权限（如果提供了userId）
      if (userId && taskStatus.metadata?.user && taskStatus.metadata.user !== userId) {
        throw new Error(`权限不足：用户 ${userId} 无权操作任务 ${taskId}`);
      }

      // 3. 获取playgroundId
      const playgroundId = taskStatus.playgroundId;
      if (!playgroundId) {
        throw new Error(`任务 ${taskId} 没有关联的playground，可能是任务尚未开始或已失败`);
      }

      this.logger.log(`🎯 找到对应的playground: ${playgroundId}`);

      // 4. 检查playground是否存在
      const playground = await this.prisma.playground.findUnique({
        where: { id: playgroundId }
      });

      if (!playground) {
        throw new Error(`Playground ${playgroundId} 不存在`);
      }

      this.logger.log(`✅ Playground验证通过: ${playground.name} (model: ${playground.model})`);

      // 5. 准备附件数据（使用与TaskWorkerService相同的处理逻辑）
      let messageAttachments: any[] = [];
      if (attachments.length > 0) {
        this.logger.log(`📎 处理 ${attachments.length} 个附件`);
        
        // 引入dataUrl函数
        const { dataUrl } = await import('../ai-coding/playground/utils/file');
        
        messageAttachments = attachments.map(attachment => {
          this.logger.log(`🔍 处理附件: ${attachment.name} (${attachment.contentType})`);
          
          if (attachment.contentType.startsWith('image/')) {
            // 图片附件处理
            const url = attachment.content.startsWith('data:')
              ? attachment.content
              : `data:${attachment.contentType};base64,${attachment.content}`;

            const imageAttachment = {
              name: attachment.name,
              contentType: attachment.contentType,
              url: url
            };

            this.logger.log(`📷 处理图片附件: ${attachment.name} -> ${url.substring(0, 50)}...`);
            return imageAttachment;
          } else {
            // 文本文件附件处理 - 使用与TaskWorkerService相同的逻辑
            let originalContent = attachment.content;

            // 检查是否是base64编码
            const isBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(originalContent) && originalContent.length % 4 === 0;
            
            if (isBase64) {
              try {
                originalContent = Buffer.from(originalContent, 'base64').toString('utf-8');
                this.logger.log(`🔄 Base64解码成功: ${attachment.name}, 解码后长度: ${originalContent.length}`);
              } catch (error) {
                this.logger.warn(`⚠️ Base64解码失败，使用原始内容: ${attachment.name}`);
                originalContent = attachment.content;
              }
            }

            // 使用dataUrl函数创建正确格式的data URL（模型期望的格式）
            const textAttachment = {
              name: attachment.name,
              contentType: attachment.contentType,
              url: dataUrl(originalContent, attachment.contentType)
            };

            this.logger.log(`📄 处理文本附件: ${attachment.name}, 内容长度: ${originalContent.length} -> ${textAttachment.url.substring(0, 50)}...`);
            return textAttachment;
          }
        });

        this.logger.log(`✅ 附件处理完成，生成 ${messageAttachments.length} 个AI格式附件`);
        this.logger.log(`📋 附件详情: ${JSON.stringify(messageAttachments.map(att => ({ 
          name: att.name, 
          contentType: att.contentType, 
          urlPrefix: att.url.substring(0, 50) + '...' 
        })), null, 2)}`);
      }

      // 6. 使用TaskWorkerService创建用户消息（复用现有逻辑）
      this.logger.log(`📝 使用TaskWorkerService创建用户消息...`);
      const userMessageId = await this.taskWorker.createUserMessage(
        playgroundId,
        messageContent,
        messageAttachments,
        taskId
      );
      this.logger.log(`📝 已创建用户消息 (messageId: ${userMessageId}, taskId: ${taskId})`);

      // 7. 使用TaskWorkerService的executeAIDialog方法处理AI对话
      this.logger.log(`🤖 使用TaskWorkerService.executeAIDialog处理AI对话...`);
      
      if (waitForCompletion) {
        // 同步等待AI回复
        this.logger.log(`⏳ 同步等待AI对话完成...`);
        try {
          await this.taskWorker.executeAIDialog(playgroundId, {
            enableAutoIteration: playground.enableAutoIteration || false,
            enableStepByStep: playground.enableStepByStep || false,
            maxWaitTime: maxWaitTime,
            dialogId: `background-task-${taskId}`,
          });
          this.logger.log(`✅ AI对话同步执行完成 (taskId: ${taskId})`);
        } catch (error) {
          this.logger.error(`❌ AI对话执行失败:`, error);
          throw error;
        }
      } else {
        // 异步执行AI对话（不阻塞接口返回）
        this.logger.log(`📤 消息已发送，开始后台异步执行AI对话...`);
        this.taskWorker.executeAIDialog(playgroundId, {
          enableAutoIteration: playground.enableAutoIteration || false,
          enableStepByStep: playground.enableStepByStep || false,
          maxWaitTime: maxWaitTime,
          dialogId: `background-task-${taskId}`,
        }).catch(error => {
          this.logger.error(`❌ 异步AI对话执行失败 (taskId: ${taskId}):`, error);
        });
      }

      // 8. 返回结果
      const result = {
        success: true,
        taskId: taskId,
        playgroundId: playgroundId,
        messageId: userMessageId,
        waitForCompletion,
        message: waitForCompletion 
          ? `消息已成功发送到任务 ${taskId} 的chat会话并等待回复完成`
          : `消息已成功发送到任务 ${taskId} 的chat会话，后台异步处理中`,
        playground: {
          id: playgroundId,
          name: playground.name,
          model: playground.model
        }
      };

      this.logger.log(`🎉 消息发送完成: ${JSON.stringify({
        taskId,
        playgroundId,
        messageId: result.messageId,
        waitForCompletion
      })}`);

      return result;

    } catch (error) {
      this.logger.error(`❌ 向任务chat发送消息失败 (taskId: ${taskId}):`, error);
      throw new Error(`发送消息失败: ${error.message}`);
    }
  }

  /**
   * 获取任务状态
   * @param taskId 任务ID
   * @returns 任务状态
   */
  async getTaskStatus(taskId: string) {
    return this.taskWatcher.getTaskStatus(taskId);
  }

  /**
   * 获取任务列表
   * @param options 查询选项
   * @returns 任务列表
   */
  async getTaskList(options: {
    user?: string;
    status?: string;
    tags?: string[];
    take?: number;
    skip?: number;
    sort?: 'asc' | 'desc';
  } = {}) {
    const { user, status, tags, take, skip, sort = 'desc' } = options;

    const where: any = {};

    if (user) {
      where.user = user;
    }

    if (status) {
      where.status = status;
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasEvery: tags // 包含所有指定标签
      };
    }

    const tasks = await this.prisma.backgroundTask.findMany({
      where,
      include: {
        items: true
      },
      orderBy: {
        created: sort
      },
      take,
      skip
    });

    return tasks;
  }

  /**
   * 根据标签搜索任务
   * @param tags 标签数组
   * @param options 其他选项
   * @returns 匹配的任务列表
   */
  async searchTasksByTags(tags: string[], options: {
    user?: string;
    status?: string;
    matchMode?: 'all' | 'any'; // 匹配模式：all=必须包含所有标签，any=包含任意标签
  } = {}) {
    const { user, status, matchMode = 'any' } = options;

    const where: any = {};

    if (user) {
      where.user = user;
    }

    if (status) {
      where.status = status;
    }

    if (tags && tags.length > 0) {
      if (matchMode === 'all') {
        where.tags = {
          hasEvery: tags // 必须包含所有标签
        };
      } else {
        where.tags = {
          hasSome: tags // 包含任意标签
        };
      }
    }

    const tasks = await this.prisma.backgroundTask.findMany({
      where,
      include: {
        items: true
      },
      orderBy: {
        created: 'desc'
      }
    });

    return tasks;
  }

  /**
   * 根据playgroundId（chatId）反查关联的后台任务ID
   * @param playgroundId 聊天页面ID（playground ID）
   * @returns 关联的后台任务信息
   */
  async getTaskByPlaygroundId(playgroundId: string) {
    try {
      this.logger.log(`🔍 根据playgroundId查找关联任务: ${playgroundId}`);

      // 通过BackgroundTaskItem表查找包含该playgroundId的任务项
      const taskItem = await this.prisma.backgroundTaskItem.findFirst({
        where: {
          playgroundId: playgroundId
        },
        include: {
          // 关联查询BackgroundTask信息
          backgroundTask: {
            include: {
              items: true // 包含所有任务项
            }
          }
        }
      });

      if (!taskItem) {
        this.logger.warn(`⚠️ 未找到playgroundId关联的任务: ${playgroundId}`);
        return null;
      }

      const task = taskItem.backgroundTask;

      this.logger.log(`✅ 找到关联任务: ${task.id} (${task.taskName})`);

      // 返回完整的任务信息
      return {
        taskId: task.id,
        taskName: task.taskName,
        taskType: task.taskType,
        status: task.status,
        progress: task.progress,
        user: task.user,
        tags: (task as any).tags || [], // 安全访问tags字段
        created: task.created,
        updated: task.updated,
        playgroundId: playgroundId,
        taskItem: {
          id: taskItem.id,
          name: taskItem.itemName,
          status: taskItem.status,
          progress: taskItem.progress,
          stage: taskItem.stage
        },
        metadata: {
          taskMetadata: task.metadata,
          itemMetadata: taskItem.metadata
        },
        // 完整的任务对象（用于需要详细信息的场景）
        fullTask: task
      };

    } catch (error) {
      this.logger.error(`❌ 根据playgroundId查找任务失败 (playgroundId: ${playgroundId}):`, error);
      throw new Error(`查找任务失败: ${error.message}`);
    }
  }

  /**
   * 根据playgroundId批量查找关联的后台任务（支持多个playground）
   * @param playgroundIds 聊天页面ID数组
   * @returns 关联的后台任务信息数组
   */
  async getTasksByPlaygroundIds(playgroundIds: string[]) {
    try {
      this.logger.log(`🔍 批量查找playgroundId关联任务: ${playgroundIds.length}个`);

      if (!playgroundIds || playgroundIds.length === 0) {
        return [];
      }

      // 批量查询包含这些playgroundId的任务项
      const taskItems = await this.prisma.backgroundTaskItem.findMany({
        where: {
          playgroundId: {
            in: playgroundIds
          }
        },
        include: {
          backgroundTask: {
            include: {
              items: true
            }
          }
        }
      });

      // 转换为返回格式
      const results = taskItems.map(taskItem => {
        const task = taskItem.backgroundTask;
        return {
          taskId: task.id,
          taskName: task.taskName,
          taskType: task.taskType,
          status: task.status,
          progress: task.progress,
          user: task.user,
          tags: (task as any).tags || [], // 安全访问tags字段
          created: task.created,
          updated: task.updated,
          playgroundId: taskItem.playgroundId,
          taskItem: {
            id: taskItem.id,
            name: taskItem.itemName,
            status: taskItem.status,
            progress: taskItem.progress,
            stage: taskItem.stage
          },
          metadata: {
            taskMetadata: task.metadata,
            itemMetadata: taskItem.metadata
          }
        };
      });

      this.logger.log(`✅ 批量查找完成: 找到${results.length}个关联任务`);

      return results;

    } catch (error) {
      this.logger.error(`❌ 批量查找任务失败:`, error);
      throw new Error(`批量查找任务失败: ${error.message}`);
    }
  }

  /**
   * 检查playgroundId是否存在关联的后台任务
   * @param playgroundId 聊天页面ID
   * @returns 是否存在关联任务
   */
  async hasTaskByPlaygroundId(playgroundId: string): Promise<boolean> {
    try {
      const count = await this.prisma.backgroundTaskItem.count({
        where: {
          playgroundId: playgroundId
        }
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`❌ 检查playground关联任务失败:`, error);
      return false;
    }
  }
} 