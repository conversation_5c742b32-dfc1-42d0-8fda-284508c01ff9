import { <PERSON>du<PERSON>, forwardRef } from '@nestjs/common';
import { DesignProjectModule } from '../design-project/design-project.module';
import { PrismaModule } from '../prisma/prisma.module';
import { BackgroundTaskController } from './background-task.controller';
import { BackgroundTaskService } from './background-task.service';
import { ProcessManagerController } from './process-manager.controller';
import { ProcessManagerService } from './process-manager.service';
import { TaskManager } from './task-manager';
import { TaskWatcherService } from './task-watcher.service';
import { TaskWorkerService } from './task-worker.service';

@Module({
  imports: [PrismaModule, forwardRef(() => DesignProjectModule)],
  providers: [
    TaskManager, 
    TaskWatcherService, 
    TaskWorkerService,
    ProcessManagerService,
    BackgroundTaskService
  ],
  exports: [
    TaskManager, 
    TaskWatcherService, 
    TaskWorkerService,
    ProcessManagerService,
    BackgroundTaskService
  ],
  controllers: [BackgroundTask<PERSON>ontroller, ProcessManagerController],
})
export class BackgroundTaskModule {} 