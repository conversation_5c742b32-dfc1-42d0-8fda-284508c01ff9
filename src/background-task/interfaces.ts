// 通用后台任务接口定义
export interface BackgroundTaskConfig {
  taskType: string;
  taskName: string;
  user: string;
  metadata?: Record<string, any>;
  tags?: string[]; // 新增：用户自定义标签数组
}

export interface BackgroundTaskItem {
  id: string;
  name: string;
  metadata?: Record<string, any>;
}

export interface BackgroundTaskResult {
  success: boolean;
  result?: Record<string, any>;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BackgroundTaskProgress {
  stage?: string;
  progress: number;
  metadata?: Record<string, any>;
}

export interface TaskExecutionContext {
  taskId: string;
  startTime: Date;
  user: string;
}

// 任务状态接口
export interface TaskStatus {
  id: string;
  status: string;
  progress: number;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  error?: string;
  playgroundId?: string;
  playgroundFiles?: Array<{
    name: string;
    path: string;
    content: string | null;
    contentType: string;
    size: number;
    error?: string;
  }>;
  metadata?: Record<string, any>;
  items: TaskItemStatus[];
}

// 任务项状态接口
export interface TaskItemStatus {
  id: string;
  status: string;
  result?: Record<string, any>;
  error?: string;
  metadata?: Record<string, any>;
} 