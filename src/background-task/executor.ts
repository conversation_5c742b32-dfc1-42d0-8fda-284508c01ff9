import { PrismaService } from '../prisma/prisma.service';
import {
  BackgroundTaskConfig,
  BackgroundTaskItem,
  BackgroundTaskResult,
  BackgroundTaskProgress,
  TaskExecutionContext,
} from './interfaces';

/**
 * 后台任务执行器抽象类
 * 所有具体的任务执行器都应该继承此类
 */
export abstract class BackgroundTaskExecutor {
  constructor(protected readonly prisma: PrismaService) {}

  /**
   * 验证任务项
   * @param items 任务项列表
   */
  abstract validateItems(items: BackgroundTaskItem[]): Promise<void>;

  /**
   * 任务开始时的钩子
   * @param taskId 任务ID
   * @param config 任务配置
   */
  abstract onTaskStart(taskId: string, config: BackgroundTaskConfig): Promise<void>;

  /**
   * 执行单个任务项
   * @param item 任务项
   * @param config 任务配置
   * @param context 执行上下文
   * @param updateProgress 更新进度的回调函数
   * @returns 任务执行结果
   */
  abstract executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: TaskExecutionContext,
    updateProgress: (progress: BackgroundTaskProgress) => Promise<void>
  ): Promise<BackgroundTaskResult>;

  /**
   * 任务完成时的钩子
   * @param taskId 任务ID
   * @param results 所有任务项的执行结果
   * @param config 任务配置
   */
  abstract onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void>;
} 