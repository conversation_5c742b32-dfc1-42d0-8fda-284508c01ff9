import { Controller, Get, Post, Delete, Param, Body, Query, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ProcessManagerService, ProcessInfo, PostChatCommand } from './process-manager.service';

@Controller('process-manager')
export class ProcessManagerController {
  private readonly logger = new Logger(ProcessManagerController.name);

  constructor(private readonly processManager: ProcessManagerService, private readonly prisma: PrismaService) {}

  /**
   * 获取所有运行中的进程
   */
  @Get('running')
  async getRunningProcesses(): Promise<ProcessInfo[]> {
    return this.processManager.getRunningProcesses();
  }

  /**
   * 获取用户的所有进程
   */
  @Get('user/:userId')
  async getUserProcesses(@Param('userId') userId: string): Promise<ProcessInfo[]> {
    return this.processManager.getUserProcesses(userId);
  }

  /**
   * 获取playground的所有进程
   */
  @Get('playground/:playgroundId')
  async getPlaygroundProcesses(@Param('playgroundId') playgroundId: string): Promise<ProcessInfo[]> {
    return this.processManager.getPlaygroundProcesses(playgroundId);
  }

  /**
   * 获取特定进程信息
   */
  @Get('process/:processId')
  async getProcessInfo(@Param('processId') processId: string): Promise<ProcessInfo> {
    const processInfo = this.processManager.getProcessInfo(processId);
    if (!processInfo) {
      throw new HttpException(`进程 ${processId} 不存在`, HttpStatus.NOT_FOUND);
    }
    return processInfo;
  }

  /**
   * 手动执行post-chat命令
   */
  @Post('execute')
  async executePostChatCommands(@Body() body: {
    playgroundId: string;
    taskId: string;
    commands: PostChatCommand[];
    user: string;
  }): Promise<{ processIds: string[] }> {
    try {
      const processIds = await this.processManager.executePostChatCommands(
        body.playgroundId,
        body.taskId,
        body.commands,
        body.user
      );
      
      return { processIds };
    } catch (error) {
      this.logger.error('执行post-chat命令失败:', error);
      throw new HttpException(
        `执行post-chat命令失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 杀死特定进程
   */
  @Delete('process/:processId')
  async killProcess(
    @Param('processId') processId: string,
    @Query('reason') reason?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.processManager.killProcess(processId, reason || 'manual');
      return {
        success: true,
        message: `进程 ${processId} 已被杀死`
      };
    } catch (error) {
      this.logger.error(`杀死进程失败:`, error);
      throw new HttpException(
        `杀死进程失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 杀死用户的所有进程
   */
  @Delete('user/:userId')
  async killUserProcesses(@Param('userId') userId: string): Promise<{ success: boolean; message: string }> {
    try {
      await this.processManager.killUserProcesses(userId);
      return {
        success: true,
        message: `用户 ${userId} 的所有进程已被杀死`
      };
    } catch (error) {
      this.logger.error(`杀死用户进程失败:`, error);
      throw new HttpException(
        `杀死用户进程失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 杀死playground的所有进程
   */
  @Delete('playground/:playgroundId')
  async killPlaygroundProcesses(@Param('playgroundId') playgroundId: string): Promise<{ success: boolean; message: string }> {
    try {
      await this.processManager.killPlaygroundProcesses(playgroundId);
      return {
        success: true,
        message: `Playground ${playgroundId} 的所有进程已被杀死`
      };
    } catch (error) {
      this.logger.error(`杀死playground进程失败:`, error);
      throw new HttpException(
        `杀死playground进程失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新用户活动时间
   */
  @Post('user/:userId/activity')
  async updateUserActivity(@Param('userId') userId: string): Promise<{ success: boolean; message: string }> {
    this.processManager.updateUserActivity(userId);
    return {
      success: true,
      message: `用户 ${userId} 活动时间已更新`
    };
  }

  /**
   * 获取进程统计信息
   */
  @Get('stats')
  async getProcessStats(): Promise<{
    total: number;
    running: number;
    completed: number;
    failed: number;
    killed: number;
    byUser: Record<string, number>;
    byPlayground: Record<string, number>;
  }> {
    const runningProcesses = this.processManager.getRunningProcesses();
    
    // 统计所有进程状态
    const allProcesses = Array.from((this.processManager as any).processes.values()) as ProcessInfo[];
    
    const stats = {
      total: allProcesses.length,
      running: allProcesses.filter(p => p.status === 'running').length,
      completed: allProcesses.filter(p => p.status === 'completed').length,
      failed: allProcesses.filter(p => p.status === 'failed').length,
      killed: allProcesses.filter(p => p.status === 'killed').length,
      byUser: {} as Record<string, number>,
      byPlayground: {} as Record<string, number>
    };

    // 按用户统计
    for (const process of allProcesses) {
      if (process.status === 'running') {
        stats.byUser[process.user] = (stats.byUser[process.user] || 0) + 1;
        stats.byPlayground[process.playgroundId] = (stats.byPlayground[process.playgroundId] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * 获取进程实时输出（WebSocket端点的HTTP版本）
   */
  @Get('process/:processId/output')
  async getProcessOutput(@Param('processId') processId: string): Promise<{
    processId: string;
    output: string;
    status: string;
  }> {
    const processInfo = this.processManager.getProcessInfo(processId);
    if (!processInfo) {
      throw new HttpException(`进程 ${processId} 不存在`, HttpStatus.NOT_FOUND);
    }

    return {
      processId,
      output: processInfo.output || '',
      status: processInfo.status
    };
  }
} 