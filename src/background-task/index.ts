import { PrismaService } from '../prisma/prisma.service';
import { BackgroundTaskConfig } from './interfaces';

// 通用后台任务框架入口文件
export * from './interfaces';
export * from './executor';

// 任务项接口
export interface BackgroundTaskItem {
  id: string;
  name: string;
  metadata?: Record<string, any>;
}

// 任务执行结果接口
export interface BackgroundTaskResult {
  success: boolean;
  result?: Record<string, any>;
  error?: string;
  metadata?: Record<string, any>;
}

// 任务进度更新接口
export interface BackgroundTaskProgress {
  stage?: string;
  progress: number;
  metadata?: Record<string, any>;
}

// 任务执行上下文接口
export interface TaskExecutionContext {
  taskId: string;
  startTime: Date;
  user: string;
}

// 任务状态接口
export interface TaskStatus {
  id: string;
  status: string;
  progress: number;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  error?: string;
  playgroundId?: string;
  playgroundFiles?: Array<{
    name: string;
    path: string;
    content: string | null;
    contentType: string;
    size: number;
    error?: string;
  }>;
  metadata?: Record<string, any>;
  items: TaskItemStatus[];
}

// 任务项状态接口
export interface TaskItemStatus {
  id: string;
  status: string;
  result?: Record<string, any>;
  error?: string;
  metadata?: Record<string, any>;
}

// 任务执行器抽象类
export abstract class BackgroundTaskExecutor {
  constructor(protected readonly prisma: PrismaService) {}

  // 验证任务项
  abstract validateItems(items: BackgroundTaskItem[]): Promise<void>;

  // 任务开始时的钩子
  abstract onTaskStart(taskId: string, config: BackgroundTaskConfig): Promise<void>;

  // 执行单个任务项
  abstract executeItem(
    item: BackgroundTaskItem,
    config: BackgroundTaskConfig,
    context: TaskExecutionContext,
    updateProgress: (progress: BackgroundTaskProgress) => Promise<void>
  ): Promise<BackgroundTaskResult>;

  // 任务完成时的钩子
  abstract onTaskComplete(
    taskId: string,
    results: BackgroundTaskResult[],
    config: BackgroundTaskConfig
  ): Promise<void>;
}

// 导出所有接口
export * from './interfaces';

// 导出执行器基类
export * from './executor';

// 导出具体实现
export * from './task-manager';
export * from './task-watcher.service';
export * from './task-worker.service';
export * from './process-manager.service';
export * from './process-manager.controller';
export * from './background-task.service';
export * from './background-task.module';