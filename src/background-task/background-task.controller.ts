import { Controller, Get, Post, Param, Res, Body, Query, Logger, StreamableFile, UseInterceptors, UploadedFiles } from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { DesignProjectBackgroundTaskService } from '../design-project/background-task.service';
import { DesignProjectService } from '../design-project/design-project.service';
import { BackgroundTaskService } from './background-task.service';
import { TaskWatcherService } from './task-watcher.service';
import { TaskWorkerService } from './task-worker.service';

// 任务响应接口定义
interface TaskResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  message: string;
  waitForCompletion?: boolean;
  // 等待完成时的额外数据
  playgroundId?: string;
  playgroundIds?: string[];
  files?: any[];
  coordinates?: any;
  // 并行任务的特殊字段
  executionMode?: 'parallel';
  pageCount?: number;
  pageResults?: any[];
  successCount?: number;
  failureCount?: number;
}

@Controller('background-tasks')
export class BackgroundTaskController {
  private readonly logger = new Logger(BackgroundTaskController.name);

  constructor(
    private readonly taskWorker: TaskWorkerService,
    private readonly backgroundTaskService: DesignProjectBackgroundTaskService,
    private readonly designProjectService: DesignProjectService,
    private readonly taskWatcher: TaskWatcherService,
    private readonly taskService: BackgroundTaskService
  ) { }

  @Get('download/:playgroundId/:fileName(*)')
  async downloadFile(
    @Param('playgroundId') playgroundId: string,
    @Param('fileName') fileName: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      const fileData = await this.taskWorker.getPlaygroundFile(playgroundId, fileName);

      res.set({
        'Content-Type': fileData.contentType
      });

      // StreamableFile 会自动处理 Buffer
      return new StreamableFile(Buffer.from(fileData.content));
    } catch (error) {
      // res.status(404).send({ message: `文件未找到: ${error.message}` });
      // 需要返回一个符合Promise<StreamableFile>的空值
      // return new StreamableFile(Buffer.from(''));
      return null;
    }
  }

  // ========================= 新增的四个任务接口 =========================

  /**
   * 1. 创建图片转代码任务
   * 支持可选的 projectId 和 prototypeId，如果不提供则不与工程和设计稿原型建立关联
   */
  @Post('img-to-code')
  async createImgToCodeTask(
    @Body() body: {
      // 可选关联信息
      projectId?: string; // 项目ID (可选)
      items: Array<{
        prototypeId?: string; // 原型ID (可选)
        id?: string; // 任务项ID，如果没有prototypeId则必须提供
        name?: string; // 任务项名称 (可选)
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据 (可选)
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    }
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到图片转代码任务请求:`, {
      projectId: body.projectId || '无关联项目',
      itemCount: body.items.length,
      waitForCompletion: body.waitForCompletion || false,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item, index) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl
          );

          // 生成唯一的任务项ID和名称
          const itemId = item.prototypeId || item.id || `img-to-code-${Date.now()}-${index}`;
          const itemName = item.name || `图片转代码_${itemId}`;

          return {
            id: itemId,
            name: itemName,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
              // 保留关联信息
              prototypeId: item.prototypeId,
            }
          };
        })
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-to-code',
        taskName: `图片转代码任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: {
          ...body.metadata,
          projectId: body.projectId, // 可选的项目关联
        },
        waitForCompletion: body.waitForCompletion,
      });

      const response = await this.buildTaskResponse(result, body.waitForCompletion, 'img-to-code');
      this.logger.log(`✅ 图片转代码任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 图片转代码任务创建失败:`, error);
      throw error;
    }
  }

  /**
   * 2. 创建图片视觉分割任务
   * 支持可选的 projectId 和 prototypeId，如果不提供则不与工程和设计稿原型建立关联
   */
  @Post('img-visual-split')
  async createImgVisualSplitTask(
    @Body() body: {
      // 可选关联信息
      projectId?: string; // 项目ID (可选)
      items: Array<{
        prototypeId?: string; // 原型ID (可选)
        id?: string; // 任务项ID，如果没有prototypeId则必须提供
        name?: string; // 项目名称 (可选)
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    }
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到图片视觉分割任务请求:`, {
      projectId: body.projectId || '无关联项目',
      itemCount: body.items.length,
      splitOptions: body.metadata,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item, index) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl
          );

          // 生成唯一的任务项ID和名称
          const itemId = item.prototypeId || item.id || `img-visual-split-${Date.now()}-${index}`;
          const itemName = item.name || `图片视觉分割_${itemId}`;

          return {
            id: itemId,
            name: itemName,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
              // 保留关联信息
              prototypeId: item.prototypeId,
            }
          };
        })
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-visual-split',
        taskName: `图片视觉分割任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: {
          ...body.metadata,
          projectId: body.projectId, // 可选的项目关联
        },
        waitForCompletion: body.waitForCompletion,
      });

      const response = await this.buildTaskResponse(result, body.waitForCompletion, 'img-visual-split');
      this.logger.log(`✅ 图片视觉分割任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 图片视觉分割任务创建失败:`, error);
      throw error;
    }
  }

  /**
   * 3. 创建从分割图生成代码任务
   * 支持可选的 projectId 和 prototypeId，如果不提供则不与工程和设计稿原型建立关联
   */
  @Post('img-split-to-code')
  async createImgSplitToCodeTask(
    @Body() body: {
      // 可选关联信息
      projectId?: string; // 项目ID (可选)
      items: Array<{
        prototypeId?: string; // 原型ID (可选)
        id?: string; // 任务项ID，如果没有prototypeId则必须提供
        name?: string; // 任务项名称 (可选)
        metadata: {
          imageContent?: string; // Base64编码的图片内容 (可选)
          imageUrl?: string; // 图片URL (可选)
          imageName?: string; // 图片文件名 (可选)
          imageType?: string; // 图片类型 (可选)
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    }
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到从分割图生成代码任务请求:`, {
      projectId: body.projectId || '无关联项目',
      itemCount: body.items.length,
    });

    try {
      // 处理图片内容，将imageUrl转换为base64
      const processedItems = await Promise.all(
        body.items.map(async (item, index) => {
          const resolvedImageContent = await this.designProjectService.resolveImageContent(
            item.metadata.imageContent,
            item.metadata.imageUrl
          );

          // 生成唯一的任务项ID和名称
          const itemId = item.prototypeId || item.id || `img-split-to-code-${Date.now()}-${index}`;
          const itemName = item.name || `从分割图生成代码_${itemId}`;

          return {
            id: itemId,
            name: itemName,
            metadata: {
              ...item.metadata,
              imageContent: resolvedImageContent,
              // 移除imageUrl，避免重复
              imageUrl: undefined,
              // 保留关联信息
              prototypeId: item.prototypeId,
            }
          };
        })
      );

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'img-split-to-code',
        taskName: `从分割图生成代码任务_${new Date().getTime()}`,
        items: processedItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview',
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: {
          ...body.metadata,
          projectId: body.projectId, // 可选的项目关联
        },
        waitForCompletion: body.waitForCompletion,
      });

      const response = await this.buildTaskResponse(result, body.waitForCompletion, 'img-split-to-code');
      this.logger.log(`✅ 从分割图生成代码任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 从分割图生成代码任务创建失败:`, error);
      throw error;
    }
  }

  /**
   * 4. 创建从切图坐标生成布局任务
   * 支持可选的 projectId 和 prototypeId，如果不提供则不与工程和设计稿原型建立关联
   */
  @Post('coords-to-layout')
  async createCoordsToLayoutTask(
    @Body() body: {
      // 可选关联信息
      projectId?: string; // 项目ID (可选)
      items: Array<{
        prototypeId?: string; // 原型ID (可选)
        id?: string; // 任务项ID，如果没有prototypeId则必须提供
        name?: string; // 任务项名称 (可选)
        metadata: {
          imgHeight?: string; // 图片高度
          imgWidth?: string; // 图片宽度
          coordinates?: Array<{ // 组件坐标数组
            name: string;
            x1: number;
            y1: number;
            x2: number;
            y2: number;
          }>;
        };
      }>;
      user: string; // 用户ID
      model?: string; // AI模型 (可选)
      enableAutoIteration?: boolean; // 是否启用自动迭代 (可选)
      enableStepByStep?: boolean; // 是否启用分步执行 (可选)
      metadata?: {}; // 任务元数据
      waitForCompletion?: boolean; // 是否等待任务完成 (可选)
    }
  ): Promise<TaskResponse> {
    this.logger.log(`🎯 收到从切图坐标生成布局任务请求:`, {
      projectId: body.projectId || '无关联项目',
      itemCount: body.items.length,
      layoutOptions: body.metadata,
    });

    try {
      // 转换为通用任务项格式
      const taskItems = body.items.map((item, index) => {
        // 生成唯一的任务项ID和名称
        const itemId = item.prototypeId || item.id || `coords-to-layout-${Date.now()}-${index}`;
        const itemName = item.name || `坐标生成布局_${itemId}`;

        return {
          id: itemId,
          name: itemName,
          metadata: {
            ...item.metadata,
            // 保留关联信息
            prototypeId: item.prototypeId,
          }
        };
      });

      const result = await this.backgroundTaskService.createAndExecuteGenericTask({
        taskType: 'coords-to-layout',
        taskName: `从切图坐标生成布局任务_${new Date().getTime()}`,
        items: taskItems,
        user: body.user,
        model: body.model || 'openrouter::google/gemini-2.5-pro-preview', // 设置默认模型
        enableAutoIteration: body.enableAutoIteration ?? false,
        enableStepByStep: body.enableStepByStep ?? false,
        metadata: {
          ...body.metadata,
          projectId: body.projectId, // 可选的项目关联
        },
        waitForCompletion: body.waitForCompletion,
      });

      const response = await this.buildTaskResponse(result, body.waitForCompletion, 'coords-to-layout');
      this.logger.log(`✅ 从切图坐标生成布局任务创建成功: ${response.taskId}`);
      return response;
    } catch (error) {
      this.logger.error(`❌ 从切图坐标生成布局任务创建失败:`, error);
      throw error;
    }
  }

  // ========================= 辅助方法 =========================

  /**
   * 构建任务响应对象
   * 复用 design-project.controller 中的逻辑
   */
  private async buildTaskResponse(
    result: any,
    waitForCompletion: boolean = false,
    taskType: string
  ): Promise<TaskResponse> {
    const baseResponse: TaskResponse = {
      taskId: result.taskId || result.id,
      status: result.status || 'pending',
      message: `${taskType} 任务已创建`,
      waitForCompletion: waitForCompletion,
    };

    // 如果需要等待完成，使用TaskWatcherService等待并获取结果
    if (waitForCompletion) {
      try {
        this.logger.log(`⏳ 等待任务完成并获取结果: ${baseResponse.taskId}`);

        // 使用TaskWatcherService等待任务完成并获取文件
        const completionResult = await this.taskWatcher.waitForTaskCompletionAndGetResults(
          baseResponse.taskId,
          undefined, // playgroundId会自动从任务状态中获取
          {
            maxAttempts: 600, // 最大尝试次数，30分钟
            pollInterval: 3000, // 每3秒检查一次
            resetState: true, // 重置检查状态
            skipFileTraversal: false // 不跳过文件遍历，确保获取文件
          }
        );

        // 更新响应状态
        baseResponse.status = completionResult.status as any || 'completed';
        baseResponse.message = `${taskType} 任务已完成`;

        if (completionResult.playgroundId) {
          baseResponse.playgroundId = completionResult.playgroundId;
        }

        // 处理文件结果
        if (completionResult.files && Object.keys(completionResult.files).length > 0) {
          // 转换文件格式以匹配预期的结构
          baseResponse.files = Object.entries(completionResult.files).map(([path, fileData]: [string, any]) => ({
            name: path.split('/').pop() || path,
            path: path,
            content: fileData.content,
            contentType: fileData.contentType,
            previewUrl: fileData.previewUrl,
            size: fileData.content ? fileData.content.length : 0
          }));

          this.logger.log(`✅ 成功获取 ${baseResponse.files.length} 个文件`);
        } else {
          this.logger.warn(`⚠️ 任务完成但未获取到文件`);
          baseResponse.files = [];
        }

        // 如果有多个playground信息（并行任务）
        if (completionResult.allPlaygroundInfo && completionResult.allPlaygroundInfo.length > 0) {
          baseResponse.playgroundIds = completionResult.allPlaygroundInfo.map(info => info.playgroundId);
          baseResponse.pageResults = completionResult.allPlaygroundInfo;
        }

        // 处理错误情况
        if (completionResult.fileError) {
          this.logger.warn(`⚠️ 文件获取失败: ${completionResult.fileError}`);
        }

        if (!completionResult.taskCompleted) {
          baseResponse.status = 'failed';
          baseResponse.message = `${taskType} 任务执行超时或失败`;
        }

      } catch (error) {
        this.logger.error(`❌ 等待任务完成失败:`, error);
        baseResponse.status = 'failed';
        baseResponse.message = `${taskType} 任务等待失败: ${error.message}`;
      }
    }

    return baseResponse;
  }

  // ========================= 通用任务管理接口 =========================

  /**
   * 查询用户任务列表
   */
  @Get('user/:userId')
  async getUserTasks(
    @Param('userId') userId: string,
    @Query('taskType') taskType?: string
  ) {
    try {
      this.logger.log(`🔍 查询用户任务列表: ${userId}`, { taskType });
      const result = await this.backgroundTaskService.getUserTasks(userId, taskType);
      return result;
    } catch (error) {
      this.logger.error(`❌ 查询用户任务列表失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务工作区文件列表
   */
  @Get(':taskId/files')
  async getTaskFiles(@Param('taskId') taskId: string) {
    try {
      this.logger.log(`🔍 获取任务工作区文件列表: ${taskId}`);

      // 首先获取任务状态，从中提取playgroundId
      const taskStatus = await this.backgroundTaskService.getTaskStatus(taskId);

      if (!taskStatus.playgroundId) {
        throw new Error('任务尚未生成工作区或工作区已被删除');
      }

      const files = await this.taskWorker.listPlaygroundFiles(taskStatus.playgroundId);
      return { taskId, playgroundId: taskStatus.playgroundId, files };
    } catch (error) {
      this.logger.error(`❌ 获取任务工作区文件列表失败:`, error);
      throw error;
    }
  }

  /**
   * 向指定任务的chat发送消息
   * @param taskId 后台任务ID
   * @param body 请求体
   */
  @Post(':taskId/send-message')
  @UseInterceptors(FilesInterceptor('files', 5))
  async sendMessageToTaskChat(
    @Param('taskId') taskId: string,
    @Body() body: {
      messageContent: string;
      waitForCompletion?: string; // 'true'/'false'
      maxWaitTime?: string; // 毫秒数字符串
      messageRole?: string;
      userId?: string;
    },
    @UploadedFiles() files?: Express.Multer.File[]
  ) {
    // 解析参数
    const options: any = {
      waitForCompletion: body.waitForCompletion !== 'false', // 默认为true，除非明确设置为false
      messageRole: body.messageRole || 'user',
    };

    // 解析等待时间
    if (body.maxWaitTime) {
      const waitTime = parseInt(body.maxWaitTime, 10);
      if (!isNaN(waitTime) && waitTime > 0) {
        options.maxWaitTime = waitTime;
      }
    }

    // 添加用户ID（如果提供）
    if (body.userId) {
      options.userId = body.userId;
    }

    // 处理附件
    if (files && files.length > 0) {
      options.attachments = files.map(file => ({
        name: file.originalname,
        content: file.buffer.toString('base64'),
        contentType: file.mimetype
      }));
    }

    return this.taskService.sendMessageToTaskChat(
      taskId,
      body.messageContent,
      options
    );
  }

  /**
   * 获取任务状态
   * @param taskId 任务ID
   */
  @Get(':taskId/status')
  async getTaskStatus(@Param('taskId') taskId: string) {
    return this.taskService.getTaskStatus(taskId);
  }

  /**
   * 获取任务列表
   * @param query 查询参数
   */
  @Get()
  async getTaskList(@Query() query: {
    user?: string;
    status?: string;
    tags?: string; // 逗号分隔的标签
    take?: string;
    skip?: string;
    sort?: 'asc' | 'desc';
  }) {
    const options: any = {};

    if (query.user) options.user = query.user;
    if (query.status) options.status = query.status;
    if (query.sort) options.sort = query.sort;

    // 解析分页参数
    if (query.take) {
      const take = parseInt(query.take, 10);
      if (!isNaN(take) && take > 0) {
        options.take = take;
      }
    }

    if (query.skip) {
      const skip = parseInt(query.skip, 10);
      if (!isNaN(skip) && skip >= 0) {
        options.skip = skip;
      }
    }

    // 解析标签
    if (query.tags) {
      options.tags = query.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    }

    return this.taskService.getTaskList(options);
  }

  /**
   * 根据标签搜索任务
   * @param body 请求体
   */
  @Post('search-by-tags')
  async searchTasksByTags(@Body() body: {
    tags: string[];
    user?: string;
    status?: string;
    matchMode?: 'all' | 'any';
  }) {
    return this.taskService.searchTasksByTags(body.tags, {
      user: body.user,
      status: body.status,
      matchMode: body.matchMode
    });
  }

  /**
   * 根据playgroundId（chatId）查找关联的后台任务
   * @param playgroundId 聊天页面ID
   */
  @Get('playground/:playgroundId/task')
  async getTaskByPlaygroundId(@Param('playgroundId') playgroundId: string) {
    const result = await this.taskService.getTaskByPlaygroundId(playgroundId);
    
    if (!result) {
      return {
        success: false,
        message: `未找到与playground ${playgroundId} 关联的后台任务`,
        playgroundId: playgroundId
      };
    }

    return {
      success: true,
      message: '查找成功',
      data: result
    };
  }

  /**
   * 批量根据playgroundId查找关联的后台任务
   * @param body 请求体，包含playgroundId数组
   */
  @Post('playground/batch-find-tasks')
  async getTasksByPlaygroundIds(@Body() body: {
    playgroundIds: string[];
  }) {
    if (!body.playgroundIds || !Array.isArray(body.playgroundIds)) {
      throw new Error('playgroundIds必须是字符串数组');
    }

    const results = await this.taskService.getTasksByPlaygroundIds(body.playgroundIds);
    
    return {
      success: true,
      message: `查找完成，找到${results.length}个关联任务`,
      data: results,
      summary: {
        total: body.playgroundIds.length,
        found: results.length,
        missing: body.playgroundIds.length - results.length
      }
    };
  }

  /**
   * 检查playgroundId是否存在关联的后台任务
   * @param playgroundId 聊天页面ID
   */
  @Get('playground/:playgroundId/exists')
  async hasTaskByPlaygroundId(@Param('playgroundId') playgroundId: string) {
    const exists = await this.taskService.hasTaskByPlaygroundId(playgroundId);
    
    return {
      success: true,
      playgroundId: playgroundId,
      hasTask: exists,
      message: exists ? '存在关联任务' : '不存在关联任务'
    };
  }
} 