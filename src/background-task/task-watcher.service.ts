import { Injectable, Logger } from '@nestjs/common';
import { TaskManager } from './task-manager';
import { TaskWorkerService } from './task-worker.service';

/**
 * 任务执行观察者
 * 负责查询任务执行状态、获取执行结果（playground中的生成的文件）
 */
@Injectable()
export class TaskWatcherService {
  private readonly logger = new Logger(TaskWatcherService.name);
  // 用于跟踪每个任务的独立检查状态，防止状态累积
  private readonly taskCheckState = new Map<string, {
    totalChecks: number;
    lastResetTime: number;
  }>();

  constructor(
    private readonly taskManager: TaskManager,
    private readonly taskWorkerService: TaskWorkerService
  ) {}

  /**
   * 重置任务检查状态
   * 在开始新的AI任务检查时调用，确保检查次数从0开始
   * @param taskId 任务ID
   * @param resetReason 重置原因（用于日志）
   */
  private resetTaskCheckState(taskId: string, resetReason: string = '新的检查周期'): void {
    const currentTime = Date.now();
    const previousState = this.taskCheckState.get(taskId);
    
    this.taskCheckState.set(taskId, {
      totalChecks: 0,
      lastResetTime: currentTime
    });
    
    if (previousState) {
      this.logger.log(`🔄 重置任务检查状态 (任务ID: ${taskId}) - 原因: ${resetReason}`);
      this.logger.log(`📊 上次状态: 检查次数=${previousState.totalChecks}, 上次重置时间=${new Date(previousState.lastResetTime).toISOString()}`);
    } else {
      this.logger.log(`🆕 初始化任务检查状态 (任务ID: ${taskId}) - 原因: ${resetReason}`);
    }
  }

  /**
   * 获取当前任务检查次数并递增
   * @param taskId 任务ID
   * @returns 当前检查次数（递增前的值）
   */
  private getAndIncrementCheckCount(taskId: string): number {
    const state = this.taskCheckState.get(taskId);
    if (!state) {
      // 如果没有状态记录，自动初始化
      this.resetTaskCheckState(taskId, '自动初始化');
      return 0;
    }
    
    const currentCount = state.totalChecks;
    state.totalChecks++;
    return currentCount;
  }

  /**
   * 清理任务检查状态
   * 在任务完成或失败时调用，避免内存泄漏
   * @param taskId 任务ID
   */
  private cleanupTaskCheckState(taskId: string): void {
    const deleted = this.taskCheckState.delete(taskId);
    
    // 同时清理进度跟踪记录
    const progressKey = `${taskId}_lastProgress`;
    const hadProgressRecord = delete (this as any)[progressKey];
    
    if (deleted || hadProgressRecord) {
      this.logger.log(`🧹 清理任务检查状态 (任务ID: ${taskId})`);
      if (hadProgressRecord) {
        this.logger.log(`🧹 清理进度跟踪记录 (任务ID: ${taskId})`);
      }
    }
  }

  /**
   * 通用的任务状态检查方法
   * 每次调用都会重置检查次数，确保每个任务都有充分的检查机会
   * @param taskId 任务ID
   * @param options 检查选项
   * @returns 任务状态检查结果
   */
  private async checkTaskStatusWithReset(
    taskId: string,
    options: {
      maxAttempts?: number;
      pollInterval?: number;
      onProgress?: (attempts: number, status: any) => void;
      resetState?: boolean; // 是否重置状态，默认true
    } = {}
  ): Promise<{
    completed: boolean;
    status: any;
    attempts: number;
    playgroundId?: string;
    processingTime: number;
  }> {
    // 检查参数
    const maxAttempts = options.maxAttempts || 600; // 增加到600次 (30分钟)，原来是300次 (15分钟)
    const pollInterval = options.pollInterval || 3000; // 每3秒检查一次
    const shouldResetState = options.resetState !== false; // 默认重置状态
    
    // 重置检查状态（如果需要）
    if (shouldResetState) {
      this.resetTaskCheckState(taskId, '开始新的检查周期');
    }
    
    let taskCompleted = false;
    let finalStatus: any = null;
    let playgroundId: string | undefined;
    
    this.logger.log(`🕒 开始检查任务状态 (任务ID: ${taskId})`);
    this.logger.log(`📊 检查参数: 最大次数=${maxAttempts}, 间隔=${pollInterval}ms, 重置状态=${shouldResetState}`);
    
    const startTime = Date.now();
    
    // 记录上次进度更新时间，用于检测任务是否停滞
    let lastProgressUpdateTime = Date.now();
    let lastProgress = 0;
    
    // 轮询直到任务完成或超时
    while (true) {
      const currentAttempts = this.getAndIncrementCheckCount(taskId);
      
      // 检查是否超过最大尝试次数
      if (currentAttempts >= maxAttempts) {
        this.logger.warn(`⚠️ 检查次数超过限制 (${maxAttempts})，停止检查 - 任务ID: ${taskId}`);
        break;
      }
      
      // 每5次轮询打印一次日志，避免日志过多
      if ((currentAttempts + 1) % 5 === 0) {
        this.logger.log(`🔄 检查任务状态 (${currentAttempts + 1}/${maxAttempts}) - 任务ID: ${taskId}`);
      }
      
      // 获取任务状态
      try {
        // 必须有任务ID才能检查状态
        if (!taskId) {
          this.logger.warn(`⚠️ 无任务ID，无法检查状态，等待下一次检查`);
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          continue;
        }
        
        // 获取任务状态
        const status = await this.taskManager.getTaskStatus(taskId);
        finalStatus = status;
        
        // 如果还没有playgroundId，尝试从任务状态中获取
        if (!playgroundId && status.playgroundId) {
          playgroundId = status.playgroundId;
          this.logger.log(`✅ 成功从任务状态获取到playgroundId: ${playgroundId}`);
        }
        
        // 调用进度回调
        if (options.onProgress) {
          options.onProgress(currentAttempts + 1, status);
        }
        
        // 检测进度跳跃，如果进度显著提升，说明可能进入了新的处理阶段
        const currentProgress = status.progress || 0;
        const progressKey = `${taskId}_lastProgress`;
        const lastProgress = (this as any)[progressKey] || 0;
        
        // 如果进度有任何更新，重置进度更新时间
        if (currentProgress !== lastProgress) {
          lastProgressUpdateTime = Date.now();
          (this as any)[progressKey] = currentProgress;
          
          // 如果进度提升超过5%，认为是新的处理阶段，重置检查次数
          if (currentProgress > lastProgress + 5) {
            this.logger.log(`🔄 检测到进度显著提升 (${lastProgress}% → ${currentProgress}%)，重置检查计数器`);
            this.resetTaskCheckState(taskId, `进度从${lastProgress}%跳跃到${currentProgress}%`);
          }
          
          // 记录进度更新
          this.logger.log(`📈 任务进度更新: ${currentProgress}% (上次进度: ${lastProgress}%)`);
        }
        
        // 检查任务是否长时间没有进度更新但仍在处理中
        // 如果超过30分钟没有进度更新，但任务状态仍为processing，记录警告但不终止任务
        const progressStallTime = Date.now() - lastProgressUpdateTime;
        if (status.status === 'processing' && progressStallTime > 30 * 60 * 1000) { // 30分钟
          this.logger.warn(`⚠️ 任务 ${taskId} 已有 ${Math.round(progressStallTime / 60000)} 分钟没有进度更新，但仍在处理中 (当前进度: ${currentProgress}%)`);
          
          // 每10分钟记录一次警告，但不终止任务
          if (progressStallTime % (10 * 60 * 1000) < pollInterval) {
            this.logger.warn(`⏱️ 任务 ${taskId} 仍在运行，但进度停滞。将继续监控...`);
          }
        }
        
        // 判断任务是否完成
        if (status.status === 'completed' || status.status === 'failed') {
          this.logger.log(`✅ 任务已结束 (状态: ${status.status})，停止轮询`);
          taskCompleted = true;
          // 清理进度记录
          delete (this as any)[progressKey];
          break;
        } else {
          if ((currentAttempts + 1) % 5 === 0) { // 减少日志输出
            this.logger.log(`ℹ️ 当前任务状态: ${status.status}, 进度: ${status.progress}%`);
          }
        }
      } catch (error) {
        this.logger.warn(`⚠️ 获取任务状态失败: ${error.message}`);
      }
      
      // 等待下一次检查
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      
      // 如果是第10次检查，输出一条提示消息
      if (currentAttempts + 1 === 10) {
        this.logger.log(`⏱️ 任务处理需要较长时间，继续等待... (已等待${(currentAttempts + 1) * pollInterval / 1000}秒)`);
      }
    }
    
    const finalAttempts = this.taskCheckState.get(taskId)?.totalChecks || 0;
    const processingTime = (Date.now() - startTime) / 1000;
    
    // 超时处理
    if (finalAttempts >= maxAttempts && !taskCompleted) {
      this.logger.warn(`⚠️ 等待任务完成超时 (${maxAttempts * pollInterval / 1000}秒)，任务ID: ${taskId}`);
    } else {
      this.logger.log(`✅ 任务处理已完成或检测到完成信号，耗时: ${processingTime}秒, 检查次数: ${finalAttempts}`);
    }
    
    // 如果任务已完成或失败，清理状态
    if (taskCompleted || finalAttempts >= maxAttempts) {
      this.cleanupTaskCheckState(taskId);
    }
    
    return {
      completed: taskCompleted,
      status: finalStatus,
      attempts: finalAttempts,
      playgroundId,
      processingTime
    };
  }

  /**
   * 等待任务完成并获取结果
   * @param taskId 任务ID
   * @param playgroundId 工作区ID（可选）
   * @param options 选项
   * @returns 任务执行结果
   */
  /**
   * 等待任务完成并获取结果（完整优化版本）
   * @param taskId 任务ID
   * @param playgroundId 工作区ID（可选）
   * @param options 选项
   * @returns 任务执行结果
   */
  async waitForTaskCompletionAndGetResults(
    taskId: string,
    playgroundId?: string,
    options: {
      maxAttempts?: number;
      pollInterval?: number;
      resetState?: boolean;
      skipFileTraversal?: boolean; // 是否跳过文件遍历
    } = {}
  ): Promise<{
    taskId: string;
    taskCompleted: boolean;
    status?: string;
    error?: string;
    playgroundId?: string;
    files?: Record<string, any>;
    fileCount?: number;
    processingTime: number;
    fileError?: string;
    waitCompletionFailed?: boolean;
    reason?: string;
    allPlaygroundInfo?: Array<{pageName: string, playgroundId: string, taskId?: string}>;
  }> {
    this.logger.log(`🚀 开始等待任务完成并获取结果 (任务ID: ${taskId})`);
    
    // 使用通用检查方法，每次调用都重置检查次数
    const checkResult = await this.checkTaskStatusWithReset(taskId, {
      maxAttempts: options.maxAttempts,
      pollInterval: options.pollInterval,
      resetState: options.resetState !== false,
      onProgress: (attempts, status) => {
        if (attempts % 10 === 0) {
          this.logger.log(`📈 任务进度更新: ${status.progress}% (检查次数: ${attempts})`);
        }
      }
    });
    
    // 获取最终的playgroundId
    const actualPlaygroundId = playgroundId || checkResult.playgroundId;
    
    // 提取任务状态和错误信息
    const taskStatus = checkResult.status?.status || (checkResult.completed ? 'completed' : 'failed');
    const taskError = checkResult.status?.error;
    
    // 检查最终是否获得了PlaygroundId
    if (!actualPlaygroundId) {
      this.logger.error(`❌ 任务完成或超时，但无法获取playgroundId`);
      return {
        taskId,
        taskCompleted: checkResult.completed,
        status: taskStatus,
        error: taskError || '任务处理完成但无法获取playgroundId',
        waitCompletionFailed: true,
        reason: '任务处理完成但无法获取playgroundId',
        processingTime: checkResult.processingTime,
      };
    }
    
    this.logger.log(`🕒 等待任务完成，PlaygroundID: ${actualPlaygroundId}`);
    
    // 如果任务失败，直接返回错误状态
    if (taskStatus === 'failed') {
      this.logger.log(`❌ 任务失败，不获取文件内容`);
      return {
        taskId,
        taskCompleted: checkResult.completed,
        status: taskStatus,
        error: taskError,
        playgroundId: actualPlaygroundId,
        processingTime: checkResult.processingTime,
      };
    }
    
    // 如果设置了跳过文件遍历，直接返回基本信息
    if (options.skipFileTraversal) {
      this.logger.log(`⚡ 跳过文件遍历，直接返回任务结果`);
      
      try {
        const taskStatus = await this.taskManager.getTaskStatus(taskId);
        const allPlaygroundInfo = this.extractAllPlaygroundInfoFromTask(taskStatus);
        
        return {
          taskId,
          taskCompleted: checkResult.completed,
          status: taskStatus.status,
          error: taskError,
          playgroundId: actualPlaygroundId,
          allPlaygroundInfo,
          processingTime: checkResult.processingTime,
        };
      } catch (error) {
        this.logger.error(`❌ 获取任务详细信息失败:`, error);
        return {
          taskId,
          taskCompleted: checkResult.completed,
          status: taskStatus,
          error: taskError,
          playgroundId: actualPlaygroundId,
          processingTime: checkResult.processingTime,
        };
      }
    }
    
    // 原有的文件遍历逻辑（现在会自动过滤无关文件）
    try {
      const fileContents = await this.getPlaygroundFiles(actualPlaygroundId);
      
      this.logger.log(`✅ 成功获取所有文件（已过滤无关文件）`);
      
      return {
        taskId,
        taskCompleted: checkResult.completed,
        status: taskStatus,
        error: taskError,
        playgroundId: actualPlaygroundId,
        files: fileContents,
        fileCount: Object.keys(fileContents).length,
        processingTime: checkResult.processingTime,
      };
    } catch (fileError) {
      this.logger.error(`❌ 获取文件内容失败:`, fileError);
      return {
        taskId,
        taskCompleted: checkResult.completed,
        status: taskStatus,
        error: taskError,
        playgroundId: actualPlaygroundId,
        fileError: fileError.message,
        processingTime: checkResult.processingTime,
      };
    }
  }

  /**
   * 从任务状态中提取所有页面的playground信息
   * @param taskStatus 任务状态
   * @returns 所有页面的playground信息
   */
  private extractAllPlaygroundInfoFromTask(taskStatus: any): Array<{pageName: string, playgroundId: string, taskId?: string}> {
    const playgroundInfos: Array<{pageName: string, playgroundId: string, taskId?: string}> = [];
    
    if (!taskStatus?.items) {
      return playgroundInfos;
    }
    
    for (const item of taskStatus.items) {
      // 🔧 新架构：每个BackgroundTaskItem都有独立的result，直接提取playgroundId
      if (item.result?.playgroundId) {
        const pageName = item.result?.page || item.result?.metadata?.pageName || item.result?.metadata?.page?.name  || `页面-${item.id}`;
        
        this.logger.log(`📋 [TaskWatcher] 从独立页面任务中提取playground信息: 页面="${pageName}", playgroundId="${item.result.playgroundId}"`);
        
        playgroundInfos.push({
          pageName: pageName,
          playgroundId: item.result.playgroundId,
          taskId: item.taskId // 任务ID
        });
      }
      // 兼容旧架构：处理spec-to-prod-code任务的特殊结构（多页面在一个item中）
      else if (item.result?.result?.results && Array.isArray(item.result.result.results)) {
        this.logger.log(`🔍 [TaskWatcher] 从spec-to-prod-code任务中提取playground信息，发现 ${item.result.result.results.length} 个页面结果`);
        
        item.result.result.results.forEach((pageResult: any) => {
          // 包含所有有playgroundId的页面，不管成功还是失败
          if (pageResult.playgroundId) {
            this.logger.log(`📋 [TaskWatcher] 提取页面playground信息: 页面="${pageResult.page}", playgroundId="${pageResult.playgroundId}"`);
            
            playgroundInfos.push({
              pageName: pageResult.page,
              playgroundId: pageResult.playgroundId,
              taskId: item.taskId // 任务ID
            });
          } else {
            this.logger.warn(`⚠️ [TaskWatcher] 页面 "${pageResult.page}" 没有playgroundId，可能处理失败`);
          }
        });
      }

    }
    
    this.logger.log(`✅ [TaskWatcher] 总共提取到 ${playgroundInfos.length} 个playground信息`);
    return playgroundInfos;
  }

  /**
   * 仅等待任务完成（不获取文件）
   * @param taskId 任务ID
   * @param options 选项
   * @returns 任务完成状态
   */
  async waitForTaskCompletion(
    taskId: string,
    options: {
      maxAttempts?: number;
      pollInterval?: number;
      resetState?: boolean; // 是否重置检查状态，默认true
    } = {}
  ): Promise<{
    taskId: string;
    taskCompleted: boolean;
    playgroundId?: string;
    processingTime: number;
    finalStatus?: any;
  }> {
    this.logger.log(`🚀 开始等待任务完成 (任务ID: ${taskId})`);
    
    // 使用通用检查方法，每次调用都重置检查次数
    const checkResult = await this.checkTaskStatusWithReset(taskId, {
      maxAttempts: options.maxAttempts,
      pollInterval: options.pollInterval,
      resetState: options.resetState !== false, // 默认重置状态
    });
    
    return {
      taskId,
      taskCompleted: checkResult.completed,
      playgroundId: checkResult.playgroundId,
      processingTime: checkResult.processingTime,
      finalStatus: checkResult.status,
    };
  }

  /**
   * 强制重置任务检查状态
   * 供外部调用，用于在开始新的AI任务前主动重置检查次数
   * @param taskId 任务ID
   * @param reason 重置原因
   */
  async forceResetTaskCheckState(taskId: string, reason: string = '外部调用重置'): Promise<void> {
    this.resetTaskCheckState(taskId, reason);
    this.logger.log(`🔄 外部调用重置任务检查状态 (任务ID: ${taskId}) - 原因: ${reason}`);
  }

  /**
   * 获取playground中的所有文件
   * @param playgroundId playground ID
   * @returns 文件内容字典
   */
  private async getPlaygroundFiles(playgroundId: string): Promise<Record<string, any>> {
    const fileContents = {};
    
    // AI对话完成后，等待文件生成的延迟机制
    this.logger.log(`⏳ 等待文件生成完成...`);
    await this.waitForFileGeneration(playgroundId);
    
    // 使用service方法获取工作区中的所有文件列表
    const filesToProcess = await this.taskWorkerService.listPlaygroundFiles(playgroundId);
    this.logger.log(`📂 找到工作区文件 ${filesToProcess.length} 个`);
    
    // 获取所有文件内容
    for (const file of filesToProcess) {
      try {
        const fileResult = await this.taskWorkerService.getPlaygroundFile(playgroundId, file.path);
        // 为每个文件生成预览地址
        const previewUrl = `/api/background-tasks/download/${playgroundId}/${file.path}`;
        
        fileContents[file.path] = {
          content: fileResult.content,
          contentType: fileResult.contentType,
          path: file.path,
          previewUrl: previewUrl // 新增预览地址字段
        };
      } catch (fileError) {
        this.logger.warn(`⚠️ 获取文件 ${file.path} 失败:`, fileError.message);
      }
    }
    
    return fileContents;
  }

  /**
   * 等待文件生成完成
   * AI对话完成后，可能需要一些时间来生成和写入文件到playground
   * @param playgroundId playground ID
   */
  private async waitForFileGeneration(playgroundId: string): Promise<void> {
    const maxWaitTime = 30000; // 最大等待30秒
    const pollInterval = 500; // 每500ms检查一次
    const maxAttempts = maxWaitTime / pollInterval;
    const startTime = Date.now();
    
    let lastFileCount = 0;
    let stableCount = 0; // 文件数量稳定次数
    const requiredStableChecks = 3; // 需要连续3次检查文件数量相同才认为稳定
    
    this.logger.log(`🔍 开始监控文件生成 (playground: ${playgroundId})`);
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        // 获取当前文件列表
        const currentFiles = await this.taskWorkerService.listPlaygroundFiles(playgroundId);
        const currentFileCount = currentFiles.length;
        
        this.logger.log(`📊 文件检查 ${attempt}/${maxAttempts}: 当前${currentFileCount}个文件`);
        
        // 如果有文件且文件数量稳定，认为生成完成
        if (currentFileCount > 0) {
          if (currentFileCount === lastFileCount) {
            stableCount++;
            this.logger.log(`✅ 文件数量稳定 ${stableCount}/${requiredStableChecks} 次`);
            
            if (stableCount >= requiredStableChecks) {
              const waitTime = Date.now() - startTime;
              this.logger.log(`🎉 文件生成完成! 等待时间: ${waitTime}ms, 文件数量: ${currentFileCount}`);
              return;
            }
          } else {
            // 文件数量发生变化，重置稳定计数
            stableCount = 0;
            this.logger.log(`🔄 文件数量变化: ${lastFileCount} -> ${currentFileCount}, 重置稳定计数`);
          }
          lastFileCount = currentFileCount;
        } else {
          // 还没有文件，重置计数
          stableCount = 0;
          lastFileCount = 0;
        }
        
        // 等待下一次检查
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
        }
      } catch (error) {
        this.logger.warn(`⚠️ 文件检查失败 (attempt ${attempt}):`, error.message);
        
        // 如果是文件访问错误，继续尝试
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
        }
      }
    }
    
    // 如果超时但有文件，仍然继续
    const finalFiles = await this.taskWorkerService.listPlaygroundFiles(playgroundId);
    const totalWaitTime = Date.now() - startTime;
    
    if (finalFiles.length > 0) {
      this.logger.log(`⚠️ 文件生成等待超时，但找到 ${finalFiles.length} 个文件 (等待时间: ${totalWaitTime}ms)`);
    } else {
      this.logger.warn(`❌ 文件生成等待超时且未找到文件 (等待时间: ${totalWaitTime}ms)`);
    }
  }

  /**
   * 检查单个任务的状态（不等待）
   * @param taskId 任务ID
   * @returns 任务状态
   */
  async getTaskStatus(taskId: string): Promise<any> {
    try {
      return await this.taskManager.getTaskStatus(taskId);
    } catch (error) {
      this.logger.error(`❌ 获取任务状态失败:`, error);
      throw new Error(`获取任务状态失败: ${error.message}`);
    }
  }

  /**
   * 获取任务检查统计信息（用于调试）
   * @param taskId 任务ID
   * @returns 检查统计信息
   */
  async getTaskCheckStats(taskId: string): Promise<{
    taskId: string;
    totalChecks: number;
    lastResetTime: number;
    isActive: boolean;
  } | null> {
    const state = this.taskCheckState.get(taskId);
    if (!state) {
      return null;
    }
    
    return {
      taskId,
      totalChecks: state.totalChecks,
      lastResetTime: state.lastResetTime,
      isActive: true
    };
  }
}