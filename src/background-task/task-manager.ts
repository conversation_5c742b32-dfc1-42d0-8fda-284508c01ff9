import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import {
  BackgroundTaskConfig,
  BackgroundTaskItem,
  BackgroundTaskExecutor,
  TaskStatus,
  TaskItemStatus,
  BackgroundTaskProgress,
} from './index';

/**
 * 任务管理器服务
 * 负责创建、更新、查询后台任务
 */
@Injectable()
export class TaskManager {
  private readonly logger = new Logger(TaskManager.name);
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 创建新任务
   * @param config 任务配置
   * @param items 任务项列表
   * @returns 任务ID
   */
  async createTask(
    config: BackgroundTaskConfig,
    items: BackgroundTaskItem[]
  ): Promise<string> {
    // 生成任务ID
    const taskId = generateId();

    // 创建任务记录
    await this.prisma.backgroundTask.create({
      data: {
        id: taskId,
        taskType: config.taskType,
        taskName: config.taskName,
        designProjectId: config.metadata?.projectId,
        user: config.user,
        status: 'pending',
        progress: 0,
        model: config.metadata?.model,
        enableAutoIteration: config.metadata?.enableAutoIteration,
        enableStepByStep: config.metadata?.enableStepByStep,
        tags: config.metadata?.tags || [], // 添加标签支持
        metadata: config.metadata, // 保存完整的元数据
      }
    });

    // 创建任务项记录
    for (const item of items) {
      await this.prisma.backgroundTaskItem.create({
        data: {
          id: generateId(),
          backgroundTaskId: taskId,
          itemId: item.id,
          itemName: item.name,
          status: 'pending',
          progress: 0,
          stage: '等待开始',
          metadata: item.metadata, // 保存任务项的元数据
        }
      });
    }

    return taskId;
  }

  /**
   * 更新任务状态
   * @param taskId 任务ID
   * @param status 状态
   * @param progress 进度
   * @param metadata 元数据
   */
  async updateTaskStatus(
    taskId: string,
    status: string,
    progress: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.prisma.backgroundTask.update({
      where: { id: taskId },
      data: {
        status,
        progress,
      }
    });
  }

  /**
   * 更新任务项状态
   * @param taskId 任务ID
   * @param itemId 任务项ID
   * @param status 状态
   * @param progress 进度
   * @param stage 阶段
   * @param result 结果
   * @param error 错误信息
   */
  async updateTaskItemStatus(
    taskId: string,
    itemId: string,
    status: string,
    progress?: number,
    stage?: string,
    result?: Record<string, any>,
    error?: string
  ): Promise<void> {
    // 查找任务项记录
    const taskItem = await this.prisma.backgroundTaskItem.findFirst({
      where: {
        backgroundTaskId: taskId,
        itemId: itemId
      }
    });
  
    if (taskItem) {
      // 提取playgroundId，支持多种结构
      let playgroundId = result?.playgroundId || result?.metadata?.playgroundId;
      
      // 特殊处理spec-to-prod-code任务 - 从result.result.playgroundIds数组中提取第一个
      if (!playgroundId && result?.result?.playgroundIds && Array.isArray(result.result.playgroundIds)) {
        playgroundId = result.result.playgroundIds[0]; // 取第一个作为主要playground
        this.logger.log(`🎯 从spec-to-prod-code任务结果中提取playgroundId: ${playgroundId}`);
      }
      
      // 也检查result.result.results数组中的第一个成功页面
      if (!playgroundId && result?.result?.results && Array.isArray(result.result.results)) {
        const firstSuccessPage = result.result.results.find((page: any) => page.success && page.playgroundId);
        if (firstSuccessPage) {
          playgroundId = firstSuccessPage.playgroundId;
          this.logger.log(`🎯 从spec-to-prod-code页面结果中提取playgroundId: ${playgroundId}`);
        }
      }
      
      // 如果仍然没有找到，尝试从失败页面中提取（失败页面也可能有playgroundId）
      if (!playgroundId && result?.result?.results && Array.isArray(result.result.results)) {
        const firstFailedPageWithPlayground = result.result.results.find((page: any) => !page.success && page.playgroundId);
        if (firstFailedPageWithPlayground) {
          playgroundId = firstFailedPageWithPlayground.playgroundId;
          this.logger.log(`🎯 从spec-to-prod-code失败页面结果中提取playgroundId: ${playgroundId}`);
        }
      }
      
      // 更新任务项状态
      await this.prisma.backgroundTaskItem.update({
        where: { id: taskItem.id },
        data: {
          status,
          progress: progress ?? taskItem.progress,
          stage: stage ?? taskItem.stage,
          result: result ?? taskItem.result,
          error: error ?? taskItem.error,
          playgroundId: playgroundId ?? taskItem.playgroundId, // 保留现有playgroundId如果没有新的
          updated: new Date()
        }
      });
      
      this.logger.log(`📊 任务项状态更新: ${itemId} -> ${status}`);
      if (playgroundId) {
        this.logger.log(`🎯 任务项playgroundId: ${playgroundId}`);
      }
    } else {
      this.logger.warn(`⚠️ 任务项不存在: ${itemId} in task ${taskId}`);
    }
  }

  /**
   * 更新任务进度
   * @param taskId 任务ID
   * @param progress 进度信息
   */
  async updateProgress(
    taskId: string,
    itemId: string,
    progress: BackgroundTaskProgress
  ): Promise<void> {
    // 更新任务项进度
    await this.updateTaskItemStatus(
      taskId,
      itemId,
      'processing',
      progress.progress,
      progress.stage
    );

    // 计算总体进度
    const taskItems = await this.prisma.backgroundTaskItem.findMany({
      where: { backgroundTaskId: taskId }
    });

    // 计算平均进度（基础进度10% + 实际任务进度90%）
    const itemsProgress = taskItems.reduce((sum, item) => sum + (item.progress || 0), 0) / taskItems.length;
    const totalProgress = Math.round(10 + (itemsProgress * 0.9));

    // 更新任务总进度（确保不超过99%，最终100%由任务完成时设置）
    const finalProgress = Math.min(totalProgress, 99);
    await this.updateTaskStatus(taskId, 'processing', finalProgress);
  }

  /**
   * 获取任务状态
   * @param taskId 任务ID
   * @returns 任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatus> {
    try {
      // 尝试从transcodeTask表获取
      const task = await this.prisma.backgroundTask.findUnique({
        where: { id: taskId },
        include: {
          items: true,
        }
      });

      if (!task) {
        throw new Error(`任务不存在: ${taskId}`);
      }

      // 查找第一个失败项目的错误信息
      const errorItem = task.items.find(item => item.status === 'failed' && item.error);
      const errorMessage = errorItem ? errorItem.error : null;

      // 提取playgroundId
      const playgroundId = this.extractPlaygroundId(task.items);

      // 返回任务状态信息（移除文件内容获取）
      return {
        id: task.id,
        status: task.status,
        progress: task.progress,
        completedAt: task.updated,
        createdAt: task.created,
        updatedAt: task.updated,
        error: errorMessage,
        playgroundId,
        metadata: {
          projectId: task.designProjectId,
          model: task.model,
          enableAutoIteration: task.enableAutoIteration,
          enableStepByStep: task.enableStepByStep,
        },
        items: task.items.map((item) => {
          // 直接返回任务项状态，不获取文件内容
          return this.mapTaskItemToStatus(item);
        }),
      };
    } catch (error) {
      this.logger.error(`❌ 获取任务状态失败:`, error);
      throw new Error(`获取任务状态失败: ${error.message}`);
    }
  }

  /**
   * 执行任务
   * @param config 任务配置
   * @param items 任务项列表
   * @param executor 任务执行器
   * @returns 任务ID
   */
  async executeTask(
    config: BackgroundTaskConfig,
    items: BackgroundTaskItem[],
    executor: BackgroundTaskExecutor
  ): Promise<string> {
    // 创建任务
    const taskId = await this.createTask(config, items);

    // 异步执行任务
    this.processTask(taskId, config, items, executor).catch(error => {
      this.logger.error(`❌ 任务执行失败 (${taskId}):`, error.stack);
      this.updateTaskStatus(taskId, 'failed', 0);
    });

    return taskId;
  }

   /**
   * 处理任务（内部方法）
   */
   private async processTask(
    taskId: string,
    config: BackgroundTaskConfig,
    items: BackgroundTaskItem[],
    executor: BackgroundTaskExecutor
  ): Promise<void> {
    try {
      // 更新任务状态为进行中
      await this.updateTaskStatus(taskId, 'processing', 0);

      // 验证任务项
      await executor.validateItems(items);

      // 执行任务开始钩子
      await executor.onTaskStart(taskId, config);

      // 更新任务进度
      await this.updateTaskStatus(taskId, 'processing', 10);

      // 创建任务执行上下文
      const context = {
        taskId,
        startTime: new Date(),
        user: config.user
      };

      this.logger.log(`🚀 开始并行处理 ${items.length} 个任务项`);

      // 创建并行处理的Promise数组
      const taskPromises = items.map(async (item, index) => {
        try {
          this.logger.log(`🔄 启动任务项 ${index + 1}/${items.length}: ${item.name}`);

          // 更新任务项状态
          await this.updateTaskItemStatus(taskId, item.id, 'processing');

          // 创建进度更新函数
          const updateProgress = async (progress: BackgroundTaskProgress) => {
            await this.updateProgress(taskId, item.id, progress);
          };

          // 执行任务项
          const result = await executor.executeItem(item, config, context, updateProgress);

          // 更新任务项状态
          if (result.success) {
            await this.updateTaskItemStatus(
              taskId,
              item.id,
              'completed',
              100,
              '完成',
              result
            );
            this.logger.log(`✅ 任务项完成: ${item.name}`);
          } else {
            await this.updateTaskItemStatus(
              taskId,
              item.id,
              'failed',
              0,
              '失败',
              result,
              result.error
            );
            this.logger.log(`❌ 任务项失败: ${item.name} - ${result.error}`);
          }

          return result;

        } catch (error) {
          this.logger.error(`❌ 处理任务项 ${item.name} 失败:`, error);

          // 更新任务项状态为失败
          await this.updateTaskItemStatus(
            taskId,
            item.id,
            'failed',
            0,
            '失败',
            undefined,
            error.message
          );

          // 返回失败结果
          return {
            success: false,
            error: error.message,
          };
        }
      });

      // 等待所有任务项完成
      this.logger.log(`⏳ 等待所有任务项完成...`);
      const results = await Promise.all(taskPromises);

      // 判断整体任务是否成功
      const successCount = results.filter(r => r.success).length;
      const allSuccess = results.every(r => r.success);

      this.logger.log(`📊 任务执行结果: ${successCount}/${results.length} 成功`);

      // 更新任务状态
      const finalProgress = allSuccess ? 100 : Math.round((successCount / results.length) * 100);
      await this.updateTaskStatus(
        taskId,
        allSuccess ? 'completed' : 'failed',
        finalProgress
      );

      // 执行任务完成钩子
      await executor.onTaskComplete(taskId, results, config);

      this.logger.log(`✅ 任务完成 (${taskId}): ${successCount}/${results.length} 成功`);

    } catch (error) {
      this.logger.error(`❌ 任务执行失败 (${taskId}):`, error);

      // 更新任务状态为失败
      await this.updateTaskStatus(taskId, 'failed', 0);

      throw error;
    }
  }

  /**
   * 获取用户的所有任务
   * @param user 用户ID
   * @param taskType 任务类型（可选）
   * @returns 任务状态列表
   */
  async getUserTasks(user: string, taskType?: string): Promise<TaskStatus[]> {
    try {
      // 构建查询条件
      const where: any = { user };

      // 如果指定了任务类型，添加到查询条件
      if (taskType) {
        where.taskType = taskType; // 直接使用taskType字段而不是metadata
      }

      // 查询任务列表
      const tasks = await this.prisma.backgroundTask.findMany({
        where,
        include: {
          items: true,
        },
        orderBy: {
          created: 'desc',
        },
      });

      // 转换为TaskStatus格式
      return tasks.map(task => {
        const playgroundId = this.extractPlaygroundId(task.items);
        const allPlaygroundInfo = this.extractAllPlaygroundInfo(task.items);

        return {
          id: task.id,
          taskName: task.taskName,
          taskType: task.taskType,
          status: task.status,
          progress: task.progress,
          completedAt: task.status === 'completed' ? task.updated : undefined,
          createdAt: task.created,
          updatedAt: task.updated,
          playgroundId,
          allPlaygroundInfo, // 新增：返回所有页面的playground信息
          metadata: {
            projectId: task.designProjectId,
            model: task.model,
            enableAutoIteration: task.enableAutoIteration,
            enableStepByStep: task.enableStepByStep,
          },
          items: task.items.map(item => this.mapTaskItemToStatus(item)),
        };
      });
    } catch (error) {
      this.logger.error(`❌ 获取用户任务列表失败:`, error.stack);
      return [];
    }
  }

  /**
   * 将任务项映射为状态对象
   */
  private mapTaskItemToStatus(item: any): TaskItemStatus {
    return {
      id: item.itemId,
      status: item.status,
      result: item.result || (item.playgroundId ? { playgroundId: item.playgroundId } : undefined),
      error: item.error || null,
      metadata: {
        playgroundId: item.playgroundId || null,
        progress: item.progress,
        stage: item.stage,
      },
    };
  }

  /**
   * 从任务项中提取playgroundId - 优化版本
   */
  private extractPlaygroundId(items: any[]): string | null {
    if (!items || items.length === 0) {
      return null;
    }
  
    // 尝试从第一个完成的项目中获取playgroundId
    const completedItem = items.find(item => item.status === 'completed' && item.playgroundId);
    if (completedItem && completedItem.playgroundId) {
      return completedItem.playgroundId;
    }
  
    // 如果没有完成的项目，尝试从第一个处理中的项目获取
    const processingItem = items.find(item => item.status === 'processing' && item.playgroundId);
    if (processingItem && processingItem.playgroundId) {
      return processingItem.playgroundId;
    }
  
    // 特殊处理：spec-to-prod-code任务的playground信息在result.result.playgroundIds中
    for (const item of items) {
      if (item.result?.result?.playgroundIds && Array.isArray(item.result.result.playgroundIds)) {
        return item.result.result.playgroundIds[0]; // 返回第一个playgroundId
      }
      
      // 也检查result.metadata.playgroundIds
      if (item.result?.metadata?.playgroundIds && Array.isArray(item.result.metadata.playgroundIds)) {
        return item.result.metadata.playgroundIds[0];
      }
    }
  
    return null;
  }

  /**
   * 提取所有页面的playground信息 - 新增方法
   */
  private extractAllPlaygroundInfo(items: any[]): Array<{pageName: string, playgroundId: string, taskId?: string}> {
    const playgroundInfos: Array<{pageName: string, playgroundId: string, taskId?: string}> = [];
    
    if (!items || items.length === 0) {
      return playgroundInfos;
    }
  
    for (const item of items) {
      // 🔧 新架构：每个BackgroundTaskItem都有独立的result，直接提取playgroundId
      if (item.result?.playgroundId) {
        const pageName = item.result?.page || item.result?.metadata?.pageName || item.result?.metadata?.page?.name || `页面-${item.itemId}`;
        
        this.logger.log(`📋 [TaskManager] 从独立页面任务中提取playground信息: 页面="${pageName}", playgroundId="${item.result.playgroundId}"`);
        
        playgroundInfos.push({
          pageName: pageName,
          playgroundId: item.result.playgroundId,
          taskId: item.itemId
        });
      }
      // 兼容旧架构：处理spec-to-prod-code任务的特殊结构（多页面在一个item中）
      else if (item.result?.result?.results && Array.isArray(item.result.result.results)) {
        this.logger.log(`🔍 [TaskManager] 从spec-to-prod-code任务中提取playground信息，发现 ${item.result.result.results.length} 个页面结果`);
        
        item.result.result.results.forEach((pageResult: any) => {
          // 包含所有有playgroundId的页面，不管成功还是失败
          if (pageResult.playgroundId) {
            this.logger.log(`📋 [TaskManager] 提取页面playground信息: 页面="${pageResult.page}", playgroundId="${pageResult.playgroundId}"`);
            
            playgroundInfos.push({
              pageName: pageResult.page,
              playgroundId: pageResult.playgroundId,
              taskId: item.itemId
            });
          } else {
            this.logger.warn(`⚠️ [TaskManager] 页面 "${pageResult.page}" 没有playgroundId，可能处理失败`);
          }
        });
      }

    }
    
    this.logger.log(`✅ [TaskManager] 总共提取到 ${playgroundInfos.length} 个playground信息`);
    return playgroundInfos;
  }
}