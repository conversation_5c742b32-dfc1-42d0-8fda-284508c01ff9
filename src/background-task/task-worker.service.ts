import type { Project } from '@prisma/client';
import { EventEmitter } from 'events';
import { generateId } from '@ai-sdk/ui-utils';
import { Injectable, Logger } from '@nestjs/common';
import { chat } from '../ai-coding/playground/ai';
import { createPlayground, startPlayground } from '../ai-coding/playground/index';
import { PrismaService } from '../prisma/prisma.service';
import { ProcessManagerService, PostChatCommand } from './process-manager.service';

/**
 * 通用任务执行器服务
 * 封装通用的任务执行逻辑，如创建playground、执行AI对话等
 */
@Injectable()
export class TaskWorkerService {
  // 用于跟踪每个AI会话的独立进度状态
  private dialogProgressState: Map<string, { attempts: number; startTime: number }> = new Map();
  private readonly logger = new Logger(TaskWorkerService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly processManager: ProcessManagerService
  ) { }

  /**
   * 创建Playground工作区
   * @param options 创建选项
   * @returns Playground ID
   */
  async createPlayground(options: {
    name: string;
    desc: string;
    user: string;
    model?: string;
    projectId?: string;
    isPublic?: boolean;
    enableAutoIteration?: boolean;
    enableStepByStep?: boolean;
    type?: string; // 新增：允许指定 Playground 类型
    enableCustomPreview?: boolean;
    tags?: string[]; // 自定义标签
  }): Promise<string> {
    // 生成安全的playground名称
    const safeName = options.name
      .replace(/[^a-zA-Z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .toLowerCase();

    const playgroundData = {
      id: generateId(),
      model: options.model || 'htsc::saas-deepseek-v3',
      desc: options.desc,
      name: safeName,
      projectId: options.projectId,
      user: options.user,
      isPublic: options.isPublic !== false,
      type: options.type || 'html', // 使用传入的类型，默认为 'html'
      enableAutoIteration: options.enableAutoIteration || false,
      enableStepByStep: options.enableStepByStep || false,
    };

    this.logger.log(`🏗️ 创建playground文件系统 (${playgroundData.id})`);

    const playground = await createPlayground(playgroundData);

    this.logger.log(`🚀 启动playground工作区 (${playground.id})`);

    try {
      await startPlayground(playground.id);
      this.logger.log(`✅ Playground工作区启动成功 (${playground.id})`);
    } catch (error) {
      this.logger.error(`❌ Playground工作区启动失败 (${playground.id}):`, error);
      throw new Error(`Playground启动失败: ${error.message || error}`);
    }

    // 保存playground信息到数据库
    await this.prisma.playground.create({
      data: {
        id: playground.id,
        projectId: options.projectId || null, // 项目id需要落库
        type: options.type || 'html', // 确保数据库中的类型也正确
        desc: playgroundData.desc,
        name: playgroundData.name,
        user: options.user,
        model: options.model || 'htsc::saas-deepseek-v3',
        isPublic: options.isPublic !== false,
        enableAutoIteration: options.enableAutoIteration || false,
        enableStepByStep: options.enableStepByStep || false,
        enableCustomPreview: options.enableCustomPreview || false,
        tags: options.tags || [],
      },
    });

    return playground.id;
  }

  /**
   * 创建用户消息
   * @param playgroundId Playground ID
   * @param content 消息内容
   * @param attachments 附件
   * @param taskId 关联的后台任务ID（可选）
   * @returns 消息ID
   */
  async createUserMessage(
    playgroundId: string,
    content: string,
    attachments?: any[],
    taskId?: string
  ): Promise<string> {
    const messageId = `${playgroundId}::${generateId()}`;

    await this.prisma.chatMessage.create({
      data: {
        id: messageId,
        playgroundId: playgroundId,
        role: 'USER',
        content: content,
        attachments: attachments || [],
        status: 'completed', // 用户消息默认为完成状态
        taskId: taskId, // 关联后台任务ID
      },
    });

    return messageId;
  }

  /**
   * 准备文件附件
   * @param files 文件列表
   * @returns 附件列表
   */
  prepareFileAttachments(files: Array<{
    name: string;
    content: string;
    contentType: string;
  }>): any[] {
    return files.map(file => {
      this.logger.log(`📎 准备文件附件:`, {
        name: file.name,
        contentType: file.contentType,
        contentLength: file.content.length,
        isImageType: file.contentType.startsWith('image/'),
      });

      // 检查内容是否是 base64 编码的
      const isBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(file.content) &&
        file.content.length > 0 &&
        file.content.length % 4 === 0;

      this.logger.log(`🔍 Base64检测结果:`, {
        fileName: file.name,
        isBase64,
        contentPreview: file.content.substring(0, 50) + '...'
      });

      // 对于图片类型，直接使用base64编码的data URL
      if (file.contentType.startsWith('image/')) {
        if (isBase64) {
          // 已经是 Base64 格式，直接使用
          return {
            name: file.name,
            contentType: file.contentType,
            url: `data:${file.contentType};base64,${file.content}`,
          };
        } else {
          // 不是 Base64 格式，进行编码
          return {
            name: file.name,
            contentType: file.contentType,
            url: `data:${file.contentType};base64,${Buffer.from(file.content).toString('base64')}`,
          };
        }
      }
      // 对于文本文件，处理方式有两种：
      // 1. 如果用于下载，需要解码后的原始内容
      // 2. 如果用于传给模型，需要原始内容然后让模型自己处理编码
      else {
        let originalContent = file.content;

        // 如果内容是base64编码的，先解码获取原始文本内容
        if (isBase64) {
          try {
            originalContent = Buffer.from(file.content, 'base64').toString('utf-8');
            this.logger.log(`🔄 解码Base64文本文件: ${file.name}`);
          } catch (error) {
            this.logger.log(`⚠️ Base64解码失败，使用原始内容: ${file.name}`);
            originalContent = file.content;
          }
        }

        // 返回两种格式的URL：
        // 1. 用于下载的URL（使用URL编码，不使用base64）
        // 2. 用于传给模型的URL（使用dataUrl函数的格式）

        // 引入dataUrl函数
        const { dataUrl } = require('../ai-coding/playground/utils/file');

        return {
          name: file.name,
          contentType: file.contentType,
          // 使用dataUrl函数创建正确格式的data URL（模型期望的格式）
          url: dataUrl(originalContent, file.contentType),
          // 同时提供下载用的URL（如果需要的话）
          downloadUrl: `data:${file.contentType};charset=utf-8,${encodeURIComponent(originalContent)}`,
        };
      }
    });
  }

  /**
   * 执行AI对话
   * @param playgroundId Playground ID
   * @param options 选项
   */
  async executeAIDialog(
    playgroundId: string,
    options: {
      projectId?: string;
      enableAutoIteration?: boolean;
      enableStepByStep?: boolean;
      maxWaitTime?: number; // 最大等待时间（毫秒）
      pollInterval?: number; // 轮询间隔（毫秒）
      dialogId?: string; // AI会话标识符，用于独立进度跟踪
      postChatCommands?: PostChatCommand[]; // chat完成后要执行的命令
    } = {}
  ): Promise<void> {
    // 为每个AI会话生成唯一标识符，避免进度累积
    const dialogId = options.dialogId || `ai-dialog-${playgroundId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    this.logger.log(`🤖 开始执行AI对话 (playground: ${playgroundId}, dialogId: ${dialogId})`);
    this.logger.log(`🔧 AI对话配置:`, {
      projectId: options.projectId,
      enableAutoIteration: options.enableAutoIteration,
      enableStepByStep: options.enableStepByStep,
      maxWaitTime: options.maxWaitTime || 480000,
      pollInterval: options.pollInterval || 3000
    });

    try {
      // 记录开始时的消息数量
      const initialMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });

      this.logger.log(`📊 AI会话开始时消息数量: ${initialMessageCount} (dialogId: ${dialogId})`);

      // 注释：AI占位符消息的创建逻辑已移至ai.ts的chat函数中
      // ai.ts会在用户消息保存后自动创建AI占位符消息，无需在此处重复创建

      // 获取最后几条消息用于调试
      const recentMessages = await this.prisma.chatMessage.findMany({
        where: { playgroundId: playgroundId },
        orderBy: { created: 'desc' },
        take: 3
      });

      this.logger.log(`📋 最近的消息:`, recentMessages.map(msg => ({
        role: msg.role,
        contentLength: msg.content?.length || 0,
        created: msg.created
      })));

      // 执行AI对话请求
      this.logger.log(`🚀 开始执行AI对话请求... (dialogId: ${dialogId})`);
      await this.executeAIDialogRequest(playgroundId, options);
      this.logger.log(`✅ AI对话请求执行完成，开始等待回复... (dialogId: ${dialogId})`);

      // 等待AI完成回复，使用独立的进度跟踪
      await this.waitForAIResponse(playgroundId, {
        initialMessageCount,
        maxWaitTime: options.maxWaitTime || 480000, // 默认8分钟，考虑一次ai会话任务继续执行自检查的场景
        pollInterval: options.pollInterval || 3000, // 默认3秒
        dialogId: dialogId // 传递会话标识符
      });

      this.logger.log(`🎉 AI对话执行完成 (playground: ${playgroundId}, dialogId: ${dialogId})`);

      // 最终验证：检查是否真的有AI回复
      const finalMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });
      const finalLatestMessage = await this.prisma.chatMessage.findFirst({
        where: { playgroundId: playgroundId },
        orderBy: { created: 'desc' }
      });

      this.logger.log(`🔍 最终验证: 消息数从${initialMessageCount}增加到${finalMessageCount}, 最新消息role=${finalLatestMessage?.role} (dialogId: ${dialogId})`);

      // 执行post-chat命令
      if (options.postChatCommands && options.postChatCommands.length > 0) {
        this.logger.log(`🚀 开始执行post-chat命令 (${options.postChatCommands.length}个命令)`);

        try {
          // 获取playground信息以获取用户ID
          const playground = await this.prisma.playground.findUnique({
            where: { id: playgroundId }
          });

          if (playground) {
            const processIds = await this.processManager.executePostChatCommands(
              playgroundId,
              dialogId, // 使用dialogId作为taskId
              options.postChatCommands,
              playground.user
            );

            this.logger.log(`✅ Post-chat命令执行完成，启动了${processIds.length}个进程: ${processIds.join(', ')}`);
          } else {
            this.logger.error(`❌ 无法找到playground信息，跳过post-chat命令执行`);
          }
        } catch (error) {
          this.logger.error(`❌ Post-chat命令执行失败:`, error);
          // 不抛出错误，避免影响主流程
        }
      }

    } catch (error) {
      this.logger.error(`❌ AI对话执行失败 (playground: ${playgroundId}, dialogId: ${dialogId}):`, error);

      // 失败时查找并更新任何processing状态的AI消息为失败状态
      try {
        const processingMessages = await this.prisma.chatMessage.findMany({
          where: {
            playgroundId: playgroundId,
            role: 'ASSISTANT',
            status: 'processing'
          }
        });

        for (const processingMessage of processingMessages) {
          await this.prisma.chatMessage.update({
            where: { id: processingMessage.id },
            data: {
              status: 'failed',
              content: `AI对话执行失败: ${error.message}`
            }
          });
          this.logger.log(`❌ 更新AI消息状态为失败: ${processingMessage.id}`);
        }
      } catch (updateError) {
        this.logger.error(`❌ 更新失败消息状态时出错:`, updateError);
      }

      // 失败时也记录当前状态
      const errorMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });
      this.logger.log(`💥 失败时消息数量: ${errorMessageCount} (dialogId: ${dialogId})`);

      throw error;
    }
  }

  /**
   * 执行AI对话请求（不等待回复）
   * @param playgroundId Playground ID
   * @param options 选项
   */
  private async executeAIDialogRequest(
    playgroundId: string,
    options: {
      projectId?: string;
      enableAutoIteration?: boolean;
      enableStepByStep?: boolean;
    } = {}
  ): Promise<void> {
    const playground = await this.prisma.playground.findUnique({
      where: { id: playgroundId }
    });

    if (!playground) {
      throw new Error(`Playground ${playgroundId} 不存在`);
    }

    // 获取项目信息
    const project: Project = {
      id: options.projectId || generateId(),
      name: playground.name || 'AI Project',
      description: playground.desc || 'AI生成项目',
      framework: playground.type || 'html', // 🔧 修复：使用playground.type而不是硬编码'html'
      componentLibrary: playground.type === 'react' ? 'antd' : 'vanilla', // 根据类型设置默认组件库
      llmstxt: '',
      created: new Date(),
      updated: new Date(),
      user: playground.user,
      projectId: null,
      config: null,
    } as Project;

    // 获取用户消息
    const existingMessages = await this.prisma.chatMessage.findMany({
      where: { playgroundId: playgroundId },
      orderBy: { created: 'asc' },
    });

    if (existingMessages.length === 0) {
      throw new Error(`Playground ${playgroundId} 中没有找到用户消息`);
    }

    // 使用最后一条用户消息
    const latestUserMessage = existingMessages[existingMessages.length - 1];
    const uiMessage = this.convertToUIMessage(latestUserMessage);

    // 确保消息时间是未来时间，避免排序问题
    const futureTime = new Date(Date.now() + 1000);
    uiMessage.createdAt = futureTime;

    const chatMsgDto = {
      id: playgroundId,
      message: uiMessage,
      enableAutoIteration: options.enableAutoIteration || playground.enableAutoIteration,
      enableStepByStep: options.enableStepByStep || playground.enableStepByStep,
    };

    const mockResponse = this.createMockServerResponse();


    this.logger.log(`🚀 调用AI工具类chat方法 (playground: ${playgroundId})`);

    // 💡 检测并提示占位消息机制
    if (uiMessage.role === 'assistant' && (!uiMessage.content || uiMessage.content.trim() === '')) {
      this.logger.log(`💡 检测到空占位消息 - 后台任务使用占位消息模拟前端页面loading效果，AI系统将基于完整对话历史生成回复`);
    }

    // 启动AI对话
    const chatPromise = new Promise<void>((resolve, reject) => {
      let isResolved = false;

      const finishHandler = () => {
        if (!isResolved) {
          isResolved = true;
          this.logger.log(`✅ AI工具类chat请求发送完成 (playground: ${playgroundId})`);
          resolve();
        }
      };

      const errorHandler = (error) => {
        if (!isResolved) {
          isResolved = true;
          // this.logger.log(`🎯 捕获到 mock response 的 error 事件 (playground: ${playgroundId})`);
          this.logger.error(`❌ AI工具类chat请求失败 (playground: ${playgroundId}):`, error);
          reject(error);
        }
      };

      try {
        mockResponse.once('finish', finishHandler);
        mockResponse.once('error', errorHandler);

        // 启动chat方法
        chat(chatMsgDto as any, playground, project, mockResponse as any);
      } catch (error) {
        if (!isResolved) {
          isResolved = true;
          reject(error);
        }
      }
    });

    // 等待chat方法完成（成功或失败）
    await chatPromise;

    this.logger.log(`🎯 AI对话请求已启动，开始等待AI回复... (playground: ${playgroundId})`);
  }

  /**
   * 等待AI完成回复
   * @param playgroundId Playground ID
   * @param options 等待选项
   */
  private async waitForAIResponse(
    playgroundId: string,
    options: {
      initialMessageCount: number;
      maxWaitTime: number;
      pollInterval: number;
      dialogId: string;
    }
  ): Promise<void> {
    this.logger.log(`⏳ 开始等待AI回复 (playground: ${playgroundId}, dialogId: ${options.dialogId})`);
    this.logger.log(`📊 等待参数: 初始消息数=${options.initialMessageCount}, 最大等待时间=${options.maxWaitTime}ms, 轮询间隔=${options.pollInterval}ms`);

    const startTime = Date.now();

    // 初始化当前会话的独立进度状态
    this.dialogProgressState.set(options.dialogId, {
      attempts: 0,
      startTime: startTime
    });

    const maxAttempts = Math.ceil(options.maxWaitTime / options.pollInterval);
    this.logger.log(`🎯 最大尝试次数: ${maxAttempts}`);

    try {
      while (true) {
        // 获取当前会话的进度状态
        const progressState = this.dialogProgressState.get(options.dialogId);
        if (!progressState) {
          throw new Error(`AI会话进度状态丢失: ${options.dialogId}`);
        }

        progressState.attempts++;

        // 检查是否超过最大尝试次数
        if (progressState.attempts > maxAttempts) {
          throw new Error(`AI会话超时 (尝试次数: ${progressState.attempts}/${maxAttempts}, dialogId: ${options.dialogId})`);
        }

        // 检查是否有新的AI消息
        const currentMessageCount = await this.prisma.chatMessage.count({
          where: { playgroundId: playgroundId }
        });

        this.logger.log(`🔍 第${progressState.attempts}次检查: 当前消息数=${currentMessageCount}, 初始消息数=${options.initialMessageCount} (dialogId: ${options.dialogId})`);

        if (currentMessageCount > options.initialMessageCount) {
          // 检查最新的消息是否是AI回复
          const latestMessage = await this.prisma.chatMessage.findFirst({
            where: { playgroundId: playgroundId },
            orderBy: { created: 'desc' }
          });

          this.logger.log(`📝 最新消息信息: role=${latestMessage?.role}, contentLength=${latestMessage?.content?.length || 0}, created=${latestMessage?.created} (dialogId: ${options.dialogId})`);

          if (latestMessage && latestMessage.role === 'ASSISTANT') {
            // 检查AI消息是否真正完成：必须有内容或状态为completed
            const hasContent = latestMessage.content && latestMessage.content.trim().length > 0;
            const isCompleted = latestMessage.status === 'completed';
            
            if (hasContent || isCompleted) {
              const waitTime = Date.now() - startTime;
              this.logger.log(`✅ AI回复完成 (playground: ${playgroundId}, dialogId: ${options.dialogId})`);
              this.logger.log(`📊 会话统计: 尝试次数=${progressState.attempts}, 等待时间=${waitTime}ms`);
              this.logger.log(`📝 AI回复内容长度: ${latestMessage.content?.length || 0} 字符`);
              this.logger.log(`🎉 AI回复内容预览: ${latestMessage.content?.substring(0, 200) || '(无内容)'}...`);

              // 清理旧的占位符消息（processing状态且content为空的消息）
              // 注释：与ai.ts的清理逻辑保持一致，ai.ts创建的占位符没有taskId
              const placeholderMessages = await this.prisma.chatMessage.findMany({
                where: {
                  playgroundId: playgroundId,
                  role: 'ASSISTANT',
                  status: 'processing',
                  NOT: {
                    id: latestMessage.id // 排除当前最新消息
                  }
                }
              });

              // 删除空的占位符消息
              for (const placeholder of placeholderMessages) {
                if (!placeholder.content || placeholder.content.trim() === '') {
                  await this.prisma.chatMessage.delete({
                    where: { id: placeholder.id }
                  });
                  this.logger.log(`🗑️ 删除占位符消息: ${placeholder.id}`);
                }
              }

              // 更新AI消息状态为完成
              if (latestMessage.status === 'processing') {
                await this.prisma.chatMessage.update({
                  where: { id: latestMessage.id },
                  data: { status: 'completed' }
                });
                this.logger.log(`✅ 更新AI消息状态为完成: ${latestMessage.id}`);
              }

              // AI回复完成后，给AI工具一些时间来生成和保存文件
              this.logger.log(`⏳ AI回复完成，等待文件生成处理...`);
              await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
              this.logger.log(`✅ 文件生成等待完成`);

              // 清理进度状态
              this.dialogProgressState.delete(options.dialogId);
              return;
            } else {
              // AI消息存在但还没有内容，继续等待
              this.logger.log(`⏳ AI消息存在但内容为空，继续等待... (playground: ${playgroundId}, dialogId: ${options.dialogId})`);
            }
          } else if (latestMessage) {
            this.logger.log(`⚠️ 检测到新消息但不是AI回复: role=${latestMessage.role} (dialogId: ${options.dialogId})`);
          }
        } else {
          this.logger.log(`ℹ️ 消息数量未增加，继续等待... (dialogId: ${options.dialogId})`);
        }

        // 每10次检查输出一次进度
        if (progressState.attempts % 10 === 0) {
          const waitTime = Date.now() - startTime;
          this.logger.log(`⏳ 等待AI回复中... (${progressState.attempts}/${maxAttempts}, 已等待: ${waitTime}ms, dialogId: ${options.dialogId})`);
        }

        // 等待下一次检查
        await new Promise(resolve => setTimeout(resolve, options.pollInterval));
      }
    } catch (error) {
      // 清理进度状态
      this.dialogProgressState.delete(options.dialogId);

      const totalWaitTime = Date.now() - startTime;
      this.logger.warn(`⚠️ 等待AI回复失败 (playground: ${playgroundId}, dialogId: ${options.dialogId}, 总等待时间: ${totalWaitTime}ms)`);

      // 检查是否至少有消息增加
      const finalMessageCount = await this.prisma.chatMessage.count({
        where: { playgroundId: playgroundId }
      });

      this.logger.log(`📊 失败时的消息统计: 最终消息数=${finalMessageCount}, 初始消息数=${options.initialMessageCount}, 新增消息=${finalMessageCount - options.initialMessageCount}`);

      if (finalMessageCount > options.initialMessageCount) {
        this.logger.log(`ℹ️ 虽然失败，但检测到有新消息产生 (${finalMessageCount - options.initialMessageCount} 条)`);

        // 获取所有新消息的详细信息
        const newMessages = await this.prisma.chatMessage.findMany({
          where: { playgroundId: playgroundId },
          orderBy: { created: 'desc' },
          take: finalMessageCount - options.initialMessageCount
        });

        this.logger.log(`📋 新消息详情:`, newMessages.map(msg => ({
          role: msg.role,
          contentLength: msg.content?.length || 0,
          created: msg.created
        })));
      }

      throw error;
    }
  }

  /**
   * 获取工作区文件列表
   * @param playgroundId Playground ID
   * @returns 文件列表
   */
  async listPlaygroundFiles(playgroundId: string): Promise<any[]> {
    try {
      this.logger.log(`🔍 开始获取工作区文件列表 (playground: ${playgroundId})`);

      // 导入playground文件列表工具
      const { listFiles } = require('../ai-coding/playground');

      // 获取工作区中的所有文件列表
      const allFiles = await listFiles(playgroundId);
      this.logger.log(`📂 原始文件列表数量: ${allFiles.length}`);

      // 记录详细的文件信息
      if (allFiles.length > 0) {
        this.logger.log(`📋 文件详情:`, allFiles.map(file => ({
          path: file.path,
          fileType: file.fileType,
          isFile: file.fileType === 1
        })));
      } else {
        this.logger.warn(`⚠️ 工作区中没有找到任何文件`);
      }

      // 只返回文件而非目录
      const filteredFiles = allFiles.filter(file => file.fileType === 1); // FileType.File = 1
      this.logger.log(`📁 过滤后文件数量: ${filteredFiles.length}`);

      if (filteredFiles.length !== allFiles.length) {
        this.logger.log(`📊 过滤统计: 总项目${allFiles.length}个, 文件${filteredFiles.length}个, 目录${allFiles.length - filteredFiles.length}个`);
      }

      return filteredFiles;
    } catch (error) {
      this.logger.error(`❌ 获取工作区文件列表失败:`, error);
      this.logger.error(`❌ 错误详情:`, {
        playgroundId,
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack?.substring(0, 500)
      });
      throw new Error(`获取工作区文件列表失败: ${error.message}`);
    }
  }

  /**
   * 获取工作区文件内容
   * @param playgroundId Playground ID
   * @param filePath 文件路径
   * @returns 文件内容
   */
  async getPlaygroundFile(playgroundId: string, filePath: string): Promise<{
    content: string;
    contentType: string;
  }> {
    try {
      // 导入playground文件访问工具
      const { getFile } = require('../ai-coding/playground');

      // 从工作区获取文件内容
      const fileContent = await getFile(playgroundId, filePath);

      // 判断文件路径，返回适当的Content-Type
      const isHtml = filePath.endsWith('.html');
      const isCss = filePath.endsWith('.css');
      const isJs = filePath.endsWith('.js');

      return {
        content: fileContent,
        contentType: isHtml ? 'text/html' :
          isCss ? 'text/css' :
            isJs ? 'application/javascript' : 'text/plain'
      };
    } catch (error) {
      this.logger.error(`❌ 获取工作区文件内容失败:`, error);
      throw new Error(`获取工作区文件内容失败: ${error.message}`);
    }
  }

  // 辅助方法
  private convertToUIMessage(msg: any): any {
    return {
      id: msg.id,
      role: msg.role === 'USER' ? 'user' : 'assistant',
      content: msg.content,
      createdAt: msg.created,
      annotations: msg.annotations || [],
      parts: msg.parts || [],
      experimental_attachments: msg.attachments,
    };
  }

  private createMockServerResponse(): any {
    const mockResponse = new EventEmitter();

    (mockResponse as any).writeHead = () => {
      // this.logger.log('🔧 [MockResponse] writeHead called');
      return mockResponse;
    };

    (mockResponse as any).write = (chunk: any) => {
      // 简化 write 方法，只记录基本日志，不进行错误检测
      // 错误检测现在由 ai.ts 中的 onError 正确处理
      // this.logger.log('📝 [MockResponse] write called');
      return true;
    };

    (mockResponse as any).end = (chunk?: any, encoding?: any, cb?: any) => {
      this.logger.log('🏁 [MockResponse] end called, chunk:', chunk ? chunk.toString().substring(0, 200) : 'null');
      if (cb && typeof cb === 'function') {
        cb();
      }
      // 触发finish事件
      process.nextTick(() => {
        mockResponse.emit('finish');
      });
      return mockResponse;
    };
    (mockResponse as any).setHeader = () => mockResponse;
    (mockResponse as any).getHeader = () => undefined;
    (mockResponse as any).removeHeader = () => mockResponse;
    (mockResponse as any).statusCode = 200;
    (mockResponse as any).statusMessage = 'OK';
    (mockResponse as any).headersSent = false;

    return mockResponse;
  }
} 