import { homedir } from 'node:os';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AiCodingModule } from './ai-coding/ai-coding.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DesignProjectModule } from './design-project/design-project.module';
import { PM2PreviewClientModule } from './pm2-preview-client/pm2-preview-client.module';
import { PrismaModule } from './prisma/prisma.module';
import { ProjectModule } from './project/project.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env.development'],
      load: [
        () => {
          const expandPath = (dir?: string) => {
            if (dir && dir.startsWith('~')) {
              return dir.replace('~', homedir());
            }
            return dir;
          };
          return {
            HOME_DIR: expandPath(process.env.HOME_DIR),
            TEMP_DIR: expandPath(process.env.TEMP_DIR),
            WEBIDE_DIR: expandPath(process.env.WEBIDE_DIR),
          };
        },
      ],
    }),
    AiCodingModule,  
    PrismaModule,
    ProjectModule,
    DesignProjectModule,
    PM2PreviewClientModule, // 添加PM2预览客户端模块

  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
