import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { 
  PM2ProcessInfo, 
  PM2StartResult, 
  PM2StopResult, 
  PM2RegisterResult
} from './interfaces';

@Injectable()
export class PM2PreviewClientService {
  private readonly logger = new Logger(PM2PreviewClientService.name);
  private readonly baseURL: string;
  private readonly httpClient: AxiosInstance;

  constructor() {
    // 从环境变量读取 PM2 Preview Server 的地址
    this.baseURL = process.env.PM2_PREVIEW_SERVER_URL || 'http://localhost:3001';
    
    this.httpClient = axios.create({
      baseURL: this.baseURL,
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    this.logger.log(`🔗 [PM2Client] PM2 Preview Server: ${this.baseURL}`);
  }

  /**
   * 🔧 启动自定义预览服务
   */
  async startCustomPreviewWithPM2(
    playgroundId: string,
    pageName: string,
    userId: string,
    projectDir: string = 'src'
  ): Promise<PM2StartResult> {
    try {
      this.logger.log(`🚀 [PM2Client] 启动自定义预览服务: ${playgroundId}`);
      
      const response = await this.httpClient.post(`/pm2-preview/start/${playgroundId}`, {
        user: userId,
        sessionId: `${userId}-${Date.now()}`, // 生成会话ID
      });

      this.logger.debug(`✅ [PM2Client] 启动服务响应: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 启动预览服务失败:`, error);
      throw new Error(`PM2启动失败: ${error.message}`);
    }
  }

  /**
   * 🔧 重启自定义预览服务
   */
  async restartCustomPreviewWithPM2(
    playgroundId: string,
    pageName: string,
    userId: string
  ): Promise<PM2StartResult> {
    try {
      this.logger.log(`🔄 [PM2Client] 重启自定义预览服务: ${playgroundId}`);
      
      const response = await this.httpClient.post(`/pm2-preview/restart/${playgroundId}`, {
        user: userId,
      });

      this.logger.debug(`✅ [PM2Client] 重启服务响应: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 重启预览服务失败:`, error.message);
      throw new Error(`重启预览服务失败: ${error.message}`);
    }
  }

  /**
   * 🔧 停止自定义预览服务
   */
  async stopCustomPreviewWithPM2(
    playgroundId: string,
    pageName?: string
  ): Promise<PM2StopResult> {
    try {
      this.logger.log(`🛑 [PM2Client] 停止自定义预览服务: ${playgroundId}`);
      
      const response = await this.httpClient.post(`/pm2-preview/stop/${playgroundId}`, {
        user: 'system',
        sessionId: `system-${Date.now()}`,
      });

      this.logger.debug(`✅ [PM2Client] 停止服务响应: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 停止预览服务失败:`, error.message);
      throw new Error(`停止预览服务失败: ${error.message}`);
    }
  }

  /**
   * 🔧 获取自定义预览进程状态
   */
  async getCustomPreviewStatusWithPM2(playgroundId: string): Promise<PM2ProcessInfo[]> {
    try {
      this.logger.debug(`🔍 [PM2Client] 获取预览服务状态: ${playgroundId}`);
      
      const response = await this.httpClient.get(`/pm2-preview/status/${playgroundId}`);

      this.logger.debug(`✅ [PM2Client] 状态查询响应: ${JSON.stringify(response.data)}`);
      return response.data.processes || [];
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 获取预览服务状态失败:`, error.message);
      throw new Error(`获取预览服务状态失败: ${error.message}`);
    }
  }

  /**
   * 🔧 注册自定义预览服务配置（不启动）
   */
  async registerCustomPreviewServiceOnly(
    playgroundId: string,
    pageName: string,
    userId: string,
    projectDir: string = 'src'
  ): Promise<PM2RegisterResult> {
    try {
      this.logger.log(`📝 [PM2Client] 注册预览服务配置: ${playgroundId}`);
      
      const response = await this.httpClient.post(`/pm2-preview/register/${playgroundId}`, {
        user: userId,
        pageName,
        projectDir,
      });

      this.logger.debug(`✅ [PM2Client] 注册服务响应: ${JSON.stringify(response.data)}`);
      return response.data;
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 注册预览服务配置失败:`, error.message);
      throw new Error(`注册预览服务配置失败: ${error.message}`);
    }
  }

  /**
   * 🔧 发送心跳信号
   */
  async updateHeartbeat(playgroundId: string, sessionId: string): Promise<boolean> {
    try {
      const response = await this.httpClient.post(`/pm2-preview/heartbeat/${playgroundId}`, {
        user: 'system',
        sessionId,
      });

      return response.data.success || false;
    } catch (error) {
      this.logger.warn(`⚠️ [PM2Client] 心跳发送失败:`, error.message);
      return false;
    }
  }

  /**
   * 🔧 获取心跳会话状态
   */
  async getHeartbeatSessionsStatus(): Promise<any[]> {
    try {
      const response = await this.httpClient.get('/pm2-preview/heartbeat-sessions');
      return response.data.sessions || [];
    } catch (error) {
      this.logger.error(`❌ [PM2Client] 获取心跳会话状态失败:`, error.message);
      return [];
    }
  }

  /**
   * 🔧 健康检查 - 检查 PM2 Preview Server 是否可用
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.httpClient.get('/pm2-preview/heartbeat-sessions');
      return response.status === 200;
    } catch (error) {
      this.logger.warn(`⚠️ [PM2Client] PM2 Preview Server 不可用: ${error.message}`);
      return false;
    }
  }
} 