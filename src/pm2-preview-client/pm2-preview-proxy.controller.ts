import { <PERSON>, Post, Get, Body, Param, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PM2PreviewClientService } from './pm2-preview-client.service';

@Controller('pm2-preview')
export class PM2PreviewProxyController {
  private readonly logger = new Logger(PM2PreviewProxyController.name);

  constructor(private readonly pm2PreviewClientService: PM2PreviewClientService) {}

  /**
   * 🔧 启动自定义预览服务（代理接口）
   */
  @Post('start/:playgroundId')
  async startCustomPreview(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.logger.log(`🚀 [PM2Proxy] 代理启动预览服务请求: ${playgroundId}`);

      const result = await this.pm2PreviewClientService.startCustomPreviewWithPM2(
        playgroundId,
        'preview', // 默认页面名称
        body.user,
        'src'
      );

      this.logger.log(`✅ [PM2Proxy] 预览服务启动成功: ${playgroundId}`);

      return {
        success: true,
        message: '预览服务启动成功',
        url: result.url,
        processId: result.processId,
        port: result.port,
      };
    } catch (error) {
      this.logger.error(`❌ [PM2Proxy] 启动预览服务失败: ${playgroundId}`, error);
      throw new HttpException(
        {
          success: false,
          message: `启动预览服务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 🔧 重启自定义预览服务（代理接口）
   */
  @Post('restart/:playgroundId')
  async restartCustomPreview(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string }
  ) {
    try {
      this.logger.log(`🔄 [PM2Proxy] 代理重启预览服务请求: ${playgroundId}`);

      const result = await this.pm2PreviewClientService.restartCustomPreviewWithPM2(
        playgroundId,
        'preview',
        body.user
      );

      this.logger.log(`✅ [PM2Proxy] 预览服务重启成功: ${playgroundId}`);

      return {
        success: true,
        message: '预览服务重启成功',
        url: result.url,
        processId: result.processId,
        port: result.port,
      };
    } catch (error) {
      this.logger.error(`❌ [PM2Proxy] 重启预览服务失败: ${playgroundId}`, error);
      throw new HttpException(
        {
          success: false,
          message: `重启预览服务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 🔧 停止自定义预览服务（代理接口）
   */
  @Post('stop/:playgroundId')
  async stopCustomPreview(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.logger.log(`🛑 [PM2Proxy] 代理停止预览服务请求: ${playgroundId}`);

      const result = await this.pm2PreviewClientService.stopCustomPreviewWithPM2(playgroundId);

      this.logger.log(`✅ [PM2Proxy] 预览服务停止成功: ${playgroundId}`);

      return {
        success: true,
        message: '预览服务停止成功',
        stoppedProcesses: result.stoppedProcesses,
      };
    } catch (error) {
      this.logger.error(`❌ [PM2Proxy] 停止预览服务失败: ${playgroundId}`, error);
      throw new HttpException(
        {
          success: false,
          message: `停止预览服务失败: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 🔧 获取自定义预览进程状态（代理接口）
   */
  @Get('status/:playgroundId')
  async getCustomPreviewStatus(@Param('playgroundId') playgroundId: string) {
    try {
      this.logger.debug(`🔍 [PM2Proxy] 代理查询预览服务状态: ${playgroundId}`);

      const processes = await this.pm2PreviewClientService.getCustomPreviewStatusWithPM2(playgroundId);

      this.logger.debug(`✅ [PM2Proxy] 状态查询成功: ${playgroundId}, 进程数: ${processes.length}`);

      return {
        success: true,
        message: '状态查询成功',
        processes: processes,
      };
    } catch (error) {
      this.logger.error(`❌ [PM2Proxy] 查询预览服务状态失败: ${playgroundId}`, error);
      throw new HttpException(
        {
          success: false,
          message: `查询预览服务状态失败: ${error.message}`,
          processes: [],
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 🔧 发送心跳信号（代理接口）
   */
  @Post('heartbeat/:playgroundId')
  async sendHeartbeat(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.logger.debug(`💓 [PM2Proxy] 代理发送心跳: ${playgroundId}`);

      const success = await this.pm2PreviewClientService.updateHeartbeat(playgroundId, body.sessionId);

      return {
        success: success,
        message: success ? '心跳发送成功' : '心跳发送失败',
      };
    } catch (error) {
      this.logger.warn(`⚠️ [PM2Proxy] 心跳发送失败: ${playgroundId}`, error);
      return {
        success: false,
        message: `心跳发送失败: ${error.message}`,
      };
    }
  }

  /**
   * 🔧 获取心跳会话状态（代理接口）
   */
  @Get('heartbeat-sessions')
  async getHeartbeatSessions() {
    try {
      this.logger.debug(`🔍 [PM2Proxy] 代理查询心跳会话状态`);

      const sessions = await this.pm2PreviewClientService.getHeartbeatSessionsStatus();

      return {
        success: true,
        message: '心跳会话查询成功',
        sessions: sessions,
      };
    } catch (error) {
      this.logger.error(`❌ [PM2Proxy] 查询心跳会话状态失败`, error);
      throw new HttpException(
        {
          success: false,
          message: `查询心跳会话状态失败: ${error.message}`,
          sessions: [],
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 