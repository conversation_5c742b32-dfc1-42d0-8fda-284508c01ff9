export interface PM2ProcessInfo {
  name: string;
  status: string;
  cpu: string;
  memory: string;
  uptime: string;
  pid?: number;
}

export interface PM2StartResult {
  processId: string;
  url?: string;
  port?: number;
  success?: boolean;
  message?: string;
  existingService?: boolean;
  runningProcesses?: number;
}

export interface PM2StopResult {
  success: boolean;
  stoppedProcesses: string[];
  message?: string;
}

export interface PM2RegisterResult {
  registered: boolean;
  serviceName: string;
  error?: string;
  success?: boolean;
  message?: string;
}

export interface PM2StatusResult {
  success: boolean;
  processes: PM2ProcessInfo[];
  message?: string;
}

export interface PM2HeartbeatResult {
  success: boolean;
  message?: string;
} 