import type { NestExpressApplication } from '@nestjs/platform-express';
import { writeFile } from 'node:fs';
import { ArgumentsHost, ExceptionFilter, HttpException, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Request, Response } from 'express';
import { WinstonModule } from 'nest-winston';
import { transports, format } from 'winston';
import { AppModule } from './app.module';
import { safeStringify } from './utils';

class LogErrorExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(LogErrorExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    this.logger.error(`Handler error: ${exception}`);
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    response
      .status(200)
      .json({
        error: exception,
        timestamp: new Date().toISOString(),
        path: request.url,
      });
  }
}

// 创建统一的日志格式化器
const logFormat = format.combine(
  format.timestamp(),
  format.splat(), // 支持 %s, %d, %o 等占位符
  format.printf((info) => {
    const { timestamp, level, context, message, ...meta } = info;

    // 1. 确定最终的上下文和元数据
    let finalContext = context;
    const finalMeta = { ...meta };

    // 如果 context 是一个对象, 将其视为元数据，并将上下文设置为 'App'
    if (typeof context === 'object' && context !== null) {
      Object.assign(finalMeta, context);
      finalContext = 'App';
    }

    // 2. 确保上下文是字符串
    const contextStr = typeof finalContext === 'string' ? finalContext : 'App';

    // 3. 构造日志消息
    let logMessage = `[${timestamp}][${level}][${contextStr}] ${message}`;

    // 4. 清理元数据并附加
    delete finalMeta[Symbol.for('splat')];
    delete finalMeta.context; // 从元数据中移除 context，避免重复打印

    if (Object.keys(finalMeta).length > 0) {
      logMessage += ' ' + safeStringify(finalMeta);
    }

    return logMessage;
  }),
);

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: WinstonModule.createLogger({
      transports: [
        new transports.File({
          filename: `log`,
          format: format.combine(
            format.cli(),
            logFormat
          ),
        }),
        new transports.Console({
          format: format.combine(
            format.colorize(),
            logFormat
          ),
        }),
      ],
    }),
  });

  app.enableCors();
  app.useBodyParser('json', { limit: '10mb' });
  app.setGlobalPrefix('api');
  app.useGlobalFilters(new LogErrorExceptionFilter());

  //  Logger 实例
  const logger: Logger = new Logger('NestApplication');

  // 捕获未处理的异常
  process.on('uncaughtException', (error) => {
    logger.error(
      'Uncaught Exception',
      error?.stack || safeStringify(error)
    );
  });

  // 捕获未处理的拒绝的Promise
  process.on('unhandledRejection', (reason, promise) => {
    logger.error(`unhandledRejection: ${reason}`, promise);
  });

  const port = process.env.PORT ?? 8080;
  await app.listen(port, () => {
    writeFile('pid', `${process.pid}`, () => { });
    new Logger('NestApplication').log(`Server listening on port ${port}`);
  });
}

bootstrap();