version: '3.8'

name: 'mongo'
services:
  mongo1:
    container_name: mongo1
    image: mongo:latest
    ports:
      - "27017:27017"
    networks:
      - mongo-network
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongo1-data:/data/db
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh --quiet
      interval: 10s
      timeout: 10s
      retries: 5

  mongo2:
    container_name: mongo2
    image: mongo:latest
    ports:
      - "27018:27017"
    networks:
      - mongo-network
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongo2-data:/data/db
    depends_on:
      - mongo1

  mongo3:
    container_name: mongo3
    image: mongo:latest
    ports:
      - "27019:27017"
    networks:
      - mongo-network
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongo3-data:/data/db
    depends_on:
      - mongo1

networks:
  mongo-network:
    driver: bridge

volumes:
  mongo1-data:
  mongo2-data:
  mongo3-data: