FROM repo-dev.htsc/public-cncp-image-base-local/node:20 AS builder
# FROM node:20-alpine AS builder

WORKDIR /app

ENV PRISMA_ENGINES_MIRROR http://repo-dev.htsc/artifactory/public-npm-binary-virtual/binaries/prisma

# 复制 pm2-preview-server 的源码和配置文件
COPY pm2-preview-server/package.json pm2-preview-server/yarn.lock ./
COPY pm2-preview-server/tsconfig.json pm2-preview-server/nest-cli.json ./
COPY pm2-preview-server/src ./src/
# 复用主服务的prisma配置 
COPY ./prisma ./prisma/
COPY pm2-preview-server/scripts ./scripts/

# 安装依赖
RUN yarn install --ignore-engines

# 设置PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING环境变量
ENV PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1

# 执行构建（包括 Prisma 同步和生成）
RUN yarn build

FROM repo-dev.htsc/public-cncp-image-base-local/node:20 AS runner
# FROM node:20-alpine AS runner

WORKDIR /app

# 复制构建结果和依赖
COPY --from=builder /app/dist ./
COPY --from=builder /app/node_modules/ ./node_modules/
COPY --from=builder /app/package.json ./package.json

# 复制 Prisma 相关文件（如果需要在运行时使用）
COPY --from=builder /app/prisma/ ./prisma/


# 设置NPM的默认registry
RUN npm config set registry http://registry.npm.htsc/

# 安装自定义项目所需的最小全局依赖
RUN npm install -g husky

# 先设置 Yarn 的默认 registry
RUN yarn config set registry http://registry.npm.htsc/

# 安装一些必要的工具，例如git等
RUN dnf install -y git && dnf clean all

# 安装xdg-utils
RUN yum install -y xdg-utils

# 用 Yarn 全局安装 PNPM
RUN yarn global add pnpm@9.15.0

# 🔧 新增：安装PM2用于管理自定义预览进程
RUN yarn global add pm2

# 验证
RUN pnpm --version
RUN pm2 --version

# 配置 pnpm 减少文件监听
RUN pnpm config set auto-install-peers false
RUN pnpm config set dedupe-peer-dependents false
RUN pnpm config set prefer-symlinked-executables false

# 复制 pm2-preview-server 的启动脚本
COPY docker/pm2-preview/entrypoint.sh entrypoint.sh
RUN chmod +x entrypoint.sh

ENV NODE_ENV production
ENV PORT 8081

# 设置环境变量优化 Node.js 和 pnpm 行为
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PNPM_HOME="/app/.pnpm"
ENV PATH="$PNPM_HOME:$PATH"

EXPOSE 8081

# 使用 entrypoint 脚本启动
ENTRYPOINT ["/app/entrypoint.sh"]