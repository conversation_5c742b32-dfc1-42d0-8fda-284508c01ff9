#!/bin/sh

# 设置严格模式
set -e

echo "=== Docker Container Initialization (4C 8G optimized) ==="

# 1. 针对4C 8G优化的文件监听限制
echo "Setting up file watcher limits for 4C 8G container..."

# 适中的 inotify 限制值 - 不要设置过高以免浪费内存
MAX_WATCHES=524288   # 512K watches (足够大型项目使用)
MAX_INSTANCES=512    # 512 instances

if [ -w /proc/sys/fs/inotify/max_user_watches ]; then
    echo $MAX_WATCHES > /proc/sys/fs/inotify/max_user_watches
    echo "✓ Set max_user_watches to $MAX_WATCHES"
else
    echo "⚠ Warning: Cannot write to /proc/sys/fs/inotify/max_user_watches (may need --privileged or --sysctl)"
fi

if [ -w /proc/sys/fs/inotify/max_user_instances ]; then
    echo $MAX_INSTANCES > /proc/sys/fs/inotify/max_user_instances
    echo "✓ Set max_user_instances to $MAX_INSTANCES"
else
    echo "⚠ Warning: Cannot write to /proc/sys/fs/inotify/max_user_instances"
fi

# 设置适中的文件描述符限制
if command -v ulimit >/dev/null 2>&1; then
    ulimit -n 32768  # 32K 对 8G 内存来说足够了
    echo "✓ Set file descriptor limit to 32768"
fi

# 2. 验证当前设置
echo ""
echo "Current system limits:"
[ -r /proc/sys/fs/inotify/max_user_watches ] && echo "  max_user_watches: $(cat /proc/sys/fs/inotify/max_user_watches)"
[ -r /proc/sys/fs/inotify/max_user_instances ] && echo "  max_user_instances: $(cat /proc/sys/fs/inotify/max_user_instances)"
echo "  file descriptors: $(ulimit -n 2>/dev/null || echo 'unknown')"

# 3. 针对4C 8G优化的环境变量
echo ""
echo "Setting up environment variables for 4C 8G..."

# Node.js 内存优化 - pm2-preview-server 使用较少内存，为 PM2 管理的进程预留更多空间
export NODE_OPTIONS="${NODE_OPTIONS:---max-old-space-size=1024 --max-semi-space-size=128}"
export UV_THREADPOOL_SIZE=8  # 4核心 * 2，不要设置过高

# pnpm 配置
export PNPM_HOME="/app/.pnpm"
export PATH="$PNPM_HOME:$PATH"

# 文件监听策略 - 智能选择监听方式
export CHOKIDAR_USEPOLLING=false
export CHOKIDAR_INTERVAL=2000  # 增加间隔减少CPU占用
export WATCHPACK_POLLING=false
export WEBPACK_POLLING=false

# 文件监听器优化配置
export CHOKIDAR_IGNORE="**/node_modules/**,**/.git/**,**/.next/**,**/dist/**,**/build/**,**/.cache/**,**/coverage/**,**/tmp/**,**/temp/**"
export FORCE_POLLING=false
export DISABLE_WATCHER=false
export VITE_WATCH_EXCLUDE="**/node_modules/**,**/.git/**"

# 错误处理配置
export NODE_NO_WARNINGS=1
export SUPPRESS_NO_CONFIG_WARNING=true

# PM2 优化配置
export PM2_HOME="/app/.pm2"
export PM2_UPDATE_NOTIFIER=false

# 针对中等规格的性能优化
export NODE_ENV=production
export NPM_CONFIG_AUDIT=false
export NPM_CONFIG_FUND=false

echo "✓ Environment variables configured for 4C 8G"

# 4. 智能进程清理
echo ""
echo "Cleaning up old processes..."
cleanup_processes() {
    local processes="pnpm nest pm2"
    for proc in $processes; do
        if pgrep -f "$proc" > /dev/null 2>&1; then
            echo "  Terminating processes: $proc"
            pkill -TERM -f "$proc" 2>/dev/null || true
            sleep 1
        fi
    done
    
    # sync-webide 进程特殊处理
    if pgrep -f "node.*sync-webide" > /dev/null 2>&1; then
        echo "  Terminating sync-webide processes"
        pkill -TERM -f "node.*sync-webide" 2>/dev/null || true
        sleep 2
        # 强制清理顽固进程
        pkill -KILL -f "node.*sync-webide" 2>/dev/null || true
    fi
}

cleanup_processes

# 清理缓存和临时文件
rm -rf /tmp/.pm2* /app/.pm2/pm2.pid 2>/dev/null || true
rm -rf /app/node_modules/.cache /app/.next/cache 2>/dev/null || true

echo "✓ Process cleanup completed"

# 5. 优化文件监听器
echo ""
echo "=== File Watcher Optimization ==="

# 尝试增加文件监听器限制（需要--privileged或--sysctl权限）
if [ -w /proc/sys/fs/inotify/max_user_watches ]; then
    echo 524288 > /proc/sys/fs/inotify/max_user_watches
    echo 512 > /proc/sys/fs/inotify/max_user_instances
    echo 32768 > /proc/sys/fs/inotify/max_queued_events
    echo "✓ File watcher limits increased successfully"
else
    echo "⚠ Cannot modify file watcher limits (need --privileged mode)"
    echo "💡 Using fallback strategies:"
    echo "  - Polling-based file watching"
    echo "  - Reduced watch scope"
    echo "  - Optimized ignore patterns"
    export CHOKIDAR_USEPOLLING=true
    export CHOKIDAR_INTERVAL=3000
    export WATCHPACK_POLLING=true
fi

echo "Current limits:"
echo "  max_user_watches: $(cat /proc/sys/fs/inotify/max_user_watches 2>/dev/null || echo 'unknown')"
echo "  max_user_instances: $(cat /proc/sys/fs/inotify/max_user_instances 2>/dev/null || echo 'unknown')"

# 确保工作目录
cd /app
echo "✓ Working directory: $(pwd)"

# 6. 服务启动
echo ""
echo "=== Starting Services ==="

# 启动 cron
if command -v crond >/dev/null 2>&1; then
    echo "Starting cron service..."
    crond
    echo "✓ Cron service started"
fi

# 创建日志目录
mkdir -p /app/logs

# pm2-preview-server 不需要 sync-webide 脚本
echo "✓ pm2-preview-server 模式，跳过 sync-webide 启动"

# 7. 优雅关闭处理
trap 'echo "Received shutdown signal, cleaning up..."; cleanup_processes; exit 0' TERM INT

# 8. 启动主应用（针对4C 8G优化）
echo ""
echo "Starting main application..."

if [ ! -f "/app/main.js" ]; then
    echo "❌ Error: /app/main.js not found"
    exit 1
fi

# PM2 配置针对4C 8G优化
if command -v pm2 >/dev/null 2>&1; then
    echo "Using PM2 with 4C 8G optimizations..."
    
    cat > /app/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'pm2-preview-server',
    script: '/app/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1536M',
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000,
    env: {
      NODE_ENV: 'production',
      NODE_OPTIONS: '--max-old-space-size=1024'
    },
    error_file: '/app/logs/err.log',
    out_file: '/app/logs/out.log',
    log_file: '/app/logs/combined.log',
    time: true,
    merge_logs: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
};
EOF
    
    # 启动应用
    exec pm2 start /app/ecosystem.config.js --no-daemon
else
    # 直接启动，使用优化的 Node.js 选项
    echo "Starting Node.js application directly..."
    exec node --max-old-space-size=1024 --max-semi-space-size=128 /app/main.js
fi