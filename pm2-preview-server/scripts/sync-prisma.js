#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 [Prisma Sync] 开始同步主服务的 Prisma 配置...');

// 定义路径
const mainProjectRoot = path.resolve(__dirname, '../..');
const pm2ServerRoot = path.resolve(__dirname, '..');
const mainPrismaDir = path.join(mainProjectRoot, 'prisma');
const pm2PrismaDir = path.join(pm2ServerRoot, 'prisma');
const mainSchemaFile = path.join(mainPrismaDir, 'schema.prisma');
const pm2SchemaFile = path.join(pm2PrismaDir, 'schema.prisma');

try {
  // 1. 检查主项目的 Prisma 配置是否存在
  if (!fs.existsSync(mainSchemaFile)) {
    throw new Error(`主项目的 Prisma schema 文件不存在: ${mainSchemaFile}`);
  }

  console.log(`✅ [Prisma Sync] 找到主项目 Prisma schema: ${mainSchemaFile}`);

  // 2. 创建 pm2-preview-server 的 prisma 目录（如果不存在）
  if (!fs.existsSync(pm2PrismaDir)) {
    fs.mkdirSync(pm2PrismaDir, { recursive: true });
    console.log(`📁 [Prisma Sync] 创建目录: ${pm2PrismaDir}`);
  }

  // 3. 复制 schema.prisma 文件
  fs.copyFileSync(mainSchemaFile, pm2SchemaFile);
  console.log(`📄 [Prisma Sync] 复制 schema.prisma: ${mainSchemaFile} → ${pm2SchemaFile}`);

  // 4. 如果主项目有其他 Prisma 相关文件，也一并复制
  const prismaFiles = ['seed.ts', 'seed.js', 'migrations'];

  prismaFiles.forEach(file => {
    const mainFile = path.join(mainPrismaDir, file);
    const pm2File = path.join(pm2PrismaDir, file);

    if (fs.existsSync(mainFile)) {
      const stat = fs.statSync(mainFile);
      if (stat.isDirectory()) {
        // 复制目录
        if (fs.existsSync(pm2File)) {
          fs.rmSync(pm2File, { recursive: true, force: true });
        }
        fs.cpSync(mainFile, pm2File, { recursive: true });
        console.log(`📁 [Prisma Sync] 复制目录: ${file}`);
      } else {
        // 复制文件
        fs.copyFileSync(mainFile, pm2File);
        console.log(`📄 [Prisma Sync] 复制文件: ${file}`);
      }
    }
  });

  // 5.拷贝环境变量
  if (fs.existsSync(path.join(mainProjectRoot, '.env.development'))) {
    const mainEnvFile = path.join(mainProjectRoot, '.env.development');
    const pm2EnvFile = path.join(pm2ServerRoot, '.env.development');
    fs.copyFileSync(mainEnvFile, pm2EnvFile);
    console.log(`📄 [Prisma Sync] 复制环境变量: ${mainEnvFile} → ${pm2EnvFile}`);
  } else {
    console.log(`📄 [Prisma Sync] 主项目没有 .env.development 文件，跳过复制`);
  }

  // 6. 生成 Prisma 客户端
  console.log('🔨 [Prisma Sync] 生成 Prisma 客户端...');

  const generateCommand = 'npx prisma generate';
  execSync(generateCommand, {
    cwd: pm2ServerRoot,
    stdio: 'inherit',
    env: { ...process.env }
  });

  console.log('✅ [Prisma Sync] Prisma 客户端生成成功！');
  console.log('🎉 [Prisma Sync] Prisma 配置同步完成！');

} catch (error) {
  console.error('❌ [Prisma Sync] 同步失败:', error.message);
  process.exit(1);
} 