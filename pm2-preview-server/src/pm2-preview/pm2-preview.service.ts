import { EventEmitter } from 'events';
import * as path from 'path';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';

export interface PM2ProcessInfo {
  name: string;
  status: string;
  cpu: string;
  memory: string;
  uptime: string;
  pid?: number;
}

export interface PM2StartResult {
  processId: string;
  url?: string;
  port?: number;
}

export interface PM2StopResult {
  success: boolean;
  stoppedProcesses: string[];
}

export interface PM2RegisterResult {
  registered: boolean;
  serviceName: string;
  error?: string;
}

@Injectable()
export class PM2PreviewService extends EventEmitter {
  private readonly logger = new Logger(PM2PreviewService.name);
  
  /**
   * 构建 playground 路径，直接使用 ConfigService 中已解析的路径
   */
  private getPlaygroundPath(...segs: string[]): string {
    const playgroundDir = this.configService.get<string>('HOME_DIR');
    return path.join(playgroundDir, ...segs);
  }
  
  // 心跳会话管理
  private readonly heartbeatSessions = new Map<string, { 
    sessionId: string; 
    user: string; 
    lastHeartbeat: number; 
    playgroundId: string; 
  }>();
  
  // 日志同步缓存
  private logSyncCache = new Map<string, Set<string>>();
  
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    super();
    
    // 路径直接从 ConfigService 获取，无需额外初始化
    
    // 启动PM2初始化
    this.initializePM2().catch(error => {
      this.logger.error('PM2初始化失败:', error);
    });
    
    // 启动心跳检查定时器
    this.startHeartbeatMonitor();
  }

  /**
   * 通过playgroundId获取项目配置中的自定义预览启动命令
   */
  private async getCustomPreviewCommandByPlaygroundId(playgroundId: string, logPrefix: string = 'PM2'): Promise<string | undefined> {
    try {
      // 首先获取playground信息
      const playground = await this.prisma.playground.findUnique({
        where: { id: playgroundId },
        select: { projectId: true }
      });
      
      let projectId: string | undefined = playground?.projectId;
      
      // 如果playground没有projectId，尝试通过backgroundTaskItem查找
      if (!projectId) {
        this.logger.log(`🔍 [${logPrefix}] playground没有projectId，尝试通过backgroundTaskItem查找...`);
        
        const backgroundTaskItem = await this.prisma.backgroundTaskItem.findFirst({
          where: { playgroundId: playgroundId },
          select: { backgroundTaskId: true }
        });
        
        if (backgroundTaskItem?.backgroundTaskId) {
          this.logger.log(`🔗 [${logPrefix}] 找到backgroundTaskId: ${backgroundTaskItem.backgroundTaskId}`);
          
          const backgroundTask = await this.prisma.backgroundTask.findUnique({
            where: { id: backgroundTaskItem.backgroundTaskId },
            select: { designProjectId: true }
          });
          
          if (backgroundTask?.designProjectId) {
            projectId = backgroundTask.designProjectId;
            this.logger.log(`🎯 [${logPrefix}] 通过backgroundTask找到projectId: ${projectId}`);
          } else {
            this.logger.log(`ℹ️ [${logPrefix}] backgroundTask没有关联的designProjectId`);
          }
        } else {
          this.logger.log(`ℹ️ [${logPrefix}] 未找到对应的backgroundTaskItem`);
        }
      } else {
        this.logger.log(`✅ [${logPrefix}] 直接从playground获取到projectId: ${projectId}`);
      }
      
      // 如果找到了projectId，则查询项目配置
      if (projectId) {
        const project = await this.prisma.project.findUnique({
          where: { id: projectId },
          select: { previewStartCommand: true }
        });
        
        if (project?.previewStartCommand) {
          this.logger.log(`🎯 [${logPrefix}] 从项目配置获取到自定义预览命令: ${project.previewStartCommand}`);
          return project.previewStartCommand;
        }
      }
      
      return undefined;
    } catch (error) {
      this.logger.warn(`⚠️ [${logPrefix}] 获取项目配置失败: ${error.message}`);
      return undefined;
    }
  }

  // 移除 ctx 初始化逻辑，直接使用 ConfigService

  /**
   * 初始化PM2
   */
  private async initializePM2(): Promise<void> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // 检查PM2是否已安装
      await execAsync('pm2 --version');
      this.logger.log('✅ PM2已安装并可用');

      // 启动PM2守护进程（如果尚未启动）
      await execAsync('pm2 ping').catch(async () => {
        this.logger.log('🚀 启动PM2守护进程...');
        await execAsync('pm2 startup');
      });

    } catch (error) {
      this.logger.error('❌ PM2初始化失败:', error);
      throw new Error(`PM2初始化失败: ${error.message}`);
    }
  }

  /**
   * 使用PM2启动自定义预览服务
   */
  async startCustomPreviewWithPM2(
    playgroundId: string,
    pageName: string,
    userId: string,
    projectDir: string = 'src'
  ): Promise<PM2StartResult> {
    try {
      const shell = await import('shelljs');
      const path = await import('path');

      this.logger.log(`🚀 [PM2] 为页面"${pageName}"启动自定义预览服务`);

      // 使用 ConfigService 构建路径
      const playgroundRoot = this.getPlaygroundPath(playgroundId);
      const workingDir = this.getPlaygroundPath(playgroundId, projectDir);
      const appName = `custom-preview-${playgroundId}-${pageName}`;

      this.logger.log(`🔍 [PM2] Playground路径调试:`);
      this.logger.log(`📁 playgroundRoot: ${playgroundRoot}`);
      this.logger.log(`📁 workingDir: ${workingDir}`);

      // 检查playground和src目录是否存在
      const fs = await import('fs');
      if (!fs.existsSync(playgroundRoot)) {
        this.logger.error(`❌ [PM2] Playground目录不存在: ${playgroundRoot}`);
        
        // 尝试列出父目录的内容进行调试
        const path = await import('path');
        const parentDir = path.dirname(playgroundRoot);
        try {
          const parentContents = fs.readdirSync(parentDir);
          this.logger.log(`📁 父目录 ${parentDir} 内容: ${parentContents.join(', ')}`);
        } catch (err) {
          this.logger.error(`❌ 无法读取父目录 ${parentDir}: ${err.message}`);
        }
        
        throw new Error(`Playground目录不存在: ${playgroundRoot}`);
      }
      
      if (!fs.existsSync(workingDir)) {
        throw new Error(`项目目录不存在: ${workingDir}，请确保Git仓库已正确克隆到src目录`);
      }

      // 1. 读取package.json确定启动脚本
      const packageJsonPath = this.getPlaygroundPath(playgroundId, projectDir, 'package.json');
      let startScript = 'start';
      
      try {
        const packageJsonContent = await fs.promises.readFile(packageJsonPath, 'utf8');
        const packageJson = JSON.parse(packageJsonContent);
        const availableScripts = Object.keys(packageJson.scripts || {});
        
        // 优先级：start > dev > serve > preview
        const scriptPriority = ['start', 'dev', 'serve', 'preview'];
        for (const script of scriptPriority) {
          if (availableScripts.includes(script)) {
            startScript = script;
            break;
          }
        }
        
        this.logger.log(`📋 [PM2] 选择启动脚本: ${startScript}`);
      } catch (error) {
        this.logger.warn(`⚠️ [PM2] 读取package.json失败，使用默认脚本: ${error.message}`);
        // 检查是否是因为文件不存在
        if (error.code === 'ENOENT') {
          throw new Error(`package.json不存在: ${packageJsonPath}，请确保项目已正确设置`);
        }
      }

      // 2. 设置环境变量
      shell.env['HOST'] = '0.0.0.0';
      shell.env['VITE_HOST'] = '0.0.0.0';
      shell.env['SERVER_HOST'] = '0.0.0.0';
      shell.env['BIND_HOST'] = '0.0.0.0';
      shell.env['HOSTNAME'] = '0.0.0.0';
      shell.env['BROWSER'] = 'none';
      shell.env['NODE_ENV'] = 'development';
      shell.env['CHOKIDAR_USEPOLLING'] = 'false';
      shell.env['WATCHPACK_POLLING'] = 'false';
      shell.env['CI'] = 'true';
      shell.env['NO_UPDATE_NOTIFIER'] = 'true';

      // 3. 切换到工作目录
      const oldCwd = shell.pwd().toString();
      shell.cd(workingDir);
      this.logger.log(`📁 [PM2] 切换到工作目录: ${workingDir}`);

      // 4. 停止现有的同名进程（如果存在）
      const deleteResult = shell.exec(`pm2 delete ${appName}`, { silent: true });
      if (deleteResult.code === 0) {
        this.logger.log(`🔄 [PM2] 删除现有进程: ${appName}`);
      } else {
        this.logger.log(`ℹ️ [PM2] 没有找到现有进程: ${appName}`);
      }

      // 5. 使用PM2启动新进程 - 使用launch.js方式，更稳定有效
      const launchPath = path.join(workingDir, 'launch.js');
      
      // 清理可能存在的旧launch.js文件
      if (fs.existsSync(launchPath)) {
        try {
          fs.unlinkSync(launchPath);
          this.logger.log(`🗑️ [PM2] 清理旧的launch.js文件: ${launchPath}`);
        } catch (error) {
          this.logger.warn(`⚠️ [PM2] 清理旧launch.js失败: ${error.message}`);
        }
      }
      
      // 通过playgroundId查询项目配置中的自定义预览启动命令
      const customPreviewCommand = await this.getCustomPreviewCommandByPlaygroundId(playgroundId, 'PM2');
      
      // 检查是否有自定义预览启动命令
      let finalCommand: string;
      let packageManager: string;
      
      if (customPreviewCommand && customPreviewCommand.trim()) {
        // 使用项目配置中的自定义预览启动命令，跳过动态检测
        this.logger.log(`✨ [PM2] 使用项目配置中的自定义预览启动命令: ${customPreviewCommand}`);
        
        const trimmedCommand = customPreviewCommand.trim();
        
        // 检查是否是完整命令（包含包管理器）
        if (trimmedCommand.startsWith('npm ') || trimmedCommand.startsWith('pnpm ') || trimmedCommand.startsWith('yarn ')) {
          // 直接使用完整命令
          finalCommand = trimmedCommand;
          packageManager = 'custom'; // 标记为自定义完整命令
        } else {
          // 如果只是脚本名，默认使用npm执行
          finalCommand = trimmedCommand;
          packageManager = 'npm';
        }
      } else {
        // 没有自定义命令时才进行动态检测
        this.logger.log(`🔍 [PM2] 未配置自定义预览启动命令，开始动态检测...`);
        const { detectedCommand, packageManager: detectedPM } = await this.detectStartCommand(workingDir);
        finalCommand = detectedCommand;
        packageManager = detectedPM;
        
        this.logger.log(`🔍 [PM2] 检测到包管理器: ${packageManager}`);
        this.logger.log(`🔍 [PM2] 检测到启动命令: ${finalCommand}`);
      }
      
      // 构造最终的启动命令
      let launchCommand: string;
      if (packageManager === 'custom') {
        // 如果是自定义完整命令，直接使用
        launchCommand = finalCommand;
      } else {
        // 如果是脚本名，使用包管理器执行
        launchCommand = `${packageManager} run ${finalCommand}`;
      }
      
      this.logger.log(`🚀 [PM2] 最终启动命令: ${launchCommand}`);
      
      // 创建launch.js启动文件
      const launchScript = `const { exec } = require("child_process");

// 启动命令: ${launchCommand}
const child = exec("${launchCommand}", { 
  windowsHide: true,
  stdio: ['inherit', 'pipe', 'pipe']
});

// 将子进程的输出转发到当前进程
if (child.stdout) {
  child.stdout.pipe(process.stdout);
}

if (child.stderr) {
  child.stderr.pipe(process.stderr);
}

child.on('error', (error) => {
  console.error('[Launch] 执行错误:', error);
});

child.on('exit', (code, signal) => {
  console.log(\`[Launch] 进程退出 - 代码: \${code}, 信号: \${signal}\`);
});

// 优雅退出处理
process.on('SIGINT', () => {
  console.log('[Launch] 收到SIGINT信号，正在停止子进程...');
  if (child && !child.killed) {
    child.kill('SIGTERM');
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('[Launch] 收到SIGTERM信号，正在停止子进程...');
  if (child && !child.killed) {
    child.kill('SIGTERM');
  }
  process.exit(0);
});
`;

      // 写入launch.js文件
      try {
        fs.writeFileSync(launchPath, launchScript);
        this.logger.log(`📝 [PM2] 创建launch.js启动文件: ${launchPath}`);
      } catch (error) {
        throw new Error(`创建launch.js启动文件失败: ${error.message}`);
      }

      // 将launch.js添加到.gitignore，避免提交到Git仓库
      await this.addToGitignore(workingDir, 'launch.js');
      
      // 使用shelljs执行PM2启动命令
      const pm2Command = `pm2 start ${launchPath} --name "${appName}" --no-autorestart --silent`;
      
      this.logger.log(`[PM2] 使用shelljs执行命令: ${pm2Command}`);
      
      // 6. 启动PM2进程
      const startResult = shell.exec(pm2Command, { silent: false });
      
      if (startResult.code !== 0) {
        this.logger.error(`❌ [PM2] 启动失败，错误代码: ${startResult.code}`);
        this.logger.error(`❌ [PM2] 错误输出: ${startResult.stderr}`);
        throw new Error(`PM2启动失败: ${startResult.stderr}`);
      }

      this.logger.log(`✅ [PM2] 启动命令执行成功`);

      // 启动多次日志同步，确保捕获完整的启动过程（覆盖5分钟，适应慢启动项目）
      this.startMultipleLogSync(playgroundId, appName, workingDir);

      // 8. 尝试检测应用端口（从日志中提取）
      let detectedPort: number | undefined;
      let previewUrl: string | undefined;

      try {
        const logsResult = shell.exec(`pm2 logs ${appName} --lines 100 --nostream`, { silent: true });
        if (logsResult.code === 0) {
          const logs = logsResult.stdout;
          
          // 更全面的端口检测模式，优先检测主服务端口
          const portPatterns = [
            // 主服务端口检测（优先级最高）
            /Server is listening at:\s*http:\/\/localhost:(\d+)/i,
            /listening at:\s*http:\/\/localhost:(\d+)/i,
            /started.*http:\/\/localhost:(\d+)/i,
            
            // 通用本地端口检测
            /Local:\s*http:\/\/localhost:(\d+)/i,
            /localhost:(\d+)/,
            /127\.0\.0\.1:(\d+)/,
            
            // 开发服务器常见模式
            /running at.*:(\d+)/i,
            /listening on port (\d+)/i,
            /server started.*:(\d+)/i,
            /dev server.*:(\d+)/i,
            /serving at.*:(\d+)/i,
          ];

          // 先检查主服务端口，再检查其他端口
          for (const pattern of portPatterns) {
            const match = logs.match(pattern);
            if (match) {
              const port = parseInt(match[1]);
              // 过滤掉明显不合理的端口号
              if (port >= 3000 && port <= 9999) {
                detectedPort = port;
                previewUrl = `http://localhost:${detectedPort}`;
                this.logger.log(`🔍 [PM2] 检测到主服务端口: ${detectedPort}`);
                break;
              }
            }
          }
          
          // 如果没找到主服务端口，尝试从应用路由中提取
          if (!detectedPort) {
            const routePattern = /http:\/\/localhost:(\d+)\/[a-zA-Z-]+/g;
            const routeMatches = logs.match(routePattern);
            if (routeMatches && routeMatches.length > 0) {
              // 取第一个路由的端口
              const firstRouteMatch = routeMatches[0].match(/http:\/\/localhost:(\d+)/);
              if (firstRouteMatch) {
                detectedPort = parseInt(firstRouteMatch[1]);
                previewUrl = `http://localhost:${detectedPort}`;
                this.logger.log(`🔍 [PM2] 从应用路由检测到端口: ${detectedPort}`);
              }
            }
          }
          
          // 输出更详细的端口检测信息
          if (detectedPort) {
            this.logger.log(`✅ [PM2] 端口检测成功: ${previewUrl}`);
          } else {
            this.logger.warn(`⚠️ [PM2] 未能检测到有效端口，日志样本:`);
            const logSample = logs.split('\n').slice(0, 10).join('\n');
            this.logger.warn(logSample);
          }
        }
      } catch (error) {
        this.logger.warn(`⚠️ [PM2] 端口检测失败: ${error.message}`);
      }

      // 9. 恢复原来的工作目录
      shell.cd(oldCwd);

      // 10. 在心跳系统中注册进程
      const sessionId = `pm2-${appName}-${Date.now()}`;
      this.heartbeatSessions.set(sessionId, {
        sessionId,
        user: userId,
        lastHeartbeat: Date.now(),
        playgroundId
      });

      this.logger.log(`🎉 [PM2] 自定义预览服务启动完成: ${appName}`);

      return {
        processId: appName,
        url: previewUrl,
        port: detectedPort
      };

    } catch (error) {
      this.logger.error(`❌ [PM2] 启动自定义预览服务失败:`, error);
      throw new Error(`PM2启动失败: ${error.message}`);
    }
  }

  /**
   * 使用PM2停止自定义预览服务
   */
  async stopCustomPreviewWithPM2(
    playgroundId: string,
    pageName?: string
  ): Promise<PM2StopResult> {
    try {
      const shell = await import('shelljs');

      this.logger.log(`🛑 [PM2] 停止playground ${playgroundId} 的自定义预览服务`);

      const stoppedProcesses: string[] = [];

      if (pageName) {
        // 停止特定页面的进程
        const appName = `custom-preview-${playgroundId}-${pageName}`;
        const result = shell.exec(`pm2 delete ${appName}`, { silent: true });
        if (result.code === 0) {
          stoppedProcesses.push(appName);
          this.logger.log(`✅ [PM2] 已停止进程: ${appName}`);
          
          // 清理对应的launch.js文件
          await this.cleanupLaunchFile(playgroundId, appName);
        } else {
          this.logger.warn(`⚠️ [PM2] 进程不存在或停止失败: ${appName}`);
        }
      } else {
        // 停止playground的所有自定义预览进程
        const listResult = shell.exec('pm2 jlist', { silent: true });
        if (listResult.code === 0) {
          try {
            const processes = JSON.parse(listResult.stdout);
            const matchingProcesses = processes.filter((proc: any) => 
              proc.name && proc.name.startsWith(`custom-preview-${playgroundId}-`)
            );

            for (const proc of matchingProcesses) {
              const deleteResult = shell.exec(`pm2 delete ${proc.name}`, { silent: true });
              if (deleteResult.code === 0) {
                stoppedProcesses.push(proc.name);
                this.logger.log(`✅ [PM2] 已停止进程: ${proc.name}`);
                
                // 清理对应的launch.js文件
                await this.cleanupLaunchFile(playgroundId, proc.name);
              } else {
                this.logger.warn(`⚠️ [PM2] 停止进程失败: ${proc.name}`);
              }
            }
          } catch (error) {
            this.logger.warn(`⚠️ [PM2] 解析进程列表失败: ${error.message}`);
          }
        }
      }

      // 清理心跳会话
      for (const [sessionId, session] of this.heartbeatSessions.entries()) {
        if (session.playgroundId === playgroundId) {
          this.heartbeatSessions.delete(sessionId);
          this.logger.log(`🧹 [PM2] 清理心跳会话: ${sessionId}`);
        }
      }

      // 清理日志同步缓存
      for (const [cacheKey] of this.logSyncCache.entries()) {
        if (cacheKey.startsWith(`${playgroundId}:`)) {
          this.logSyncCache.delete(cacheKey);
          this.logger.log(`🧹 [PM2] 清理日志同步缓存: ${cacheKey}`);
        }
      }

      return {
        success: true,
        stoppedProcesses
      };

    } catch (error) {
      this.logger.error(`❌ [PM2] 停止自定义预览服务失败:`, error);
      return {
        success: false,
        stoppedProcesses: []
      };
    }
  }

  /**
   * 仅在PM2中注册自定义预览服务配置（不启动）
   * 用于环境变量禁用启动但仍需要后续手动重启功能的场景
   */
  async registerCustomPreviewServiceOnly(
    playgroundId: string,
    pageName: string,
    userId: string,
    projectDir: string = 'src'
  ): Promise<PM2RegisterResult> {
    try {
      const shell = await import('shelljs');
      

      this.logger.log(`📝 [PM2-Register] 为页面"${pageName}"注册自定义预览服务配置（不启动）`);

      const playgroundRoot = this.getPlaygroundPath(playgroundId);
      const workingDir = this.getPlaygroundPath(playgroundId, projectDir);
      const appName = `custom-preview-${playgroundId}-${pageName}`;

      // 检查目录是否存在
      const fs = await import('fs');
      if (!fs.existsSync(playgroundRoot)) {
        throw new Error(`Playground目录不存在: ${playgroundRoot}`);
      }
      
      if (!fs.existsSync(workingDir)) {
        throw new Error(`项目目录不存在: ${workingDir}，请确保Git仓库已正确克隆到src目录`);
      }

      // 读取package.json确定启动脚本
      const packageJsonPath = this.getPlaygroundPath(playgroundId, projectDir, 'package.json');
      let startScript = 'start';
      
      try {
        const packageJsonContent = await fs.promises.readFile(packageJsonPath, 'utf8');
        const packageJson = JSON.parse(packageJsonContent);
        const availableScripts = Object.keys(packageJson.scripts || {});
        
        // 优先级：start > dev > serve > preview
        const scriptPriority = ['start', 'dev', 'serve', 'preview'];
        for (const script of scriptPriority) {
          if (availableScripts.includes(script)) {
            startScript = script;
            break;
          }
        }
        
        this.logger.log(`📋 [PM2-Register] 选择启动脚本: ${startScript}`);
      } catch (error) {
        this.logger.warn(`⚠️ [PM2-Register] 读取package.json失败，使用默认脚本: ${error.message}`);
        if (error.code === 'ENOENT') {
          throw new Error(`package.json不存在: ${packageJsonPath}，请确保项目已正确设置`);
        }
      }

                    // 通过playgroundId查询项目配置中的自定义预览启动命令
       const customPreviewCommand = await this.getCustomPreviewCommandByPlaygroundId(playgroundId, 'PM2-Register');
      
      // 检查是否有自定义预览启动命令
      let finalCommand: string;
      let packageManager: string;
      
      if (customPreviewCommand && customPreviewCommand.trim()) {
        // 使用项目配置中的自定义预览启动命令，跳过动态检测
        this.logger.log(`✨ [PM2-Register] 使用项目配置中的自定义预览启动命令: ${customPreviewCommand}`);
        finalCommand = customPreviewCommand.trim();
        
        // 从自定义命令中提取包管理器（如果包含）
        if (finalCommand.startsWith('npm ')) {
          packageManager = 'npm';
          finalCommand = finalCommand.replace(/^npm\s+/, '');
        } else if (finalCommand.startsWith('pnpm ')) {
          packageManager = 'pnpm';
          finalCommand = finalCommand.replace(/^pnpm\s+/, '');
        } else if (finalCommand.startsWith('yarn ')) {
          packageManager = 'yarn';
          finalCommand = finalCommand.replace(/^yarn\s+/, '');
        } else {
          // 如果没有包管理器前缀，默认使用npm
          packageManager = 'npm';
        }
      } else {
        // 没有自定义命令时才进行动态检测
        this.logger.log(`🔍 [PM2-Register] 未配置自定义预览启动命令，开始动态检测...`);
        const { detectedCommand, packageManager: detectedPM } = await this.detectStartCommand(workingDir);
        finalCommand = detectedCommand;
        packageManager = detectedPM;
        
        this.logger.log(`🔍 [PM2-Register] 检测到包管理器: ${packageManager}`);
        this.logger.log(`🔍 [PM2-Register] 检测到启动命令: ${finalCommand}`);
      }
      
      // 构造最终的启动命令
      let launchCommand: string;
      if (packageManager === 'custom') {
        // 如果是自定义完整命令，直接使用
        launchCommand = finalCommand;
      } else {
        // 如果是脚本名，使用包管理器执行
        launchCommand = `${packageManager} run ${finalCommand}`;
      }
      
      this.logger.log(`🚀 [PM2-Register] 最终启动命令: ${launchCommand}`);
      
      // 创建launch.js启动文件
      const launchPath = this.getPlaygroundPath(playgroundId, 'launch.js');
      const launchScript = `const { exec } = require("child_process");

// 启动命令: ${launchCommand}  
const child = exec("${launchCommand}", { 
  windowsHide: true,
  stdio: ['inherit', 'pipe', 'pipe']
});

// 将子进程的输出转发到当前进程
if (child.stdout) {
  child.stdout.pipe(process.stdout);
}

if (child.stderr) {
  child.stderr.pipe(process.stderr);  
}

child.on('error', (error) => {
  console.error('[Launch-Register] 执行错误:', error);
});

child.on('exit', (code, signal) => {
  console.log(\`[Launch-Register] 进程退出 - 代码: \${code}, 信号: \${signal}\`);
});

// 优雅退出处理  
process.on('SIGINT', () => {
  console.log('[Launch-Register] 收到SIGINT信号，正在停止子进程...');
  if (child && !child.killed) {
    child.kill('SIGTERM');
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('[Launch-Register] 收到SIGTERM信号，正在停止子进程...');
  if (child && !child.killed) {
    child.kill('SIGTERM');
  }
  process.exit(0);
});
`;

      // 写入launch.js文件
      await fs.promises.writeFile(launchPath, launchScript, 'utf8');

      this.logger.log(`✅ [PM2-Register] launch.js启动文件已创建: ${launchPath}`);

      // 删除可能存在的同名进程（确保干净状态）
      shell.exec(`pm2 delete ${appName}`, { silent: true });

      // 在心跳系统中注册服务（标记为未启动状态）
      const sessionId = `pm2-registered-${appName}-${Date.now()}`;
      this.heartbeatSessions.set(sessionId, {
        sessionId,
        user: userId,
        lastHeartbeat: Date.now(),
        playgroundId
      });

      this.logger.log(`✅ [PM2-Register] 自定义预览服务配置已注册（未启动）: ${appName}`);
      this.logger.log(`💡 [PM2-Register] 用户可通过"重启预览服务"功能启动应用`);

      return {
        registered: true,
        serviceName: appName
      };

    } catch (error) {
      this.logger.error(`❌ [PM2-Register] 注册自定义预览服务配置失败:`, error);
      return {
        registered: false,
        serviceName: `custom-preview-${playgroundId}-${pageName}`,
        error: error.message
      };
    }
  }

  /**
   * 使用PM2重启自定义预览服务
   */
  async restartCustomPreviewWithPM2(
    playgroundId: string,
    pageName: string,
    userId: string,
    projectDir: string = 'src'
  ): Promise<PM2StartResult> {
    try {
      this.logger.log(`🔄 [PM2] 重启页面"${pageName}"的自定义预览服务`);

      // 先停止现有进程
      await this.stopCustomPreviewWithPM2(playgroundId, pageName);

      // 等待一段时间确保进程完全停止
      await this.sleep(1000);

      // 启动新进程
      const result = await this.startCustomPreviewWithPM2(playgroundId, pageName, userId, projectDir);

      this.logger.log(`✅ [PM2] 重启完成: ${result.processId}`);
      return result;

    } catch (error) {
      this.logger.error(`❌ [PM2] 重启自定义预览服务失败:`, error);
      throw error;
    }
  }

  /**
   * 获取PM2管理的自定义预览进程状态
   */
  async getCustomPreviewStatusWithPM2(playgroundId: string): Promise<PM2ProcessInfo[]> {
    try {
      const shell = await import('shelljs');

      const listResult = shell.exec('pm2 jlist', { silent: true });
      if (listResult.code !== 0) {
        this.logger.warn(`⚠️ [PM2] 获取进程列表失败: ${listResult.stderr}`);
        return [];
      }

      const processes = JSON.parse(listResult.stdout);

      const customPreviewProcesses = processes
        .filter((proc: any) => proc.name && proc.name.startsWith(`custom-preview-${playgroundId}-`))
        .map((proc: any) => ({
          name: proc.name,
          status: proc.pm2_env.status,
          cpu: proc.monit?.cpu ? `${proc.monit.cpu}%` : 'N/A',
          memory: proc.monit?.memory ? `${Math.round(proc.monit.memory / 1024 / 1024)}MB` : 'N/A',
          uptime: proc.pm2_env.pm_uptime ? this.formatUptime(Date.now() - proc.pm2_env.pm_uptime) : 'N/A',
          pid: proc.pid || undefined
        }));

      return customPreviewProcesses;

    } catch (error) {
      this.logger.error(`❌ [PM2] 获取进程状态失败:`, error);
      return [];
    }
  }

  /**
   * 格式化运行时间
   */
  private formatUptime(uptimeMs: number): string {
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 启动心跳监控器（调整检查频率）
   */
  private startHeartbeatMonitor() {
    setInterval(() => {
      this.checkHeartbeats();
    }, 5 * 60 * 1000); // 每5分钟检查一次
    
    // 同时输出心跳监控启动信息
    this.logger.log(`💓 心跳监控器已启动 - 超时时间: 30分钟, 检查间隔: 5分钟`);
  }

  /**
   * 检查心跳超时（延长到30分钟）
   */
  private async checkHeartbeats() {
    const now = Date.now();
    const heartbeatTimeout = 30 * 60 * 1000; // 30分钟超时
    
    const totalSessions = this.heartbeatSessions.size;
    if (totalSessions > 0) {
      this.logger.debug(`💓 [HeartbeatCheck] 检查 ${totalSessions} 个活跃会话的心跳状态`);
    }

    let timeoutCount = 0;
    const timeoutSessions: string[] = [];

    for (const [key, session] of this.heartbeatSessions.entries()) {
      const activeTime = now - session.lastHeartbeat;
      const activeMinutes = Math.round(activeTime / (1000 * 60));
      
      if (activeTime > heartbeatTimeout) {
        timeoutCount++;
        timeoutSessions.push(key);
        
        this.logger.warn(`💔 心跳超时（${activeMinutes}分钟 > 30分钟），自动停止预览服务: ${session.playgroundId}, 会话: ${session.sessionId}`);
        
        // 移除超时的会话
        this.heartbeatSessions.delete(key);
        
        // 停止对应的PM2进程
        try {
          await this.stopCustomPreviewWithPM2(session.playgroundId);
          this.logger.log(`✅ 已停止超时的PM2预览服务: ${session.playgroundId}`);
        } catch (error) {
          this.logger.error(`❌ 停止超时PM2进程失败: ${session.playgroundId}`, error);
        }
      } else {
        // 记录还未超时的会话状态（调试用）
        const remainingMinutes = Math.round((heartbeatTimeout - activeTime) / (1000 * 60));
        this.logger.debug(`💓 [HeartbeatCheck] 会话 ${key}: 活跃时间 ${activeMinutes}分钟, 剩余 ${remainingMinutes}分钟超时`);
      }
    }

    // 输出心跳检查汇总
    if (timeoutCount > 0) {
      this.logger.log(`📊 [HeartbeatCheck] 本次检查结果: ${timeoutCount} 个会话超时, ${this.heartbeatSessions.size} 个会话继续活跃`);
    } else if (totalSessions > 0) {
      this.logger.debug(`📊 [HeartbeatCheck] 本次检查结果: 所有 ${totalSessions} 个会话均正常`);
    }
  }

  /**
   * 注册心跳会话（增加详细信息）
   */
  registerHeartbeatSession(playgroundId: string, sessionId: string, user: string) {
    const key = `${playgroundId}:${sessionId}`;
    const now = Date.now();
    this.heartbeatSessions.set(key, {
      sessionId,
      user,
      playgroundId,
      lastHeartbeat: now
    });
    
    // 输出更详细的注册信息
    const totalSessions = this.heartbeatSessions.size;
    this.logger.log(`💓 注册心跳会话: ${key} (用户: ${user})`);
    this.logger.log(`📊 当前活跃会话数: ${totalSessions}, 超时时间: 30分钟`);
  }

  /**
   * 更新心跳时间戳（增加活跃时间显示）
   */
  updateHeartbeat(playgroundId: string, sessionId: string) {
    const key = `${playgroundId}:${sessionId}`;
    const session = this.heartbeatSessions.get(key);
    if (session) {
      const now = Date.now();
      const lastUpdateTime = session.lastHeartbeat;
      session.lastHeartbeat = now;
      
      // 计算并显示会话活跃时间
      const activeTime = Math.round((now - lastUpdateTime) / 1000);
      this.logger.debug(`💓 更新心跳: ${key} (间隔: ${activeTime}秒)`);
    } else {
      this.logger.warn(`⚠️ 心跳会话不存在: ${key}`);
      this.logger.debug(`📋 当前活跃会话: ${Array.from(this.heartbeatSessions.keys()).join(', ')}`);
    }
  }

  /**
   * 注销心跳会话（增加会话统计）
   */
  unregisterHeartbeatSession(playgroundId: string, sessionId: string) {
    const key = `${playgroundId}:${sessionId}`;
    const removed = this.heartbeatSessions.delete(key);
    if (removed) {
      const remainingSessions = this.heartbeatSessions.size;
      this.logger.log(`💔 注销心跳会话: ${key}`);
      this.logger.log(`📊 剩余活跃会话数: ${remainingSessions}`);
    } else {
      this.logger.warn(`⚠️ 要注销的心跳会话不存在: ${key}`);
    }
  }

  /**
   * 获取心跳会话状态信息
   */
  getHeartbeatSessionsStatus(): Array<{
    key: string;
    playgroundId: string;
    sessionId: string;
    user: string;
    lastHeartbeat: Date;
    activeTimeMinutes: number;
    timeUntilTimeout: number;
  }> {
    const now = Date.now();
    const heartbeatTimeout = 30 * 60 * 1000; // 30分钟
    
    return Array.from(this.heartbeatSessions.entries()).map(([key, session]) => {
      const activeTime = now - session.lastHeartbeat;
      const timeUntilTimeout = Math.max(0, heartbeatTimeout - activeTime);
      
      return {
        key,
        playgroundId: session.playgroundId,
        sessionId: session.sessionId,
        user: session.user,
        lastHeartbeat: new Date(session.lastHeartbeat),
        activeTimeMinutes: Math.round(activeTime / (1000 * 60)),
        timeUntilTimeout: Math.round(timeUntilTimeout / (1000 * 60)) // 分钟
      };
    });
  }

  /**
   * 启动多次日志同步，确保捕获完整的应用启动过程（覆盖5分钟启动窗口）
   */
  private async startMultipleLogSync(playgroundId: string, appName: string, workingDir: string): Promise<void> {
    try {
      this.logger.log(`🚀 [PM2-MultiSync] 开始多次日志同步: ${appName} (适应各种启动速度)`);
      
      // 等待Git操作完成后开始同步
      setTimeout(async () => {
        await this.waitForGitOperationsToComplete(playgroundId);
        await this.performMultipleLogSync(playgroundId, appName, workingDir);
      }, 2000);
      
    } catch (error) {
      this.logger.error(`❌ [PM2-MultiSync] 启动多次日志同步失败:`, error);
    }
  }

  /**
   * 执行多次日志同步，适应不同启动速度的项目
   */
  private async performMultipleLogSync(playgroundId: string, appName: string, workingDir: string): Promise<void> {
    const maxSyncCount = 20; // 增加到20次，适应慢启动项目
    
    // 优化同步间隔策略：
    // 前期密集 (0-30秒): 快速捕获常规项目启动
    // 中期适中 (30秒-2分钟): 捕获中等复杂度项目
    // 后期稀疏 (2-5分钟): 确保慢启动项目也能被捕获
    const syncIntervals = [
      // 前期密集同步 (0-30秒) - 适应快速启动项目
      0, 2000, 4000, 6000, 8000, 10000, 15000, 20000, 25000, 30000,
      
      // 中期适中同步 (30秒-2分钟) - 适应中等复杂度项目
      40000, 50000, 60000, 75000, 90000,
      
      // 后期稀疏同步 (2-5分钟) - 适应慢启动项目
      120000, 150000, 180000, 240000, 300000 // 最长5分钟
    ];
    
    this.logger.log(`🚀 [PM2-MultiSync] 启动多次日志同步: ${appName} (共${maxSyncCount}次，覆盖5分钟启动窗口)`);
    
    for (let i = 0; i < maxSyncCount; i++) {
      const syncNumber = i + 1;
      const interval = syncIntervals[i];
      
      setTimeout(async () => {
        try {
          await this.syncPM2LogsOnce(playgroundId, appName, workingDir, syncNumber);
          
          if (syncNumber === maxSyncCount) {
            this.logger.log(`✅ [PM2-MultiSync] 多次日志同步完成: ${appName} (共${maxSyncCount}次，历时5分钟)`);
          } else if (syncNumber === 10) {
            this.logger.log(`⏱️ [PM2-MultiSync] 快速阶段同步完成: ${appName} (前10次，后续将继续监控慢启动项目)`);
          }
        } catch (error) {
          this.logger.error(`❌ [PM2-MultiSync] 第${syncNumber}次同步失败: ${appName}`, error);
        }
      }, interval);
    }
  }

  /**
   * 智能增量同步PM2日志到playground logs（避免重复）
   * 使用内容hash去重，只同步新增的日志内容
   */
  private async syncPM2LogsOnce(playgroundId: string, appName: string, workingDir: string, syncNumber?: number): Promise<void> {
    try {
      const shell = await import('shelljs');
      const fsModule = await import('fs');
      const crypto = await import('crypto');
      
      
      // 获取playground的日志文件路径
      const playgroundLogPath = this.getPlaygroundPath(playgroundId, 'logs', 'log.txt');
      const playgroundLogDir = path.dirname(playgroundLogPath);

      // 确保playground日志目录存在
      if (!fsModule.existsSync(playgroundLogDir)) {
        await fsModule.promises.mkdir(playgroundLogDir, { recursive: true });
        this.logger.log(`📁 [PM2-LogSync] 创建playground日志目录: ${playgroundLogDir}`);
      }

      const syncInfo = syncNumber ? `第${syncNumber}次` : '手动';
      this.logger.log(`🔄 [PM2-LogSync] ${syncInfo}同步 ${appName} 的日志...`);
      
      // 初始化该应用的日志缓存
      const cacheKey = `${playgroundId}:${appName}`;
      if (!this.logSyncCache.has(cacheKey)) {
        this.logSyncCache.set(cacheKey, new Set());
      }
      const syncedHashes = this.logSyncCache.get(cacheKey)!;
      
      try {
        // 检查Git状态，如果有冲突就跳过
        const srcDir = this.getPlaygroundPath(playgroundId, 'src');
        try {
          const gitStatusResult = shell.exec(`cd "${srcDir}" && git status --porcelain logs/log.txt`, { silent: true });
          if (gitStatusResult.code === 0 && gitStatusResult.stdout.trim()) {
            this.logger.warn(`⚠️ [PM2-LogSync] logs/log.txt有Git修改，跳过日志同步`);
            return;
          }
        } catch (gitError) {
          // Git检查失败不影响日志同步
        }

        // 根据同步次数调整行数，第一次获取更多日志
        const logLines = syncNumber === 1 ? 150 : (syncNumber && syncNumber <= 3 ? 50 : 30);
        const logsResult = shell.exec(`pm2 logs ${appName} --lines ${logLines} --raw --nostream`, { silent: true });
        
        if (logsResult.code !== 0) {
          this.logger.warn(`⚠️ [PM2-LogSync] PM2进程 ${appName} 日志获取失败`);
          return;
        }
        
        if (logsResult.stdout && logsResult.stdout.trim()) {
          const rawLogLines = logsResult.stdout.trim().split('\n');
          const newValidLogs: string[] = [];
          let newLogCount = 0;
          let duplicateCount = 0;
          
          for (const line of rawLogLines) {
            // 更智能的日志过滤逻辑
            const trimmedLine = line.trim();
            
            // 跳过完全空行
            if (!trimmedLine) {
              continue;
            }
            
            // 更精确的PM2系统消息过滤
            if (trimmedLine.includes('[TAILING]') ||
                trimmedLine.includes('last ') ||
                trimmedLine.includes('Tailing last') ||
                trimmedLine.includes('-error.log') ||
                trimmedLine.includes('-out.log') ||
                trimmedLine.match(/^\d+\|[^|]*\|\s*$/) // 只有进程名的空行
            ) {
              continue;
            }
            
            // 更准确的PM2前缀解析
            let actualLog = trimmedLine;
            
            // 去掉PM2的进程前缀 (如: "0|custom-p | " 或 "0|custom-preview-xxx | ")
            const pm2PrefixMatch = actualLog.match(/^\d+\|[^|]*\|\s*(.*)/);
            if (pm2PrefixMatch && pm2PrefixMatch[1]) {
              actualLog = pm2PrefixMatch[1].trim();
            }
            
            // 跳过处理后仍然为空的行
            if (!actualLog) {
              continue;
            }
            
            // 增强重要日志识别，特别是错误和状态信息
            const isImportantLog = (
              actualLog.includes('Server is listening at:') || // 服务器启动信息
              actualLog.includes('http://localhost:') || // 本地端口信息
              actualLog.includes('http://') || // 网络地址信息
              actualLog.includes('compiled') || // 编译状态
              actualLog.includes('Compiling') || // 编译中
              actualLog.includes('ready') || // 就绪状态
              actualLog.includes('Starting') || // 启动状态
              actualLog.includes('Config:') || // 配置文件信息
              actualLog.includes('Use ') || // 工具版本信息
              actualLog.includes('Warning:') || // 警告信息
              actualLog.includes('Error:') || // 错误信息
              actualLog.includes('Info:') || // 信息
              actualLog.includes('ELIFECYCLE') || // npm/pnpm生命周期错误
              actualLog.includes('Command failed') || // 命令执行失败
              actualLog.includes('进程退出') || // 进程退出信息
              actualLog.includes('exit code') || // 退出代码
              actualLog.includes('SIGTERM') || // 信号
              actualLog.includes('SIGKILL') || // 信号
              actualLog.includes('[Launch]') // 启动脚本日志
            );
            
            // 过滤掉Windows路径但保留重要的配置路径
            const hasWindowsPath = actualLog.includes('C:\\Users\\<USER>\n') + '\n';
            await fsModule.promises.appendFile(playgroundLogPath, allLogs, 'utf8');
            this.logger.log(`✅ [PM2-LogSync] ${syncInfo}同步了 ${newLogCount} 条新日志到: ${playgroundLogPath} (跳过${duplicateCount}条重复)`);
          } else {
            if (duplicateCount > 0) {
              this.logger.log(`ℹ️ [PM2-LogSync] ${syncInfo}没有新日志，跳过${duplicateCount}条重复日志`);
            } else {
              this.logger.log(`ℹ️ [PM2-LogSync] ${syncInfo}没有找到有效的日志内容`);
            }
          }
          
          // 限制缓存大小，避免内存泄漏
          if (syncedHashes.size > 1000) {
            // 清理最旧的一半缓存
            const hashArray = Array.from(syncedHashes);
            const toKeep = hashArray.slice(hashArray.length / 2);
            syncedHashes.clear();
            toKeep.forEach(hash => syncedHashes.add(hash));
            this.logger.debug(`🧹 [PM2-LogSync] 清理日志缓存，保留${toKeep.length}条记录`);
          }
        }
        
      } catch (error) {
        this.logger.error(`❌ [PM2-LogSync] 增量同步日志失败:`, error);
      }
      
    } catch (error) {
      this.logger.error(`❌ [PM2-LogSync] 增量日志同步失败:`, error);
    }
  }
  
  /**
   * 等待Git操作完成
   */
  private async waitForGitOperationsToComplete(playgroundId: string): Promise<void> {
    try {
      
      const fsModule = await import('fs');
      const srcDir = this.getPlaygroundPath(playgroundId, 'src');
      
      // 最多等待30秒
      const maxWaitTime = 30000;
      const startTime = Date.now();
      
      while (Date.now() - startTime < maxWaitTime) {
        try {
          // 检查Git锁文件是否存在
          const gitLockFiles = [
            path.join(srcDir, '.git', 'index.lock'),
            path.join(srcDir, '.git', 'refs', 'heads', 'HEAD.lock'),
            path.join(srcDir, '.git', 'HEAD.lock')
          ];
          
          let hasGitLock = false;
          for (const lockFile of gitLockFiles) {
            if (fsModule.existsSync(lockFile)) {
              hasGitLock = true;
              break;
            }
          }
          
          if (!hasGitLock) {
            this.logger.log(`✅ [GitWait] Git操作已完成，可以安全启动日志同步`);
            return;
          }
          
          this.logger.debug(`⏳ [GitWait] Git操作进行中，继续等待...`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
          
        } catch (error) {
          // 如果检查失败，认为Git操作已完成
          this.logger.debug(`⚠️ [GitWait] Git状态检查失败，假设操作已完成: ${error.message}`);
          return;
        }
      }
      
      this.logger.warn(`⏰ [GitWait] Git操作等待超时，强制继续`);
      
    } catch (error) {
      this.logger.error(`❌ [GitWait] 等待Git操作失败:`, error);
    }
  }

  /**
   * 动态检测项目的启动命令和包管理器
   */
  private async detectStartCommand(workingDir: string): Promise<{ detectedCommand: string; packageManager: string }> {
    try {
      // 动态导入fs模块
      const fsModule = await import('fs');
      const packageJsonPath = path.join(workingDir, 'package.json');
      
      // 检查package.json是否存在
      if (!fsModule.existsSync(packageJsonPath)) {
        this.logger.warn(`⚠️ [DetectCommand] package.json不存在: ${packageJsonPath}`);
        return { detectedCommand: 'start', packageManager: 'npm' };
      }

      // 读取package.json
      const packageJsonContent = fsModule.readFileSync(packageJsonPath, 'utf8');
      const packageJson = JSON.parse(packageJsonContent);
      const scripts = packageJson.scripts || {};

      this.logger.log(`📋 [DetectCommand] 可用脚本: ${Object.keys(scripts).join(', ')}`);

      // 检测包管理器
      let packageManager = 'npm'; // 默认
      
      if (fsModule.existsSync(path.join(workingDir, 'pnpm-lock.yaml'))) {
        packageManager = 'pnpm';
      } else if (fsModule.existsSync(path.join(workingDir, 'yarn.lock'))) {
        packageManager = 'yarn';
      } else if (fsModule.existsSync(path.join(workingDir, 'package-lock.json'))) {
        packageManager = 'npm';
      }

      // 启动脚本优先级
      const startCommands = ['start', 'dev', 'serve', 'preview', 'develop'];
      
      let detectedCommand = 'start'; // 默认
      
      for (const cmd of startCommands) {
        if (scripts[cmd]) {
          detectedCommand = cmd;
          this.logger.log(`✅ [DetectCommand] 选择启动脚本: ${cmd}`);
          break;
        }
      }
      
      if (!scripts[detectedCommand]) {
        this.logger.warn(`⚠️ [DetectCommand] 未找到合适的启动脚本，使用默认: ${detectedCommand}`);
      }

      return { detectedCommand, packageManager };
      
    } catch (error) {
      this.logger.error(`❌ [DetectCommand] 检测启动命令失败:`, error);
      return { detectedCommand: 'start', packageManager: 'npm' };
    }
  }

  /**
   * 将指定文件添加到.gitignore，避免提交到Git仓库
   */
  private async addToGitignore(workingDir: string, fileName: string): Promise<void> {
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      const gitignorePath = path.join(workingDir, '.gitignore');
      
      // 检查.gitignore文件是否存在
      let gitignoreContent = '';
      if (fs.existsSync(gitignorePath)) {
        try {
          gitignoreContent = await fs.promises.readFile(gitignorePath, 'utf8');
        } catch (error) {
          this.logger.warn(`⚠️ [GitIgnore] 读取.gitignore文件失败: ${error.message}`);
        }
      }
      
      // 检查文件是否已经在.gitignore中
      const lines = gitignoreContent.split('\n');
      const fileAlreadyIgnored = lines.some(line => {
        const trimmedLine = line.trim();
        return trimmedLine === fileName || 
               trimmedLine === `/${fileName}` || 
               trimmedLine === `./${fileName}`;
      });
      
      if (fileAlreadyIgnored) {
        this.logger.log(`✅ [GitIgnore] ${fileName} 已在.gitignore中，无需重复添加`);
        return;
      }
      
      // 添加文件到.gitignore
      const newContent = gitignoreContent.trim() ? 
        `${gitignoreContent}\n\n# PM2启动文件\n${fileName}\n` : 
        `# PM2启动文件\n${fileName}\n`;
      
      await fs.promises.writeFile(gitignorePath, newContent, 'utf8');
      this.logger.log(`📝 [GitIgnore] 已将 ${fileName} 添加到.gitignore`);
      
         } catch (error) {
       this.logger.error(`❌ [GitIgnore] 添加${fileName}到.gitignore失败:`, error);
       // 不抛出错误，因为这不是关键功能，不应该阻塞PM2启动
       this.logger.warn(`⚠️ [GitIgnore] 继续执行PM2启动，但${fileName}可能会被Git跟踪`);
     }
   }

  /**
   * 清理进程对应的launch.js文件
   */
  private async cleanupLaunchFile(playgroundId: string, appName: string): Promise<void> {
    try {
      const fs = await import('fs');
      const path = await import('path');
      
      // 从进程名推断工作目录
      const playgroundRoot = this.getPlaygroundPath(playgroundId);
      const workingDir = this.getPlaygroundPath(playgroundId, 'src');
      const launchPath = path.join(workingDir, 'launch.js');
      
      // 检查launch.js文件是否存在
      if (fs.existsSync(launchPath)) {
        try {
          await fs.promises.unlink(launchPath);
          this.logger.log(`🗑️ [PM2] 已清理launch.js文件: ${launchPath}`);
        } catch (error) {
          this.logger.warn(`⚠️ [PM2] 清理launch.js文件失败: ${error.message}`);
        }
      } else {
        this.logger.log(`ℹ️ [PM2] launch.js文件不存在，无需清理: ${launchPath}`);
      }
      
    } catch (error) {
      this.logger.error(`❌ [PM2] 清理launch.js文件异常:`, error);
      // 不抛出错误，因为这不是关键操作
    }
  }
} 