import { Controller, Get, Post, Param, Body, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { PM2PreviewService } from './pm2-preview.service';

@Controller('pm2-preview')
export class PM2PreviewController {
  private readonly logger = new Logger(PM2PreviewController.name);

  constructor(
    private readonly pm2PreviewService: PM2PreviewService,
    private readonly prisma: PrismaService
  ) {}

  /**
   * 重启spec-to-prod-code类型playground的自定义预览服务
   */
  @Post('restart/:playgroundId')
  async restartCustomPreview(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string }
  ) {
    try {
      this.logger.log(`🔄 重启自定义预览服务: ${playgroundId}, 用户: ${body.user}`);
      
      // 先获取playground信息以确定页面名称
      const playground = await this.prisma.playground.findUnique({
        where: { id: playgroundId }
      });

      if (!playground) {
        throw new Error(`Playground ${playgroundId} 不存在`);
      }

      // 从playground的tags中提取页面名称
      const pageTag = playground.tags?.find(tag => tag.startsWith('page-'));
      const pageName = pageTag ? pageTag.replace('page-', '') : 'default';

      const result = await this.pm2PreviewService.restartCustomPreviewWithPM2(
        playgroundId,
        pageName,
        body.user
      );
      
      return {
        success: true,
        message: '自定义预览服务重启成功',
        processId: result.processId,
        url: result.url,
        port: result.port
      };
    } catch (error) {
      this.logger.error(`❌ 重启自定义预览服务失败: ${playgroundId}`, error);
      throw new Error(`PM2重启失败: ${error.message}`);
    }
  }

  /**
   * 智能启动自定义预览服务（先检查状态再决定是否启动）
   */
  @Post('start/:playgroundId')
  async startCustomPreviewWithHeartbeat(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.logger.log(`🔍 智能启动自定义预览服务: ${playgroundId}, 用户: ${body.user}, 会话: ${body.sessionId}`);
      
      // 先检查是否已有运行中的预览服务
      const existingProcesses = await this.pm2PreviewService.getCustomPreviewStatusWithPM2(playgroundId);
      const runningProcesses = existingProcesses.filter(p => 
        p.status === 'online' || p.status === 'running'
      );

      if (runningProcesses.length > 0) {
        this.logger.log(`✨ 发现现有运行中的预览服务，直接连接: ${playgroundId}`);
        
        // 注册心跳会话，连接到现有服务
        this.pm2PreviewService.registerHeartbeatSession(playgroundId, body.sessionId, body.user);
        
        // 尝试从现有进程获取URL信息（可以从PM2日志中解析）
        let previewUrl: string | undefined;
        try {
          // 这里可以添加从PM2日志中解析URL的逻辑
          // 暂时返回一个占位符，前端会尝试从其他方式获取
        } catch (error) {
          this.logger.warn(`⚠️ 从现有进程获取URL失败: ${error.message}`);
        }
        
        return {
          success: true,
          message: `已连接到现有预览服务 (${runningProcesses.length}个进程运行中)`,
          processId: runningProcesses[0].name,
          url: previewUrl,
          existingService: true, // 标记这是现有服务
          runningProcesses: runningProcesses.length
        };
      }

      // 如果没有运行中的服务，则启动新的
      this.logger.log(`🚀 没有现有服务，启动新的预览服务: ${playgroundId}`);
      
      const playground = await this.prisma.playground.findUnique({
        where: { id: playgroundId }
      });

      if (!playground) {
        throw new Error(`Playground ${playgroundId} 不存在`);
      }

      const pageTag = playground.tags?.find(tag => tag.startsWith('page-'));
      const pageName = pageTag ? pageTag.replace('page-', '') : 'default';

      const result = await this.pm2PreviewService.startCustomPreviewWithPM2(
        playgroundId,
        pageName,
        body.user
      );

      // 注册心跳会话
      this.pm2PreviewService.registerHeartbeatSession(playgroundId, body.sessionId, body.user);
      
      return {
        success: true,
        message: '新自定义预览服务启动成功',
        processId: result.processId,
        url: result.url,
        port: result.port,
        existingService: false // 标记这是新启动的服务
      };
    } catch (error) {
      this.logger.error(`❌ 智能启动自定义预览服务失败: ${playgroundId}`, error);
      throw new Error(`PM2启动失败: ${error.message}`);
    }
  }

  /**
   * 发送心跳信号
   */
  @Post('heartbeat/:playgroundId')
  async sendCustomPreviewHeartbeat(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.pm2PreviewService.updateHeartbeat(playgroundId, body.sessionId);
      
      return {
        success: true,
        message: '心跳信号发送成功'
      };
    } catch (error) {
      this.logger.error(`❌ 发送心跳信号失败: ${playgroundId}`, error);
      // 心跳失败不抛异常，返回错误信息即可
      return {
        success: false,
        message: `心跳失败: ${error.message}`
      };
    }
  }

  /**
   * 停止自定义预览服务并注销心跳
   */
  @Post('stop/:playgroundId')
  async stopCustomPreviewHeartbeat(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; sessionId: string }
  ) {
    try {
      this.logger.log(`🛑 停止自定义预览服务: ${playgroundId}, 用户: ${body.user}, 会话: ${body.sessionId}`);
      
      // 使用PM2停止自定义预览服务
      const result = await this.pm2PreviewService.stopCustomPreviewWithPM2(playgroundId);

      // 注销心跳会话
      this.pm2PreviewService.unregisterHeartbeatSession(playgroundId, body.sessionId);
      
      return {
        success: true,
        message: '自定义预览服务停止成功',
        stoppedProcesses: result.stoppedProcesses
      };
    } catch (error) {
      this.logger.error(`❌ 停止自定义预览服务失败: ${playgroundId}`, error);
      throw new Error(`PM2停止失败: ${error.message}`);
    }
  }

  /**
   * 获取自定义预览进程状态
   */
  @Get('status/:playgroundId')
  async getCustomPreviewStatus(
    @Param('playgroundId') playgroundId: string
  ) {
    try {
      const processes = await this.pm2PreviewService.getCustomPreviewStatusWithPM2(playgroundId);
      
      return {
        success: true,
        processes
      };
    } catch (error) {
      this.logger.error(`❌ 获取自定义预览状态失败: ${playgroundId}`, error);
      return {
        success: false,
        message: `获取状态失败: ${error.message}`,
        processes: []
      };
    }
  }

  /**
   * 获取心跳会话状态信息
   */
  @Get('heartbeat-sessions')
  async getHeartbeatSessionsStatus() {
    try {
      const sessions = this.pm2PreviewService.getHeartbeatSessionsStatus();
      
      return {
        success: true,
        sessions,
        totalSessions: sessions.length
      };
    } catch (error) {
      this.logger.error(`❌ 获取心跳会话状态失败:`, error);
      return {
        success: false,
        message: `获取心跳会话状态失败: ${error.message}`,
        sessions: [],
        totalSessions: 0
      };
    }
  }

  /**
   * 注册自定义预览服务配置（不启动）
   */
  @Post('register/:playgroundId')
  async registerCustomPreviewServiceOnly(
    @Param('playgroundId') playgroundId: string,
    @Body() body: { user: string; pageName?: string; projectDir?: string }
  ) {
    try {
      const playground = await this.prisma.playground.findUnique({
        where: { id: playgroundId }
      });

      if (!playground) {
        throw new Error(`Playground ${playgroundId} 不存在`);
      }

      // 从playground的tags中提取页面名称（如果未提供）
      const pageName = body.pageName || 
        (playground.tags?.find(tag => tag.startsWith('page-'))?.replace('page-', '') || 'default');

      const result = await this.pm2PreviewService.registerCustomPreviewServiceOnly(
        playgroundId,
        pageName,
        body.user,
        body.projectDir || 'src'
      );
      
      return {
        success: result.registered,
        message: result.registered ? '自定义预览服务配置注册成功' : '注册失败',
        serviceName: result.serviceName,
        error: result.error
      };
    } catch (error) {
      this.logger.error(`❌ 注册自定义预览服务配置失败: ${playgroundId}`, error);
      return {
        success: false,
        message: `注册失败: ${error.message}`
      };
    }
  }
} 