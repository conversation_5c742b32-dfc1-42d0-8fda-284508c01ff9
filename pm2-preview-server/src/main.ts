import { ValidationPipe, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { safeStringify } from './utils/index'

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用验证管道
  app.useGlobalPipes(new ValidationPipe());

  // 启用CORS
  app.enableCors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    credentials: true,
  });

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('PM2 Preview Server')
    .setDescription('API for managing PM2 preview processes')
    .setVersion('1.0')
    .addTag('pm2-preview')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  //  Logger 实例
  const logger: Logger = new Logger('NestApplication');

  // 捕获未处理的异常
  process.on('uncaughtException', (error) => {
    logger.error(
      'Uncaught Exception',
      error?.stack || safeStringify(error)
    );
  });

  // 捕获未处理的拒绝的Promise
  process.on('unhandledRejection', (reason, promise) => {
    logger.error(`unhandledRejection: ${reason}`, promise);
  });



  const port = process.env.PM2_PREVIEW_PORT || 3001;
  await app.listen(port);

  console.log(`🚀 PM2 Preview Server is running on: http://localhost:${port}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api`);
}

bootstrap(); 