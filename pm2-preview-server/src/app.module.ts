import { homedir } from 'node:os';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PM2PreviewModule } from './pm2-preview/pm2-preview.module';
import { PrismaModule } from './prisma/prisma.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env.development'],
      load: [
        () => {
          const expandPath = (dir?: string) => {
            if (dir && dir.startsWith('~')) {
              return dir.replace('~', homedir());
            }
            return dir;
          };
          console.log('目录配置为：', {
            HOME_DIR: expandPath(process.env.HOME_DIR),
            TEMP_DIR: expandPath(process.env.TEMP_DIR),
            WEBIDE_DIR: expandPath(process.env.WEBIDE_DIR),
          })
          return {
            HOME_DIR: expandPath(process.env.HOME_DIR),
            TEMP_DIR: expandPath(process.env.TEMP_DIR),
            WEBIDE_DIR: expandPath(process.env.WEBIDE_DIR),
          };
        },
      ],
    }),
    PrismaModule,
    PM2PreviewModule,
  ],
})
export class AppModule { } 