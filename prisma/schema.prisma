// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Playground {
  id         String @id @default(nanoid(8)) @map("_id")
  type       String
  desc       String
  name       String
  created    DateTime @default(now())
  tags       String[]
  user       String
  model      String
  projectId  String?
  isPublic   Boolean?

  // HTML Agent 执行配置
  enableAutoIteration Boolean? // 是否启用自动迭代
  enableStepByStep    Boolean? // 是否启用分步执行
  enablePreview       Boolean? // 是否启用预览功能
  enableCustomPreview Boolean? // 是否支持用户自定义预览地址（如spec-to-prod-code场景）
  customPreviewUrl    String?  // 用户自定义的预览地址（持久化保存）

  // 分步执行数据（仅当enableStepByStep为true时有效）
  stepByStepData Json? // 存储步骤执行的完整状态

  // 后台任务关联
  backgroundTaskkId String? // 关联的后台任务ID
}

enum UserRole {
  USER      @map("user")
  ASSISTANT @map("assistant")
}

model ChatMessage {
  id              String @id @map("_id")
  playgroundId    String
  role            UserRole
  content         String
  attachments     Json[] @default([])
  parts           Json[] @default([])
  annotations     Json[] @default([])
  created         DateTime @default(now())
  
  // 消息状态字段
  status          String? @default("completed") // pending, processing, completed, failed
  taskId          String? // 关联的后台任务ID（如果是后台任务触发的消息）
}

model Project {
  id                String @id @default(nanoid(8)) @map("_id")
  name              String
  description       String
  framework         String?
  componentLibrary  String?
  llmstxt           String?
  created           DateTime @default(now())
  user              String

  projectId         String? // 关联的项目代码模板ID

  updated           DateTime? @updatedAt @default(now())

  // 新增字段：Git相关配置
  gitUrl            String? // Git仓库地址
  gitBranch         String? // Git分支（生码分支）
  gitCodeDir        String? // 代码生成目录

  // 新增字段：预览服务配置
  previewStartCommand String? // 自定义预览启动命令，如 "npm run start"

  // 新增字段：蓝湖配置
  lanhuProjectId    String? // 蓝湖项目ID
  lanhuToken        String? // 蓝湖Token

  // 关联的设计稿页面
  pages             DesignPage[]

  // 关联的转码任务
  transcodeTasks BackgroundTask[]

  // 项目级代码生成任务状态
  status            String? // 代码生成任务状态

  // 转码配置
  config            Json? // 转码相关配置，如输出格式、优化选项等
  
  // 关联的自定义提示词模板
  promptTemplates   ProjectPromptTemplate[]
}


// 设计稿页面（主页面）
model DesignPage {
  id                String @id @default(nanoid(8)) @map("_id")
  name              String // 页面名称
  description       String? // 页面描述
  designProjectId   String // 所属设计稿转码工程ID
  
  // 页面原型
  prototypes        DesignPagePrototype[]
  
  // 时间戳
  created           DateTime @default(now())
  updated           DateTime @updatedAt

  // 状态
  status            String @default("pending") // pending, processing, completed, failed

  // 原型合并结果（任务完成后填充）
  resultHtml        String? // 原型合并后的HTML
  resultCss         String? // 原型合并后的CSS
  playgroundId      String? // 关联的chat页面ID，用于查看转码结果
  
  // 关联的设计稿转码工程
  designProject     Project @relation(fields: [designProjectId], references: [id], onDelete: Cascade)
}

// 设计稿页面原型
model DesignPagePrototype {
  id                String @id @default(nanoid(8)) @map("_id")
  prototypeName     String // 原型名称（如A、B、C）
  englishName       String? // 英文名
  designPageId      String // 所属页面ID
  
  // 设计稿文件
  htmlContent       String? // HTML文件内容
  cssContent        String? // CSS文件内容
  htmlFileName      String? // 原始HTML文件名
  cssFileName       String? // 原始CSS文件名
  imgFileName       String? // 原始图片存储地址
  imgFileLink       String? // 图片访问链接
  lanhuVersionId    String? // 蓝湖版本ID
  imgWidth          String?// 图片宽度
  imgHeight         String? // 图片高度
  
  // 状态
  status            String @default("pending") // pending, processing, completed, failed
  
  // 转码结果（任务完成后填充）
  resultHtml        String? // 转码后的HTML
  resultCss         String? // 转码后的CSS
  playgroundId      String? // 关联的chat页面ID，用于查看转码结果
  
  // 关联的转码任务条目
  transcodeTaskItems BackgroundTaskItem[]

  // 关联的页面
  designPage DesignPage @relation(fields: [designPageId], references: [id], onDelete: Cascade)

  // 智能切图资产
  slicedAssets      Json? // 存储切图资产信息
}

// 通用后台任务模型
model BackgroundTask {
  id                String @id @default(nanoid(8)) @map("_id")
  taskType          String // 任务类型，如：design-transcode, data-export, batch-process等
  taskName          String // 任务名称
  user              String // 执行用户
  
  // 任务状态
  status            String @default("pending") // pending, processing, completed, failed, cancelled
  progress          Int @default(0) // 整体进度 0-100
  
  // 任务元数据（JSON格式，存储任务特定的配置信息）
  metadata          Json?
  
  // 关联的设计稿转码工程ID
  designProjectId   String?
  
  // 任务配置
  model             String? @default("htsc::saas-deepseek-v3") // 使用的模型
  enableAutoIteration Boolean? @default(false) // 是否启用自动迭代
  enableStepByStep    Boolean? @default(false) // 是否启用分步执行
  
  // 工作流相关字段
  workflowStatus    Json? // 工作流状态信息，包括各个节点的状态
  parentTaskId      String? // 父任务ID（用于子任务关联）
  
  // 新增：标签相关字段
  tags              String[] @default([]) // 用户自定义标签数组，支持按标签分类和搜索任务
  
  // 任务条目
  items             BackgroundTaskItem[]
  
  // 时间戳
  created           DateTime @default(now())
  updated           DateTime @updatedAt
  DesignProject     Project? @relation(fields: [designProjectId], references: [id])
}

// 通用后台任务条目
model BackgroundTaskItem {
  id                String @id @default(nanoid(8)) @map("_id")
  backgroundTaskId  String // 所属后台任务ID
  itemId            String // 条目标识符（如原型ID、文件ID等）
  itemName          String // 条目名称
  
  // 任务状态
  status            String @default("pending") // pending, processing, completed, failed
  progress          Int @default(0) // 进度 0-100
  stage             String @default("等待开始") // 当前阶段描述
  
  // 执行结果
  result            Json? // 执行结果（JSON格式）
  error             String? // 错误信息
  playgroundId      String? // 生成的chat页面ID
  
  // 条目元数据（JSON格式，存储条目特定的信息）
  metadata          Json?
  
  // 时间戳
  created           DateTime @default(now())
  updated           DateTime @updatedAt
  
  // 关联
  backgroundTask    BackgroundTask @relation(fields: [backgroundTaskId], references: [id], onDelete: Cascade)
  DesignPagePrototype   DesignPagePrototype? @relation(fields: [designPagePrototypeId], references: [id])
  designPagePrototypeId String?
}

// 系统全局配置
model SystemConfig {
  id                    String @id @default("system") @map("_id") // 固定ID，确保只有一条记录
  lanhuAuthorization    String? // 蓝湖全局授权令牌
  
  // 预留其他全局配置字段
  // 例如：AI模型配置、系统设置等
  
  created               DateTime @default(now())
  updated               DateTime @updatedAt
}

// 项目自定义提示词模板
model ProjectPromptTemplate {
  id                String @id @default(nanoid(8)) @map("_id")
  projectId         String // 关联项目ID
  taskType          String // 任务类型：img-to-code, img-visual-split, img-split-to-code, coords-to-layout, spec-to-prod-code, design-transcode, design-merge
  promptType        String // 提示词类型：main（主提示词）、check（自检查提示词）、target（任务目标提示词，仅spec-to-prod-code）
  content           String // 提示词内容
  
  // 版本和状态管理
  version           Int @default(1) // 版本号
  status            String @default("active") // active, archived
  
  // 时间戳
  created           DateTime @default(now())
  updated           DateTime @updatedAt
  
  // 用户信息
  createdBy         String // 创建用户
  updatedBy         String? // 最后更新用户
  
  // 关联项目
  project           Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  // 唯一索引：一个项目的每个任务类型和提示词类型组合应该是唯一的
  @@unique([projectId, taskType, promptType])
}
